<template>
  <el-dialog
    title="变量设置"
    v-model="isVisible"
    width="400px"
  >
    <div class="setting-wrapper">
      <el-form
        :model="field"
        :rules="rules"
        ref="form"
        label-position="top"
        @submit.native.prevent
      >
        <el-form-item label="字段类型" prop="keyType">
          <el-radio-group v-model="field.keyType">
            <el-radio-button :label="KEY_TYPE.TEXT">文本</el-radio-button>
            <el-radio-button :label="KEY_TYPE.PARAGRAPH">段落</el-radio-button>
            <el-radio-button :label="KEY_TYPE.SELECT">下拉选项</el-radio-button>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="变量KEY" prop="keyCode">
          <el-input v-model="field.keyCode" :minlength="1" :maxlength="20"/>
        </el-form-item>

        <el-form-item label="字段名称" prop="keyName">
          <el-input v-model="field.keyName" :minlength="1" :maxlength="50"/>
        </el-form-item>

        <el-form-item v-if="field.keyType === KEY_TYPE.TEXT" label="最大长度">
          <el-input-number v-model="field.params.max_length" controls-position="right" :min="1" :max="100" />
        </el-form-item>

        <el-form-item v-if="field.keyType === KEY_TYPE.PARAGRAPH" label="最大长度">
          <el-input-number v-model="field.params.max_length" controls-position="right" :min="1" :max="10000" />
        </el-form-item>

        <el-form-item v-else-if="field.keyType === KEY_TYPE.SELECT" label="选项">
          <el-input
            v-for="(item, idx) of field.params.options"
            :key="idx"
            :maxlength="100"
            v-model="field.params.options[idx]"
            class="input-option"
          >
            <template #append><Delete class="btn-remove-row" @click="removeRow(idx)" /></template>
          </el-input>
          <el-button :icon="Plus" size="small" class="btn-append-row" @click="appendRow" />
        </el-form-item>

        <el-form-item label="必填" prop="isRequired" for="__never">
          <el-switch v-model="field.isRequired" :active-value="1" :inactive-value="0" />
        </el-form-item>

      </el-form>
    </div>
    <template #footer>
      <el-button @click="isVisible=false">取 消</el-button>
      <el-button
        :disabled="!field.keyCode || !field.keyName"
        type="primary"
        @click="ok"
      >确 定</el-button>
    </template>
  </el-dialog>
</template>

<script setup>
import { KEY_TYPE } from '@/config/agent.js';
import { Plus, Delete } from '@element-plus/icons-vue';

// 是否显示对话框
const isVisible = ref(false);

// 传入的字段配置
const field = ref({});

// 传入的字段的索引
const curIdx = ref(0);

// 字段的校验规则
const rules = {
  keyCode: [
    { required: true, message: '请输入变量KEY', trigger: 'blur' },
    { pattern: /^[a-zA-Z_][a-zA-Z0-9_]*$/, message: '必须是字母，数字或下划线，且不能以数字开头!', trigger: 'blur' },
  ],
  keyName: [
    { required: true, message: '请输入字段名称', trigger: 'blur' },
  ]
};

// 表单组件的引用
const form = ref(null);

const emit = defineEmits(['update'])

/**
 * 传入字段设置，并打开弹窗
 */
function open(theIdx, theField) {
  let params = {};
  try {
    params = theField.remark?JSON.parse(theField.remark):{};
    if (typeof params !== 'object') {
      remark = {}
    }
  } catch {
    params = {}
  }
  if (!params.hasOwnProperty('options')) {
    params.options = [];
  }
  if (!params.hasOwnProperty('max_length')) {
    params.max_length = theField.keyType === KEY_TYPE.PARAGRAPH
      ? 1000
      : 50;
  }

  field.value = {
    keyType: theField.keyType,
    keyCode: theField.keyCode,
    keyName: theField.keyName,
    isRequired: theField.isRequired,
    params
  };
  curIdx.value = theIdx;
  isVisible.value = true;
}

/**
 * 确定
 */
function ok() {
  form.value.validate().then(() => {
    const params = {};

    switch(field.value.keyType) {
      case KEY_TYPE.TEXT:
        params.max_length = field.value.params.max_length;
        break;

      case KEY_TYPE.PARAGRAPH:
        params.max_length = field.value.params.max_length;
        break;

      case KEY_TYPE.SELECT:
        params.options = field.value.params.options;
        break;
    }

    const newValue = {
      keyCode: field.value.keyCode,
      keyType: field.value.keyType, 
      keyName: field.value.keyName,
      isRequired: field.value.isRequired ? 1 : 0,
      remark: JSON.stringify(params)
    };

    emit('update', curIdx.value, newValue);
    isVisible.value = false;
  });
}

/**
 * 添加下拉选项
 */
function appendRow() {
  field.value.params.options.push('');
}

/**
 * 删除下拉选项
 */
function removeRow(idx) {
  field.value.params.options.splice(idx, 1);
}

defineExpose({
  open
});
</script>

<style scoped>
.sub-title {
  position: relative;
  top: -20px;
}
.input-option {
  margin-top: 5px;
}
::v-deep .el-input-group__append {
  cursor: pointer;
}
.btn-remove-row {
  width: 16px;
}
.btn-append-row {
  margin-top: 5px;
}
</style>

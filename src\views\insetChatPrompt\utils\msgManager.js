import { reactive, ref } from "vue";
import md from "./markdown";

export class MessageItemManager {
  refMessageItemList = reactive([]);
  nowMessageText = "";
  nowMessageHtml = ref("");
  nowMessageRole = ref({});
  nowMessageLine = ref("");
  loading = ref(false);
  waitting = ref(false);
  nowSpeakIndex = ref(-1)
//   msgRecords = ref([]);

  pushUserMesage(content) {
    console.log(this.refMessageItemList);
    this.refMessageItemList.push({ type: 'user', message: content });
    // this.msgRecords.value.push({ role: "user", content: content });
  }

  pushSystemMesage(content,roleInfo){
    this.refMessageItemList.push({ type: 'robot', message: md.render(content),roleInfo:roleInfo });
    this.nowSpeakIndex.value = this.refMessageItemList.length - 1
  }

  appendSystemMesage(newMsg){
    if(this.nowSpeakIndex.value != -1){
        this.refMessageItemList[this.nowSpeakIndex.value].message = md.render(newMsg)
    }
  }

//   loadHistoryMessage(messages) {
//     messages.forEach((item) => {
//       this.refMessageItemList.push({ isUser: true, content: item.userMessage });
//       this.refMessageItemList.push({ isUser: false, content: item.agentMessage , roleInfo:this.nowMessageRole.value});
//     })
//   }

//   loadTeamHistoryMessage(messages) {
//     messages.forEach((item) => {
//       // this.refMessageItemList.push({ isUser: true, content: item.userMessage });
//       JSON.parse(item.teamMessage).forEach((item) => {
//         this.refMessageItemList.push({ isUser: false, content: item.agentMessage , roleInfo:{key:item.agent,name:''}});
//       })
//     })
//   }

  /**
   * 消息接收结束
   *
   * @param record 是否记录本次接收的消息
   */
  messageReceiveDone(record = true) {
    // if (this.nowMessageLine.value !== "") {
    //   this.nowMessageText += this.nowMessageLine.value;
    //   this.nowMessageHtml.value = md.render(this.nowMessageText);
    //   this.nowMessageLine.value = "";
    // }
    // if (record) {
    //   this.msgRecords.value.push({ role: "assistant", content: this.nowMessageText });
    // }
    // this.nowMessageText = "";
    // this.refMessageItemList.push({
    //   isUser: false,
    //   content: this.nowMessageHtml.value,
    //   roleInfo: this.nowMessageRole.value,
    // });
    this.loading.value = false;
    this.nowSpeakIndex.value = -1
    // this.nowMessageHtml.value = "";
    // this.nowMessageRole.value = {}
  }

  clearNowMessageRole(){
    this.nowMessageRole.value = {}
  }

  do() {
    this.loading.value = true;
  }

  waittingStart() {
    this.waitting.value = true;
  }

  waittingEnd() {
    this.waitting.value = false;
  }

  /**
   * 清空消息历史，包含界面显示的和记录的
   */
  cleanHistory() {
    // this.cleanRecords();
    // if (this.refMessageItemList.length > 0) {
    //   this.refMessageItemList.splice(0, this.refMessageItemList.length);
    // }
    // this.nowMessageRole.value = {}
  }

  /**
   * 清空消息记录
   */
  cleanRecords() {
    // if (this.msgRecords.length > 0) {
    //   this.msgRecords.value = [];
    // }
  }

  deleteLastMsgRecord() {
    // return this.msgRecords.value.pop();
  }

  /**
   * 向现在的消息中添加新内容
   * TODO 优化md的转换，减少转化次数
   * @param newContent 新的内容
   */
  appendNowMessageContent(newContent) {
    try {
    //   if (parse) {
    //     newContent = JSON.parse(`"${newContent}"`);
    //   }
    //   this.nowMessageText = newContent;
    //   this.nowMessageLine.value = "";
    //   this.nowMessageHtml.value = md.render(this.nowMessageText);
    //   this.nowMessageRole.value = role;
    } catch (e) {
      console.log(e);
    }
  }
}

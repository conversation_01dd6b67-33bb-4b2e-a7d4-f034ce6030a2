<template>
    <div class="background-image">
        <div class="chat-header">
            <div style="background-color: #5050E6;
                width:40px;height:40px;
                border-radius:25px;margin-right: 10px;
                display:flex;
                align-items:center;
                justify-content:center;
                ">
                <img :src="AIChatImage" width="30" height="30" />
                <!-- <span style="color:#FFFFFF;line-height:30px;padding:0 7px;">AI</span> -->
            </div>
            <div class="chat-name">智能助手</div>
        </div>
        <div class="ai-chat-content" ref="chatContentBox">
            <div class="robot-chat-content">
                <div class="title">试试以下 AI 功能，提升工作效率</div>
                <div class="instruct-list">
                    <template v-for="instruct in instructList" :key="instruct.id">
                        <span class="instruct-item" @click="clickInstructItem(instruct)">
                            <div class="left-item-box">
                                <img :src="baseUrl + instruct.icon" width="30" height="30" />
                                <p class="item-text">{{ instruct.label }}</p>
                            </div>
                            <el-icon><ArrowRight /></el-icon>
                        </span>
                    </template>
                </div>
            </div>
            <template v-for="msg in msgManager.refMessageItemList">
                <div class="chat-message">
                    <template v-if="msg.type=='user'">
                        <div class="user-chat-area">
                            <div class="user-box">
                                <div class="user-name">我</div>
                                <div class="user-icon"><img :src="mockUserImg"/></div>
                            </div>
                            <div class="user-chat-message">{{ msg.message }}</div>
                        </div>
                    </template>
                    <template v-if="msg.type=='robot'">
                        <div class="robot-chat-area">
                            <div class="robot-box">
                                <div class="robot-icon"><img :src="msg.roleInfo.icon_src?(baseUrl + msg.roleInfo.icon_src):mockDefaultImg"/></div>
                                <div class="robot-name">{{msg.roleInfo.name}}</div>
                            </div>
                            <v-md-preview class="robot-chat-message" :text="msg.message"/>
                        </div>
                    </template>
                </div>
            </template>
        </div>
        <div class="bottom-area">
            <div class="bar">
                <div class="tools-list" >
                    <template v-for="tool in toolList">
                        <div>
                            <el-popover
                                placement="top"
                                :width="200"
                                trigger="click"
                                content="this is content, this is content, this is content"
                                :disabled="tool.id != 3"
                                >
                                <template #reference>
                                    <div class="tools-item" @click="clickToolItem(tool)">
                                        <el-tooltip
                                            :content="tool.label"
                                            :disabled="!tool.isToptip"
                                            placement="top"
                                            >
                                            <img class="tool-icon" :src="tool.icon" width="20px" height="20px"/>
                                        </el-tooltip>
                                        <span class="tool-name"  v-if="!tool.isToptip">{{tool.label}}</span>
                                    </div>
                                </template>
                                <template #default>
                                    <div class="tool-pop-list">
                                        <template v-for="popTool in popToolList">
                                            <div class="tool-pop-item">
                                                <div class="tool-pop-item-icon"><img width="20px" height="20px" :src="baseUrl + popTool.icon" /></div>
                                                <div class="tool-pop-item-title">{{ popTool.label }}</div>
                                            </div>
                                        </template>
                                    </div>
                                </template>
                            </el-popover>
                        </div>
                    </template>
                </div>
            </div>
            <div class="submit-area">
                <div class="content-area">
                    <div class="user-input-area">
                        <div class="top-area">
                            <div class="text-area">
                                <textarea ref="userInputTextarea" v-model="userInputTextareaValue" :placeholder="userInputTextareaPlaceholer" autocomplete="off" 
                                    class="user-input" :class="{'show-tool-tip':userInputIsShowToolTip}" 
                                    :style="{'text-indent': textAreaIndent}" @keydown="handleKeyDown" @keydown.enter.shift.prevent="handleShiftEnter" @input="handleInput"></textarea>
                                <span ref="toolTip" class="tool-tip" 
                                    :style="{
                                        'display':userInputIsShowToolTip?'block':'none'
                                        ,'transform':toolTipTranslateY}"
                                >{{ toolTipContent }}</span>
                            </div>
                        </div>
                        <div class="bottom-info-area">
                            <div class="send-btn" :class="{'disabled':isDisabledSend}" @click="sendMessage"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <el-dialog v-model="uploadDialogVisible" style="margin-top: 15vh;width: 90%;">
            <DocUpload v-model="docFileUploads" :disabled="true" />
        </el-dialog>
    </div>
</template>
<script setup>
import AIChatImage from './svg/ai_chat.svg'
import DocUpload from '@/components/DocUpload/index.vue'
import VMdPreview from '@kangc/v-md-editor/lib/preview';
import '@kangc/v-md-editor/lib/style/base-editor.css';
import githubTheme from '@kangc/v-md-editor/lib/theme/github.js';
import '@kangc/v-md-editor/lib/theme/style/github.css';
import "highlight.js/styles/atom-one-light.css";
import hljs from 'highlight.js';
import {ArrowRight} from '@element-plus/icons-vue'
import { nextTick } from 'vue';
import {TaskQueue} from './utils/taskQueue'
import { MessageItemManager } from './utils/msgManager';
import mockDefaultImg from '@/assets/images/robot.png'
import { ElMessage } from 'element-plus'
import UploadDocIcon from './svg/upload_doc-icon.svg'
import SmartDocIcon from './svg/smart_doc-icon.svg'
import MoreToolIcon from './svg/more_tool-icon.svg'

//baseUrl
const baseUrl = import.meta.env.VITE_APP_BASE_API;
//初始化md样式
VMdPreview.use(githubTheme,{
  Hljs: hljs,
})
//消息列表ref
const chatContentBox = ref(null)
//初始化消息管理者
const msgManager = new MessageItemManager()
//初始化消息队列对象
const taskQueue = new TaskQueue()
//初始化消息队列对象的Complete
taskQueue.setOnComplete(() => {
    msgManager.messageReceiveDone()
    if(currentInstruct.value){
        currentInstruct.value = null
    }
})
//模拟的用户icon
const mockUserImg = 'https://gimg2.baidu.com/image_search/src=http%3A%2F%2Fsafe-img.xhscdn.com%2Fbw1%2F4d40b566-1f0a-4f8d-bc97-c513df8775b3%3FimageView2%2F2%2Fw%2F1080%2Fformat%2Fjpg&refer=http%3A%2F%2Fsafe-img.xhscdn.com&app=2002&size=f9999,10000&q=a80&n=0&g=0n&fmt=auto?sec=1706239065&t=82418a0dc2a04830de8580a08a94ff20'

const docFileUploads = ref('')

//指令列表
const instructList = ref([
    {
        id:1,
        label:'产品设计',
        tip:'为我 [产品设计] 主题为：',
        icon:'/profile/upload/2024/02/07/1006232_20240207094311A003.png'
    },
    {
        id:2,
        label:'家电售后',
        tip:'为我 [家电售后] 主题为：',
        icon:'/profile/upload/2024/02/04/12076406_20240204013706A010.png'
    },
    {
        id:3,
        label:'活动策划',
        tip:'为我 [活动策划] 主题为：',
        icon:'/profile/upload/2024/04/24/活动策划_20240424055006A009.png'
    },
    {
        id:4,
        label:'申请报销',
        tip:'为我 [申请报销] 主题为：',
        icon:'/profile/upload/2024/04/24/1713937372145_20240424053735A006.jpg'
    }
])
//工具列表
const toolList = ref([
    {
        id:1,
        label:'智能文档',
        icon:SmartDocIcon,
        isToptip:false
    },
    {
        id:2,
        label:'上传文档',
        icon:UploadDocIcon,
        isToptip:true
    },
    {
        id:3,
        label:'更多工具',
        icon:MoreToolIcon,
        isToptip:true
    }
])
//more tool
const popToolList = ref([
    {
        id:1,
        label:'OCR识别发票信息',
        icon:'/profile/upload/2024/04/24/1713928139994_20240424030343A002.jpg'
    }
])
//发送消息的禁用状态
const isDisabledSend = ref(true)
//是否展示用户输入时的提示
const userInputIsShowToolTip = ref(false)
//用户输入框的placeholder
const userInputTextareaPlaceholer=ref('小助手提醒：选择AI功能，快速体验')
//用户输入框的ref元素
const userInputTextarea = ref(null)
//用户输入框的值
const userInputTextareaValue  = ref('')
//用户输入框的提示的ref元素
const toolTip = ref(null)
//用户输入框的提示
const toolTipContent = ref('')
//用户输入框的提示的偏移量
const textAreaIndent = ref('0px')
//当输入框内容过多时，tip的偏移量
const toolTipTranslateY = ref('translateY(0px)')
//当前点击选中的指令
const currentInstruct = ref(null)
//上传文件弹出框的显示状态
const uploadDialogVisible = ref(false)

//点击工具触发事件
const clickToolItem = (tool) => {
    if(tool.id == 1){
        toolTipContent.value = '为我定制一篇文档主题为：'
        userInputIsShowToolTip.value = true
        nextTick(() => {
            textAreaIndent.value = toolTip.value.clientWidth + 5 +'px'
            userInputTextareaPlaceholer.value = ''
            userInputTextarea.value.focus()
        })
    }
    if(tool.id == 2){
        uploadDialogVisible.value = true
    }
}

//点击指令触发事件
const clickInstructItem = (instruct) => {
    currentInstruct.value = instruct
    toolTipContent.value = instruct.tip
    userInputIsShowToolTip.value = true
    nextTick(() => {
        textAreaIndent.value = toolTip.value.clientWidth + 5 +'px'
        userInputTextareaPlaceholer.value = ''
        userInputTextarea.value.focus()
    })
}

//输入框滑动事件
const handleScroll = () => {
    if (userInputTextarea.value) {
        toolTipTranslateY.value = 'translateY(-' + userInputTextarea.value.scrollTop + 'px)'
    }  
}

//输入框按下键时触发事件
const handleKeyDown = (e) => {
    if (e.key === 'Enter') {  
        e.preventDefault();
        if (e.shiftKey) {
            userInputTextareaValue.value += '\n';  
        } else {
            sendMessage();  
        }  
    }
    if(e.key === 'Backspace' && !userInputTextareaValue.value){
        userInputIsShowToolTip.value = false
        textAreaIndent.value = '0px'
        toolTipTranslateY.value = 'translateY(0px)'
        toolTipContent.value = ''
        userInputTextareaPlaceholer.value = '小助手提醒：选择AI功能，快速体验'
    }
}

//发送消息
const sendMessage = () => {
    if(msgManager.loading.value){
        ElMessage.warning('正在处理中，请稍后再试')
        return;
    }
    if(userInputTextareaValue.value){
        msgManager.pushUserMesage(userInputTextareaValue.value)
            msgManager.do()
        if(!currentInstruct.value){
            taskQueue.addTask(printRebotMessage,{message:'抱歉，我暂时无法回答您的问题，请稍后再试.'})
        }else{
            if(currentInstruct.value.id == 4){
                const masgArr = [
                    {
                        "message":"我会根据提供的时间范围，在邮箱中查找收集符合条件的发票文件，以下是收集结果:\n\n| 发票名 | 邮件标题 | 收件时间 | 点击查看 |\n| ------ | ------ | ------ | ------ |\n| 市北区厨大将无二餐厅_发票金额142.00元.pdf | 【电子发票】市北区厨大将无二餐厅（发票金额：142.00元） | 2024-04-18 17:48:22 | <a href=\"http://172.16.1.19/images/1351504038/市北区厨大将无二餐厅_发票金额142.00元.pdf\" target=\"_blank\">查看发票</a> |\n| 滴滴电子发票.pdf | 滴滴出行电子发票及行程报销单 | 2024-04-18 19:43:25 | <a href=\"http://172.16.1.19/images/1351504040/滴滴电子发票.pdf\" target=\"_blank\">查看发票</a> |\n| 电子发票.pdf | 厦门航空电子发票 | 2024-04-18 19:45:05 | <a href=\"http://172.16.1.19/images/1351504041/电子发票.pdf\" target=\"_blank\">查看发票</a> |",
                        "role":{
                            "name":"发票收集助手",
                            "icon_src":"/profile/upload/2024/04/24/1713938449240(1)_20240424055642A011.png"
                        }
                    },
                    {
                        "message":"我会识别发票文件内容，以下是发票内容明细:\n\n| 项目名称 | 发票金额(元) | 发票代码 | 发票日期 | 发票名 | 点击查看 |\n| ------ | ------ | ------ | ------ | ------ | ------ |\n| 餐饮服务 | 142.00 | 037022200211 | 2023年03月09日 | 市北区厨大将无二餐厅_发票金额142.00元.pdf | <a href=\"http://172.16.1.19/images/1351504038/市北区厨大将无二餐厅_发票金额142.00元.pdf\" target=\"_blank\">查看发票</a> |\n| 运输服务 | 122.09 | 031002200111 | 2023年09月27日 | 滴滴电子发票.pdf | <a href=\"http://172.16.1.19/images/1351504040/滴滴电子发票.pdf\" target=\"_blank\">查看发票</a> |\n| 运输服务 | 880.00 | 035022200211 | 2023年06月07日 | 电子发票.pdf | <a href=\"http://172.16.1.19/images/1351504041/电子发票.pdf\" target=\"_blank\">查看发票</a> |",
                        "role":{
                            "name":"识别助手",
                            "icon_src":"/profile/upload/2024/04/24/1713938449240(1)_20240424055642A011.png"
                        }
                    },
                    {
                        "message":"根据以上的发票内容明细，已为您生成报销单",
                        "role":{
                            "name":"报销单填报助手",
                            "icon_src":"/profile/upload/2024/04/24/1713938449240(1)_20240424055642A011.png"
                        }
                    },
                    {
                        "message":"根据公司报销政策，您的报销单符合报销标准，审批通过.",
                        "role":{
                            "name":"财务审批助手",
                            "icon_src":"/profile/upload/2024/04/24/1713938449240(1)_20240424055642A011.png"
                        }
                    }
                ]
                masgArr.forEach((item) => { 
                    taskQueue.addTask(printRebotMessage,{message:item.message,role:item.role})
                })
            }else{
                taskQueue.addTask(printRebotMessage,{message:'抱歉，我暂时无法回答您的问题，请稍后再试.'})
            }
        }
        userInputTextareaValue.value='';
        isDisabledSend.value = true
        userInputIsShowToolTip.value = false
        textAreaIndent.value = '0px'
        userInputTextareaPlaceholer.value = '小助手提醒：选择AI功能，快速体验'
    }
}

//打印rebot信息
const printRebotMessage = (printInfo) => {
    return new Promise((resolve) => {
        let roleInfo = printInfo.role
        if(!roleInfo){
            roleInfo = {
                icon_src:'',
                name:'小智'
            }
        }
        let txt = printInfo.message.split('')
        let tempTxt = ''
        msgManager.pushSystemMesage(tempTxt,roleInfo);
        txt.forEach((item,index) => {
            setTimeout(() => {
                msgManager.appendSystemMesage(tempTxt+=item);
                moveScroll()
                if(index == txt.length-1){
                    resolve()
                }
            }, 30 * index );
            // timeres.push(timer) 
        })
    })
}

//输入框输入时的事件
const handleInput = () => {
    if(userInputTextareaValue.value){
        isDisabledSend.value = false
    }else{
        isDisabledSend.value = true
    }
}

//定位到最新内容
const moveScroll = () => {
  nextTick(()=> {
    chatContentBox.value.scrollTop = chatContentBox.value.scrollHeight;
  })
}

onMounted(() => {
    if (userInputTextarea.value) {  
        userInputTextarea.value.addEventListener('scroll', handleScroll);
    }  
})

onUnmounted(() => {
    if (userInputTextarea.value) {  
        userInputTextarea.value.removeEventListener('scroll', handleScroll);  
    }  
})

</script>
<style scoped lang="scss">
.background-image {
    height: 100%;
    width: 100%;
    // box-shadow: -15px 0 10px -12px #eee; 
    // border-left: 1px solid #eee;
    // background: url(https://edu-wenku.bdimg.com/v1/pc/2023-new-index/theme-2-bg-1703314437526.svg) !important;
    // background-size: cover !important;
    background-color: white;
}
.chat-header {
    align-items: center;
    border: none;
    display: flex;
    height: 56px;
    margin: 0 0 0 16px;
    .chat-name {
        color: #1c1f1e;
        font-family: PingFang SC;
        font-size: 22px;
        font-weight: 600;   
        margin-bottom: 0;
        line-height: 30px;
    }
}
.ai-chat-content{
    margin: 0;
    padding: 16px 16px 0;
    max-height: calc(100vh - 56px - 184px);
    overflow-y: auto;
    overflow-x: hidden;
    .robot-chat-content{
        padding: 0;
        margin-bottom:12px ;
        .title{
            font-family: PingFangSC-Medium;
            font-size: 14px;
            font-weight: 500;
            line-height: 20px;
        }
        .instruct-list{
            margin-top: 12px;
            padding-bottom: 2px;
            .instruct-item{
                background-color: #fff;
                border: 1px solid #DEDEFE;
                border-radius: 8px;
                margin-bottom: 8px;
                justify-content: space-between;
                align-items: center;
                color: #222;
                cursor: pointer;
                display: flex;
                flex-direction: row;
                padding: 12px 13px;
                word-break: break-all;
                font-size: 14px;
                line-height: 1.4;
                &:hover{
                   color: #5050E6; 
                }
                .left-item-box{
                    display: flex;
                    flex-direction: row;
                    align-items:center;
                    img{
                        margin-right: 8px;
                        border-radius: 4px;
                        border: 1px solid #E6E6E6;
                    }
                    .item-text{
                        font-weight: 500;
                        padding-top: 1px;
                        display: flex;
                        font-family: PingFangSC-Regular;
                        font-size: 15px;
                        line-height: 20px;
                        max-width: 90%;
                        position: relative;
                        white-space: normal;
                        margin: 0;
                        padding: 0;
                    }
                }
            }
        }
    }

    .chat-message{
        display: flex;
        flex-direction: column;
        .user-chat-area{
            align-self: flex-end;
            display: flex;
            flex-direction: column;
            margin-bottom: 10px;
            padding-left: 0;
            .user-box{
                display: flex;
                flex-direction: row;
                justify-content: end;
                align-items: center;
                margin-bottom: 3px;
                .user-name{
                    line-height: 34px;
                    height: 34px;
                    color: #B2B2B2;
                    font-size: 13px;
                }
                .user-icon{
                    img{
                        height: 34px;
                        width: 34px;
                        border: 1px solid #E6E6E6;
                        border-radius: 8px;
                        background-color: #dfdfff
                    }
                    margin-left:4px;
                }
            }
            .user-chat-message{
                background-color: #5050E6;
                border-radius: 12px;
                border-top-right-radius: 0;
                box-sizing: border-box;
                color: #fff;
                // cursor: pointer;
                display: inline-block;
                font-size: 14px;
                line-height: 22px;
                min-height: 26px;
                outline: none;
                padding: 9px 14px;
                white-space: normal;
                word-break: break-word;
            }
        }
        .robot-chat-area{
            align-items: flex-start;
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
            margin-bottom: 10px;
            position: relative;
            .robot-box{
                display: flex;
                flex-direction: row;
                align-items: center;
                margin-bottom: 3px;
                .robot-name{
                    line-height: 34px;
                    height: 34px;
                    color: #B2B2B2;
                    font-size: 13px;
                }
                .robot-icon{
                    img{
                        height: 34px;
                        width: 34px;
                        border: 1px solid #E6E6E6;
                        border-radius: 8px;
                        background-color: #dfdfff
                    }
                        margin-right:4px;
                    // background-color: #dfdfff
                }
            }
            .robot-chat-message{
                background-color: #F9FBFC;
                border-radius: 0 12px 12px;
                max-width: 100%;
                padding: 12px 14px;
                // width: 100%;
                box-sizing: border-box;
                overflow: hidden;
                ::v-deep(.github-markdown-body) {
                    padding: 0;
                    font-size: 13px !important;
                    p{
                        margin-bottom: 0;
                    }
                }
            }
        }
    }
}
.bottom-area{
    background-color: transparent;
    padding-top: 10px;
    bottom: 0;
    position: absolute;
    text-align: center;
    width: 100%;
    z-index: 11;
    .bar{
        border-top: 1px solid transparent;
        background-color: transparent;
        box-sizing: border-box;
        padding: 0 16px;
        width: 100%;
        align-items: center;
        display: flex;
        justify-content: flex-start;
        .tools-list{
            background: transparent;
            display: flex;
            height: 48px;
            padding-top: 10px;
            .tools-item{
                background: #fff;
                box-shadow: 1px 1px 5px 0 #d9d9ff;
                align-items: center;
                border-radius: 8px;
                cursor: pointer;
                display: flex;
                height: 30px;
                margin-right: 8px;
                outline: none;
                padding: 6px 8px;
                .tool-icon{
                    background-repeat: no-repeat;
                    background-size: cover;
                    display: inline-block;
                    height: 20px;
                    width: 20px;
                }
                .tool-name{
                    color: #1c1f1e;
                    font-family: PingFangSC-Regular;
                    font-size: 12px;
                    line-height: 12px;
                    margin-left: 6px;
                }
            }
        }
    }
    .submit-area{
        padding: 0 16px 12px;
        box-sizing: border-box;
        width: 100%;
        .content-area{
            background-image: linear-gradient(90deg, #5050E6, #b6b6ff);
            border-radius: 12px;
            box-shadow: 0 0 12px 0 rgba(0,155,109,.15);
            box-sizing: border-box;
            height: 100%;
            padding: 2px;
            width: 100%;
            .user-input-area{
                border: 0 !important;
                border-radius: 10px;
                padding: 8px 10px;
                position: relative;
                transition: opacity .5s;
                width: 100%;
                background-color: #fff;
                .top-area{
                    display: flex;
                    position: relative;
                    .text-area{
                        line-height: 1.4;
                        flex: 1;
                        font-size: 14px;
                        overflow: hidden;
                        position: relative;
                        .tool-tip{
                            align-content: center;
                            align-items: center;
                            background-color: #f0f2f2;
                            border-radius: 4px;
                            display: flex;
                            height: 26px;
                            line-height: 26px;
                            padding-left: 10px;
                            padding-right: 0;
                            cursor: text;
                            left: 0;
                            position: absolute;
                            top: 0;
                            font-size: 14px;
                        }
                        .user-input{
                            font-size: 14px;
                            height: 62px;
                            border: none;
                            box-sizing: border-box;
                            color: #222;
                            line-height: 20px;
                            outline: 0;
                            overflow-y: auto;
                            width: 100%;
                        }
                        .show-tool-tip{
                            line-height: 26px;
                        }
                    }
                    textarea{
                        font: 100% arial, helvetica, clean;
                        overflow: auto;
                        vertical-align: top;
                        resize: none;
                    }
                }
                .bottom-info-area{
                    display: flex;
                    justify-content: flex-end;
                    margin-top: 5px;
                    .send-btn{
                        background: linear-gradient(316deg, #5050E6 16.71%, #5050E6 116.53%);
                        border-radius: 8px;
                        height: 26px;
                        position: relative;
                        width: 36px;
                        &:after{
                            background: url(https://edu-wenku.bdimg.com/v1/pc/aigc/presentation-sample/send-1702458556573.svg) no-repeat;
                            background-size: cover;
                            content: "";
                            height: 16px;
                            position: absolute;
                            top: 50%;
                            transform: translate(-50%, -50%);
                            width: 16px;
                        }
                    }
                    .disabled{
                        cursor: not-allowed;
                    }
                }
            }
        }
    }
}
</style>
<style>
.el-dialog {
     display: flex;
     flex-direction: column;
     margin:0 !important;
     position:absolute;
     top:50%;
     left:50%;
     transform:translate(-50%,-50%);
     /*height:600px;*/
     max-height:calc(100% - 200px);
     max-width:calc(100% - 30px);
}
.el-dialog .el-dialog__body {
     flex:1;
     overflow: auto;
}

.tool-pop-list .tool-pop-item{
    align-items: center;
    border-radius: 8px;
    box-sizing: border-box;
    cursor: pointer;
    display: flex;
    height: 40px;
    padding: 0 16px;
}
.tool-pop-list .tool-pop-item:hover{
    background-color: #f5f5f5;
}
.tool-pop-list .tool-pop-item .tool-pop-item-icon{
    flex: 0 0 20px;
    height: 20px;
    line-height: 0;
    margin-right: 8px;
    width: 20px;
    img{
        height: 20px;
        width: 20px;
    }
}
.tool-pop-list .tool-pop-item .tool-pop-item-title{
    font-size: 13px;
}
</style>
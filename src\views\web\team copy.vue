<template>
    <div class="main-body">
        <div class="history" :style="{'width':showDiv?'20%':'2%'}">
            <div v-if="showDiv" class="show-history">
                <conversation-list ref="conversation"
                    @after-delete="(list) => {console.log('after-delete'); console.log(list)}"
                    @select="conversationSelect" @new-chat="openNewSessionClear"
                />
                <div class="suspended-button">
                    <div class="box">
                        <el-button link text :icon="ArrowLeft" @click="showDiv=!showDiv"></el-button>
                    </div>
                </div>
            </div>
            <div class="hide-history" v-if="!showDiv">
                <div class="box">
                    <el-button link text :icon="ArrowRight" @click="showDiv=!showDiv"></el-button>
                </div>
            </div>
        </div>
        <div class="chat" :style="{'width':!showDiv ?'98%':'100%'}">
            <div v-if="currentInstruct">
                <!--会话型团队-->
                <session-records-team :message-item-manager="messageItemManagerTeam" :chat-url="''" :current-instruct="currentInstruct" 
                :preface-state="false" :preface-height="0" :is-title="false" :is-key-list="true" ref="messageCtrl" :is-public-visit="true"
                @conversation-unselect="conversationUnselect" @conversation-refresh="conversationRefresh">
                </session-records-team>
            </div>
        </div>
    </div>
</template>
<script setup>
    import {ref,onMounted, onUnmounted, nextTick} from 'vue'
    import { useRoute } from 'vue-router';
    import sessionRecordsTeam from '@/components/YiMaiChatIntegration/YiMaiChat/sessionRecordsTeam.vue'
    import { getCurrentInstructTeam } from '@/api/YiMaiChat/sessionRecordsDify'
    import { MessageItemManagerTeam } from '@/components/YiMaiChatIntegration/YiMaiChat/utils/msgManagerTeam';
    import ConversationList from '@/components/YiMaiChatIntegration/webConversationList.vue'
    import { ArrowLeft , ArrowRight } from '@element-plus/icons-vue'

    const messageItemManagerTeam = new MessageItemManagerTeam()

    const teamUrl = import.meta.env.VITE_APP_TEAM_CHAT

    const route = useRoute();
    
    // 获取路径参数
    const token = route.params.token; 
    console.log(token)//cbc96127-aa79-4ed4-b76e-92eca7306e66

    const showDiv = ref(true)

    //会话组件
    const messageCtrl = ref(null)

    const currentInstruct = ref(null)

    //获取团队详情
    async function getCurrentInstructTeamMethod(){
        await getCurrentInstructTeam(token).then(response => {
            let currentInstructStaging = {}
            // currentInstructStaging = {...instruct,...response}
            currentInstructStaging.appId = token
            currentInstructStaging.chatUrl = teamUrl+'/console/api/apps/'+token+'/team_chat_messages'
            currentInstructStaging.isTaskOrientedTeam = false
            currentInstruct.value = currentInstructStaging
        })
    }

    getCurrentInstructTeamMethod()

    //历史记录组件
    const conversation = ref(null)
    //历史记录取消选中
    const conversationUnselect = () => {
        conversation.value.unselect()
    }

    //历史记录刷新
    const conversationRefresh = () => {
        conversation.value.refresh(true)
    }
    
</script>
<style scoped lang="scss">
    .main-body{
        width: 100%;
        display: flex;
        justify-content: space-between;
        background-color: #F7F7FA;
        .history{
            margin-right: 10px;
            .show-history{
                display: flex;
                justify-content: space-between;
                flex-shrink: 0;
                flex-grow: 0;
                .suspended-button{
                    width: 10%;
                    display: flex;
                    align-items: center;
                    padding: 0 5px;
                    .box{
                        display: flex;
                        align-items: center;
                        height: 30px;
                        background-color: #EEECF6;
                        border-radius: 5px;
                        ::v-deep .el-button.is-link {
                            padding: 0;
                            color: #909090;
                        }
                    }
                }
            }
            .hide-history{
                width: 2%;
                display: flex;
                align-items: center;
                padding: 0 5px;
                height: calc( 100vh );
                .box{
                    display: flex;
                    align-items: center;
                    height: 30px;
                    background-color: #EEECF6;
                    border-radius: 5px;
                    ::v-deep .el-button.is-link {
                        padding: 0;
                        color: #909090;
                    }
                }
            }
        }
    }
    
</style>
import request from '@/utils/request'

// 查询租户管理列表
export function listTenant(query) {
  return request({
    url: '/tenant/tenant/list',
    method: 'get',
    params: query
  })
}

// 查询租户管理详细
export function getTenant(id) {
  return request({
    url: '/tenant/tenant/' + id,
    method: 'get'
  })
}

// 新增租户管理
export function addTenant(data) {
  return request({
    url: '/tenant/tenant',
    method: 'post',
    data: data
  })
}

// 修改租户管理
export function updateTenant(data) {
  return request({
    url: '/tenant/tenant',
    method: 'put',
    data: data
  })
}

// 修改租户状态
export function changeTenantStatus(id, status) {
  const data = {
    id,
    status
  }
  return request({
    url: '/tenant/tenant/changeTenantStatus',
    method: 'put',
    data: data
  })
}

// 删除租户管理
export function delTenant(id) {
  return request({
    url: '/tenant/tenant/' + id,
    method: 'delete'
  })
}

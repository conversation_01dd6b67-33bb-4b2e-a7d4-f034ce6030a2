<template>
    <div>
        <div class="field">
            <span class="field-title requrie">多模态模型</span>
            <model-selector :model-groups="modelGroups" type="vision" :model-name="modelName"
                :model-provider="modelProvider" :teleported="false" :enable-default-model="true"
                @update:modelName="(val) => emit('update:modelName', val)"
                @update:modelProvider="(val) => emit('update:modelProvider', val)"
                @update:providerIcon="(val) => providerIcon = val" @update:isModelValid="(val) => isModelValid = val"
                style="width: 200px;" :is-disable=props.isDisable />
        </div>

        <el-divider />

        <div class="field">
            <span class="field-title">参数</span>
            <div></div>
        </div>

        <!-- 参数设置开始 -->
        <div class="adjust-list-wrapper">
            <span v-if="!props.modelName || !props.modelProvider || !adjustItemList || !isModelValid" class="empty">
                无
            </span>
            <template v-else>
                <div v-for="item of adjustItemList" :key="item.name" class="adjust-item">
                    <div class="left">
                        {{ item.label.zh_Hans }}
                        <el-tooltip placement="top" effect="light">
                            <el-icon :size="14" color="lightgray" class="icon-info">
                                <question-filled />
                            </el-icon>
                            <template #content>
                                <div class="tip">{{ item.help.zh_Hans }}</div>
                            </template>
                        </el-tooltip>
                        <el-switch v-model="item.enabled" size="small"
                            @change="(enabled) => toggleEnabled(item, enabled)" :disabled="props.isDisable" />
                    </div>
                    <div class="right">
                        <template v-if="item.type === 'int' || item.type === 'float'">
                            <el-slider v-model="item.value" class="slider" :min="item.min" :max="item.max"
                                :step="item.step" @change="enable(item)" size="small" :disabled="props.isDisable" />
                            <el-input-number v-model="item.value" class="number" :min="item.min" :max="item.max"
                                :step="item.step" @change="enable(item)" size="small" controls-position="right"
                                :disabled="props.isDisable" />
                        </template>
                        <el-input v-else-if="item.type === 'string' && !item.options" v-model="item.value"
                            @change="enable(item)" size="small" :disabled="props.isDisable" />
                        <el-select v-else-if="item.type === 'string' && item.options" v-model="item.value"
                            @change="enable(item)" size="small" :disabled="props.isDisable">
                            <el-option v-for="option of item.options" :key="option" :label="option" :value="option" />
                        </el-select>
                        <el-switch v-else-if="item.type === 'boolean'" v-model="item.value" @change="enable(item)"
                            size="small" :disabled="props.isDisable" />
                    </div>
                </div>
            </template>
        </div>

    </div>


</template>

<script setup>
import { onMounted, watch } from 'vue';
import { QuestionFilled, WarningFilled, Tools } from '@element-plus/icons-vue';
import { getModelParamRules } from '@/api/model';
import ModelSelector from './modelSelector.vue';
import robot from "@/assets/icons/svg/robot.svg";
import iterations from "@/assets/icons/svg/iterations.svg";
import { getAvailableModels } from '@/api/model/index.js';

const props = defineProps({
    /**
     * 模型参数
     */
    modelParams: {
        type: String,
        required: true
    },

    /**
     * 模型的名称
     */
    modelName: {
        type: String,
        required: true
    },

    /**
     * 模型的供应商
     */
    modelProvider: {
        type: String,
        required: true
    },

    /**
     * 宽度
     */
    width: {
        type: Number,
        required: false,
        default: 400
    },
    /**
     * 是否不可操作
     */
    isDisable: {
        type: Boolean,
        required: false,
        default: false
    },
    agentMode: {
        type: String,
        required: false,
        default: ''
    },
});

// 调节项
const adjustItemList = ref([]);

/**
 * 原数据
 *
 * 为了实现效果: 切换至其他模型时，参数选项为默认值。
 * 切换至原本保存的模型时，加载原来保存的参数。 
 */
const originalData = {
    modelName: '',
    modelProvider: '',
    modelParams: '',
}

// 当前模型是否有效
const isModelValid = ref(true);

// 当前模型图标
const providerIcon = ref('');

// 事件
const emit = defineEmits(['update:modelName', 'update:modelProvider', 'update:modelParams', 'update:agentMode']);

// 模型列表
const modelGroups = ref([]);
//获取agentmode
const agentMode = ref(null)
const getAgentMode = () => {
    if (!props.modelProvider || !props.modelName || !modelGroups.value || modelGroups.value.length == 0) {
        return;
    }
    let selModelGroups = modelGroups.value.filter(x => x.provider == props.modelProvider)[0].models.filter(x => x.model == props.modelName)[0]
    if (!selModelGroups.features) {
        agentMode.value = 'ReAct'
    } else {
        if (selModelGroups.features.includes('tool-call') || selModelGroups.features.includes('multi-tool-call')) {
            agentMode.value = 'Function Calling'
        } else {
            agentMode.value = 'ReAct'
        }
    }
}
/**
 * 获取可选的模型数据
 */
async function getModelData() {
    await getAvailableModels('vision').then((res) => {
        const groups = [];

        res.data.forEach((group) => {
            if (group.status === 'active') {
                const models = [];
                group.models.forEach((item) => {
                    if (item.status === 'active') {
                        models.push(item);
                    }
                });
                groups.push(Object.assign({}, group, { models }));
            }
        });

        modelGroups.value = groups;
        getAgentMode()
    });
}

onMounted(() => {
    getModelData();
});

//agentMode
watch(() => props.agentMode, val => {
    if (props.agentMode && val) {
        agentMode.value = val
    }
    if (props.agentMode && !val) {
        agentMode.value = 'ReAct'
    }
}, { deep: true, immediate: true });

watch(() => agentMode.value, val => {
    emit('update:agentMode', val);
}, { deep: true, immediate: true });

/**
 * 获取模型的预置参数
 */
async function getModelParamData() {
    if (!props.modelProvider || !props.modelName) {
        return;
    }

    const params = JSON.parse(originalData.modelParams || '{}');

    const res = await getModelParamRules(props.modelProvider, { model: props.modelName });
    adjustItemList.value = res.data.map((item) => {
        item.enabled = false;

        switch (item.type) {
            case 'int':
                item.step = 1;
                item.value = item.default
                    ? item.default
                    : 0;
                if (item.min === null) {
                    item.min = -100000;
                }
                if (item.max === null) {
                    item.max = 100000;
                }
                break;

            case 'float':
                item.step = 0.1;
                item.value = item.default
                    ? item.default
                    : 0;
                if (item.min === null) {
                    item.min = -100000;
                }
                if (item.max === null) {
                    item.max = 100000;
                }
                break;

            case 'string':
                item.value = item.default
                    ? item.default
                    : '';
                break;

            case 'boolean':
                item.value = item.default
                    ? item.default
                    : false;
                break;
        }

        if (props.modelName === originalData.modelName && props.modelProvider === originalData.modelProvider && params[item.name]) {
            item.value = params[item.name];
            item.enabled = true;
        }

        return item;
    });
}

/**
 * 根据adjustItemList，生成参数对像
 */
function getParams() {
    const value = {};
    adjustItemList.value.forEach((item) => {
        if (item.enabled) {
            value[item.name] = item.value;
        }
    });

    return value;
}

watch(
    () => props.modelName + props.modelProvider,
    () => {
        getModelParamData();
        getAgentMode();
    },
    { immediate: true }
);

watch(adjustItemList, () => {
    const jsonParams = JSON.stringify(getParams());
    if (jsonParams !== props.modelParams) {
        emit('update:modelParams', jsonParams);
    }
}, { deep: true });

watch(() => props.modelParams, (newVal, oldVal) => {
    /*
     * 逻辑:
     * 观察props.modelParams, 如果原来为空串或'{}'，变化为有值，且当前originalData中的参数也没有值。
     * 则将当前的props中的数据，为原数据，复制到originalData中。
     * 
     * 不能通过观察props.modelName, props.modelProvider来判断当前props中的数据为原数据。
     * 因为ModelSelector组件，可能会反向传递默认值，使props.modelName, props.modelProvider变化
     */
    if ((!oldVal || oldVal === '{}') && newVal && newVal !== '{}' && !originalData.modelName) {
        originalData.modelName = props.modelName
        originalData.modelProvider = props.modelProvider
        originalData.modelParams = props.modelParams
    }

    const params = JSON.parse(props.modelParams || '{}');
    if (props.modelName === originalData.modelName && props.modelProvider === originalData.modelProvider && params) {
        adjustItemList.value.forEach((item) => {
            if (params[item.name]) {
                item.value = params[item.name];
                item.enabled = true;
            }
        });
    }
}, { immediate: true });

/**
 * 点击每个选项的是否启用的开关 
 */
function toggleEnabled(item, enabled) {
    if (!enabled && item.default !== null) {
        item.value = item.default;
    }
}

/**
 * 启用某个选项
 */
function enable(item) {
    if (item.value !== item.default && !item.enabled) {
        item.enabled = true;
    }
}
</script>

<style scoped>
.empty {
    display: block;
    width: 100%;
    text-align: center;
    color: lightgray;
}

.adjust-list-wrapper {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: stretch;
}

.adjust-list-wrapper>.right {
    display: flex;
    flex-direction: row;
    justify-content: flex-end;
    align-items: center;
    flex-wrap: nowrap;
}

.adjust-item {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    height: 36px;
    margin-bottom: 16px;
    width: 100%;
}

.adjust-item>.left {
    color: gray;
    font-size: 14px;
    flex-shrink: 0;
    flex-grow: 0;
    width: 140px;
}

.adjust-item>.left>.icon-info {
    vertical-align: middle;
    margin-right: 10px;
}

.adjust-item>.right {
    display: flex;
    flex-direction: row;
    justify-content: flex-end;
    align-items: center;
    flex-wrap: nowrap;
    flex-grow: 1;
    flex-shrink: 1;
}

.adjust-item>.right>div {
    flex-shrink: 0;
    flex-grow: 0;
}

.adjust-item>.right>.slider {
    width: calc(100% - 110px);
    margin-right: 10px;
    --el-slider-height: 2px;
}

.adjust-item>.right>.number {
    margin-left: 10px;
    width: 67px;
}

.adjust-item>.right>.number :deep(.el-input) {
    width: 67px;
}

.adjust-item>.right>.number :deep(.el-input__wrapper) {
    padding-right: 25px;
    padding-left: 5px;
}

.tip {
    display: block;
    text-wrap: wrap;
    white-space: pre-wrap;
    max-width: 200px;
    line-height: 1.5;
}

.preset-list {
    width: 100%;
    margin: 10px 0 15px 0;
}

.no-click-events {
    pointer-events: none;
}

.field {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1em;
}

.field .field-title {
    color: #383743;
    display: block;
    font-size: 14px;
    font-weight: 600;
    line-height: 22px;
    margin-bottom: 8px;
}

.requrie {
    &:after {
        color: #FF441E;
        content: "*";
        font-weight: 600;
        margin-left: 4px;
    }
}
</style>
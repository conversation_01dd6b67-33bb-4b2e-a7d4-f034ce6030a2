<template>
  <el-dropdown @command="handleCommand" placement="right">
    <el-avatar :size="28" :src="userStore.avatar" class="avatar" />
    <template #dropdown>
      <el-dropdown-menu>
        <!-- <router-link v-hasPermi="['edu:permission-management', 'edu:personal-center']" to="/edu/profile">
          <el-dropdown-item>个人中心</el-dropdown-item>
        </router-link>
        <router-link v-hasPermi="['edu:permission-management', 'edu:personal-center']" to="/permissions">
          <el-dropdown-item>权限管理</el-dropdown-item>
        </router-link>
        <router-link v-hasRole="['admin']" to="/edu/full-dify" target="_blank">
          <el-dropdown-item>智能体</el-dropdown-item>
        </router-link> -->
        <el-dropdown-item  command="logout">
          <span>退出登录</span>
        </el-dropdown-item>
      </el-dropdown-menu>
    </template>
  </el-dropdown>
</template>

<script setup>
import { ElMessageBox } from 'element-plus';
import useUserStore from '@/store/modules/user'

const userStore = useUserStore()

// 处理菜单点击
function handleCommand(command) {
  switch (command) {
    case "logout":
      logout();
      break;
    default:
      break;
  }
}

// 注销
function logout() {
  ElMessageBox.confirm('确定注销并退出系统吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    userStore.logOut().then(() => {
      location.href = '/';
    })
  }).catch(() => { });
}

</script>

<style scope>
.avatar {
  cursor: pointer;
  border: 1px solid rgba(0,0,0,0.1);
  outline: 0;
}
</style>
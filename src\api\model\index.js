import request from '@/utils/difyRequest'

/**
 * 按分类获取可使用的模型
 * @param {String} type  模型类别: text-generation, text-embeddings, speech2text
 */
export function getAvailableModels(type) {
  return request({
      url: `/console/api/workspaces/current/model-type/${type}`,
      method: 'get'
  });
}

/**
 * 获取所有模型 
 * @returns 
 */
export function getAllModels(params) {
  return request({
      url: `/console/api/workspaces/current/all_models`,
      method: 'get',
      params
  });
}

/**
 * 获取所有的模型供应商
 */
export function getModelProviders() {
  return request({
    url: `/console/api/workspaces/current/model-providers`,
    method: 'get'
  });
}

/**
 * 获取默认模型
 */
export function getDefaultModel(type) {
  return request({
    url: `/console/api/workspaces/current/default-model?model_type=${type}`,
    method: 'get'
  });
}

/**
 * 获取模型的认证配置
 */
export function getModelCredentials(provider, params) {
  return request({
    url: `/console/api/workspaces/current/model-providers/${provider}/models/credentials`,
    method: 'get',
    params
  });
}

/**
 * 获取供应商的认证配置
 */
export function getProviderCredentials(provider) {
  return request({
    url: `/console/api/workspaces/current/model-providers/${provider}/credentials`,
    method: 'get'
  });
}

/**
 * 添加/修改模型
 */
export function addOrUpdateModel(provider, params) {
  return request({
    url: `/console/api/workspaces/current/model-providers/${provider}/models`,
    method: 'post',
    data: params
  });
}

/**
 * 配置供应商的认证参数
 */
export function configureProvider(provider, params) {
  return request({
    url: `/console/api/workspaces/current/model-providers/${provider}`,
    method: 'post',
    data: params
  });
}

/**
 * 删除供应商认证配置
 */
export function delProvider(provider) {
  return request({
    url: `/console/api/workspaces/current/model-providers/${provider}`,
    method: 'delete',
  });
}

/**
 * 删除模型
 */
export function delModel(provider, params) {
  return request({
    url: `/console/api/workspaces/current/model-providers/${provider}/models`,
    method: 'delete',
    data: params
  });
}


/*
 * 获取模型的预置参数
 */
export function getModelParamRules(provider, params) {
  return request({
    url: `/console/api/workspaces/current/model-providers/${provider}/models/parameter-rules`,
    method: 'get',
    params
  });
}

/**
 * 设置默认模型
 */
export function setDefaultModels(params) {
  return request({
    url: `/console/api/workspaces/current/default-model`,
    method: 'post',
    data: params
  });
}
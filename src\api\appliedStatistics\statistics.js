import request from '@/utils/conversationClientRequest'

//获取应用总览数据
export function getOverviewData() {
    return request({
        url: 'statistics/overview',
        method: 'get'
    })
}
// 获取用户数图表数据
export function getUserChartData(params) {
    return request({
        url: 'statistics/chart/user',
        method: 'get',
        params
    })
}
//获取应用总使用数图表数据
export function getUseChartData(params) {
    return request({
        url: 'statistics/chart/conversation',
        method: 'get',
        params
    })
}
//获取总消息数图表数据
export function getMessagesChartData(params) {
    return request({
        url: 'statistics/chart/messages',
        method: 'get',
        params
    })
}
//获取Token消耗总数图表数据
export function getTokenConsumeChartData(params) {
    return request({
        url: 'statistics/chart/tokens',
        method: 'get',
        params
    })
}

//获取Token消耗总数排行榜
export function getTokensRankingData() {
    return request({
        url: 'statistics/tokens/ranking',
        method: 'get'
    })
}
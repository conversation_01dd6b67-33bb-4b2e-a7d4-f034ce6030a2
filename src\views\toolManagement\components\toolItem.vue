<template>
    <div class="tool-item">
        <div class="title-area">
            <div class="icon">
                <template v-if="tool.type === 'builtin'">
                    <div class="img" :style="{backgroundImage:`url(${tool.icon})`}"></div>
                </template>
                <template v-else>
                    <span :style="{backgroundColor:tool.icon.background}" class="emoji">
                        <single-emoji :content="tool.icon.content" :size="45"></single-emoji>
                    </span>
                </template>
            </div>
            <div class="text">
                <div class="name">
                    <div style="overflow: hidden;white-space: nowrap;text-overflow: ellipsis;">{{ tool.label.zh_Hans }}</div>
                    <div class="right-buttons">
                        <el-dropdown :hide-on-click="false">
                            <el-button text :icon="More" @click.stop></el-button>
                            <template #dropdown>
                            <el-dropdown-menu>
                                <el-dropdown-item @click="showTool" icon="Suitcase">工具</el-dropdown-item>
                                <el-dropdown-item @click="selectTool" icon="Edit" v-if="tool.type != 'builtin'">编辑</el-dropdown-item>
                                <el-dropdown-item @click="toAuth" v-if="tool.type == 'builtin' && Object.keys(tool.team_credentials).length">
                                    <svg-icon style="margin-right: 5px;" 
                                    :icon-class="'api-key-auth'" 
                                    :size="'1em'" />授权
                                </el-dropdown-item>
                                <el-dropdown-item @click="toDeleteAuth" v-if="tool.type == 'builtin' && Object.keys(tool.team_credentials).length && tool.is_team_authorization">
                                    <svg-icon style="margin-right: 5px;" 
                                    :icon-class="'api-key-auth'" 
                                    :size="'1em'" />取消授权
                                </el-dropdown-item>
                                <el-dropdown-item @click="deleteTool" icon="Delete" style="color:red" v-if="tool.type != 'builtin'">删除</el-dropdown-item>
                            </el-dropdown-menu>
                            </template>
                        </el-dropdown>
                    </div>
                </div>
                <div class="author">
                    {{ tool.description.zh_Hans }}
                </div>
                <div class="tags" v-show="labelContent(tool.labels)">
                    <template v-for="tag in labelContent(tool.labels).split(',')">
                        <el-tag type="info" size="small" style="margin-right: 5px;">{{tag}}</el-tag>
                    </template>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import singleEmoji from './singleEmoji'
import { More,Suitcase,Edit,Delete } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'

const props = defineProps({
    tool:{
        type:Object,
        default:() => {return {}}
    },
    selected:{
        type:Boolean,
        default:false
    },
    tagOptions:{
        type:Array,
        default:() => {return []}
    }
})

const emits = defineEmits(['set-actived-tool','delete-tool','to-auth','to-delete-auth'])

const selectTool = () => {
    emits('set-actived-tool',props.tool,'edit')
}

const showTool = () => {
    emits('set-actived-tool',props.tool,'show')
}

//授权
const toAuth = () => {
    emits('to-auth',props.tool)
}

//取消授权
const toDeleteAuth = () => {
    ElMessageBox.confirm(
        '确认取消授权该工具吗？',
        '系统提示',
        {
            confirmButtonText: '确认',
            cancelButtonText: '取消',
            type: 'warning',
        }
    ).then(() => {
        emits('to-delete-auth',props.tool)
    }).catch(() => {
    })
}

const deleteTool = ()=> {
    ElMessageBox.confirm(
        '确认删除该工具吗？',
        '系统提示',
        {
            confirmButtonText: '确认',
            cancelButtonText: '取消',
            type: 'warning',
        }
    ).then(() => {
        emits('delete-tool',props.tool)
    }).catch(() => {
    })
}

const labelContent = (labels) => {
    if(!labels)
        return ''

    return labels.map(name => {
        const label = props.tagOptions.find(item => item.name === name)
        return label?.label['zh_Hans']
    }).join(', ')
}

</script>

<style lang="scss" scoped>
.tool-item{
    min-height: 130px;
    display: flex;
    flex-direction: column;
    grid-column: span 1 / span 1;
    background-color: white;
    border-radius: 8px;
    border: 1px solid rgb(234,236,240);
    border-color: transparent;
    box-shadow: 0 0 1px 0 rgba(0,0,0,.1);
    // cursor: pointer;
    transition: all .2s;
    box-shadow: 0 4px 10px 0 #0000000d;
    .title-area{
        display: flex;
        // align-items: center;
        height: 90px;
        max-height: calc(150px - 42px);
        flex-shrink: 0;
        flex-grow: 0;
        gap: .75rem;
        padding: 24px;
        padding-bottom: 10px;
        .icon{
            position: relative;
            flex-shrink: 0;
            width: 65px;
            height: 65px;
            .img{
                height: 65px;
                width: 65px;
                background-size: cover;
                background-position: 50%;
                background-repeat: no-repeat;
                border-radius: .375rem;
            }
            .emoji{
                height: 65px;
                width: 65px;
                border-radius: .5rem;
                display: flex;
                position: relative;
                align-items: center;
                justify-content: center;
                line-height: 1.75rem;
                border-radius: .375rem;
            }
        }
        .text{
            width: 0;
            flex-grow: 1;
            padding: 1px 0;
            .name{
                font-weight: 600;
                color: rgb(29, 41, 57);
                line-height: 1.5rem;
                font-size: 1.25rem;
                display: flex;
                align-items: center;
                margin-bottom: 8px;
                justify-content: space-between
            }
            .author{
                color: rgb(102, 112, 133);
                line-height: 18px;
                font-weight: 500;
                font-size: 12px;
                display: flex;
                align-items: center;
            }
        }
    }
    .desc{
        margin-bottom: .5rem;
        flex-grow: 1;
        padding: 0 14px;
        font-size: .75rem;
        line-height: 1.5;
        overflow: hidden;
        max-height: 36px;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        text-overflow: ellipsis;
        color: rgb(102, 112, 133)
    }
    .tags{
        // margin-top: .25rem;
        // display: flex;
        height: 42px;
        flex-shrink: 0;
        align-items: center;
        padding-bottom: 6px;
        // padding-left: 24px;
        // padding-right: 6px;
        padding-top: .50rem;
        overflow: hidden;
        -webkit-box-orient: vertical;
        text-overflow: ellipsis;
        white-space: nowrap;
        .tags-title{
            position: relative;
            width: 100%;
            display: flex;
            align-items: center;
            gap: .25rem;
            border-radius: .375rem;
            padding: 7px 0;
            color: rgb(102, 112, 133);
            .tags-text{
                flex-grow: 1;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
                text-align: start;
                font-size: .75rem;
                font-weight: 400;
                line-height: 18px;
            }
        }
    }
}
.selected{
    border-color: #5050E6;
}

.right-buttons{
    //min-width: 100px;
    display: flex;
    box-sizing: border-box;
    justify-content: end;
    align-items: center;
    max-height: 24px;
    ::v-deep .el-button{
        padding: 0 3px !important;
        height: 20px !important;
    }
}
</style>
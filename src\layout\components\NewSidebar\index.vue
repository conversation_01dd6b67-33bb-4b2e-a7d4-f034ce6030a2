<template>
  <div
    :class="{ 'has-logo': showLogo }"
    :style="{
      backgroundColor: '#f3f3f3',
    }"
  >
    <img :src="collapseIcon" class="toggle" @click="toggleSideBar" />
    <logo v-if="showLogo" :collapse="isCollapse" />
    <el-scrollbar>
      <el-menu
        :default-active="activeMenu"
        :collapse="isCollapse"
        :background-color="
          sideTheme === 'theme-dark'
            ? variables.menuLightBackground
            : variables.menuLightBackground
        "
        :text-color="
          sideTheme === 'theme-dark'
            ? variables.menuLightColor
            : variables.menuLightColor
        "
        :unique-opened="true"
        :active-text-color="theme"
        :collapse-transition="false"
        mode="vertical"
        popper-class="popperBox"
      >
        <sidebar-item
          v-for="(route, index) in sidebarRouters"
          :key="route.path + index"
          :item="route"
          :base-path="route.path"
          :collapse="isCollapse"
        />
      </el-menu>
    </el-scrollbar>
    <user style="margin-left: 14px" />
  </div>
</template>

<script setup>
import Logo from "./Logo";
import SidebarItem from "./SidebarItem";
import user from "../user.vue";
import variables from "@/assets/styles/variables.module.scss";
import useAppStore from "@/store/modules/app";
import useSettingsStore from "@/store/modules/settings";
import usePermissionStore from "@/store/modules/permission";
import router, { constantRoutes } from "@/router";
import collapseIcon from "@/assets/logo/collapseIcon.png";

const route = useRoute();
const appStore = useAppStore();
const settingsStore = useSettingsStore();
const permissionStore = usePermissionStore();
function toggleSideBar() {
  appStore.toggleSideBar();
}
// const sidebarRouters =  computed(() => permissionStore.sidebarRouters);
// const sidebarRouters = computed(() => constantRoutes);
const sidebarRouters = computed(() => {
  return [
    {
      path: "/brief/home",
      component: () => import("@/views/brief/home/<USER>"),
      meta: {
        title: "首页",
        icon: "menu-ai",
        noCache: false,
        link: null,
      },
    },
    {
      path: "/briefGeneration",
      component: () => import("@/layout/newLayout.vue"),
      meta: {
        title: "简报生成",
        icon: "menu-brief",
        affix: true,
        newIcon: "ri-article-line",
      },
      redirect: "/briefGeneration/list",
      children: [
        {
          path: "list",
          component: () => import("@/views/briefGeneration/list"),
          hidden: true,
          meta: { activeMenu: "/briefGeneration" }, // 关键
        },
        {
          path: "add",
          component: () => import("@/views/briefGeneration/add"),
          hidden: true,
          children: [
            {
              path: "config",
              component: () =>
                import("@/views/briefGeneration/add/config/index.vue"),
              hidden: true,
              meta: { activeMenu: "/briefGeneration" }, // 关键
            },
            {
              path: "create/:type/:id?",
              component: () =>
                import("@/views/briefGeneration/add/create/index.vue"),
              hidden: true,
              meta: { activeMenu: "/briefGeneration" }, // 关键
            },
          ],
        },
      ],
    },
  ];
});
const showLogo = computed(() => settingsStore.sidebarLogo);
const sideTheme = computed(() => settingsStore.sideTheme);
const theme = computed(() => settingsStore.theme);
const isCollapse = computed(() => !appStore.sidebar.opened);

const activeMenu = computed(() => {
  const { meta, path } = route;
  // if set path, the sidebar will highlight the path you set
  if (meta.activeMenu) {
    return meta.activeMenu;
  }
  return path;
});
</script>
<style lang='scss' scoped>
.toggle {
  height: 15px;
  position: absolute;
  top: 22px;
  right: 18px;
  cursor: pointer;
}
.has-logo {
  .el-scrollbar {
    height: calc(100% - 140px) !important;
  }
}
.el-menu {
  --el-menu-item-height: 36px;
  --el-menu-sub-item-height: 30px;
  --el-menu-base-level-padding: 10px;
}
</style>
<style>
.popperBox {
  --el-menu-item-height: 36px;
  --el-menu-sub-item-height: 30px;
  --el-menu-base-level-padding: 10px;
}
</style>

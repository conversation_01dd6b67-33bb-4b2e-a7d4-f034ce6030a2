<template>
  <div :class="['container', isBatchDelete ? 'batch-delete': 'not-batch-delete']">
    <div class="top">
      <el-divider content-position="left" class="divider">
        历史记录
      </el-divider>
      <div class="search-and-clear" v-if="conversationList.length > 0">
        <el-input
          v-model="keyword"
          :suffix-icon="keyword.length > 0 ? null : Search"
          :clearable="keyword.length > 0"
          class="input-keyword"
          placeholder="搜索历史记录"
        />
        <el-button
          type="info"
          :icon="Delete"
          circle
          class="btn-clear"
          @click="enterBatchDeleteMode"
        />
      </div>
      <div
        class="empty"
        v-if="(conversationList.length === 0) || (conversationList.length > 0 && keyword && filteredConversationList.length === 0)"
      >
        无搜索结果
      </div>
      <div class="toolbar-del" v-if="filteredConversationList.length > 0 && isBatchDelete">
        <span class="check-all">
          <input type="checkbox" :checked="isAllChecked" @click="toggleCheckAll" />
          全选
        </span>
        <span class="del" @click="batchDel">
          <delete/>删除
        </span>
        <span class="quit" @click="quitBatchDeleteMode">
          <close/>退出
        </span>
      </div>
    </div>

    <div class="conversation-list-wrapper">
      <el-scrollbar>
        <div class="conversation-list">
            <div
              v-for="(conversation, index) of filteredConversationList"
              :key="conversation.applicationType + conversation.sessionId"
              :class="[
                'conversation',
                { active: conversation === curConversation },
              ]"
              @click="select(index)"
            >
              <input
                type="checkbox"
                v-model="conversation.checked"
                @click.stop="calcIsAllChecked"
                class="checkbox"
              />
              <span class="conversation-title">
                {{ conversation.title }}
              </span>
              <span class="operation">
                <Delete class="btn" @click.stop="del(index)" />
                <Edit class="btn" @click.stop="rename(index)" />
              </span>
            </div>
        </div>
      </el-scrollbar>
    </div>
  </div>
</template>

<script setup>
import { Edit, Delete, Search, Close } from "@element-plus/icons-vue";
import { onMounted, watch, computed } from "vue";
import {
  getConversationList,
  delConversation,
  renameConversation
} from "@/api/session/session.js";
import { ElMessageBox, ElMessage } from "element-plus";

// 搜索的关键词
const keyword = ref("");

// 会话列表
const conversationList = ref([]);

// 根据关键词过滤后的会话列表
const filteredConversationList = ref([]);

// 当前会话ID
const curConversation = ref(null);

// 是否正在批量删除
const isBatchDelete = ref(false);

// 是否全选
const isAllChecked = ref(false)

// 事件定义
const emit = defineEmits(['select', 'after-delete']);

// 搜索
watch(keyword, () => {
  query();
}, {immediate: true});

// 全选状态计算
let calcTimeout = 0;
function calcIsAllChecked() {
  if (calcTimeout) {
    clearTimeout(calcTimeout);
  }
  setTimeout(() => {
    isAllChecked.value = !filteredConversationList.value.find((item) => !item.checked);
  }, 10)
}
watch(filteredConversationList, () => {
  calcIsAllChecked();
}, {immediate: true});

/**
 * 搜索
 */
function query() {
  filteredConversationList.value = 
    keyword.value.trim()
    ? conversationList.value.filter((item) => item.title && item.title.toLowerCase().includes(keyword.value.toLowerCase()))
    : conversationList.value;
}

/**
 * 获取历史会话记录, 并选中第一个
 */
async function getList() {
  const rs = await getConversationList('');
  conversationList.value = rs.data.map((item) => {
    return {
      ...item,
      checked: false
    }
  });
  query();
}

/**
 * 重命名会话
 */
function rename(index) {
  const conversation = filteredConversationList.value[index];
  ElMessageBox.prompt("请输入新的会话名称:", "重命名会话", {
    inputValue: conversation.title,
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    inputPattern: /^.{1,15}$/,
    inputErrorMessage: "会话名称不能为空，且不能超过10个字符",
  })
    .then(({ value }) => {
      renameConversation({
        applicationId: conversation.applicationId,
        applicationType: conversation.applicationType,
        sessionId: conversation.sessionId,
        title: value,
      }).then(() => {
        conversation.title = value;
        ElMessage({
          type: "success",
          message: "操作成功!",
        });
      });
    })
    .catch(() => {});
}

/**
 * 删除会话
 */
function del(index) {
  ElMessageBox.confirm("请问是否确定删除当前会话?", "删除会话", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    const conversation = filteredConversationList.value[index];
    delConversation([{
        applicationId: conversation.applicationId,
        applicationType: conversation.applicationType,
        sessionId: conversation.sessionId,
    }]).then(() => {
      ElMessage({
        type: "success",
        message: "操作成功!",
      });
      emit('after-delete', [conversation]);
      filteredConversationList.value.splice(index, 1);
      const realIdx = conversationList.value.findIndex((item) => item === conversation);
      if (realIdx >= 0) {
        conversationList.value.splice(realIdx, 1);
      }
    });
  })
  .catch((e) => {console.log(e)});
}

/**
 * 批量删除会话
 */
function batchDel() {
  ElMessageBox.confirm("确定要删除选中的会话吗?", "删除选中的会话", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    const params = [];
    const checkedConversationList = filteredConversationList.value.filter((item) => item.checked);
    for (const conversation of checkedConversationList) {
      params.push({
        applicationId: conversation.applicationId,
        applicationType: conversation.applicationType,
        sessionId: conversation.sessionId,
      });
    }
    delConversation(params).then(() => {
      ElMessage({
        type: "success",
        message: "操作成功!",
      });
      emit('after-delete', checkedConversationList.value);
      conversationList.value = conversationList.value.filter((item) => !checkedConversationList.includes(item));
      filteredConversationList.value = filteredConversationList.value.filter((item) => !checkedConversationList.includes(item));
    });
  })
  .catch((e) => {console.log(e)});
}


/**
 * 选中会话
 */
function select(index, fireEvent = true) {
  curConversation.value = filteredConversationList.value[index];
  /*
  const prom = getConversationDetail({
    sessionId: curConversation.value.sessionId,
    applicationType: curConversation.value.applicationType
  });
  */
  if (fireEvent) {
    emit('select', curConversation.value);
  }
}

/**
 * 选中第一个会话
 */
function selectFirst(fireEvent = true) {
  if (conversationList.value.length > 0) {
    select(0, fireEvent);
  }
}

/**
 * 取消选中
 */
function unselect() {
  curConversation.value = null;
}

/**
 * 创建并选中会话
 */
function createAndSelect() {
  if (
    conversationList.value.length > 0 &&
    conversationList.value[0].sessionId === "new"
  ) {
    return;
  }

  const newConversation = {
    applicationId: curConversation.applicationId,
    applicationType: curConversation.applicationType,
    sessionId: "new",
    title: "新的对话",
    createdTime: new Date().toISOString(),
  };

  conversationList.value = [newConversation, ...conversationList.value];
}

/**
 * 进入批量删除模式
 */
function enterBatchDeleteMode() {
  isBatchDelete.value = true;
}

/**
 * 退出批量删除模式
 */
function quitBatchDeleteMode() {
  isBatchDelete.value = false;
}

/**
 * 复选框 全选/全不选
 */
function toggleCheckAll(e) {
  // 全选
  if (!isAllChecked.value) {
    filteredConversationList.value.forEach((item) => {
      item.checked = true;
    });

  // 全不选
  } else {
    filteredConversationList.value.forEach((item) => {
      item.checked = false;
    });
  }
  calcIsAllChecked();
}

/**
 * 刷新
 */
async function refresh() {
  await getList()
}

onMounted(() => {
  getList();
});

defineExpose({
  refresh,
  selectFirst,
  unselect
});
</script>

<style scoped lang="scss">

.container.not-batch-delete {
  .conversation {
    input {
      display: none;
    }

    &:hover {
      background-color: white;

      .operation {
        display: flex !important;
      }
    }
  }
}

.container.batch-delete {
  .conversation {
    justify-content: flex-start !important;
  }
}

.container {
  width: 100%;
  height: 100vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: stretch;
  border-right: 1px solid lightgray;

  .top {
    width: 100%;
    height: auto;
    flex-grow: 0;
    flex-shrink: 0;
    .search-and-clear {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
      padding: 0 10px;

      .input-keyword {
        width: calc(100% - 40px);

        :deep(.el-input__wrapper) {
          background-color: #e3e0f0;
          border-radius: 20px;
        }
      }

      .btn-clear {
        background-color: #e3e0f0;
        border-color: #e3e0f0;
        color: #9494b0;
      }
    }

    .divider {
      width: calc(100% - 20px);
      position: relative;
      left: 10px;

      :deep(.el-divider__text) {
        background-color: #f2f2f6;
        left: 0;
        padding-left: 5px;
        color: #8587aa;
      }
    }

    .empty {
      width: 100%;
      height: 50px;
      display: flex;
      flex-direction: row;
      justify-content: center;
      align-items: center;
      flex-grow: 0;
      flex-shrink: 0;
      color: #9c9cb8;
      font-size: 14px;
    }

    .toolbar-del {
      display: flex;
      flex-direction: row;
      justify-content: space-evenly;
      align-items: center;
      padding: 0 10px;
      height: 40px;
      background-color: #dedeea;
      border-radius: 20px;
      width: calc(100% - 20px);
      position: relative;
      left: 5px;

      svg {
        width: 14px;
        height: 14px;
        margin-right: 3px;
      }

      .check-all {
        display: flex;
        flex-direction: row;
        justify-content: flex-start;
        align-items: center;

        input {
          margin-right: 5px;
        }
      }

      .del, .quit {
        cursor: pointer;
      }
    }
  }

  .conversation-list-wrapper {
    width: 100%;
    overflow: hidden;
    flex-grow: 1;
    flex-shrink: 1;

    .conversation-list {
      width: 100%;
      height: 100%;
      overflow: visible;
      display: flex;
      flex-direction: column;
      justify-content: flex-start;
      align-items: center;
      padding-top: 5px;

      .conversation.active {
        background-color: #dedeea;
      }
      .conversation {
        width: calc(100% - 20px);
        height: 40px;
        border-radius: 20px;
        position: relative;
        padding: 0 10px;
        font-size: 14px;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        cursor: pointer;

        input {
          margin-right: 5px;
        }

        .conversation-title {
          display: block;
          text-overflow: ellipsis;
          text-wrap: nowrap;
          white-space: nowrap;
          overflow: hidden;
        }

        .operation {
          flex-direction: row;
          justify-content: flex-end;
          align-items: center;
          padding: 0 5px;
          display: none;

          .btn {
            width: 14px;
            height: 14px;
            color: gray;
            margin-left: 5px;

            &:hover {
              color: black;
            }
          }
        }
      }
    }
  }

  .suspended-button {
    width: 10%;
    display: flex;
    align-items: center;
    padding: 0 5px;
    flex-grow: 0;
    flex-shrink: 0;

    .box {
      display: flex;
      align-items: center;
      height: 30px;
      background-color: #eeecf6;
      border-radius: 5px;
      ::v-deep .el-button.is-link {
        padding: 0;
        color: #909090;
      }
    }
  }
}

</style>

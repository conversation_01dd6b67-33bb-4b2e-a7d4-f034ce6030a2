<template>
    <Emoji :data="emojiIndex" :style="{'width': size + 'px', 'height': size + 'px'}" :emoji="findEmojiByContent()" :size="size" />
</template>
<script setup>
import "emoji-mart-vue-fast/css/emoji-mart.css";

import { EmojiIndex, Emoji } from "emoji-mart-vue-fast/src";

import data from "emoji-mart-vue-fast/data/all.json";

let emojiIndex = new EmojiIndex(data);

const props = defineProps({
    content: String,
    size:{
        type: Number,
        default: 24
    }
})

const findEmojiByContent = () => {
    return  emojiIndex.findEmoji(props.content)
}

</script>
<style lang="scss" scoped>
.emoji-mart-emoji{
    padding: 0;
}
</style>
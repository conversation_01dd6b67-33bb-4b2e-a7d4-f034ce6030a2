<template>
    <div class="message-renderer" v-html="html"></div>
</template>

<script setup>
import markdownIt from 'markdown-it';
import mditMath from 'markdown-it-math';
import katex from 'katex';
import 'katex/dist/katex.css';
import hljs from "highlight.js";
import "highlight.js/styles/vs2015.css";

// --------------------- 图片加载 ------------------------
const imageLoader = reactive({})

/**
 * 加载图片
 */
function loadImage(url) {
    if (imageLoader[url]) {
        return;
    }

    const img = new Image();
    img.src = url;
    img.onload = () => {
        imageLoader[url] = 'finished';
    };
    img.onerror = () => {
        imageLoader[url] = 'failed';
    };
}

/**
 * 图片渲染
 * url: 图片url
 */
function renderImage(url, title) {
    if (url === '/markdown-unfinished.png') {
        return '<div class="image-loading"></div>';
    }

    if (!imageLoader[url]) {
        loadImage(url);
    }

    if (imageLoader[url]) {
      switch (imageLoader[url]) {
          case 'loading':
              return '<div class="image-loading"></div>';

          case 'finished':
              return title
                ? `<div class="image-wrapper"><img src="${url}"><span class="image-title">${title}</span></div>`
                : `<img src="${url}">`;

          case 'failed':
              return '<div class="image-failed"></div>';
      }
    }

    return '<div class="image-loading"></div>';
}

// --------------- 初始化markdown-it，并生成window.markdownIt实例 ------------------


const emptyToken = () => {
  return {
    attrs: null,
    block: false,
    children: null,
    content: '',
    hidden: false,
    info: '',
    level: 1,
    map: null,
    markup: '',
    meta: null,
    nesting: 0,
    tag: '',
    type: 'text'
  };
};

if (!window.markdownIt) {
  const md = markdownIt({
    html: false,
    linkify: true,
    typographer: true,
    breaks: true,

    // 代码块渲染
    highlight: function (str, lang) {
      if (lang && hljs.getLanguage(lang)) {
        try {
          return '<pre><code class="hljs">' +
                hljs.highlight(str, { language: lang, ignoreIllegals: true }).value +
                '</code></pre>';
        } catch (__) {}
      }

      return '<pre><code class="hljs">' + md.utils.escapeHtml(str) + '</code></pre>';
    }
  });

  // 公式
  md.use(mditMath, {
    inlineOpen: '$$',
    inlineClose: '$$',
    blockOpen: '$$$',
    blockClose: '$$$',
    renderingOptions: {},
    inlineRenderer: function(str) {
      return katex.renderToString(str, {
        throwOnError: false
      })
    },
    blockRenderer: function(str) {
      return katex.renderToString(str, {
        throwOnError: false
      })
    },
  });

  // 链接
  const orgLinkOpenRenderer = md.renderer.rules.link_open
    ? md.renderer.rules.link_open.bind(md.renderer.rules)
    : md.renderer.renderToken.bind(md.renderer);

  md.renderer.rules.image = function(tokens, idx, options, env, self) {
    const url = tokens[idx].attrGet('src');
    const title = tokens[idx].attrGet('title') || tokens[idx].attrGet('alt');

    return renderImage(url, title);
  };

  md.renderer.rules.link_open = function(tokens, idx, options, env, self) {
    const isSimpleLink =
      tokens[idx + 2] &&
      tokens[idx + 1].type === 'text' &&
      tokens[idx + 2].type === 'link_close';

    if (!isSimpleLink) {
      return orgLinkOpenRenderer(tokens, idx, options, env, self);
    }

    let url = tokens[idx].attrGet('href') || '';
    let ext = url.split('.').pop();
    let text = tokens[idx + 1].content;

    // 纯粹的url地址转换成的链接，其文字内容是链接本身
    // 不能直接对比url === text，有可能其中一个是经过编码的，另一个是未经过编码的
    // 也不能用decodeURI等方法解码后再判断url === text, decodeURI可能会抛出异常
    const urlPattern = /^http(s?):\/\//;
    if (text.trim().match(urlPattern)) {
      text = '';
    }

    try {
      url = encodeURI(decodeURI(url) || '');
      ext = url.split('.').pop();
    } catch (e) {
      console.log(url);
      console.log(e);
    }

    // 图片链接
    if (url.match(/\.(jpg|jpeg|png|gif)[?&]*/)) {
      tokens[idx] = emptyToken();
      tokens[idx + 1] = emptyToken();
      tokens[idx + 2] = emptyToken();

      return renderImage(url);
    }

    // 视频链接
    if (url.match(/\.(mp4|webm|ogg)[?&]*/)) {
      tokens[idx] = emptyToken();
      tokens[idx + 1] = emptyToken();
      tokens[idx + 2] = emptyToken();

      return (
        '<br>' +
        '<video controls>' +
        `<source src="${url}" type="video/${ext}"></source>` +
        'Your browser does not support the video tag.' +
        '</video>' +
        '<br>'
      );
    }

    // pdf链接
    if (url.match(/\.(pdf)[?&]*/)) {
      tokens[idx] = emptyToken();
      tokens[idx + 1] = emptyToken();
      tokens[idx + 2] = emptyToken();

      const title = text.trim() || '详细信息请参考链接';
      return `<a href="${url}" target='_blank'>${title}</a>`;
    }

    // 普通链接
    tokens[idx].attrSet('target', '_blank');
    return orgLinkOpenRenderer(tokens, idx, options, env, self);
  };

  // 表格
  md.renderer.rules.table_open = function(tokens, idx, options, env, self) {
    return '<div class="table-wrapper"><table>';
  };

  md.renderer.rules.table_close = function(tokens, idx, options, env, self) {
    return '</table></div>';
  };

  window.markdownIt = md;
}

const props = defineProps({
    cnt: {
        type: String,
        required: true
    }
});

const html = computed(() => {
    return window.markdownIt.render(props.cnt).trim() || "<p></p>";
});

</script>

<style scoped>
.message-renderer {
  max-width: calc(100vw - 600px);
}
.message-renderer :deep(img) {
  max-width: 100%;
  padding: 0.3em 0;
  display: block;
}
.message-renderer :deep(video) {
  width: min(100%, 640px);
}
.message-renderer :deep(.image-loading) {
  display: block;
  width: 300px;
  height: 200px;
  padding: 0.3em 0;
  background-image: url(/images/img-placeholder.png);
  border-radius: 3px;
  position: relative;
}
.message-renderer :deep(.image-loading::before) {
  content: '';
  display: block;
  position: absolute;
  left: calc(50% - 24px);
  top: calc(50% - 24px);
  width: 48px;
  height: 48px;
  border: 5px solid #FFF;
  border-bottom-color: transparent;
  border-radius: 50%;
  box-sizing: border-box;
  animation: rotation 2s linear infinite;
}
@keyframes rotation {
  0% {
      transform: rotate(0deg);
  }
  100% {
      transform: rotate(360deg);
  }
}
.message-renderer :deep(.image-failed) {
  display: block;
  width: 300px;
  height: 200px;
  padding: 0.3em 0;
  background-image: url(/images/img-failed.png);
  border-radius: 3px;
}
.message-renderer :deep(.image-wrapper) {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
}
.message-renderer :deep(.image-wrapper .image-title) {
  display: block;
  text-align: center;
}
.message-renderer :deep(pre) {
  border-radius: 5px;
  text-align: left;
  max-width: calc(100% - 32px);
  overflow: hidden;
}
.message-renderer :deep(.table-wrapper) {
  width: 100%;
  max-width: 100%;
  height: auto;
  overflow: auto;
}
.message-renderer :deep(table) {
  border-collapse: collapse;
  width: auto;
}
.message-renderer :deep(table th) {
  text-wrap: nowrap;
  white-space: nowrap;
  border: 1px solid lightgray;
  padding: 0.5em 1em;
}
.message-renderer :deep(table td) {
  border: 1px solid lightgray;
  padding: 0.5em 1em;
}
.message-renderer :deep(tr:nth-child(2n)) {
  background-color: #f6f8fa;
}
.message-renderer :deep(tr:hover td) {
  background-color: #f2f2f6;
}
.message-renderer :deep(a) {
  color: #409eff;
}
.message-renderer :deep(a:hover) {
  text-decoration: underline;
}


</style>
<template>
    <div>
        <div v-for="sceneType in sceneTypeList">
            <div style="line-height: 48px;font-size: 20px;margin-left: 20px;margin-top: 20px;">
                {{ sceneType.name }}
            </div>
            <div v-loading="loading">
                <el-row>
                    <div v-for="(item, index) in toolList.filter(item => item.toolStage == sceneType.key)" :key="item"
                        style="margin: 20px;max-width: 20%;flex: 0 0 20%;" @click="sceneUse(item)">
                        <el-card :body-style="{ padding: '0px' }">
                            <div slot="header" class="clearfix">
                                <img :src="item.toolImage" style="width: 48px;">

                                <el-icon style="float: right; padding: 3px 0;cursor: pointer;" @click="editTool(item)"  onclick="event.cancelBubble=true">
                                    <Operation />
                                </el-icon>
                            </div>

                            <div style="line-height: 48px;font-size: 16px;font-weight: 600;cursor: pointer;">{{
                                item.toolName }}</div>
                            <div
                                style="line-height: 24px;font-size: 14px;height:175px;padding: 5px;overflow: hidden;text-overflow: ellipsis;cursor: pointer;">
                                {{ item.toolDesc }}
                            </div>
                        </el-card>
                    </div>
                </el-row>
            </div>
        </div>

        <el-dialog v-model="editToolStepVisible" width="85%">
            <editStep1 v-if="editToolStep == 1" @nextStep="editToolGo" v-bind:sceneInfo="toolInfo"></editStep1>
            <editStep2 v-if="editToolStep == 2" @nextStep="editToolGo" v-bind:sceneInfo="toolInfo"></editStep2>
        </el-dialog>
    </div>
</template>

<script setup>
import { useRoute,useRouter } from 'vue-router';

import { ref, reactive, toRefs } from 'vue'
import editStep1 from '@/components/ecommerce/scene/sceneEditStep1'
import editStep2 from '@/components/ecommerce/scene/sceneEditStep2'
import { getToolList } from "@/api/ecommerce/tool"

const { proxy } = getCurrentInstance();

//抽屉ref
const useSceneDrawer = ref(null)
//使用会话要用到的参数
const useSceneInfo = ref({})

const loading = ref(false)

const router = useRouter()
const query = reactive(router.currentRoute.value.query)

const editToolStepVisible = ref(false)

const editToolStep = ref(1)

const editToolGo = (val) => {
    editToolStep.value = val
}

const sceneTypeList = ref([
    {
        key: 0,
        name: '热点事件'
    },
    {
        key: 1,
        name: '营销类型'
    },
    {
        key: 2,
        name: '节日和季节性活动'
    },
    {
        key: 3,
        name: '人物故事（场景工具专属）'
    }
])

//工具信息
const toolInfo = ref({
    id: 0,
    toolImage: '',
    toolName: '',
    toolDesc: '',
    toolStage: '',
    contentType: '',
    prompt: '',
    toolType: '',
    ecommerceToolKeys: []
});

//工具列表
const toolList = ref([])

//编辑
const editTool = (tool) => {
    editToolStep.value = 1
    editToolStepVisible.value = true
    toolInfo.value = tool
}

//取消
const cancel = () => {
    toolVisible.value = false;
    reset()
}

//创建工具
const createTool = () => {
    reset()
    toolTitle.value = '新建工具';
    toolVisible.value = true;
}

//工具列表
const getToolsList = () => {
    loading.value = true;
    getToolList(2,query.shopId).then(response => {
        toolList.value = response.rows;
        total.value = response.total;
        loading.value = false;
    })
}
//获取工具列表
getToolsList()
</script>


<style lang='scss' scoped>
.bottom {
    margin-top: 13px;
    line-height: 12px;
}

.button {
    padding: 0;
    float: right;
}

.image {
    width: 100%;
    display: block;
}

.clearfix:before,
.clearfix:after {
    display: table;
    content: "";
}

.clearfix:after {
    clear: both
}
</style>
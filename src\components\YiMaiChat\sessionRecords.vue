<template>
    <div class="content">
        <div class="ai-chat-content" ref="recordsBox">
            <!--开场预置问题-->
            <div class="open-question-box" v-if="isOpenQuestionShow && currentInstruct" 
            :style="{'margin-left':prefaceState?'6%':'0'}">
                <!--开场白-->
                <div style="display: flex;justify-content: left;" v-if="currentInstruct.prologue">
                    <div class="prologue" v-if="!prefaceState">{{ currentInstruct.prologue }}</div>
                </div>
                <div v-if="currentInstruct.openingQuestionList.length>0">
                    <div v-for="(item , index) in currentInstruct.openingQuestionList.slice(0,3)" style="display: flex;margin-bottom: 5px;">
                        <div class="open-question"  @click="openQuestionClick(item)" v-if="item.openingQuestion">
                            {{ item.openingQuestion }}
                        </div>
                    </div>
                </div>
            </div>
            <div v-for="(item, index) in props.messageItemManager.refMessageItemList" :class="item.isUser?'user-record-box':'robot-record-box'" :key="index" v-if="!prefaceState">
                <div class="head-img" :style="{'background-color':!item.isUser && !item.relation?'#dfdfff':''}">
                    <img :src="teamAgentSelectedList?teamAgentSelectedList.find(teamAgent => teamAgent.agentId == item.roleInfo.key)?
                    baseUrl+teamAgentSelectedList.find(teamAgent => teamAgent.agentId == item.roleInfo.key).agentIconUrl:sessionmodel
                    :sessionmodel
                    " v-if="!item.isUser && !item.relation">
                </div>
                <div :class="item.isUser?userContentClassName:robotContentClassName" >
                    <div class="user-name" v-if="(!item.isUser && !item.relation) || item.isUser">
                        {{ item.isUser ? '我' : item.roleInfo.name ? item.roleInfo.name : 
                        (teamAgentSelectedList.find(teamAgent => teamAgent.agentId == item.roleInfo.key)?
                        teamAgentSelectedList.find(teamAgent => teamAgent.agentId == item.roleInfo.key).agentName : '小智')}}
                    </div>
                    <!--状态信息-->
                    <div v-if="item.processStatus && item.event" class="process-status" :style="{ 'min-width': item.processStatusShow?'100%':''}">
                        <div v-if="!item.processStatusShow" :key="processStatusKey" 
                        @click="item.processStatusShow=!item.processStatusShow;processStatusKey = processStatusKey+1;" 
                        class="status">
                            <el-icon style="margin-right: 5px;"><CircleCheck /></el-icon> 
                            运行完毕 
                            <el-icon style="margin-left: 5px;"><ArrowDownBold /></el-icon>
                        </div>
                        <div v-if="item.processStatusShow" class="process" >
                            <div @click="item.processStatusShow=!item.processStatusShow" class="process-top">
                                <div class="process-left">
                                    <el-icon style="margin-right: 5px;"><Fold /></el-icon> 隐藏运行过程
                                </div>
                                <el-icon><ArrowUpBold /></el-icon>
                            </div>
                            <div class="process-content">
                                <div class="process-title" @click="item.processStatus.isShow=!item.processStatus.isShow" 
                                :style="{'background-color':item.processStatus.isShow?'#F0F0F5':'white',
                                'border-bottom-right-radius': item.processStatus.isShow?'0px':'10px',
                                'border-bottom-left-radius': item.processStatus.isShow?'0px':'10px',
                                }">
                                    <img :src="item.processStatus.type=='tool'?sessionTool:''"/>
                                    {{ item.processStatus.description }}
                                </div>
                                <div class="process-child-status" v-if="item.processStatus.isShow">
                                    <div v-for="(childStatus,index) in item.processStatus.child_status">
                                        <span v-if="childStatus.isShow">
                                            {{ childStatus.description }}
                                        </span>
                                    </div>
                                    <!-- <delayed-display :child-status="item.processStatus.child_status"></delayed-display> -->
                                </div>
                            </div>
                            <div class="process-bottom">
                                <div class="bottom-content">运行完毕</div>
                            </div>
                        </div>
                    </div>
                    <!--内容-->
                    <div :class="item.isUser?userBoxClassName:robotBoxClassName" 
                    v-if="!item.event || (item.event && item.choices.length>0)">
                        <div v-if="item.isUser" style="display: flex;align-items: center;">
                            <img :src="pdfRed" style="height: 25px;width: 20px;margin-right: 5px;"
                            v-if="item.content && item.content.includes('.pdf')">
                            {{ item.content }}
                        </div>
                        <template v-else>
                            <message-renderer :cnt="item.content"/>
                        </template>
                        <!-- <div v-html="item.content" v-else></div> -->
                    </div>
                    <!--引用出处-->
                    <div v-if="!item.isUser && item.knowledgeList && item.knowledgeList.length>0" class="knowledge-list">
                        <citation-list :data="item.knowledgeList"></citation-list>
                    </div>
                    <!--选项-->
                    <div v-if="!item.isUser && item.event && item.choices.length>0 && item.choicesShow">
                        <div v-for="(choices,index) in item.choices" class="choices" @click="{item.choicesShow=false;choicesClick(choices)}" >
                            {{ choices.label }}</div>
                    </div>
                    <!--建议问题-->
                    <div v-if="!item.isUser && item.autosuggestion" style="margin-top: 5px;">
                        <div v-for="(autosuggestion,index) in JSON.parse(item.autosuggestion)" class="choices" @click="autosuggestionClick(autosuggestion)" >{{ autosuggestion }}</div>
                    </div>
                </div>
                <div class="head-img" v-if="item.isUser">
                    <!--baseUrl+-->
                    <img :src="userStore.avatar">
                </div>
            </div>
            <div class="robot-record-box" v-if="props.messageItemManager.loading.value && !prefaceState">
                <div class="head-img" v-loading="props.messageItemManager.nowMessageRole.value.key && !props.messageItemManager.waitting.value?false:true"
                    :style="{'background-color':props.messageItemManager.waitting.value?'#dfdfff':''}" >
                    <img :src="teamAgentSelectedList?
                    (teamAgentSelectedList.find(teamAgent => teamAgent.agentId == props.messageItemManager.nowMessageRole.value.key)?
                    baseUrl+teamAgentSelectedList.find(teamAgent => teamAgent.agentId == props.messageItemManager.nowMessageRole.value.key).agentIconUrl
                    :sessionmodel
                    ):sessionmodel
                    " v-if="!props.messageItemManager.waitting.value">
                </div>
                <div class="robot-content">
                    <div class="user-name">
                        {{ props.messageItemManager.nowMessageRole.value.name || '小智'}}
                    </div>
                    <div class="robot-record-item">
                        <template v-if="props.messageItemManager.waitting.value">
                            wait...
                        </template>
                        <template v-else>
                            <message-renderer :cnt="props.messageItemManager.nowMessageHtml.value" />
                        </template>
                    </div>
                </div>
            </div>
        </div>
        <div class="bottom-area" :style="{'padding-left':prefaceState?'6%': '0'}">
            <div class="bar">
                <div class="tools-list" >
                    <!-- <div class="tools-item" @click="">
                        <img class="tool-icon" :src="picture" width="20px" height="20px"/>
                    </div> -->
                    <!--<div class="tools-item" @click="">
                        <img class="tool-icon" :src="exportsvg" width="20px" height="20px"/>
                    </div> -->
                    <el-tooltip :disabled="disabled" content="新会话" placement="top" effect="light" v-if="!prefaceState">
                        <div class="tools-item" @click="openNewSession">
                            <img class="tool-icon" :src="newSession" width="20px" height="20px"/>
                        </div>
                    </el-tooltip>
                </div>
            </div>
            <div class="submit-area">
                <div class="content-area">
                    <div class="user-input-area">
                        <div class="top-area">
                            <div class="text-area">
                                <textarea ref="userInputTextarea" v-model="userInputTextareaValue" placeholder="输入" autocomplete="off" 
                                    class="user-input"  @keydown="handleKeyDown" @keydown.enter.shift.prevent="handleShiftEnter" @input="handleInput"
                                    ></textarea>
                            </div>
                        </div>
                        <div class="bottom-info-area">
                            <el-button type="primary" :icon="Promotion" size="small" round :disabled="isDisabledSend" @click="runTest"></el-button>
                            <!-- <div class="send-btn" :class="{'disabled':isDisabledSend}" @click="runTest"></div> -->
                        </div>
                    </div>
                </div>
            </div>
            <!-- <div class="tip">内容由AI生成，无法确保真实准确。</div> -->
        </div>
    </div>
</template>
<script setup>
    import { defineProps, nextTick,ref, watch } from 'vue';
    import {v4 as uuidv4} from 'uuid'
    import useUserStore from '@/store/modules/user'
    import { CircleCheck ,ArrowDownBold ,Fold ,ArrowUpBold,Promotion } from '@element-plus/icons-vue'
    import { getToken } from "@/utils/auth";
    import { TaskQueue } from './utils/taskQueue'
    import sessionmodel from "@/assets/icons/svg/session-model.svg"
    import MessageRenderer from '@/components/Chat/messageRenderer.vue'
    import newSession from "@/assets/icons/svg/newSession.svg" 
    import CitationList from '@/components/Chat/citationList.vue'

    const props = defineProps({
        messageItemManager:{
            type:Object,
            required: false
        },
        //服务路径
        chatUrl:{
            type:String,
            required:true
        },
        keywords:{
            type:Object,
            required:false,
            default: () => {
                return {}
            }
        },
        //是否是调试
        isDebugging:{
            type:Boolean,
            required:false,
            default:false
        },
        currentInstruct:{
            type:Object,
            required:true
        }
    });

    let ws = null;

    //当前点击选中的指令
    const currentInstruct = ref(null)
    watch(() => props.currentInstruct, val => {
        if (val) {
            currentInstruct.value = val
            return val
        } else {
            currentInstruct.value = null
            return {}
        }
    },{ deep: true, immediate: true });

    //消息列表ref
    const recordsBox = ref(null)
    const isMessageEnding = ref(false)
    //初始化消息队列对象
    const taskQueue = new TaskQueue()
    //初始化消息队列对象的Complete
    taskQueue.setOnComplete(() => {
        // if(currentInstruct.value){
        //     currentInstruct.value = null
        // }
        if(!taskQueue.running && !isMessageEnding.value && ws.readyState == 1 && taskQueue.index == taskQueue.tasks.length ){
            props.messageItemManager.clearNowMessageRole();
            props.messageItemManager.do();
            props.messageItemManager.waittingStart();
            taskQueue.clearTasks()
        }else{
            // userInputTextareaValue.value = ''
            // if(ws.readyState == 3){
                
            // }
            props.messageItemManager.clearNowMessageRole();
        }
        if(!props.messageItemManager.waitting.value){
            isDisabledSend.value = false
        }
        // if(ws.readyState == 2 || ws.readyState == 3 || ws.readyState == 0){
        //     // userInputTextareaValue.value = ''
        //     isDisabledSend.value = true;
        // }
    })

    const userBoxClassName = "user-record-item";
    const userContentClassName = "user-content";

    const robotBoxClassName = "robot-record-item";
    const robotContentClassName = "robot-content";
    let timeres = []
    //是否展示开场预置问题
    const isOpenQuestionShow = ref(true)
    const initMessage = ref({})

    //用户输入框的ref元素
    const userInputTextarea = ref(null)
    //发送消息的禁用状态
    const isDisabledSend = ref(true)
    //用户输入框的值
    const userInputTextareaValue  = ref('')
    //前言展示状态
    const prefaceState = ref(false)
    //文件
    const fileVisible = ref(false)
    const fileStorage = ref(null)
    //是否需要刷新历史记录
    const needRefreshConversation = ref(false)

    //是否第一次发送
    const firstState = ref(true)
    //输入框输入时的事件
    const handleInput = () => {
        if(userInputTextareaValue.value && !props.messageItemManager.waitting.value ){
            if(firstState.value || (!firstState.value && ws.readyState == 1)){
                isDisabledSend.value = false
            }
        }else{
            isDisabledSend.value = true
        }
    }

    //输入框按下键时触发事件
    const handleKeyDown = (e) => {
        if (e.key === 'Enter') {  
            e.preventDefault();
            if (e.shiftKey) {
                userInputTextareaValue.value += '\n';  
            } else {
                if(!isDisabledSend.value){
                    runTest();  
                }
            }  
        }
    }
    
    const chatUrlBringId=ref('')
    const userStore = useUserStore()
    const sessionId = ref(null)
    //是否是历史会话记录
    const isHistorySession = ref(false)
    //点击发送
    const runTest = () =>{
        if(currentInstruct.value && userInputTextareaValue.value){
            let pamrams = {};
            if(!isHistorySession.value){
                sessionId.value = uuidv4()
            }
            // if(currentInstruct.value.applicationType=='team'){
            //     if(currentInstruct.value.keyList){
            //         currentInstruct.value.keyList.forEach(item => {
            //             pamrams[item.keyCode]=''
            //         })
            //     }
            //     chatUrlBringId.value=props.chatUrl+'/'+currentInstruct.value.applicationId
            //     //产品设计
            //     if(currentInstruct.value.applicationId==54){
            //         pamrams.product=userInputTextareaValue.value
            //         const selectedAgents = JSON.parse(currentInstruct.value.arrangeJson).nodes.filter(node => node.data.type == 'agent').map(node => node.data.data.agentId + '')
            //         initMessage.value = {
            //             teamId: currentInstruct.value.applicationId,
            //             agentIds:selectedAgents,
            //             sessionId:sessionId.value,
            //             keywords:pamrams,
            //             userName:userStore.id
            //         }
            //     }else{
            //         //智能履约
            //         if(currentInstruct.value.applicationId==115){
            //             pamrams.userQuestion=userInputTextareaValue.value
            //         }
            //         initMessage.value = {
            //             teamId: currentInstruct.value.applicationId,
            //             keywords:pamrams,
            //             arrangeJson:JSON.parse(currentInstruct.value.arrangeJson),
            //             prompt:currentInstruct.value.prompt,
            //             sessionId:sessionId.value,
            //             token:getToken(),
            //             userName:userStore.id
            //         }
            //         //发票报销团队
            //         if(currentInstruct.value.applicationId==91 || currentInstruct.value.applicationId==123){
            //             initMessage.value.message=userInputTextareaValue.value
            //         }
            //     }
            // }else if(currentInstruct.value.applicationType=='agent'){
                if(currentInstruct.value.keyList){
                    currentInstruct.value.keyList.forEach(item => {
                        pamrams[item.keyCode]=''
                    })
                }
                chatUrlBringId.value=props.chatUrl+'/'+currentInstruct.value.agentId
                initMessage.value = {
                    id: currentInstruct.value.agentId,
                    agent_name: currentInstruct.value.agentName,
                    token: getToken(),
                    user_name: userStore.id,
                    sessionId: sessionId.value,
                    model_id: "",
                    keywords: pamrams,
                    temperature: "",
                    top_p: "",
                    presence_penalty: "",
                    frequency_penalty: "",
                    max_tokens: "",
                    prompt: currentInstruct.value.prompt,
                    knowledgeList: currentInstruct.value.knowledgeBaseList,
                    max_recall: "",
                    min_match: "",
                    message: userInputTextareaValue.value
                }
            // }
        }else{
            if(!currentInstruct.value){
                ElMessage.warning("请选择Agent！")
            }
        }
        if((currentInstruct.value && userInputTextareaValue.value) && firstState.value){
            sendMessage()
        }
        else if(userInputTextareaValue.value && !firstState.value){
            sendMsgHandle()
        }
    }

    //发送消息
    const sendMessage = () => {
        if(props.messageItemManager.loading.value){
            ElMessage.warning('正在处理中，请稍后再试')
            return;
        }
        if(userInputTextareaValue.value){
            firstState.value=false
            prefaceState.value=false
            isOpenQuestionShow.value=false
            if(!isHistorySession.value){
                props.messageItemManager.cleanHistory();
            }
            props.messageItemManager.pushUserMessage(userInputTextareaValue.value);
            initConnect().then(() => {
                needRefreshConversation.value = true
                customSend(initMessage.value,false)
            })
            userInputTextareaValue.value = ''
        }
    }

    const initConnect = () => {
        return new Promise((resolve,reject) => {
            isDisabledSend.value = true;
            ws = new WebSocket(chatUrlBringId.value);
            ws.addEventListener('open', () => {
                console.log('WebSocket连接已打开');
                isDisabledSend.value = false;
                resolve();
            });
            ws.addEventListener('message', (event) => {
                props.messageItemManager.waittingEnd();
                const message = event.data;
                let obj = JSON.parse(message)
                if(obj.status == 0){
                    isMessageEnding.value = false;
                }else if(obj.status == 1){
                    isMessageEnding.value = true;
                }else if(obj.status == 2){
                    ws.close()
                    isMessageEnding.value = false;
                    isDisabledSend.value = true;
                }
                taskQueue.addTask(printBotMessage,obj)
            });
            ws.addEventListener('close', () => {
                isDisabledSend.value = true;
                //关闭wait状态
                props.messageItemManager.disconnected()
                console.log('WebSocket连接已断开');
            });
            ws.addEventListener('error', (error) => {
                isDisabledSend.value = true;
                console.error('发生错误：', error);
            });
        })
        
    }

    const printBotMessage = (message) => {
        return new Promise((resolve) => {
            if(needRefreshConversation.value){
                // conversation.value.refresh().then(() => {
                //     conversation.value.selectFirst(false)                 
                // })
            }
            props.messageItemManager.do();
            props.messageItemManager.waittingEnd();
            let role = {
                name:message.role,
                key:message.agentId
            };
            let txt ;
            if(typeof message.messages == 'string'){
                txt = message.messages.split('')
            }else if(typeof message.messages == 'object'){
                txt = message.messages.map(item => item+'   ')
            }
            let tempTxt = ''
            txt.forEach((item,index) => {
                let timer = setTimeout(() => {
                    props.messageItemManager.appendNowMessageContent(tempTxt += item,role,message.process_status
                    ,message.event?message.event:'',message.relation?message.relation:'',message.choices?message.choices:[]
                    ,message.knowledgeList?message.knowledgeList:[],message.autosuggestion?message.autosuggestion:'',false);
                    if(index == txt.length-1){
                        props.messageItemManager.deleteLastMsgRecord();
                        props.messageItemManager.messageReceiveDone();
                        resolve(message)
                    }
                }, 30 * index );
                timeres.push(timer) 
            })
        })
    }

    function sendMsgHandle() {
        if(ws.readyState == 1){
            if (!userInputTextareaValue.value) {
                ElMessage.warning("你要发送什么呢？")
                return;
            }
            let obj = {
                message:userInputTextareaValue.value
            }
            needRefreshConversation.value = false
            customSend(obj,true);
        }
    }

    const customSend = (message,needPushUserMessage = true) => {
        props.messageItemManager.do();
        props.messageItemManager.waittingStart();
        clearTasks()
        isDisabledSend.value = true;
        if(needPushUserMessage){
            props.messageItemManager.pushUserMessage(userInputTextareaValue.value);
        }
        userInputTextareaValue.value = ''
        ws.send(JSON.stringify(message))
    }

    const clearTasks = () => {
        taskQueue.clearTasks()
    }

    //建议问题处理
    const autosuggestionClick = (autosuggestion) => {
        userInputTextareaValue.value=autosuggestion
        sendMsgHandle()
    }

    //当前选中团队活智能体新会话
    const openNewSession = () => {
        sessionId.value = null
        //是否展示开场预置问题
        isOpenQuestionShow.value=true
        //是否发起新一轮会话（是）
        firstState.value = true
        //输入框内容清空
        userInputTextareaValue.value = ''
        //是否是历史会话记录
        isHistorySession.value = false
        props.messageItemManager.new()
        if(ws){
            ws.close()
        }
        clearTimeres()
    }
    const clearTimeres = () => {
        timeres.forEach(element => {
            clearTimeout(element)
        });
        timeres = []
    }

    //选择开场问题
    const openQuestionClick = (openQuestion) => {
        userInputTextareaValue.value = openQuestion.openingQuestion
        runTest()
    }

    //定位到最新内容
    const moveScroll = () => {
        nextTick(()=> {
            recordsBox.value.scrollTop = recordsBox.value.scrollHeight;
        })
    }

    watch(props.messageItemManager.nowMessageHtml, () => {
        moveScroll();
    })

    watch(props.messageItemManager.nowMessageLine, () => {
        moveScroll();
    });

    watch(props.messageItemManager.refMessageItemList, (newValue, oldValue) => {
        if(newValue.length!=oldValue.length){
            moveScroll()
        }
    });

    watch(props.messageItemManager.waitting, () => {
        moveScroll();
    });

</script>
<style lang="scss" scoped>
.content{
    padding: 10px 10%;
    text-align: center;
    width: 100%;
    .top{
        height: 80px;
        line-height: 80px;
        font-size: 30px;
        color: #5050E6;
        font-weight: 550;
    }
    .ai-chat-content{
        height: calc(100vh - 380px);
        overflow-y: auto;
        overflow-x: hidden;
        .chat-message{
            display: flex;
            flex-direction: column;
            .user-chat-area{
                align-self: flex-end;
                display: flex;
                flex-direction: column;
                margin-bottom: 10px;
                padding-left: 0;
                .user-box{
                    display: flex;
                    flex-direction: row;
                    justify-content: end;
                    align-items: center;
                    margin-bottom: 3px;
                    .user-name{
                        line-height: 34px;
                        height: 34px;
                        color: #B2B2B2;
                        font-size: 13px;
                    }
                    .user-icon{
                        img{
                            width:30px;
                            height:30px;
                            background-color: #6977F1;
                            border-radius: 5px;
                            border: 1px solid #E6E6E6;
                        }
                        margin-left:4px;
                    }
                }
                .user-chat-message{
                    background-color: #5050E6;
                    border-radius: 12px;
                    border-top-right-radius: 0;
                    box-sizing: border-box;
                    color: #fff;
                    // cursor: pointer;
                    display: inline-block;
                    font-size: 14px;
                    line-height: 22px;
                    min-height: 26px;
                    outline: none;
                    padding: 9px 14px;
                    white-space: normal;
                    word-break: break-word;
                }
            }
            .robot-chat-area{
                align-items: flex-start;
                display: flex;
                flex-direction: column;
                justify-content: flex-start;
                margin-bottom: 10px;
                position: relative;
                .robot-box{
                    display: flex;
                    flex-direction: row;
                    align-items: center;
                    margin-bottom: 3px;
                    .robot-name{
                        line-height: 34px;
                        height: 34px;
                        color: #B2B2B2;
                        font-size: 13px;
                    }
                    .robot-icon{
                        img{
                            width:30px;
                            height:30px;
                            background-color: #6977F1;
                            border-radius: 5px;
                            border: 1px solid #E6E6E6;
                        }
                            margin-right:4px;
                        // background-color: #dfdfff
                    }
                }
                .robot-chat-message{
                    background-color: #F9FBFC;
                    border-radius: 0 12px 12px;
                    max-width: 100%;
                    padding: 12px 14px;
                    // width: 100%;
                    box-sizing: border-box;
                    overflow: hidden;
                    ::v-deep(.github-markdown-body) {
                        padding: 0;
                        font-size: 13px !important;
                        p{
                            margin-bottom: 0;
                        }
                    }
                }
            }
        }
        .preface{
            display: flex;
            justify-content: space-between;
            .preface-left{
                width: 6%;
                img{
                    width:25px;
                    height:25px;
                    background-color: #6977F1;
                    border-radius: 5px;
                }
            }
            .preface-right{
                width: 94%;
                background-color: white;
                border-radius: 5px;
                padding: 20px;
                .preface-font{
                    line-height: 30px;
                    font-size: 14px;
                    text-align: left;
                    color: #747796;
                }
                .preface-tool{
                    background-color: #F7F8FD;
                    padding: 20px 10px;
                    border-radius: 5px;
                    cursor: pointer; 
                    .preface-title{
                        display: flex;
                        align-items: center;
                        img{
                            width:20px;
                            height:20px;
                            border-radius: 5px;
                        }
                    }
                    .preface-content{
                        overflow: hidden; 
                        white-space: nowrap;
                        text-overflow:ellipsis;
                        width: 100%;
                        line-height: 30px;
                        font-size: 14px;
                        text-align: left;
                        color: #878AA5;
                    }
                }
            }
        }
        .open-question-box{
            margin-top: 20px;
            display: flex;
            flex-direction: column;
            .prologue{
                border:1px solid #E9EAEE;
                border-radius: 10px;
                background-color: white;
                color: black;
                padding: 10px 20px;
                font-size: 15px;
                text-align:left;
                margin-bottom: 5px;
            }
            .open-question{
                border:1px solid #E9EAEE;
                border-radius: 10px;
                background-color: white;
                color: #7A8698;
                padding: 10px 20px;
                display: inline-block;
                font-size: 15px;
                cursor: pointer;
            }
        }
        .record-box-user {
            width: calc(100%);
            // margin-left: 10px;
            display: inline-flex;
            .head-img{
                img{
                    height: 40px;
                    width: 40px;
                    border: 2px solid #E6E6E6;
                    border-radius: 8px;
                    margin-left: 10px;
                    background-color: #dfdfff
                }
            }
        
        }
        .record-box-robot {
            width: calc(100%);
            // margin-left: 10px;
            display: inline-flex;
            .head-img{
                height: 40px;
                width: 44px;
                border-radius: 8px;
                margin-right: 10px;
                img{
                    height: 40px;
                    width: 40px;
                    border: 2px solid #E6E6E6;
                    border-radius: 8px;
                    background-color: #dfdfff;
                }
            }
            ::v-deep .el-loading-mask {
                position: absolute;
                z-index: 2000;
                background-color: #f5f0f08f;
                margin: 0;
                top: 0;
                right: 0;
                bottom: 0;
                left: 0;
                transition: opacity var(--el-transition-duration);
                height: 40px;
                width: 41px;
                border-radius: 10px;
                padding: 7px;
            }
            ::v-deep .el-loading-spinner {
                top: 50%;
                margin-top: calc((0px - var(--el-loading-spinner-size)) / 2);
                width: 100%;
                text-align: center;
                position: absolute;
                width: 41px;
                height: 40px;
                /* align-items: center; */
                display: flex;
                align-items: center;
            }
            ::v-deep .el-loading-spinner .circular {
                display: inline;
                -webkit-animation: loading-rotate 2s linear infinite;
                animation: loading-rotate 2s linear infinite;
                height: 27px;
                width: 25px;
            }
        
        }
        .record-item {
            display: inline-block;
            border-radius: 10px;
            background-color: #fff;
            padding: 8px 12px;
            max-width: calc(100% - 60px);
            margin-bottom: 10px;
            font-size: 15px;
        }
        .user-record-box {
            @extend .record-box-user;
            justify-content: flex-end;
            text-align: right;
            float: right;
        }
        .robot-record-box {
            @extend .record-box-robot;
            text-align: left;
        }
        .user-name{
            line-height: 20px;
            height: 20px;
            color: #B2B2B2;
            font-size: 12px;
        }
        .robot-record-item {
            @extend .record-item;
            color: #032220;
            background-color: #F9FBFC;
            font-size: 14px;
            font-weight: 500;
        }
        .user-record-item {
            @extend .record-item;
            color: white;
            background-color: #5050e6;
            text-align: left;
            font-size: 14px;
            font-weight: 500;
        }
        .user-content{
            display: flex;
            flex-direction: column;
            align-items: end;
            width: 100%;
        }
        .robot-content {
            display: flex;
            flex-direction: column;
            align-items: start;
            width: 100%;
        }
        .process-status{
            .status{
                background-color: #F9FBFC;
                padding: 10px;
                border-radius: 10px;
                margin-bottom: 5px;
                color: #19CD56;
                display: flex;
                align-items: center;
                cursor: pointer;
            }
            .process{
                color: #032220;
                background-color: #F9FBFC;
                margin-bottom: 5px;
                border-radius: 10px;
                width: calc( 100% - 60px );
                .process-top{
                    display: flex;
                    justify-content: space-between;
                    align-items:center;
                    font-size:18px;
                    padding: 8px 12px;
                    background-color: white;
                    border-top-left-radius: 10px;
                    border-top-right-radius: 10px;
                    cursor: pointer;
                    .process-left{
                        display: flex;
                        align-items: center;
                    }
                }
                .process-content{
                    padding: 5px;
                    .process-title{
                        display: flex;
                        align-items: center;
                        // margin-left: 5px;
                        padding: 10px;
                        background-color: white;
                        border-radius: 10px;
                        cursor: pointer;
                        img{
                            height: 20px;
                            width: 20px;
                            margin-right: 10px;
                        }
                        &:hover{
                            background-color: #F0F0F5;
                        }
                    }
                    .process-child-status{
                        background-color: #F7F7FA;
                        padding: 5px 15px;
                        font-size: 13px;
                        line-height: 20px;
                        border-bottom-right-radius: 10px;
                        border-bottom-left-radius: 10px;
                    }
                }
                .process-bottom{
                    background-color: #F9FBFC;
                    padding: 10px;
                    border-bottom-left-radius: 10px;
                    border-bottom-right-radius: 10px;
                    .bottom-content{
                        width: 100px;
                        padding: 5px;
                        font-size: 12px;
                        background-color: #ECF7EC;
                        color: #32A252;
                        display: flex;
                        justify-content: center;
                        border-radius: 5px;
                    }
                }
            }
        }
        .knowledge-list{
            color: #032220;
            background-color: #F9FBFC;
            width: calc( 100% - 60px );
            padding: 8px 12px;
            border-radius: 10px;
        }
        .choices{
            background-color: #F5F6F7;
            // min-width: 150px;
            padding: 10px;
            display: flex;
            align-items: center;
            justify-content: left;
            border-radius: 15px;
            border: 1px solid #E8E9ED;
            margin-bottom: 5px;
            color: #032220;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;

            &:hover{
                background-color: #ececec;
            }
        }
    } 
    .bottom-area{
        background-color: transparent;
        padding-top: 10px;
        bottom: 0;
        // position: absolute;
        text-align: center;
        width: 100%;
        z-index: 11;
        .bar{
            border-top: 1px solid transparent;
            background-color: transparent;
            box-sizing: border-box;
            padding: 0 16px;
            width: 100%;
            align-items: center;
            display: flex;
            justify-content: flex-start;
            .tools-list{
                background: transparent;
                display: flex;
                height: 48px;
                padding-top: 10px;
                .tools-item{
                    background: #fff;
                    box-shadow: 1px 1px 5px 0 #d9d9ff;
                    align-items: center;
                    border-radius: 8px;
                    cursor: pointer;
                    display: flex;
                    height: 30px;
                    margin-right: 8px;
                    outline: none;
                    padding: 6px 8px;
                    .tool-icon{
                        background-repeat: no-repeat;
                        background-size: cover;
                        display: inline-block;
                        height: 20px;
                        width: 20px;
                    }
                    .tool-name{
                        color: #1c1f1e;
                        font-family: PingFangSC-Regular;
                        font-size: 12px;
                        line-height: 12px;
                        margin-left: 6px;
                    }
                }
            }
        }
        .submit-area{
            // padding: 0 16px 12px;
            box-sizing: border-box;
            width: 100%;
            .content-area{
                background-image: linear-gradient(90deg, #5050E6, #b6b6ff);
                border-radius: 12px;
                box-shadow: 0 0 12px 0 rgba(0,155,109,.15);
                box-sizing: border-box;
                height: 100%;
                padding: 2px;
                width: 100%;
                .user-input-area{
                    border: 0 !important;
                    border-radius: 10px;
                    padding: 8px 10px;
                    position: relative;
                    transition: opacity .5s;
                    width: 100%;
                    background-color: #fff;
                    .top-area{
                        display: flex;
                        position: relative;
                        .text-area{
                            line-height: 1.4;
                            flex: 1;
                            font-size: 14px;
                            overflow: hidden;
                            position: relative;
                            .tool-tip{
                                align-content: center;
                                align-items: center;
                                background-color: #f0f2f2;
                                border-radius: 4px;
                                display: flex;
                                height: 26px;
                                line-height: 26px;
                                padding-left: 10px;
                                padding-right: 0;
                                cursor: text;
                                left: 0;
                                position: absolute;
                                top: 0;
                                font-size: 14px;
                            }
                            .user-input{
                                font-size: 14px;
                                height: 62px;
                                border: none;
                                box-sizing: border-box;
                                color: #222;
                                line-height: 20px;
                                outline: 0;
                                overflow-y: auto;
                                width: 100%;
                            }
                            .show-tool-tip{
                                line-height: 26px;
                            }
                        }
                        textarea{
                            font: 100% arial, helvetica, clean;
                            overflow: auto;
                            vertical-align: top;
                            resize: none;
                        }
                    }
                    .bottom-info-area{
                        display: flex;
                        justify-content: flex-end;
                        margin-top: 5px;
                        .send-btn{
                            background: linear-gradient(316deg, #5050E6 16.71%, #5050E6 116.53%);
                            border-radius: 10px;
                            height: 26px;
                            position: relative;
                            width: 36px;
                            &:after{
                                background: url(https://edu-wenku.bdimg.com/v1/pc/aigc/presentation-sample/send-1702458556573.svg) no-repeat;
                                background-size: cover;
                                content: "";
                                height: 16px;
                                position: absolute;
                                top: 50%;
                                transform: translate(-50%, -50%);
                                width: 16px;
                            }
                        }
                        .disabled{
                            cursor: not-allowed;
                        }
                    }
                }
            }
        }
        .tip{
            line-height: 25px;
            font-size: 12px;
            text-align: left;
            color: #9E9EBB;
        }
    }
}

</style>
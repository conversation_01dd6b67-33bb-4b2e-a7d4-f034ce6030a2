import request from '@/utils/request'

// 新建课程
export function createCourse (data) {
    return request({
        url: '/lessons',
        method: 'post',
        data
    })
}

// 编辑课程
export function editCourse (data) {
    return request({
        url: '/lessons',
        method: 'put',
        data
    })
}


// 获取课程列表
export function getCourseList (params) {
    return request({
        url: '/lessons/list',
        method: 'get',
        params
    })
}

// 获取我学的课
export function getCoursesToLearn (params) {
    return request({
        url: '/lessons/student/list',
        method: 'get',
        params
    })
}

// 设置置顶
export function renameCourse (params) {
    return request({
        url: '/lessons/list',
        method: 'get',
        params
    })
}

// 删除课程
export function delCourse (id) {
    return request({
        url: `/lessons/delete/${id}`,
        method: 'post',
    })
}

// 获取班级列表
export function getCourseClassList (params) {
    return request({
        url: '/ClassInfo/list',
        method: 'get',
        params
    })
}
// 获取班级列表-教师
export function getCourseClassList_teacher (params) {
    return request({
        url: '/ClassInfo/getClassByUser?roleType=1',
        method: 'get',
        params
    })
}

// 设置置顶
export function setTopType (data) {
    return request({
        url: '/lessons/top',
        method: 'post',
        data
    })
}

// 课程状态修改
export function changeCourseStatus (data) {
    return request({
        url: '/lessons/status',
        method: 'post',
        data
    })
}

// 获取课程详情
export function getCourseDetail (lessonId) {
    return request({
        url: `/lessons/${lessonId}`,
        method: 'get',
    })
}


// 获取课程的任务目录树
export function getCourseTaskDirectoryTree (params) {
    return request({
        url: '/lessons/task/catalog',
        method: 'get',
        params
    })
}

// 保存课程的任务目录节点
export function saveCourseTaskDirectoryNode (data) {
    return request({
        url: '/lessons/catalog/save',
        method: 'post',
        data
    })
}

// 删除课程的任务目录节点
export function delCourseTaskDirectoryNode (data) {
    return request({
        url: '/lessons/catalog/delete',
        method: 'post',
        data
    })
}

// 获取课程的任务目录树
export function getCourseTaskList (params) {
    return request({
        url: '/lessons/task/list',
        method: 'get',
        params
    })
}

// 获取任务详情
export function getTaskDetail (params) {
    return request({
        url: '/lessons/task/one?taskId=' + params,
        method: 'get',
        params
    })
}

// 保存任务
export function saveTask (data) {
    return request({
        url: '/lessons/task/save',
        method: 'post',
        data
    })
}

// 删除任务
export function delTask (data) {
    return request({
        url: '/lessons/task/delete',
        method: 'post',
        data
    })
}

// 获取文档列表
export function getCourseResourceList (data) {
    return request({
        url: '/FileResource/getFileListByLesson',
        method: 'post',
        data
    })
}

// 发布课程
export function publishCourse (data) {
    return request({
        url: '/lessons/status',
        method: 'post',
        data
    })
}

// 设置任务开启或禁用
export function setTaskState (data) {
    return request({
        url: '/lessons/update/effective',
        method: 'post',
        data
    })
}

// 新增历史记录
export function addHistory (data) {
    return request({
        url: '/lesson/conversation',
        method: 'post',
        data
    })
}

// 获取历史记录
export function getTheHistory (params) {
    return request({
        url: '/lesson/conversation/list',
        method: 'get',
        params
    })
}
// 学生端课程设置置顶
export function setStudentTopType (data) {
    return request({
        url: '/lessonTop/add',
        method: 'post',
        data
    })
}
// 学生端课程设置取消置顶
export function delStudentTopType (data) {
    return request({
        url: `/lessonTop/remove/${data.id}`,
        method: 'delete'
    })
}
<template>
    <div class="message-send" v-show="chatUrl != ''">
        <!-- <el-button :icon="Clock" text></el-button> -->
        <div class="input-content">
            <el-input maxlength="1000" 
                :disabled="isDisabledSend"
                :placeholder="'您可以向我提任何问题'"
                @keyup.enter="sendMsgHandle" 
                resize="none" 
                :autosize="{ minRows: 1, maxRows: 5 }"
                v-model="inputContent">
                <template #suffix>
                    <el-button link :disabled="isDisabledSend" :icon="Promotion" @click="sendMsgHandle" />
                </template>
            </el-input>
        </div>
        <!-- <el-button :icon="CirclePlus" text></el-button> -->
    </div>
</template>
<script setup>
import "highlight.js/styles/atom-one-light.css";
import 'element-plus/es/components/message/style/css';
import { ElMessage } from 'element-plus';
import { ref, defineProps, nextTick } from 'vue';
import { Clock,Promotion,CirclePlus} from '@element-plus/icons-vue';

let ws = null;

const props = defineProps({
    messageItemManager:{
        type:Object,
        required: false
    },
    chatUrl:{
        type:String,
        required: false,
        default:""
    },
    sessionId:{
        type:String,
        required: false,
        default:""
    },
    keywords:{
        type:Object,
        required:false,
        default: () => {
            return {}
        }
    }
});

const inputContent = ref('');
const isDisabledSend = ref(true)
const isConnectSuccess = ref(false)
const uuid = ref(props.sessionId)

watch(() => props.sessionId, (newVal) => {
    uuid.value = newVal
})

function sendMsgHandle() {
    if (inputContent.value.trim() === '') {
        ElMessage.warning("你要发送什么呢？")
        return;
    }
    const message = {
        message:inputContent.value
    }
    if(props.messageItemManager.msgRecords.value.length === 0){
        message.keywords = props.keywords || {}
    }
    props.messageItemManager.pushUserMesage(inputContent.value);
    wsSend(message);
}

const wsSend = (message) => {
    if(isConnectSuccess.value === false){
        ElMessage.warning("请先连接到服务器")
        return;
    }
    isDisabledSend.value = true
    props.messageItemManager.do();
    props.messageItemManager.waittingStart();
    ws.send(JSON.stringify(message))
}

const sendInitMsg = (msg) => {
    if(isConnectSuccess.value === false){
        ElMessage.warning("请先连接到服务器")
        return;
    }
    ws.send(JSON.stringify(msg))
}

const printBotMessage = (message) => {
    return new Promise((resolve) => {
        props.messageItemManager.do();
        if(message.key=='image'){
            message.messages='<img src="'+message.messages+'"/>'
            props.messageItemManager.appendNowMessageContent(message.messages,props.messageItemManager.nowMessageRole.value,false);
            props.messageItemManager.deleteLastMsgRecord();
            props.messageItemManager.messageReceiveDone();
            resolve(message)
        }else if(message.key=='text' || message.key=='tips'){
            let txt = message.messages.split('')
            let tempTxt = ''
            txt.forEach((item,index) => {
                setTimeout(() => {
                    props.messageItemManager.appendNowMessageContent(tempTxt += item,props.messageItemManager.nowMessageRole.value,false);
                    if(index == txt.length-1){
                        props.messageItemManager.deleteLastMsgRecord();
                        props.messageItemManager.messageReceiveDone();
                        resolve(message)
                    }
                }, 30 * index );
            })
        }
    })
}

const initConnectWS = (initInfo) => {
    if(ws != null){
        ws.close()
        ws = null
        isConnectSuccess.value = false
        isDisabledSend.value = true
    }
    ws = new WebSocket(props.chatUrl);
    ws.addEventListener('open', () => {
        console.log('WebSocket连接已建立');
        isConnectSuccess.value = true
        isDisabledSend.value = false
        if(initInfo){
            sendInitMsg(initInfo)
            isDisabledSend.value = false
        }
    });
    ws.addEventListener('message', (event) => {
        props.messageItemManager.waittingEnd();
        const message = event.data;
        let obj = JSON.parse(message)
        printBotMessage(obj).then(()=>{
            if(obj.key=='text'){
                inputContent.value = ''
                isDisabledSend.value = false
            }
            if(obj.key=='image'){
            }
        })
    });
    ws.addEventListener('close', () => {
        console.log('WebSocket连接已断开');
        isConnectSuccess.value = false
        isDisabledSend.value = true
    })
    ws.addEventListener('error', () => {
        console.log('WebSocket连接出错');
        isConnectSuccess.value = false
        isDisabledSend.value = true
    })
}

defineExpose({
    initConnectWS
})

// printBotMessage({key:'image'})
</script>

<style lang="scss" scoped >

$btn-box-right-width: 100px;
$btn-box-left-width: 45px;

.message-send {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 30px 10px;
  height: 100%;

  .input-content {
    margin: 0 10px;
    width: calc(100% - $btn-box-right-width - $btn-box-left-width);
  }

  .loading-box {
    height: 100%;
    margin-left: 10px;
  }

  .btn-box {
    position: relative;
    height: 31px;
    .icon-svg {
      height: 30px;
      margin: 0 8px;
      cursor: pointer
    }
  }

  .btn-box-right {
    width: $btn-box-right-width;
    text-align: right;
  }

  .btn-box-left {
    width: $btn-box-left-width;
    text-align: left;
  }

}
</style>
<template>
    <div class="container">
        <div class="content">
            <div class="ai-chat-content" :style="{'height':props.isTitle?'calc( 100vh - 270px)':'calc( 100vh - 250px)'}" ref="recordsBox">
                <div :style="{'margin-top':prefaceHeight && prefaceState?prefaceHeight+10+'px':'0',
                    'height':prefaceHeight && prefaceState?'calc( 100vh - '+(prefaceHeight+280)+'px'+' )':'',
                    'overflow-y':prefaceHeight && prefaceState?'auto':''
                }">
                    <!--用户输入-->
                    <div class="user-input" v-if="isKeyList && currentInstruct && currentInstruct.keyList && currentInstruct.keyList.length>0 
                    && currentInstruct.keyList[0].keyCode!='' && currentInstruct.keyList[0].keyName!=''">
                        <div style="display: flex;justify-content: left;margin-bottom: 20px;font-weight: 600;">用户输入</div>
                        <div v-for="(item,index) in currentInstruct.keyList" >
                            <div v-if="item.keyCode && item.keyName" class="key-input">
                                <div style="width: 25%;">{{ item.keyName }}</div>
                                <div style="width: 70%;" v-if="!item.keyType ||item.keyType=='text'">
                                    <el-input v-model="keyList[item.keyCode]" :placeholder="item.keyName" type="text"
                                    :maxlength="item.remark?JSON.parse(item.remark).max_length?JSON.parse(item.remark).max_length:50:50" />
                                </div>
                                <div style="width: 70%;" v-if="item.keyType=='paragraph'">
                                    <el-input v-model="keyList[item.keyCode]" type="textarea" show-word-limit rows="4" :placeholder="item.keyName"
                                    :maxlength="item.remark?JSON.parse(item.remark).max_length?JSON.parse(item.remark).max_length:50:50" />
                                </div>
                                <div style="width: 70%;" v-if="item.keyType=='select'">
                                    <el-select clearable v-model="keyList[item.keyCode]" :placeholder="item.keyName" style="width: 100%;">
                                        <el-option v-for="(item,index) in item.remark?JSON.parse(item.remark).options?JSON.parse(item.remark).options:[]:[]" 
                                        :key="item" :label="item" :value="item" />
                                    </el-select>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!--开场预置问题-->
                    <div class="open-question-box" v-if="isOpenQuestionShow && currentInstruct">
                        <!--开场白-->
                        <div style="display: flex;justify-content: left;" v-if="currentInstruct.prologue && !prefaceState">
                            <div class="prologue">{{ currentInstruct.prologue }}</div>
                        </div>
                        <div v-if="currentInstruct.openQuestion && JSON.parse(currentInstruct.openQuestion).length>0">
                            <div v-for="(item , index) in JSON.parse(currentInstruct.openQuestion).slice(0,3)" style="display: flex;margin-bottom: 5px;">
                                <div class="open-question"  @click="openQuestionClick(item)" v-if="item.openingQuestion">
                                    {{ item.openingQuestion }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div v-for="(item, index) in props.messageItemManager.refMessageItemList"
                :class="item.isUser?'user-record-box':'robot-record-box'" :key="index">
                    <div class="head-img" :style="{'background-color':!item.isUser?'#dfdfff':''}">
                        <img :src="currentInstruct.applicationIcon?baseUrl+currentInstruct.applicationIcon:AgentDefaultIcon" v-if="!item.isUser">
                    </div>
                    <div :class="item.isUser?userContentClassName:robotContentClassName" >
                        <div class="user-name">
                            {{ item.isUser ? '我' : currentInstruct.applicationName?currentInstruct.applicationName : '小智'}}
                        </div>
                        <!--状态信息-->
                        
                        <!--内容-->
                        <div :class="item.isUser?userBoxClassName:robotBoxClassName">
                            <div v-if="item.isUser" style="display: flex;align-items: center;">
                                <img :src="pdfRed" style="height: 25px;width: 20px;margin-right: 5px;"
                                v-if="item.content && item.content.includes('.pdf')">
                                {{ item.content }}
                            </div>
                            <template v-else>
                                <!--工具信息-->
                                <div class="tool-info-box" v-if="currentInstruct.mode=='agent' && item.chat_message.mark
                                && item.chat_message.mark.toolInfoList && item.chat_message.mark.toolInfoList.length>0">
                                    <div v-for="(tool,index) in item.chat_message.mark.toolInfoList" class="tool-info">
                                        <div class="title" @click="tool.isOpen = !tool.isOpen">
                                            <img :src="tool.state?greenYes:''" class="img" />
                                            <div style="margin-left: 5px;">{{ tool.state?'已使用':'' }}</div>
                                            <div style="margin-left: 5px;">{{ tool.toolInfo.tool_name.includes('dataset_')?'知识库':tool.toolInfo.tool_name }}</div>
                                            <el-icon style="margin-left: 5px;" v-if="!tool.isOpen"><ArrowDownBold /></el-icon>
                                            <el-icon style="margin-left: 5px;" v-if="tool.isOpen"><ArrowUpBold /></el-icon>
                                        </div>
                                        <div class="content" v-if="tool.isOpen">
                                            <div class="in-response-to">
                                                <div class="hr">
                                                    请求来自 {{ tool.toolInfo.tool_name.includes('dataset_')?'知识库':tool.toolInfo.tool_name.toUpperCase() }}
                                                </div>
                                                <div class="br">
                                                    {"{{ tool.toolInfo.tool_name }}":{{ tool.toolInfo.tool_input }}}
                                                </div>
                                            </div>
                                            <div class="in-response-to">
                                                <div class="hr">
                                                    响应来自 {{ tool.toolInfo.tool_name.includes('dataset_')?'知识库':tool.toolInfo.tool_name.toUpperCase() }}
                                                </div>
                                                <div class="br">
                                                    {"{{ tool.toolInfo.tool_name }}":{{ tool.toolInfo.observation }}}
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <message-renderer :cnt="item.content"/>
                            </template>
                            <!--操作-->
                            <div class="hidden-area" v-if="!item.isUser">
                                <div class="operation">
                                    <!--复制-->
                                    <div class="box" v-if="item.content">
                                        <el-tooltip content="复制" effect="light" placement="top" popper-class="tooltip-pop">
                                            <el-button :icon="CopyDocument" link @click="copyClick(item.content)"></el-button>
                                        </el-tooltip>
                                    </div>
                                    <!--重新生成-->
                                    <div class="box" v-if="item.isRegenerationState">
                                        <el-tooltip content="重新生成" effect="light" placement="top" popper-class="tooltip-pop">
                                            <el-button :icon="Refresh" size="small" link @click="regeneration(item)"></el-button>
                                        </el-tooltip>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!--more info-->
                        <div v-if="!item.isUser" class="more-info">
                            <div class="response-latency" v-if="item.chat_message.response_latency">
                                耗时 {{ item.chat_message.response_latency.toFixed(2) }} 秒
                            </div>
                            <div class="tokens" v-if="item.chat_message.answer_tokens">
                                花费 Token {{ item.chat_message.answer_tokens + item.chat_message.query_tokens }}
                            </div>
                        </div>
                        <!--引用出处-->
                        <div v-if="!item.isUser && item.chat_message.mark.knowledge_quote && item.chat_message.mark.knowledge_quote.length>0" class="knowledge-list">
                            <citation-list :data="item.chat_message.mark.knowledge_quote"></citation-list>
                        </div>
                        <!--选项-->
                        <!-- <div v-if="!item.isUser && item.event && item.choices.length>0 && item.choicesShow">
                            <div v-for="(choices,index) in item.choices" class="choices" @click="{item.choicesShow=false;choicesClick(choices)}" >
                                {{ choices.label }}</div>
                        </div> -->
                        <!--建议问题-->
                        <div v-if="!item.isUser && item.chat_message.mark.suggested_questions" style="margin-top: 5px;">
                            <div v-for="(autosuggestion,index) in item.chat_message.mark.suggested_questions" class="choices" @click="autosuggestionClick(autosuggestion)" >{{ autosuggestion }}</div>
                        </div>
                    </div>
                    <div class="head-img" v-if="item.isUser">
                        <!--baseUrl+-->
                        <img :src="userStore.avatar">
                    </div>
                </div>
                <div class="robot-record-box" v-if="props.messageItemManager.loading.value">
                    <div class="head-img" v-loading="!props.messageItemManager.waitting.value?false:true"
                        :style="{'background-color':props.messageItemManager.waitting.value?'#dfdfff':''}" >
                        <img :src="currentInstruct.applicationIcon?baseUrl+currentInstruct.applicationIcon:AgentDefaultIcon" 
                        v-if="!props.messageItemManager.waitting.value">
                    </div>
                    <div class="robot-content">
                        <div class="user-name">
                            {{ currentInstruct.applicationName || '小智'}}
                        </div>
                        <div class="robot-record-item">
                            <!--工具信息-->
                            <div class="tool-info-box" v-if="currentInstruct.mode=='agent' && props.messageItemManager.nowMessageToolInfo.value
                            && props.messageItemManager.nowMessageToolInfo.value.length>0">
                                <div v-for="(tool,index) in props.messageItemManager.nowMessageToolInfo.value" class="tool-info">
                                    <div class="title" @click="tool.isOpen = !tool.isOpen">
                                        <img :src="greenYes" class="img" v-if="tool.state" />
                                        <el-icon v-if="!tool.state"><Loading /></el-icon>
                                        <div style="margin-left: 5px;">{{ tool.state?'已使用':'使用中' }}</div>
                                        <div style="margin-left: 5px;" v-if="tool.state">{{ tool.toolInfo.tool_name.includes('dataset_')?'知识库':tool.toolInfo.tool_name }}</div>
                                        <el-icon style="margin-left: 5px;" v-if="!tool.isOpen && tool.state"><ArrowDownBold /></el-icon>
                                        <el-icon style="margin-left: 5px;" v-if="tool.isOpen && tool.state"><ArrowUpBold /></el-icon>
                                    </div>
                                    <div class="content" v-if="tool.isOpen">
                                        <div class="in-response-to">
                                            <div class="hr">
                                                请求来自 {{ tool.toolInfo.tool_name.includes('dataset_')?'知识库':tool.toolInfo.tool_name.toUpperCase() }}
                                            </div>
                                            <div class="br">
                                                {"{{ tool.toolInfo.tool_name }}":{{ tool.toolInfo.tool_input }}}
                                            </div>
                                        </div>
                                        <div class="in-response-to">
                                            <div class="hr">
                                                响应来自 {{ tool.toolInfo.tool_name.includes('dataset_')?'知识库':tool.toolInfo.tool_name.toUpperCase() }}
                                            </div>
                                            <div class="br">
                                                {"{{ tool.toolInfo.tool_name }}":{{ tool.toolInfo.observation }}}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div v-if="props.messageItemManager.waitting.value">
                                wait...
                            </div>
                            <template v-else>
                                <message-renderer :cnt="props.messageItemManager.nowMessageHtml.value" />
                            </template>
                        </div>
                        <!--停止响应-->
                        <div class="stop-responding" v-if="!props.messageItemManager.waitting.value" @click="stopResponding()">
                            <el-icon style="margin-right: 5px;"><VideoPause /></el-icon>
                            停止响应
                        </div>
                    </div>
                </div>
            </div>
            <div class="bottom-area">
                <div class="bar">
                    <div class="tools-list" >
                        <!-- <div class="tools-item" @click="">
                            <img class="tool-icon" :src="picture" width="20px" height="20px"/>
                        </div> -->
                        <!--<div class="tools-item" @click="">
                            <img class="tool-icon" :src="exportsvg" width="20px" height="20px"/>
                        </div> -->
                        <el-tooltip :disabled="disabled" content="新会话" placement="top" effect="light">
                            <div class="tools-item" @click="openNewSession" v-if="!prefaceState">
                                <img class="tool-icon" :src="newSession" width="20px" height="20px"/>
                            </div>
                        </el-tooltip>
                    </div>
                </div>
                <div class="submit-area">
                    <div class="content-area">
                        <div class="user-input-area">
                            <div class="top-area">
                                <div class="text-area">
                                    <textarea ref="userInputTextarea" v-model="userInputTextareaValue" :placeholder="userInputTextareaPlaceholer" autocomplete="off" 
                                        class="user-input" :class="{'show-tool-tip':userInputIsShowToolTip}" :style="{'text-indent': textAreaIndent}" 
                                        @keydown="handleKeyDown" @keydown.enter.shift.prevent="handleShiftEnter" @input="handleInput"
                                        ></textarea>
                                    <span ref="toolTip" class="tool-tip" 
                                        :style="{
                                            'display':userInputIsShowToolTip?'block':'none'
                                            ,'transform':toolTipTranslateY}"
                                    >{{ toolTipContent }}</span>
                                </div>
                            </div>
                            <div class="bottom-info-area">
                                <el-button type="primary" :icon="Promotion" size="small" round :disabled="isDisabledSend" @click="runTest"></el-button>
                                <!-- <div class="send-btn" :class="{'disabled':isDisabledSend}" @click="runTest"></div> -->
                            </div>
                        </div>
                    </div>
                </div>
                <!-- <div class="tip">内容由AI生成，无法确保真实准确。</div> -->
            </div>
        </div>
    </div>
</template>
<script setup>
    import { defineProps, nextTick,onBeforeUnmount,ref, watch } from 'vue';
    import {v4 as uuidv4} from 'uuid'
    import useUserStore from '@/store/modules/user'
    import { CircleCheck ,ArrowDownBold ,Fold ,ArrowUpBold,Promotion,Refresh,VideoPause,CopyDocument } from '@element-plus/icons-vue'
    import { getToken } from "@/utils/auth";
    import { TaskQueue } from './utils/taskQueue'
    import AgentDefaultIcon from '@/assets/icons/svg/agent-default.svg'
    import MessageRenderer from '@/components/Chat/messageRenderer.vue'
    import newSession from "@/assets/icons/svg/newSession.svg" 
    import CitationList from '@/components/Chat/citationList.vue'
    import { conversationGenerate } from "@/api/YiMaiChat/sessionRecords"
    import { ElMessage } from 'element-plus'
    import { parse } from '@vue/compiler-sfc';
    import { getDetail } from '@/api/agent/body'
    import useClipboard from 'vue-clipboard3';
    import greenYes from '@/assets/icons/svg/green-yes.svg'

    const { proxy } = getCurrentInstance();
    const baseUrl = import.meta.env.VITE_APP_BASE_API;
    const emit = defineEmits();

    const props = defineProps({
        messageItemManager:{
            type:Object,
            required: false
        },
        //服务路径
        chatUrl:{
            type:String,
            required:true
        },
        currentInstruct:{
            type:Object,
            required:true
        },
        //是否展示前言
        prefaceState:{
            type:Boolean,
            required:false,
            default:false
        },
        //前言高度
        prefaceHeight:{
            type:Number,
            required:false,
            default:0
        },
        //是否展示title
        isTitle:{
            type:Boolean,
            required:false,
            default:false
        },
        //是否展示用户输入表单
        isKeyList:{
            type:Boolean,
            required:false,
            default:false
        },
    });

    let ws = null;

    //消息列表ref
    const recordsBox = ref(null)
    const isMessageEnding = ref(false)

    const userBoxClassName = "user-record-item";
    const userContentClassName = "user-content";

    const robotBoxClassName = "robot-record-item";
    const robotContentClassName = "robot-content";
    //是否展示开场预置问题
    const isOpenQuestionShow = ref(true)

    //用户输入框的ref元素
    const userInputTextarea = ref(null)
    //用户输入框的提示
    const toolTipContent = ref('')
    //是否展示用户输入时的提示
    const userInputIsShowToolTip = ref(false)
    //用户输入框的提示的ref元素
    const toolTip = ref(null)
    //用户输入框的提示的偏移量
    const textAreaIndent = ref('0px')
    //用户输入框的placeholder
    const userInputTextareaPlaceholer=ref('选择Agent后，输入')
    //当输入框内容过多时，tip的偏移量
    const toolTipTranslateY = ref('translateY(0px)')
    //发送消息的禁用状态
    const isDisabledSend = ref(true)
    //用户输入框的值
    const userInputTextareaValue  = ref('')
    //文件
    const fileVisible = ref(false)
    const fileStorage = ref(null)
    //是否需要刷新历史记录
    const needRefreshConversation = ref(false)

    //是否第一次发送
    const firstState = ref(true)
    const chatUrlBringId=ref('')
    const userStore = useUserStore()
    const conversationId = ref(null)
    const conversationInfo = ref({})
    //是否展示用户输入列表
    const isKeyList = ref(false)
    const keyList = ref({})
    const is_done = ref(true)
    //是否是历史记录
    const isHistory = ref(false)

    //是否展示前言
    const prefaceState = ref(false)
    watch(() => props.prefaceState, val => {
        if (val) {
            prefaceState.value = true
            return true
        } else {
            prefaceState.value = false
            return false
        }
    },{ deep: true, immediate: true });

    //前言高度
    const prefaceHeight = ref(0)
    watch(() => props.prefaceHeight, val => {
        if (val) {
            prefaceHeight.value = val
            return val
        } else {
            prefaceHeight.value = 0
            return 0
        }
    },{ deep: true, immediate: true });

    //清空输入框内容
    const clearTextAreaInfo = () => {
        toolTipContent.value = ''
        userInputIsShowToolTip.value = false
        textAreaIndent.value = '0px'
        toolTipTranslateY.value = 'translateY(0px)'
    }

    //选中智能体或团队
    const clickInstructItem = () => {
        if(currentInstruct && !currentInstruct.value.interType || currentInstruct.value.interType==1){
            toolTipContent.value ='选中【'+currentInstruct.value.applicationName+'】'
            userInputIsShowToolTip.value = true
            nextTick(() => {
                textAreaIndent.value = toolTip.value.clientWidth + 5 +'px'
                userInputTextareaPlaceholer.value = ''
                userInputTextarea.value.focus()
            })
        }else{
            // 是否展示前言 （是）
            prefaceState.value = false
            emit("updatePrefaceState", prefaceState.value);
            userInputTextareaPlaceholer.value = ''
            clearTextAreaInfo()
        }
    }

    //当前点击选中的指令
    const currentInstruct = ref({})
    const reload = ref(false)

    //输入框输入时的事件
    const handleInput = () => {
        if(userInputTextareaValue.value && !props.messageItemManager.waitting.value && is_done.value){
            if(firstState.value || (!firstState.value && ws.readyState == 1)){
                isDisabledSend.value = false
            }
        }else{
            isDisabledSend.value = true
        }
    }

    //输入框滑动事件
    const handleScroll = () => {
        if (userInputTextarea.value) {
            toolTipTranslateY.value = 'translateY(-' + userInputTextarea.value.scrollTop + 'px)'
        }  
    }

    //输入框按下键时触发事件
    const handleKeyDown = (e) => {
        if (e.key === 'Enter') {  
            e.preventDefault();
            if (e.shiftKey) {
                userInputTextareaValue.value += '\n';  
            } else {
                if(!isDisabledSend.value){
                    runTest();  
                }
            }  
        }
    }

    //用户输入表单验证
    const keyListCheck = () => {
        if(currentInstruct.value.keyList && currentInstruct.value.keyList.length>0 && props.isKeyList){
            for(let i=0;i<currentInstruct.value.keyList.length;i++){
                if(!keyList.value[currentInstruct.value.keyList[i].keyCode] && currentInstruct.value.keyList[i].isRequired==1){
                    ElMessage.warning(currentInstruct.value.keyList[i].keyName+'必须填写')
                    return false
                }
                if((currentInstruct.value.keyList[i].keyType==1 || currentInstruct.value.keyList[i].keyType==2)
                && keyList.value[currentInstruct.value.keyList[i].keyCode] 
                && keyList.value[currentInstruct.value.keyList[i].keyCode].length>(currentInstruct.value.keyList[i].remark?
                JSON.parse(currentInstruct.value.keyList[i].remark).max_length?
                JSON.parse(currentInstruct.value.keyList[i].remark).max_length:50:50)){
                    ElMessage.warning(currentInstruct.value.keyList[i].keyName+'长度不能大于'+JSON.parse(currentInstruct.value.keyList[i].remark).max_length)
                    return false
                }
            }
        }
        return true
    }

    const initConnect = () => {
        return new Promise((resolve,reject) => {
            isDisabledSend.value = true;
            ws = new WebSocket(chatUrlBringId.value+'?token='+`Bearer ${getToken()}`);
            ws.addEventListener('open', () => {
                if(!isHistory.value){
                    emit('conversationRefresh')
                }
                console.log('WebSocket连接已打开');
                isDisabledSend.value = false;
                resolve();
            });
            ws.addEventListener('message', (event) => {
                const message = event.data;
                let obj = JSON.parse(message)
                if(obj.command=='history' && isHistory.value){
                    props.messageItemManager.loadHistoryMessage(obj.data.history)
                }//提示但并关闭连接 需开启新会话
                else if(obj.command=='error'){
                    proxy.$modal.msgError(obj.data.message);
                    isDisabledSend.value = true;
                    //关闭wait状态
                    props.messageItemManager.disconnected()
                    ws.close()
                }
                //提示但不关闭连接
                else if(obj.command=='alert'){
                    ElMessage.warning(obj.data.message)
                    isDisabledSend.value = true;
                    //关闭wait状态
                    props.messageItemManager.disconnected()
                }
                //内容
                else if(obj.command=='answer'){
                    props.messageItemManager.waittingEnd();
                    is_done.value = obj.data.is_done
                    if(!obj.data.is_done){
                        isDisabledSend.value = true;
                    }else{
                        isDisabledSend.value = false;
                    }
                    printBotMessage(obj,obj.data.is_done)
                }
                //Agent
                else if(obj.command == 'agent_answer'){
                    //内容
                    if(obj.data.status == 'answer'){
                        props.messageItemManager.waittingEnd();
                        is_done.value = 0
                        isDisabledSend.value = true;
                        printBotMessage(obj, is_done.value)
                    }
                    //结束
                    if(obj.data.status == 'end'){
                        is_done.value = 1
                        isDisabledSend.value = false;
                        printBotMessage(obj, is_done.value)
                    }
                    //工具调用开始
                    if(obj.data.status == 'tool'){
                        props.messageItemManager.toolHandle(obj.data.chat_message,true)
                    }
                    //工具调用完成
                    if(obj.data.status == 'observation'){
                        props.messageItemManager.toolHandle(obj.data.chat_message,false)
                    }
                }
            });
            ws.addEventListener('close', () => {
                isDisabledSend.value = true;
                //关闭wait状态
                props.messageItemManager.waittingEnd();
                console.log('WebSocket连接已断开');
            });
            ws.addEventListener('error', (error) => {
                isDisabledSend.value = true;
                console.error('发生错误：', error);
            });
        })
        
    }

    const customSend = (message,needPushUserMessage = true,command) => {
        if(command=='query'){
            props.messageItemManager.do();
            props.messageItemManager.waittingStart();
            isDisabledSend.value = true;
            if(needPushUserMessage){
                props.messageItemManager.pushUserMessage(userInputTextareaValue.value);
            }
            userInputTextareaValue.value = ''
            tempTxt.value=''
        }
        if(command=='abort'){}
        if (command == 'regenerate') {
            props.messageItemManager.do();
            props.messageItemManager.waittingStart();
            isDisabledSend.value = true;
            tempTxt.value = ''
        }
        ws.send(JSON.stringify(message))
    }

    //发送消息
    const sendMessage = () => {
        if(props.messageItemManager.loading.value){
            ElMessage.warning('正在处理中，请稍后再试')
            return;
        }
        if(userInputTextareaValue.value || isHistory.value){
            isKeyList.value=false
            firstState.value=false
            isOpenQuestionShow.value=false
            // 是否展示前言 （是）
            prefaceState.value = false
            emit("updatePrefaceState", prefaceState.value);
            if(!isHistory.value){
                clearTextAreaInfo()
            }
            props.messageItemManager.cleanHistory();
            if(userInputTextareaValue.value){
                props.messageItemManager.pushUserMessage(userInputTextareaValue.value);
            }
            initConnect().then(() => {
                needRefreshConversation.value = true
                let obj = {
                    command: 'query',
                    data: {
                        query: userInputTextareaValue.value
                    }
                }
                if(userInputTextareaValue.value){
                    customSend(obj,false,'query')
                }
            })
        }
    }

    //点击发送
    const runTest = async () =>{
        if((currentInstruct.value && userInputTextareaValue.value) && firstState.value){
            if(!isHistory.value){
                //用户表单校验
                if(!keyListCheck()){
                    return
                }
                let query={
                    name: userInputTextareaValue.value,
                    from_source: {
                        user_id: userStore.id,
                        user_name: userStore.name,
                        app_id:currentInstruct.value.applicationId,
                        app_name:currentInstruct.value.applicationName
                    },
                    mark: {
                        is_debug: false
                    },
                    inputs: {
                        keywords: Object.keys(keyList.value),
                        values: Object.values(keyList.value)
                    },
                    first_query: userInputTextareaValue.value
                }
                const response = await conversationGenerate(query)
                    conversationInfo.value=response
                    conversationId.value=response.id
                    chatUrlBringId.value=props.chatUrl+'/'+conversationId.value
            }
            sendMessage()
        }else if(userInputTextareaValue.value && !firstState.value){
            sendMsgHandle('query')
        }
    }

    //历史记录
    const historyHandle = () => {
        chatUrlBringId.value=props.chatUrl+'/'+currentInstruct.value.conversation.id
        sendMessage()
    }

    watch(() => props.currentInstruct, val => {
        if(val){
            currentInstruct.value = null
            currentInstruct.value = val
            if(currentInstruct.value.keyList && currentInstruct.value.keyList.length>0 && props.isKeyList){
                isKeyList.value = true
            }
            isHistory.value = val.isHistory?true:false
            if(!isHistory.value){
                clickInstructItem()
            }else{
                userInputTextareaPlaceholer.value = ''
                historyHandle()
            }
        }
        else{
            currentInstruct.value = {}
        }
    },{ deep: true, immediate: true });

    const tempTxt = ref('')
    const printBotMessage = (message,is_done) => {
        return new Promise((resolve) => {
            if(needRefreshConversation.value){
                // conversation.value.refresh().then(() => {
                //     conversation.value.selectFirst(false)                 
                // })
            }
            props.messageItemManager.do();
            props.messageItemManager.waittingEnd();
            let role = {
                name:message.data.chat_message.role,
                key:message.data.chat_message.id
            };
            tempTxt.value+=message.data.chat_message.content
            props.messageItemManager.appendNowMessageContent(tempTxt.value,role,message.data.chat_message,false);
            if(is_done){
                props.messageItemManager.messageReceiveDone();
            }
        })
    }

    function sendMsgHandle(command,info = {}) {
        if(ws.readyState == 1){
            if (!userInputTextareaValue.value && command=='query') {
                ElMessage.warning("你要发送什么呢？")
                return;
            }
            let obj ={}
            if(command=='query'){
                obj = {
                    command: 'query',
                    data: {
                        query: userInputTextareaValue.value
                    }
                }
                customSend(obj,true,command);
            }
            if(command=='abort'){
                obj = {
                    command: "abort",
                    data: {}
                }
                customSend(obj,false,command);
            }
            if(command == 'regenerate'){
                obj = {
                    command: "regenerate",
                    data: {
                        message_id:info.chat_message.id
                    }
                }
                customSend(obj, false, command);
            }
            needRefreshConversation.value = false
        }
    }

    //选择开场问题
    const openQuestionClick = (openQuestion) => {
        //用户表单校验
        if(!keyListCheck()){
            return
        }
        userInputTextareaValue.value = openQuestion.openingQuestion
        runTest()
    }

    //重新生成
    const regeneration = (item) => {
        props.messageItemManager.deleteLast()
        sendMsgHandle('regenerate',item)
    }

    //建议问题处理
    const autosuggestionClick = (autosuggestion) => {
        if(!props.messageItemManager.waitting.value && is_done.value)
        {
            userInputTextareaValue.value=autosuggestion
            sendMsgHandle('query')
        }
    }

    //停止响应
    const stopResponding = () =>{
        sendMsgHandle('abort')
    }

    const { toClipboard } = useClipboard();
    //复制
    const copyClick = async item => {
      try {
        await toClipboard(item);
        proxy.$modal.msgSuccess("复制成功");
      } catch (e) {
        console.error(e);
      }
    };

    //清除处理
    const SessionClear = () => {
        if(ws){
            sendMsgHandle('abort')
            ws.close()
        }
        //是否展示开场预置问题
        isOpenQuestionShow.value=true
        //是否发起新一轮会话（是）
        firstState.value = true
        //是否可以发送信息 （否）
        isDisabledSend.value = true
        //输入框内容清空
        userInputTextareaValue.value = ''
        clearTextAreaInfo()
    }

    //当前应用新会话
    const openNewSession = () => {
        SessionClear()
        Object.keys(keyList.value).forEach(element => {
            keyList.value[element]=''
        });
        //是否展示用户输入
        isKeyList.value = true
        //是否是历史会话记录
        isHistory.value = false
        //历史记录取消选中
        // emit('conversationUnselect')
        props.messageItemManager.new()
    }

    //新会话清除选中应用
    const openNewSessionClear = () => {
        SessionClear()
        keyList.value = []
        //选中的团队或智能体清空
        currentInstruct.value = null  
        // 是否展示前言 （是）
        prefaceState.value = true
        emit("updatePrefaceState", prefaceState.value);
        //是否是历史会话记录
        isHistory.value = false
        //历史记录取消选中
        emit('conversationUnselect')
        props.messageItemManager.new()
    }

    const openHistorySessionClear = () => {
        SessionClear()
        keyList.value = []
        //选中的团队或智能体清空
        currentInstruct.value = null  
        //是否是历史会话记录
        isHistory.value = true
        props.messageItemManager.new()
    }

    const closeSession = () => {
        openNewSession()
    }

    defineExpose({
        closeSession,
        openNewSessionClear,
        openHistorySessionClear
    })

    onBeforeUnmount(()=>{
        closeSession()
    })

    //定位到最新内容
    const moveScroll = () => {
        nextTick(()=> {
            recordsBox.value.scrollTop = recordsBox.value.scrollHeight;
        })
    }

    watch(props.messageItemManager.nowMessageHtml, () => {
        moveScroll();
    })

    watch(props.messageItemManager.nowMessageLine, () => {
        moveScroll();
    });

    watch(props.messageItemManager.refMessageItemList, (newValue, oldValue) => {
        moveScroll()
    });

    watch(props.messageItemManager.waitting, () => {
        moveScroll();
    });

    onMounted(() => {
        if (userInputTextarea.value) {  
            userInputTextarea.value.addEventListener('scroll', handleScroll);
        }  
    })

    onUnmounted(() => {
        if (userInputTextarea.value) {  
            userInputTextarea.value.removeEventListener('scroll', handleScroll);  
        }  
    })

</script>
<style lang="scss" scoped>
.container{
    position: relative;
    width: 100%;
    .content{
        padding: 10px 20%;
        text-align: center;
        width: 100%;
        .top{
            height: 80px;
            line-height: 80px;
            font-size: 30px;
            color: #5050E6;
            font-weight: 550;
        }
        .ai-chat-content{
            overflow-y: auto;
            overflow-x: hidden;
            .user-input{
                background-color: white;
                padding:20px 20px 1px 20px;
                border-radius: 10px;
                .key-input{
                    font-size: 15px;
                    display: flex;
                    justify-content: space-between;
                    margin-bottom: 20px;
                    // align-items: center;
                    text-align: left;
                }
            }
            .chat-message{
                display: flex;
                flex-direction: column;
                .user-chat-area{
                    align-self: flex-end;
                    display: flex;
                    flex-direction: column;
                    margin-bottom: 10px;
                    padding-left: 0;
                    .user-box{
                        display: flex;
                        flex-direction: row;
                        justify-content: end;
                        align-items: center;
                        margin-bottom: 3px;
                        .user-name{
                            line-height: 34px;
                            height: 34px;
                            color: #B2B2B2;
                            font-size: 13px;
                        }
                        .user-icon{
                            img{
                                width:30px;
                                height:30px;
                                background-color: #6977F1;
                                border-radius: 5px;
                                border: 1px solid #E6E6E6;
                            }
                            margin-left:4px;
                        }
                    }
                    .user-chat-message{
                        background-color: #5050E6;
                        border-radius: 12px;
                        border-top-right-radius: 0;
                        box-sizing: border-box;
                        color: #fff;
                        // cursor: pointer;
                        display: inline-block;
                        font-size: 14px;
                        line-height: 22px;
                        min-height: 26px;
                        outline: none;
                        padding: 9px 14px;
                        white-space: normal;
                        word-break: break-word;
                    }
                }
                .robot-chat-area{
                    align-items: flex-start;
                    display: flex;
                    flex-direction: column;
                    justify-content: flex-start;
                    margin-bottom: 10px;
                    position: relative;
                    .robot-box{
                        display: flex;
                        flex-direction: row;
                        align-items: center;
                        margin-bottom: 3px;
                        .robot-name{
                            line-height: 34px;
                            height: 34px;
                            color: #B2B2B2;
                            font-size: 13px;
                        }
                        .robot-icon{
                            img{
                                width:30px;
                                height:30px;
                                background-color: #6977F1;
                                border-radius: 5px;
                                border: 1px solid #E6E6E6;
                            }
                                margin-right:4px;
                            // background-color: #dfdfff
                        }
                    }
                    .robot-chat-message{
                        background-color: #F9FBFC;
                        border-radius: 0 12px 12px;
                        max-width: 100%;
                        padding: 12px 14px;
                        // width: 100%;
                        box-sizing: border-box;
                        overflow: hidden;
                        ::v-deep(.github-markdown-body) {
                            padding: 0;
                            font-size: 13px !important;
                            p{
                                margin-bottom: 0;
                            }
                        }
                    }
                }
            }
            .open-question-box{
                margin-top: 20px;
                display: flex;
                flex-direction: column;
                .prologue{
                    border:1px solid #E9EAEE;
                    border-radius: 10px;
                    background-color: white;
                    color: black;
                    padding: 10px 20px;
                    font-size: 15px;
                    text-align:left;
                    margin-bottom: 5px;
                }
                .open-question{
                    border:1px solid #E9EAEE;
                    border-radius: 10px;
                    background-color: white;
                    color: #7A8698;
                    padding: 10px 20px;
                    display: inline-block;
                    font-size: 15px;
                    max-width: 100%;
                    text-align: left;
                    cursor: pointer;
                }
            }
            .record-box-user {
                width: calc(100%);
                display: inline-flex;
                .head-img{
                    margin-left: 10px;
                    img{
                        height: 40px;
                        width: 40px;
                        // border: 2px solid #E6E6E6;
                        border-radius: 8px;
                        background-color: #dfdfff;
                        margin:0;
                    }
                }
            
            }
            .record-box-robot {
                width: calc(100%);
                // margin-left: 10px;
                display: inline-flex;
                .head-img{
                    height: 40px;
                    width: 44px;
                    border-radius: 8px;
                    margin-right: 10px;
                    img{
                        height: 40px;
                        width: 40px;
                        // border: 2px solid #E6E6E6;
                        border-radius: 8px;
                        background-color: #dfdfff;
                        margin:0;
                    }
                }
                ::v-deep .el-loading-mask {
                    position: absolute;
                    z-index: 2000;
                    background-color: #f5f0f08f;
                    margin: 0;
                    top: 0;
                    right: 0;
                    bottom: 0;
                    left: 0;
                    transition: opacity var(--el-transition-duration);
                    height: 40px;
                    width: 41px;
                    border-radius: 10px;
                    padding: 7px;
                }
                ::v-deep .el-loading-spinner {
                    top: 50%;
                    margin-top: calc((0px - var(--el-loading-spinner-size)) / 2);
                    width: 100%;
                    text-align: center;
                    position: absolute;
                    width: 41px;
                    height: 40px;
                    /* align-items: center; */
                    display: flex;
                    align-items: center;
                }
                ::v-deep .el-loading-spinner .circular {
                    display: inline;
                    -webkit-animation: loading-rotate 2s linear infinite;
                    animation: loading-rotate 2s linear infinite;
                    height: 23px;
                    width: 23px;
                }
            
            }
            .record-item {
                display: inline-block;
                border-radius: 10px;
                background-color: #fff;
                padding: 8px 12px;
                max-width: calc(100% - 60px);
                margin-bottom: 10px;
                font-size: 15px;
            }
            .user-record-box {
                @extend .record-box-user;
                justify-content: flex-end;
                text-align: right;
                float: right;
            }
            .robot-record-box {
                @extend .record-box-robot;
                text-align: left;
                .tool-info-box{
                    margin-bottom: 5px;
                    display: inline-block;
                    .tool-info{
                        background-color: #F2F4F7;
                        border-radius: 5px;    
                        margin: 5px 0 5px 0;
                        cursor: pointer;
                        .title{
                            color: #667085;
                            font-size: 12px;
                            display: flex;
                            align-items: center;
                            padding: 7px 10px;
                            .img{
                                width: 15px;
                                height: 15px;
                            }
                        }
                        .content{
                            border-top: 1px solid white;
                            padding: 10px;
                            font-size: 12px;
                            color: #7C8593;
                            .in-response-to{
                                border: 1px solid #E6E8EA;
                                border-radius: 5px;
                                margin-bottom: 10px;
                                .hr{
                                    background-color: white;
                                    border-top-left-radius: 5px;
                                    border-top-right-radius: 5px;
                                    border-bottom: 1px solid #E6E8EA;
                                    padding: 5px 10px;
                                    text-align: left;
                                }
                                .br{
                                    background-color: #F9FAFB;
                                    border-bottom-left-radius: 5px;
                                    border-bottom-right-radius: 5px;
                                    padding: 10px;
                                    text-align: left;
                                }
                            }
                        }
                    }
                }
            }
            .user-name{
                line-height: 20px;
                height: 20px;
                color: #B2B2B2;
                font-size: 12px;
            }
            .robot-record-item {
                @extend .record-item;
                color: #032220;
                background-color: #F9FBFC;
                font-size: 14px;
                font-weight: 500;
                position:relative;
                .hidden-area{
                    cursor:auto;
                    display:none;
                    top:-15px;
                    right:-45px;
                    position:absolute;
                    width: 100%;
                    // padding:10px 20px;
                    .operation{
                        display: flex;
                        justify-content: right;
                        gap: .25rem;
                        .box{
                            background-color: white;
                            padding: 4px 3px;
                            border-radius: 5px;
                            border: 1px solid #E9EBEF;
                        }
                    }
                }
            }
            .stop-responding{
                border: 1px solid #E9EAEE;
                border-radius: 10px;
                background-color: white;
                color: #5050E6;;
                padding: 10px 20px;
                display: flex;
                font-size: 15px;
                align-items: center;
                justify-content: center;
                cursor: pointer;
            }
            .user-record-item {
                @extend .record-item;
                color: white;
                background-color: #5050e6;
                text-align: left;
                font-size: 14px;
                font-weight: 500;
            }
            .user-content{
                display: flex;
                flex-direction: column;
                align-items: end;
                width: 100%;
            }
            .robot-content {
                display: flex;
                flex-direction: column;
                align-items: start;
                width: 100%;
                .more-info {
                    opacity: 0;
                    display: flex;
                    font-size: .75rem;
                    line-height: 1rem;
                    align-items: center;
                    height: 18px;
                    margin-left: .25rem;
                    margin-bottom: .25rem;
                    color: rgb(152, 162, 179);
                    width: 100%;

                    .response-latency {
                        margin-right: .5rem;
                        flex-shrink: 0;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        white-space: nowrap;
                        max-width: 33.33%;
                    }

                    .tokens {
                        margin-right: .5rem;
                        flex-shrink: 0;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        white-space: nowrap;
                        max-width: 33.33%;
                    }
                }

                &:hover {
                    .more-info {
                        opacity: 100;
                    }
                }

                &:hover {
                    .hidden-area {
                        display: block;
                    }
                }
            }
            .process-status{
                .status{
                    background-color: #F9FBFC;
                    padding: 10px;
                    border-radius: 10px;
                    margin-bottom: 5px;
                    color: #19CD56;
                    display: flex;
                    align-items: center;
                    cursor: pointer;
                }
                .process{
                    color: #032220;
                    background-color: #F9FBFC;
                    margin-bottom: 5px;
                    border-radius: 10px;
                    width: calc( 100% - 60px );
                    .process-top{
                        display: flex;
                        justify-content: space-between;
                        align-items:center;
                        font-size:18px;
                        padding: 8px 12px;
                        background-color: white;
                        border-top-left-radius: 10px;
                        border-top-right-radius: 10px;
                        cursor: pointer;
                        .process-left{
                            display: flex;
                            align-items: center;
                        }
                    }
                    .process-content{
                        padding: 5px;
                        .process-title{
                            display: flex;
                            align-items: center;
                            // margin-left: 5px;
                            padding: 10px;
                            background-color: white;
                            border-radius: 10px;
                            cursor: pointer;
                            img{
                                height: 20px;
                                width: 20px;
                                margin-right: 10px;
                            }
                            &:hover{
                                background-color: #F0F0F5;
                            }
                        }
                        .process-child-status{
                            background-color: #F7F7FA;
                            padding: 5px 15px;
                            font-size: 13px;
                            line-height: 20px;
                            border-bottom-right-radius: 10px;
                            border-bottom-left-radius: 10px;
                        }
                    }
                    .process-bottom{
                        background-color: #F9FBFC;
                        padding: 10px;
                        border-bottom-left-radius: 10px;
                        border-bottom-right-radius: 10px;
                        .bottom-content{
                            width: 100px;
                            padding: 5px;
                            font-size: 12px;
                            background-color: #ECF7EC;
                            color: #32A252;
                            display: flex;
                            justify-content: center;
                            border-radius: 5px;
                        }
                    }
                }
            }
            .operation{
                display: flex;
                // justify-content: end;
                width: calc( 100% - 60px );
            }
            .knowledge-list{
                color: #032220;
                background-color: #F9FBFC;
                width: calc( 100% - 60px );
                padding: 8px 12px;
                border-radius: 10px;
            }
            .choices{
                background-color: #F5F6F7;
                // min-width: 150px;
                padding: 10px;
                display: flex;
                align-items: center;
                justify-content: left;
                border-radius: 15px;
                border: 1px solid #E8E9ED;
                margin-bottom: 5px;
                color: #032220;
                font-size: 14px;
                font-weight: 500;
                cursor: pointer;

                &:hover{
                    background-color: #ececec;
                }
            }
        } 
        .bottom-area{
            background-color: transparent;
            padding-top: 10px;
            bottom: 0;
            // position: absolute;
            text-align: center;
            width: 100%;
            z-index: 11;
            .bar{
                border-top: 1px solid transparent;
                background-color: transparent;
                box-sizing: border-box;
                padding: 0 16px;
                width: 100%;
                align-items: center;
                display: flex;
                justify-content: flex-start;
                .tools-list{
                    background: transparent;
                    display: flex;
                    height: 48px;
                    padding-top: 10px;
                    .tools-item{
                        background: #fff;
                        box-shadow: 1px 1px 5px 0 #d9d9ff;
                        align-items: center;
                        border-radius: 8px;
                        cursor: pointer;
                        display: flex;
                        height: 30px;
                        margin-right: 8px;
                        outline: none;
                        padding: 6px 8px;
                        .tool-icon{
                            background-repeat: no-repeat;
                            background-size: cover;
                            display: inline-block;
                            height: 20px;
                            width: 20px;
                        }
                        .tool-name{
                            color: #1c1f1e;
                            font-family: PingFangSC-Regular;
                            font-size: 12px;
                            line-height: 12px;
                            margin-left: 6px;
                        }
                    }
                }
            }
            .submit-area{
                // padding: 0 16px 12px;
                box-sizing: border-box;
                width: 100%;
                .content-area{
                    background-image: linear-gradient(90deg, #5050E6, #b6b6ff);
                    border-radius: 12px;
                    box-shadow: 0 0 12px 0 rgba(0,155,109,.15);
                    box-sizing: border-box;
                    height: 100%;
                    padding: 2px;
                    width: 100%;
                    .user-input-area{
                        border: 0 !important;
                        border-radius: 10px;
                        padding: 8px 10px;
                        position: relative;
                        transition: opacity .5s;
                        width: 100%;
                        background-color: #fff;
                        .top-area{
                            display: flex;
                            position: relative;
                            .text-area{
                                line-height: 1.4;
                                flex: 1;
                                font-size: 14px;
                                overflow: hidden;
                                position: relative;
                                .tool-tip{
                                    align-content: center;
                                    align-items: center;
                                    background-color: #f0f2f2;
                                    border-radius: 4px;
                                    display: flex;
                                    height: 26px;
                                    line-height: 26px;
                                    padding-left: 10px;
                                    padding-right: 0;
                                    cursor: text;
                                    left: 0;
                                    position: absolute;
                                    top: 0;
                                    font-size: 14px;
                                }
                                .user-input{
                                    font-size: 14px;
                                    height: 62px;
                                    border: none;
                                    box-sizing: border-box;
                                    color: #222;
                                    line-height: 20px;
                                    outline: 0;
                                    overflow-y: auto;
                                    width: 100%;
                                }
                                .show-tool-tip{
                                    line-height: 26px;
                                }
                            }
                            textarea{
                                font: 100% arial, helvetica, clean;
                                overflow: auto;
                                vertical-align: top;
                                resize: none;
                            }
                        }
                        .bottom-info-area{
                            display: flex;
                            justify-content: flex-end;
                            margin-top: 5px;
                            .send-btn{
                                background: linear-gradient(316deg, #5050E6 16.71%, #5050E6 116.53%);
                                border-radius: 10px;
                                height: 26px;
                                position: relative;
                                width: 36px;
                                &:after{
                                    background: url(https://edu-wenku.bdimg.com/v1/pc/aigc/presentation-sample/send-1702458556573.svg) no-repeat;
                                    background-size: cover;
                                    content: "";
                                    height: 16px;
                                    position: absolute;
                                    top: 50%;
                                    transform: translate(-50%, -50%);
                                    width: 16px;
                                }
                            }
                            .disabled{
                                cursor: not-allowed;
                            }
                        }
                    }
                }
            }
            .tip{
                line-height: 25px;
                font-size: 12px;
                text-align: left;
                color: #9E9EBB;
            }
        } 
    }
    .coverage-area{
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: calc(100vh - 190px);
        background-color: #fffffff5;
        padding: 20px;
        .title{
            display: flex;
            align-items: center;
            font-size: 19px;
            font-weight: 600;
        }
        .describe{
            margin-top: 20px;
            font-size: 15px;
            font-weight: 500;
        }
        .buttons{
            margin-top: 20px;
            display: flex;
            justify-content: end;
        }
    }
}

</style>
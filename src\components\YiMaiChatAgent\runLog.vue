<template>
    <div class="run-log-content">
        <div class="log-detail">
            <div class="log-detail-area">
                <div class="log-result-info">
                    <div class="log-result-info-content">
                        <div class="log-result-info-content-item">
                            <div class="label">状态</div>
                            <div class="content">
                                <el-tag type="success">SUCCESS</el-tag>
                            </div>
                        </div>
                        <div class="log-result-info-content-item">
                            <div class="label">运行时间</div>
                            <div class="content">
                                1.400s
                            </div>
                        </div>
                        <div class="log-result-info-content-item">
                            <div class="label">总 Token 数</div>
                            <div class="content">
                                104 Tokens
                            </div>
                        </div>
                    </div>
                </div>
                <div style="padding: 0 1rem;gap: 1rem;display: flex;flex-direction: column;">
                    <div style="font-weight: bold;">Input</div>
                    <div class="input-area">
                        <custom-code-mirror 
                            :value="inputVal" 
                            :height="'200px'"/>
                    </div>
                    <div style="font-weight: bold;">Output</div>
                    <div class="output-area">
                        <custom-code-mirror 
                            :value="outputVal" 
                            :height="'200px'"/>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
<script setup>
import CustomCodeMirror from '@/components/CustomCodeMirror/index'
const LogItem = ref(null)

const inputVal = ref('')
const outputVal = ref('')

const mockInput = {
  "sys.query": "mock sys.query",
  "sys.files": [],
  "sys.conversation_id": "mock",
  "sys.user_id": "mock"
}

const mockOutput = {
  "answer": "mock answer"
}

const setLogItem = (val) => {
    LogItem.value = val
}

inputVal.value = JSON.stringify(mockInput, null, 4)
outputVal.value = JSON.stringify(mockOutput, null, 4)

defineExpose({
    setLogItem
})

</script>
<style lang="scss" scoped>
.run-log-content {
    width: 100%;
    height: 100%;
    background-color: #fff;
    display: flex;
    flex-direction: column;
    flex-grow: 1;
    .log-detail{
        height: 0;
        flex-grow: 1;
        overflow-y: auto;
        .log-detail-area{
            padding: .5rem 0;
            .log-result-info{
                padding: .5rem 1rem;
                .log-result-info-content{
                    border-radius: .5rem;
                    border-width: .5px;
                    padding: 10px .75rem;
                    // background-color: rgb(236, 253, 243);
                    border-color: rgb(234, 236, 240);
                    border-style: solid;
                    display: flex;
                    .label{
                        font-size: .75rem;
                        font-weight: 500;
                        line-height: 18px;
                        color: rgb(152, 162, 179);
                        margin-bottom: 5px;
                    }
                    .content{
                        display: flex;
                        height: 28px;
                        align-items: center;
                        gap: .25rem;
                        font-size: .75rem;
                        font-weight: 600;
                        line-height: .75rem;
                    }
                    .log-result-info-content-item{
                        max-width: 160px;
                        flex:33%;
                    }
                }
            }
        }
    }
}
</style>
<template>
    <div>
        <el-popover :visible="statePopover" placement="bottom" :width="stateColWith" :show-arrow="false" popper-class="customPopper">
            <div class="custom-cursor">
                <div v-for="(item,index) in props.tabs" key="index" :class="tabSelect==item.value?'tab_click':'tab'" @click="changeSelect(item)">
                    <el-icon :size="11" :style="{'color': tabSelect==item.value?'#5050E6':'white','margin':'0 5px'}">
                        <Check/>
                    </el-icon>
                    {{ item.label }}</div>
            </div>
            <template #reference>
                <div ref="stateRef" @click="checkState" class="custom-cursor state">
                    <span style="color:#999999;font-size:14px">{{props.name}}：</span>
                    <span style="font-size:14px">{{tabSelectLable}}</span>
                    <el-icon :size="13">
                        <CaretBottom/>
                    </el-icon>
                </div> 
            </template>
        </el-popover>
    </div>
</template>
<script setup>
import { ref, reactive, toRefs } from 'vue'
const { proxy } = getCurrentInstance();

const props = defineProps({
    tabs: {
        type: Array,
        default: () => [],
    },
    tabSelect: {
        type: String,
        required: false
    },
    tabSelectLable: {
        type: String,
        required: false
    },
    name:{
        type: String,
        required: false
    },
})

const statePopover = ref(false);
const stateColWith =ref(0);

const tabSelect = ref(props.tabSelect)
const tabSelectLable = ref(props.tabSelectLable)

//触发下拉
const checkState = () => {
    statePopover.value=!statePopover.value;
}

const emit = defineEmits(['changeSelect']);

//切换状态
const changeSelect = (states) => {
    tabSelect.value=states.value;
    tabSelectLable.value=states.label;
    emit('changeSelect',states)
    statePopover.value=!statePopover.value;
}

</script>
<style lang='scss' scoped>
.state{
    line-height: 32px;
    // width:125px;
    background-color:#E6E6E6;
    border-radius: 8px;
    padding:0 5px
}
.tab{
    line-height:20px;
    font-size:12px;
    color: #032220;
    padding: 5px;
    margin: 2px;
    border-radius: var(--radius-sm, 4px);
}
.tab_click{
    line-height:20px;
    font-size:12px;
    color: #032220;
    padding: 5px;
    margin: 2px;
    border-radius: var(--radius-sm, 4px);
    background: #F4F4F6; 
}
.custom-cursor { cursor: pointer; }
::v-deep .pagination-container{
    background:#F7F7FA
}
</style>
<style >
    .customPopper {
        min-width: 120px!important;
    }
    .el-popover {
        --el-popover-padding:0!important; 
    }
</style>
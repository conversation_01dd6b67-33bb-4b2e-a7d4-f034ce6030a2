import request from '@/utils/request'

// 工具列表
export function getToolList(toolType, shopId) {
    return request({
        url: '/ecommerce/tool/list?toolType=' + toolType + '&shopId=' + shopId,
        method: 'get'
    })
}

//工具设计
export function toolEdit(query) {
    return request({
        url: '/ecommerce/tool',
        method: 'put',
        data: query
    })
}

// 店铺设计信息
export function getToolInfo(id) {
    return request({
        url: '/ecommerce/tool/' + id,
        method: 'get'
    })
}
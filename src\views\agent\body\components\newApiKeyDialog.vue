<template>
  <el-dialog
    v-model="isVisible"
    width="550px"
  >
    <template #header>
        <div class="title-wrapper">
            <key-svg class="icon-key" />
            新的密钥
        </div>
    </template>
    请保管好您的密钥
    <div class="key-wrapper">
        <div class="key">{{ key }}</div>
        <el-button :icon="CopyDocument" link circle @click="handleCopyBtnClick"></el-button>
    </div>
    <template #footer>
        <el-button @click="isVisible = false">好的</el-button>
    </template>
  </el-dialog>
</template>

<script setup>
import { Key as KeySvg, CopyDocument } from '@element-plus/icons-vue';
import copy from 'copy-to-clipboard';
import { ElMessage } from 'element-plus';

// 是否打开弹窗
const isVisible = ref(false);

// 密钥
const key = ref('');

/**
 * 打开弹窗，并展示密钥 
 * @param {*} theKey 
 */
function open(theKey) {
    key.value = theKey;
    isVisible.value = true;
}

function handleCopyBtnClick() {
    copy(key.value);
    ElMessage({
        type: 'success',
        message: '已复制到剪贴板'
    })
}

defineExpose({
    open
})
</script>

<style scoped>
.icon-key {
    width: 1.5em;
    height: 1.5em;
    color: cadetblue;
}
.title-wrapper {
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
}
.key-wrapper {
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
    width: 100%;
    height: 80px;
    border: 0;
    background-color: #eeeeee;
    border-radius: 5px;
    overflow: hidden;
    margin-top: 10px;
}
.key-wrapper .key {
    text-wrap: wrap;
    word-break: break-all;
    line-height: 1.5;
    padding: 0 5px 0 15px;
}
</style>

import request from '@/utils/difyRequest'

//知识库列表
export function getKnowledgeBaseList(params) {
    return request({
        url: '/console/api/datasets',
        method: 'get',
        params
    })
}

//新增知识库
export function addKnowledgeBase(data) {
    return request({
        url: '/console/api/datasets',
        method: 'post',
        data
    })
}

//修改知识库
export function editKnowledgeBase(data) {
    return request({
        url: '/console/api/datasets/'+data.id,
        method: 'patch',
        data
    })
}

//删除知识库
export function delKnowledgeBase(id) {
    return request({
        url: '/console/api/datasets/'+id,
        method: 'delete',
    })
}

//知识库详情
export function getKnowledgeBaseInfo(id) {
    return request({
        url: '/console/api/datasets/'+id,
        method: 'get'
    })
}

//知识库文档列表
export function getDocumentList(params,id) {
    return request({
        url: '/console/api/datasets/'+id+'/documents',
        method: 'get',
        params
    })
}

//知识库文档禁用
export function disableDocumentd(data) {
    return request({
        url: '/console/api/datasets/'+data.knowledgeId+'/documents/'+data.id+'/status/disable',
        method: 'patch'
    })
}

//知识库文档启用
export function availableDocumentd(data) {
    return request({
        url: '/console/api/datasets/'+data.knowledgeId+'/documents/'+data.id+'/status/enable',
        method: 'patch'
    })
}

//删除文档
export function delDocument(data) {
    return request({
        url: '/console/api/datasets/'+data.knowledgeId+'/documents/'+data.id,
        method: 'delete',
    })
}

//文档详情
export function getDocumentdInfo(knowledgeId,id) {
    return request({
        url: '/console/api/datasets/'+knowledgeId+'/documents/'+id+'?metadata=without',
        method: 'get'
    })
}

//知识库文件分段列表
export function getDocumentSegmentsList(params,knowledgeId,id) {
    return request({
        url: '/console/api/datasets/'+knowledgeId+'/documents/'+id+'/segments',
        method: 'get',
        params
    })
}

//修改分段
export function editDocumentSegmentationt(data) {
    return request({
        url: '/console/api/datasets/'+data.knowledgeId+'/documents/'+data.documentsId+'/segments/'+data.id,
        method: 'patch',
        data
    })
}

//新增分段
export function addDocumentSegmentation(data,knowledgeId,documentsId) {
    return request({
        url: '/console/api/datasets/'+knowledgeId+'/documents/'+documentsId+'/segment',
        method: 'post',
        data
    })
}

//分段开启
export function enableDocumentSegmentation(data) {
    return request({
        url: '/console/api/datasets/'+data.knowledgeId+'/segments/'+data.SegmentationId+'/enable',
        method: 'patch'
    })
}

//分段关闭
export function disableDocumentSegmentation(data) {
    return request({
        url: '/console/api/datasets/'+data.knowledgeId+'/segments/'+data.SegmentationId+'/disable',
        method: 'patch'
    })
}

//删除段落 /console/api/datasets/6d1505c3-25d0-4ec2-bc03-fb9ff9909221/documents/f57cd8e4-9694-452e-9c89-06c224208441/segments/5a40d41d-0426-494d-b902-ada799adc520
export function delDocumentSegmentation(knowledgeId,documentsId,id) {
    return request({
        url: '/console/api/datasets/'+knowledgeId+'/documents/'+documentsId+'/segments/'+id,
        method: 'delete',
    })
}

//召回测试-历史查询
export function historicalQuery(params,knowledgeId) {
    return request({
        url: '/console/api/datasets/'+knowledgeId+'/queries',
        method: 'get',
        params
    })
}

//测试
export function testing(data,knowledgeId) {
    return request({
        url: '/console/api/datasets/'+knowledgeId+'/hit-testing',
        method: 'post',
        data
    })
}
<template>
    <div class="message-send" v-show="chatUrl != ''">
        <!-- <el-button :icon="Clock" text></el-button> -->
        <div class="input-content">
            <el-input maxlength="1000" 
                :disabled="isDisabledSend"
                :placeholder="'您可以向我提任何问题'"
                @keyup.enter="sendMsgHandle" 
                resize="none" 
                :autosize="{ minRows: 1, maxRows: 5 }"
                v-model="inputContent">
                <template #suffix>
                    <el-button link :disabled="isDisabledSend" :icon="Promotion" @click="sendMsgHandle" />
                </template>
            </el-input>
        </div>
        <!-- <el-button :icon="CirclePlus" text></el-button> -->
    </div>
</template>
<script setup>
// import { useRoute } from 'vue-router';
import "highlight.js/styles/atom-one-light.css";
import 'element-plus/es/components/message/style/css';
import { ElMessage } from 'element-plus';
import { ref, defineProps } from 'vue';
import { Clock,Promotion,CirclePlus} from '@element-plus/icons-vue';
import {TaskQueue} from '@/types/taskQueue'
// import { v4 as uuidv4 } from 'uuid';

let ws = null;

const props = defineProps({
    messageItemManager:{
        type:Object,
        required: false
    },
    chatUrl:{
        type:String,
        required: false,
        default:""
    }
});

const inputContent = ref('');
const isDisabledSend = ref(false)
const taskQueue = new TaskQueue()

function sendMsgHandle() {
    if (inputContent.value.trim() === '') {
        ElMessage.warning("你要发送什么呢？")
        return;
    }
    props.messageItemManager.pushUserMesage(inputContent.value);
    // if(sessionStorage.getItem('sessionId')==null){
    //     sessionStorage.setItem('sessionId',uuidv4())
    // }
    wsSend();
}

const wsSend = () => {
    ws = new WebSocket(props.chatUrl);
    isDisabledSend.value = true
    props.messageItemManager.waittingStart();
    // ws = new WebSocket("ws://172.16.1.19:29999");
    ws.addEventListener('open', () => {
        ws.send('{'+inputContent.value+'}')
        console.log('WebSocket连接已建立');
    });
    ws.addEventListener('message', (event) => {
        const message = event.data;
        let obj = JSON.parse(message)
        if(obj.key != 'user_proxy'){
            console.log(obj)
            taskQueue.addTask(printBotMessage,obj)
        }
    });
    ws.addEventListener('close', () => {
        props.messageItemManager.waittingEnd();
        if(taskQueue.tasks.length == 0){
            printBotMessage({
                key:'system',
                name:'系统消息',
                content:'抱歉，我无法给出回答'
            })
            inputContent.value = ''
            isDisabledSend.value = false
        }else{
            taskQueue.setOnComplete(() => {
                inputContent.value = ''
                isDisabledSend.value = false
                taskQueue.clearTasks()
            })
        }
        console.log('WebSocket连接已断开');
    });
    ws.addEventListener('error', (error) => {
        isDisabledSend.value = false
        console.error('发生错误：', error);
    });
}

const printBotMessage = (message) => {
    return new Promise((resolve) => {
        props.messageItemManager.do();
        let txt = message.content.split('')
        let tempTxt = ''
        let role = {
            name:message.name,
            key:message.key
        };
        txt.forEach((item,index) => {
            setTimeout(() => {
                props.messageItemManager.appendNowMessageContent(tempTxt += item,role,false);
                if(index == txt.length-1){
                    props.messageItemManager.deleteLastMsgRecord();
                    props.messageItemManager.messageReceiveDone();
                    resolve(message)
                }
            }, 30 * index );
        })
    })
}

</script>

<style lang="scss" scoped >

$btn-box-right-width: 100px;
$btn-box-left-width: 45px;

.message-send {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10px;
  height: 100%;

  .input-content {
    margin: 0 10px;
    width: calc(100% - $btn-box-right-width - $btn-box-left-width);
  }

  .loading-box {
    height: 100%;
    margin-left: 10px;
  }

  .btn-box {
    position: relative;
    height: 31px;
    .icon-svg {
      height: 30px;
      margin: 0 8px;
      cursor: pointer
    }
  }

  .btn-box-right {
    width: $btn-box-right-width;
    text-align: right;
  }

  .btn-box-left {
    width: $btn-box-left-width;
    text-align: left;
  }

}
</style>
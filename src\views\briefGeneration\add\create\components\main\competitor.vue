<template>
  <div v-if="ishumanInput" class="container">
    <div class="formBox">
      <el-form ref="formRef" style="max-width: 600px" :model="dynamicValidateForm" label-width="auto"
        class="demo-dynamic">
        <div class="titlebk">
          <div class="titletxt">确认竞争品牌
          </div>
          <div class="handleIcon" @click.prevent="addCompetitive">
            <i class="ri-add-line"></i>
          </div>
        </div>
        <div class="customBox">
          <el-form-item v-for="(domain, index) in dynamicValidateForm.competitive" :key="domain.key"
            :prop="'competitive.' + index + '.value'">
            <el-input v-model="domain.value" placeholder="请输入竞争品牌" style="width: 65%" />
            <div class="handleIcon" @click.prevent="removeCompetitive(domain)" v-show="index > 0"
              style="color: #FF9100;">
              <i class="ri-delete-bin-5-fill"></i>
            </div>
          </el-form-item>
        </div>
        <div class="titlebk">
          <div class="titletxt">确认标杆品牌
          </div>
          <div class="handleIcon" @click.prevent="addBenchmark">
            <i class="ri-add-line"></i>
          </div>
        </div>
        <div class="customBox">
          <el-form-item v-for="(domain, index) in dynamicValidateForm.benchmark" :key="domain.key"
            :prop="'benchmark.' + index + '.value'">
            <el-input v-model="domain.value" placeholder="请输入标杆品牌" style="width: 65%" />
            <div class="handleIcon" @click.prevent="removeBenchmark(domain)" v-show="index > 0" style="color: #FF9100;">
              <i class="ri-delete-bin-5-fill"></i>
            </div>
          </el-form-item>
        </div>
      </el-form>
    </div>
    <div class="footer">
      <div class="btn" @click="handleConfirm">确定</div>
    </div>
  </div>
</template>

<script setup>
import { ref, watch, onMounted, defineEmits } from "vue";
import { useRouter } from "vue-router";
import { ElMessage } from "element-plus";
import {
  ishumanInput,
  workflowtaskId,
  workflowtaskChildId,
  startGenerating,
  competitorStr,
  configData
} from "@/views/briefGeneration/add/create/js/sharedState.js";
import { workflowtaskStart } from "@/api/briefGeneration/index.js";
const router = useRouter();
const emit = defineEmits([
  "scroll-Bottom",
]);
const dynamicValidateForm = reactive({
  // 竞争品牌
  competitive: [
    { key: 1, value: "" },
    // { key: 2, value: "" },
    // { key: 3, value: "" },
    // { key: 4, value: "" }
  ],
  // 标杆品牌
  benchmark: [
    { key: 1, value: "" }
  ],
});
// 监听 visible 的变化
watch(
  () => ishumanInput.value,
  (newVisible) => {
    if (!newVisible) {
      dynamicValidateForm.competitive = [
        { key: 1, value: "" },
        // { key: 2, value: "" },
        // { key: 3, value: "" },
        // { key: 4, value: "" }
      ];
      dynamicValidateForm.benchmark = [{ key: 1, value: "" }];
    } else {
      setTimeout(() => {
        emit("scroll-Bottom");
      }, 200);
    }
  },
  { immediate: true }
);
// watch(
//   () => top5Options.value,
//   (newValue) => {
//     dynamicValidateForm.competitive = newValue;
//   },
//   { immediate: true }
// );


const removeBenchmark = (item) => {
  const index = dynamicValidateForm.benchmark.indexOf(item);
  if (index !== -1) {
    dynamicValidateForm.benchmark.splice(index, 1);
  }
};
const addBenchmark = () => {
  if (dynamicValidateForm.benchmark.length >= 5) {
    ElMessage.warning("最多只能有5个标杆品牌！");
    return;
  }
  dynamicValidateForm.benchmark.push({
    key: Date.now(),
    value: "",
  });
};
const removeCompetitive = (item) => {
  const index = dynamicValidateForm.competitive.indexOf(item);
  if (index !== -1) {
    dynamicValidateForm.competitive.splice(index, 1);
  }
};
const addCompetitive = () => {
  if (dynamicValidateForm.competitive.length >= 5) {
    ElMessage.warning("最多只能有5个竞争品牌！");
    return;
  }
  dynamicValidateForm.competitive.push({
    key: Date.now(),
    value: "",
  });
};

const handleConfirm = async () => {
  try {
    // 校验是否有竞争品牌和标杆品牌
    let { competitive, benchmark } = dynamicValidateForm;
    const hasCompetitive2 = competitive && competitive.some((d) => d.value.trim() === "");
    const hasBenchmark2 = benchmark && benchmark.some((d) => d.value.trim() === "");
    if (competitive && competitive.length == 0) {
      ElMessage.warning("请至少填写一个竞争品牌！");
      return;
    }
    if (benchmark && benchmark.length == 0) {
      ElMessage.warning("请至少填写一个标杆品牌！");
      return;
    }
    if (hasCompetitive2) {
      ElMessage.warning("有未填写的竞争品牌！");
      return;
    }
    if (hasBenchmark2) {
      ElMessage.warning("有未填写的标杆品牌！");
      return;
    }
    try {
      let res = await workflowtaskStart({
        query: configData.value.brandName,
        jingping: dynamicValidateForm.competitive.map(v => v.value),
        biaogan: dynamicValidateForm.benchmark.map(v => v.value),
      });
      if (res.code == 200) {
        workflowtaskId.value = res.data.id;
        workflowtaskChildId.value=res.data.children||[]
        ishumanInput.value = false;
        startGenerating.value = true;
        competitorStr.value = ``; //输入完毕后隐藏
        console.log('后台工作流通知成功');
      }
    } catch (error) {
      console.error('后台工作流通知失败:', error);
    }
  } catch (e) {
    console.error(e);
  }
};
onMounted(() => { });
</script>

<style lang="scss" scoped>
.container {
  .titlebk {
    width: 100%;
    height: 24px;
    margin-top: 8px;
    display: flex;
    align-items: center;

    .titletxt {
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 16px;
      color: #fff;
      line-height: 16px;
    }

    .handleIcon {
      font-size: 24px;
      color: #00E5FF;
      line-height: 24px;
    }
  }

  .footer {
    text-align: center;

    .btn {
      width: 80px;
      height: 34px;
      color: #fff;
      display: inline-block;
      margin-top: 24px;
      cursor: pointer;
      line-height: 34px;
      border-radius: 20px;
      background-color: rgba(0, 229, 255, 1);
      font-size: 14px;
      text-align: center;
      font-family: SourceHanSansSC-regular;
    }
  }

  .formBox {
    padding: 0 36px;

    .el-checkbox-group {
      display: flex;
      flex-direction: column;
    }

    .el-form-item {
      margin-bottom: 15px;
    }
  }

  .el-form-item__content {
    display: flex;
    align-items: center;
  }

  .el-form-item--default {
    margin-bottom: 0;
  }

  .handleIcon {
    margin-left: 10px;
    cursor: pointer;
    font-size: 16px;
  }

  .customBox {
    padding: 20px 0 0 17px;
  }
}
</style>
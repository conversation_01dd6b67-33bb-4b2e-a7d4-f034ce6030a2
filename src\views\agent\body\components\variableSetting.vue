<template>
  <div>
    <el-table v-if="fields.length > 0" :data="fields" size="small">
      <el-table-column prop="label" label="变量 KEY">
        <template #default="scope">
          <el-input
            v-model="scope.row.keyCode"
            :maxlength="20"
            size="default"
            placeholder="key"
            @blur="setLabel(scope.row)"
            @input="validateVariable(scope.row)"
          />
        </template>
      </el-table-column>
      <el-table-column prop="variable" label="字段名称">
        <template #default="scope">
          <el-input v-model="scope.row.keyName" :maxlength="50" :placeholder="scope.row.keyName || scope.row.keyCode" size="default" />
        </template>
      </el-table-column>
      <el-table-column prop="required" label="必填" width="80">
        <template #default="scope">
          <el-switch
            v-model="scope.row.isRequired"
            :active-value="1"
            :inactive-value="0"
            size="default"
          />
        </template>
      </el-table-column>
      <el-table-column label="操作" width="90">
        <template #default="scope">
          <el-button
            :icon="Setting"
            circle
            link
            size="default"
            class="btn-configure"
            @click="configureField(scope.$index)"
          />
          <el-button
            :icon="Delete"
            circle
            link
            size="default"
            class="btn-remove"
            @click="removeField(scope.$index)"
          />
        </template>
      </el-table-column>
    </el-table>
    <variable-dialog ref="variableDialog" @update="updateField" />
  </div>
</template>

<script setup>
import VariableDialog from './variableDialog.vue';
import { ElMessage } from 'element-plus';
import { KEY_TYPE } from '@/config/agent.js';
import { Delete, Setting } from '@element-plus/icons-vue';
import { watch } from 'vue';

const props = defineProps({
  /**
   * 
   * 字段， 其值格式如下:
   *    [{
   *         "keyType": 1,
   *         "keyName": "产品名称",
   *         "keyCode": "name",
   *         "isRequired": 0,
   *         "remark": "{\"max_length\":48}"
   *     },
   *     {
   *         "keyType": 2,
   *         "keyName": "产品描述",
   *         "keyCode": "desc",
   *         "isRequired": 0,
   *         "remark": "{\"max_length\":48}"
   *     },
   *     {
   *         "keyType": 3,
   *         "keyName": "产品分类",
   *         "keyCode": "category",
   *         "isRequired": 0,
   *         "remark": "{\"options\": [\"硬件\", \"软件\"]}"
   *     }]
   * keyType: 1.文本 2.段落 3.下拉选项
  */
  modelValue: {
    type: Array,
    required: true
  }
});

// 字段
const fields= ref([]);

// 设置字段的弹窗
const variableDialog = ref(null);

// 事件定义
const emit = defineEmits(['update:modelValue']);

/**
 * 添加字段
 */
function appendField() {
  fields.value.push({
    keyType: KEY_TYPE.TEXT,
    keyCode: '',
    keyName: '',
    isRequired: 0,
    remark: '{"max_length": 50}'
  });
}

/**
 * 删除字段
 */
function removeField(idx) {
  fields.value.splice(idx, 1);
}

/**
 * 设置字段
 */
function configureField(idx) {
  variableDialog.value.open(idx, fields.value[idx]);
}

/**
 * 更新字段
 */
function updateField(idx, field) {
  const newFields = [...fields.value];
  newFields[idx] = field;
  emit('update:modelValue', newFields);
}

/**
 * KEY输入框失焦时，设置字段名称
 * 如果字段名称为空，则使用KEY作为字段名称
 */
function setLabel(item) {
  if (!item.keyName && item.keyCode) {
    item.keyName = item.keyCode;
  }
}

/**
 * 校验"变量 KEY"字段, 并移除输入的非法字符
 */
function validateVariable(item) {
  const pattern = /^[a-zA-Z_][a-zA-Z0-9_]*$/;
  if (item.keyCode && !item.keyCode.match(pattern)) {
    ElMessage({
      type: 'error',
      message: '必须是字母，数字或下划线，且不能以数字开头!'
    });
  }

  item.keyCode = item.keyCode.replace(/^[0-9]+/, '');
  item.keyCode = item.keyCode.replace(/[^a-zA-Z0-9_]/g, '');
}

watch(fields, () => {
  const jsonFields = JSON.stringify(fields.value);
  const jsonModelValue = JSON.stringify(props.modelValue);

  if (jsonFields !== jsonModelValue) {
    const nfields = JSON.parse(JSON.stringify(fields.value));
    emit('update:modelValue', nfields);
  }
}, {deep: true});

watch(props, () => {
  const jsonFields = JSON.stringify(fields.value);
  const jsonModelValue = JSON.stringify(props.modelValue);

  if (jsonFields !== jsonModelValue) {
    fields.value = JSON.parse(JSON.stringify(props.modelValue));
  }
}, {immediate: true});

defineExpose({
  appendField
})
</script>

<style scoped>
.btn-remove {
  margin-left: 0;
}
</style>
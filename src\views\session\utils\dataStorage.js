export class dataStorage {

    getInitMessage4(message){
        let initMessage4 = {"id":"4","user_message":{
            "teamId": 115,
            "keywords": {
                "userQuestion": "最近的履约日期是什么时间"
            },
            "arrangeJson": {
            "nodes": [
                {
                "id": "start",
                "type": "custom",
                "position": {
                    "x": 90.93689939053979,
                    "y": 298.851301645003
                },
                "data": {
                    "type": "start",
                    "data": {
                    "label": "开始",
                    "desc": "工作流的起始节点，用于设置启动工作流所需的信息。"
                    }
                }
                },
                {
                "id": "end",
                "type": "custom",
                "position": {
                    "x": 1633.356819049674,
                    "y": 247.26768554015416
                },
                "data": {
                    "type": "end",
                    "data": {
                    "label": "结束",
                    "desc": "工作流的最后一个节点，用于在工作流运行后返回结果信息。"
                    }
                }
                },
                {
                "id": "985aef8b-b533-4f6b-ad18-18cc1c502635",
                "type": "custom",
                "position": {
                    "x": 398.3930340137854,
                    "y": 158.02420134772512
                },
                "data": {
                    "type": "agent",
                    "data": {
                    "agentId": 98,
                    "agentName": "协议数据分析师",
                    "agentCode": "contract_data_analyst",
                    "agentDescribe": "可分析协议中的数据，按照要求回答用户问题",
                    "goal": null,
                    "agentIconUrl": "/profile/upload/2024/05/10/协议数据分析师_20240510015305A013.png",
                    "agentType": 1,
                    "modelId": 16,
                    "temperature": 0.2,
                    "input": [],
                    "prompt": "# 角色\n你是一个基于提供的背景知识，针对用户问题{userQuestion}进行信息检索并严格遵守规则回答的智能助手。\n\n## 技能\n### 技能 1: 提供哪些协议\n- 如果用户询问哪些协议，从背景知识中找出协议合同的名称，按示例格式输出。示例“xx公司与许可方签订了一份名为《xx》的协议”\n\n### 技能 2: 回答关于签订的协议许可费条款的问题\n- 如果用户的问题涉及“协议许可费条款”，则按照“第五条”段落下所有内容进行完整详细的回答，分点回答，输出内容分8个点。\n\n### 技能 3: 提供履约日期\n- 如果用户询问履约日期，从背景知识中找出离当前（2024-05-15）最近的一个日期且大于当前日期，直接提供履约日期即可。\n\n## 限制\n- 必须使用中文回答。\n- 引用背景知识中的原始内容，确保信息的完整性和准确性。\n- 处理数字、日期时要精确无误。\n- 如果背景知识中找不到与问题直接相关的信息，需声明“找不到相关信息”。\n- 如果一次多个问题，回复时逐一解答用户的问题，并分段显示。\n- 若输出的回答内容“技能x:”改为“问题x:\"，不要显示当前时间。",
                    "output": [
                        {
                        "name": "agreementData",
                        "type": "String"
                        }
                    ]
                    }
                },
                "label": "985aef8b-b533-4f6b-ad18-18cc1c502635 node"
                },
                {
                "id": "616d360c-c735-4a6c-afad-e614ea2cff41",
                "type": "custom",
                "position": {
                    "x": 798.1381058588623,
                    "y": 37.97705164819402
                },
                "data": {
                    "type": "agent",
                    "data": {
                    "agentId": 99,
                    "agentName": "项目管理专员",
                    "agentCode": "project_administrator",
                    "agentDescribe": "读取解析后的协议信息,识别履约日期,设置提醒",
                    "goal": null,
                    "agentIconUrl": "/profile/upload/2024/05/10/项目管理专员_20240510015637A014.png",
                    "agentType": 1,
                    "modelId": 16,
                    "temperature": 0.2,
                    "input": [
                        {
                        "key": "agreementData",
                        "name": "协议数据"
                        }
                    ],
                    "prompt": "作为项目管理专员，您需要根据{agreementData}中的信息找到履约日期（付款日期）是什么时候，按照固定的回答模板去回答问题：\n<已成功为您设置与A公司合同中约定的XXXX年XX月XX日付款提醒。请您密切关注该日期，确保按时支付款项，避免任何不必要的延误。>。注意不要输入分析过程，直接输出结果即可",
                    "output": [
                        {
                        "name": "answerTemplate",
                        "type": "String"
                        }
                    ]
                    }
                },
                "label": "616d360c-c735-4a6c-afad-e614ea2cff41 node"
                },
                {
                "id": "f9f384c2-6802-47fe-8091-2c72dd17e9d1",
                "type": "custom",
                "position": {
                    "x": 1200.138105858862,
                    "y": -81.02294835180592
                },
                "data": {
                    "type": "tool_api",
                    "data": {
                    "toolId": 39,
                    "toolName": "意向逻辑判断工具",
                    "toolCode": "logical_judgment",
                    "toolDesc": "去判断用户的是否意向",
                    "toolType": 2,
                    "toolImageUrl": "/profile/upload/2024/05/13/2669099_20240513081257A015.png",
                    "toolUrl": "http://***********/ai-agent/system/tool/get_reminder?",
                    "requestMethod": "GET",
                    "headerList": [
                        {
                        "paramName": "Accept",
                        "paramValues": "*/*"
                        },
                        {
                        "paramName": "Accept-Encoding",
                        "paramValues": "gzip, deflate, br"
                        },
                        {
                        "paramName": "User-Agent",
                        "paramValues": "PostmanRuntime-ApipostRuntime/1.1.0"
                        },
                        {
                        "paramName": "Connection",
                        "paramValues": "keep-alive"
                        },
                        {
                        "paramName": "Cache-Control",
                        "paramValues": "no-cache"
                        }
                    ],
                    "inKey": [
                        {
                        "name": "intention",
                        "desc": "意向",
                        "type": "String",
                        "ifNece": false
                        },
                        {
                        "name": "answerTemplate",
                        "desc": "回答模板",
                        "type": "String",
                        "ifNece": false
                        }
                    ],
                    "outKey": [
                        {
                        "name": "reminder",
                        "desc": "提醒语",
                        "type": "String",
                        "ifNece": false
                        }
                    ]
                    }
                },
                "label": "f9f384c2-6802-47fe-8091-2c72dd17e9d1 node"
                }
            ],
            "edges": [
                {
                "id": "vueflow__edge-startstart__handle-right-985aef8b-b533-4f6b-ad18-18cc1c502635985aef8b-b533-4f6b-ad18-18cc1c502635__handle-left",
                "type": "close",
                "source": "start",
                "target": "985aef8b-b533-4f6b-ad18-18cc1c502635",
                "sourceHandle": "start__handle-right",
                "targetHandle": "985aef8b-b533-4f6b-ad18-18cc1c502635__handle-left",
                "data": {},
                "label": "",
                "markerEnd": "arrowclosed",
                "style": {
                    "stroke": "#5050E6"
                },
                "labelBgStyle": {
                    "fill": "#5050E6"
                },
                "sourceX": 394.93711006015957,
                "sourceY": 329.8513042811777,
                "targetX": 394.39302453477427,
                "targetY": 327.0243031489391
                },
                {
                "id": "vueflow__edge-985aef8b-b533-4f6b-ad18-18cc1c502635985aef8b-b533-4f6b-ad18-18cc1c502635__handle-right-616d360c-c735-4a6c-afad-e614ea2cff41616d360c-c735-4a6c-afad-e614ea2cff41__handle-left",
                "type": "close",
                "source": "985aef8b-b533-4f6b-ad18-18cc1c502635",
                "target": "616d360c-c735-4a6c-afad-e614ea2cff41",
                "sourceHandle": "985aef8b-b533-4f6b-ad18-18cc1c502635__handle-right",
                "targetHandle": "616d360c-c735-4a6c-afad-e614ea2cff41__handle-left",
                "data": {},
                "label": "",
                "markerEnd": "arrowclosed",
                "style": {
                    "stroke": "#5050E6"
                },
                "labelBgStyle": {
                    "fill": "#5050E6"
                },
                "sourceX": 702.3932446834051,
                "sourceY": 327.0243031489391,
                "targetX": 794.1381314353657,
                "targetY": 215.97717477718305
                },
                {
                "id": "vueflow__edge-616d360c-c735-4a6c-afad-e614ea2cff41616d360c-c735-4a6c-afad-e614ea2cff41__handle-right-f9f384c2-6802-47fe-8091-2c72dd17e9d1f9f384c2-6802-47fe-8091-2c72dd17e9d1__handle-left",
                "type": "close",
                "source": "616d360c-c735-4a6c-afad-e614ea2cff41",
                "target": "f9f384c2-6802-47fe-8091-2c72dd17e9d1",
                "sourceHandle": "616d360c-c735-4a6c-afad-e614ea2cff41__handle-right",
                "targetHandle": "f9f384c2-6802-47fe-8091-2c72dd17e9d1__handle-left",
                "data": {},
                "label": "",
                "markerEnd": "arrowclosed",
                "style": {
                    "stroke": "#5050E6"
                },
                "labelBgStyle": {
                    "fill": "#5050E6"
                },
                "sourceX": 1102.138386639511,
                "sourceY": 215.97717477718305,
                "targetX": 1196.1382015463944,
                "targetY": 160.4772069441232
                },
                {
                "id": "vueflow__edge-f9f384c2-6802-47fe-8091-2c72dd17e9d1f9f384c2-6802-47fe-8091-2c72dd17e9d1__handle-right-endend__handle-left",
                "type": "close",
                "source": "f9f384c2-6802-47fe-8091-2c72dd17e9d1",
                "target": "end",
                "sourceHandle": "f9f384c2-6802-47fe-8091-2c72dd17e9d1__handle-right",
                "targetHandle": "end__handle-left",
                "data": {},
                "label": "",
                "markerEnd": "arrowclosed",
                "style": {
                    "stroke": "#5050E6"
                },
                "labelBgStyle": {
                    "fill": "#5050E6"
                },
                "sourceX": 1504.1384567505397,
                "sourceY": 160.4772069441232,
                "targetX": 1629.3568446261772,
                "targetY": 278.2677232318433
                }
            ],
            "position": [
                -596.7461855390052,
                -147.95545245242056
            ],
            "zoom": 1.7411011265922485,
            "viewport": {
                "x": -596.7461855390052,
                "y": -147.95545245242056,
                "zoom": 1.7411011265922485
            }
            },
            "prompt": "1111",
            "sessionId": "9ab696bc-2761-49c0-a1dc-bb70d46612be",
            "token": "eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6IjA5ZWM1OTY3LThmYjYtNGY3ZC04MDQyLWMzZjMxN2E4MmFlZCJ9.pQHrVVSLQoFqf-oU9b2_5Yc5H38KXW0pn-LQsF1X9cjEZe2tLyPaNkZvWi3fAsB0hdBdmiOXBmhELaiu_figOA",
            "userName": 1
        }}
        initMessage4.user_message.keywords.userQuestion=message
        return initMessage4
    }
}
<template>
    <aside class="work-flow-aside" v-show="!isCollapseSideBar">
        <div class="team-config">
            <div class="title">
              {{props.title}} 
              <el-button  size="small" icon="ArrowLeft" style="width: 20px;height: 20px;padding: 0;"
                  @click="isCollapseSideBar = true"></el-button>
            </div>
            <select-nodes></select-nodes>
        </div>
    </aside>
</template>
<script setup>
import { ref } from 'vue'

import SelectNodes from './components/selectNodes'

const props = defineProps({
    title: {
        type: String,
        default: '团队配置编排',
        required: false
    },
})

const isCollapseSideBar = ref(false)

defineExpose({
    isCollapseSideBar
})

</script>
<style lang="scss" scoped>
.work-flow-aside {
    max-height: 100%;
    width: 300px;
    background-color: white;
    padding: 8px 12px;
    padding-top: 20px;
    line-height: none;

    .team-config {
        //   height: 220px;
        border-bottom: 1px solid #ebeef5;

        .title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 20px;
            height: 32px;
            line-height: 32px;
            align-items: center;
            justify-content: space-between;
            display: flex;
            flex-direction: row;
        }

        .field {
            display: flex;
            margin-bottom: 10px;

            .label {
                width: 100px;
                text-align: left;
                margin-right: 10px;

                ::after {
                    content: ' :';
                }
            }

            .content {
                width: 80%;
            }
        }
    }

    .ai-choose {
        margin-top: 20px;

        .title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 20px;
        }

        .field {
            display: flex;
            margin-bottom: 10px;
        }

        .ai-options-content {
            height: 80%;
            display: flex;

            .ai-option {
                border: 1px solid #ccc;
                margin: 1%;
                height: 48%;
                width: 48%;
                display: flex;
                justify-content: center;
                align-items: center;
                flex-direction: column;

                .option-icon {
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    height: 50px;
                }

                .option-title {
                    height: 26px;
                    line-height: 26px;
                    font-size: 16px;
                    font-weight: bold;
                }

                .option-detail {
                    height: 16px;
                    line-height: 16px;
                    font-size: 12px;
                }
            }
        }
    }
}

::v-deep .el-dialog {
    display: flex;
    flex-direction: column;
    margin: 0 !important;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    max-height: calc(100% - 30px);
    max-width: calc(100% - 30px);
}

::v-deep(.el-dialog__body) {
    padding: 0;
}
</style>
<style scoped>
.my-header {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    gap: 16px;
    height: 40px;
}

.dialog-footer {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    height: 40px;
    /* padding: 0 20%; */
}
</style>
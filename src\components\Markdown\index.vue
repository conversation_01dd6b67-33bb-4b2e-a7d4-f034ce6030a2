<template>
  <div class="markdown-wrapper">
    <div class="codes">
      <transport v-for="(code, idx) of codes" :key="idx" :to="code.to">
        <echarts v-if="code.type === 'echarts'" :option="code.option" />
        <iframe-html v-else-if="code.type === 'html'" :html="code.html" />
      </transport>
    </div>
    <span v-html="html"></span>
  </div>
</template>

<script setup>
/**
 * sse接口流式返回时，会频繁重绘，```html和```echarts等进行重绘时会闪烁
 * 为避免闪烁，把```html和```echarts等的渲染专门拿出来，在外部渲染，再通过css移至需要展示的位置。
 * 原markdown内容中的代码块的绘制，只绘制一个占位元素
 * 
 * transport组件，作用是将其子元素的位置移至某个目标占位元素处，并调整其区域的大小
 * 只有一个参数to,  to是目标占位元素的id
 * 
 * 为什么有componentId
 * 这个是因为一个页面上会有多个markdown组件，util.js是公用的，但是需要区分，使各组件之间不会混淆
 */
import { defineProps, shallowRef, nextTick, onMounted, onUnmounted } from 'vue'
import { renderMarkdown } from './util.js'
import Echarts from './echarts.vue'
import IframeHtml from './iframeHtml.vue'
import Transport from './transport.vue'
import { uniqId } from '@/utils'

const props = defineProps({
	cnt: {
		type: String,
		required: true
	},
  showLoading: {
    type: Boolean,
    default: false
  },
  //是否开启html
  enableHtml: {
    type: Boolean,
    default: false
  }
})

const componentId = uniqId()
const html = ref('')
const codes = shallowRef([])

const renderContent = () => {
  const res = renderMarkdown(componentId, props.cnt, props.enableHtml)
  let tmpHtml = res.html
  let tmpCodes = res.codes

  if (props.showLoading) {
    tmpHtml = addLoading(tmpHtml)
  }

  html.value = tmpHtml
  codes.value = tmpCodes
}

watch(() => props.cnt, renderContent, {immediate: true})

// 监听图片加载完成事件，重新渲染
const handleImageLoaded = (event) => {
  // 检查当前内容是否包含这个图片URL
  if (props.cnt && props.cnt.includes(event.detail.url)) {
    nextTick(() => {
      renderContent()
    })
  }
}

const handleImageFailed = (event) => {
  // 检查当前内容是否包含这个图片URL
  if (props.cnt && props.cnt.includes(event.detail.url)) {
    nextTick(() => {
      renderContent()
    })
  }
}

onMounted(() => {
  window.addEventListener('markdown-image-loaded', handleImageLoaded)
  window.addEventListener('markdown-image-failed', handleImageFailed)
})

onUnmounted(() => {
  window.removeEventListener('markdown-image-loaded', handleImageLoaded)
  window.removeEventListener('markdown-image-failed', handleImageFailed)
})

// 在渲染出来的html的末尾，加上loading状态展示
function addLoading(htmlCnt) {
  if (htmlCnt.substring(length - 4, length) === '</p>') {
    return htmlCnt.substring(0, length - 4) + '<span class="loader"></span></p>'
  }

  return htmlCnt + '<span class="loader"></span>'
}
</script>

<style scoped>
.codes {
  width: 100%;
  height: 0;
  position: relative;
  overflow: visible;
}
:deep(.iframe-html) {
	width: 100%;
	min-height: 300px;
	border: 0;
}
:deep(img) {
  max-width: 100%;
  padding: 0.3em 0;
  display: block;
}
:deep(video) {
  width: min(100%, 640px);
}
:deep(.image-loading) {
  display: block;
  width: min(300px, 100%);
  height: 200px;
  padding: 0.3em 0;
  background-image: url(/images/img-placeholder.png);
  border-radius: 3px;
  position: relative;
}
:deep(.image-loading::before) {
  content: '';
  display: block;
  position: absolute;
  left: calc(50% - 24px);
  top: calc(50% - 24px);
  width: 48px;
  height: 48px;
  border: 5px solid #FFF;
  border-bottom-color: transparent;
  border-radius: 50%;
  box-sizing: border-box;
  animation: rotation 2s linear infinite;
}
@keyframes rotation {
  0% {
      transform: rotate(0deg);
  }
  100% {
      transform: rotate(360deg);
  }
}
:deep(.image-failed) {
  display: block;
  width: min(300px, 100%);
  height: 200px;
  padding: 0.3em 0;
  background-image: url(/images/img-failed.png);
  border-radius: 3px;
}
:deep(.image-wrapper) {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
}
:deep(.image-wrapper .image-title) {
  display: block;
  text-align: center;
}
:deep(pre) {
  border-radius: 5px;
  text-align: left;
  max-width: calc(100% - 32px);
  overflow: hidden;
}
:deep(.table-wrapper) {
  width: 100%;
  max-width: 100%;
  height: auto;
  overflow: auto;
}
:deep(table) {
  border-collapse: collapse;
  width: auto;
}
:deep(table th) {
  text-wrap: nowrap;
  white-space: nowrap;
  border: 1px solid lightgray;
  padding: 0.5em 1em;
}
:deep(table td) {
  border: 1px solid lightgray;
  padding: 0.5em 1em;
}
:deep(tr:nth-child(2n)) {
  /* background-color: #f6f8fa; */
}
:deep(tr:hover td) {
  /* background-color: #f2f2f6; */
}
:deep(a) {
  color: #409eff;
}
:deep(a:hover) {
  text-decoration: underline;
}
:deep(.loader) {
  display: inline-block;
  width: 6px;
  aspect-ratio: 1;
  border-radius: 50%;
  animation: l5 1s infinite linear alternate;
  position: relative;
  left: 10px;
  margin-right: 20px;
}
@keyframes l5 {
  0% {
    box-shadow: 12px 0 #fff, -12px 0 #666666;
    background: #fff;
  }
  33% {
    box-shadow: 12px 0 #fff, -12px 0 #666666;
    background: #666666;
  }
  66% {
    box-shadow: 12px 0 #666666, -12px 0 #fff;
    background: #666666;
  }
  100% {
    box-shadow: 12px 0 #666666, -12px 0 #fff;
    background: #fff;
  }
}
:deep(.echarts-graph) {
  width: min(1000px, 90%);
  aspect-ratio: 16 / 9;
  border-radius: 3px;
  overflow: hidden;
}
:deep(.echarts-loading) {
  display: block;
  width: min(300px, 100%);
  height: 200px;
  padding: 0.3em 0;
  background-image: url(/images/img-placeholder.png);
  border-radius: 3px;
  position: relative;
}
:deep(.echarts-loading::before) {
  content: '';
  display: block;
  position: absolute;
  left: calc(50% - 24px);
  top: calc(50% - 24px);
  width: 48px;
  height: 48px;
  border: 5px solid #FFF;
  border-bottom-color: transparent;
  border-radius: 50%;
  box-sizing: border-box;
  animation: rotation 2s linear infinite;
}
:deep(.mermaid-graph) {
  width: min(1000px, 90%);
  background-color: white;
  height: auto;
  border-radius: 3px;
  overflow: hidden;
}
</style>
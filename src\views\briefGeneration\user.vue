<template>
  <div class="wrapper">
    <el-avatar :size="38" :src="userStore.avatar" class="avatar" />
    <div class="nick">{{ userStore.nick }}</div>
    <div class="divider"></div>
    <div class="btn-log-out" @click="logout()">
      <inline-svg src="/icons/power-off.svg" />
      退出
    </div>
  </div>
  <!--
  <el-dropdown @command="handleCommand" placement="right">
    <el-avatar :size="28" :src="userStore.avatar" class="avatar" />
    <template #dropdown>
      <el-dropdown-menu>
        <router-link v-hasPermi="['edu:permission-management', 'edu:personal-center']" to="/edu/profile">
          <el-dropdown-item>个人中心</el-dropdown-item>
        </router-link>
        <router-link v-hasPermi="['edu:permission-management', 'edu:personal-center']" to="/permissions">
          <el-dropdown-item>权限管理</el-dropdown-item>
        </router-link>
        <router-link v-hasRole="['admin']" to="/edu/full-dify" target="_blank">
          <el-dropdown-item>智能体</el-dropdown-item>
        </router-link>
        <el-dropdown-item  command="logout">
          <span>退出登录</span>
        </el-dropdown-item>
      </el-dropdown-menu>
    </template>
  </el-dropdown>
  -->
</template>

<script setup>
import { ElMessageBox } from 'element-plus';
import useUserStore from '@/store/modules/user'
import InlineSvg from 'vue-inline-svg'

const userStore = useUserStore()

// 处理菜单点击
function handleCommand(command) {
  switch (command) {
    case "logout":
      logout();
      break;
    default:
      break;
  }
}

// 注销
function logout() {
  ElMessageBox.confirm('确定注销并退出系统吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    customClass: "junzhiMessage",
    type: 'warning'
  }).then(() => {
    userStore.logOut().then(() => {
      location.href = '/';
    })
  }).catch(() => { });
}

</script>

<style lang="scss" scoped>
.avatar {
  cursor: pointer;
  border: 1px solid rgba(0,0,0,0.1);
  outline: 0;
}

.wrapper {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  align-items: center;
  height: 38px;

  .nick {
    font-size: 14px;
    color: #FFFFFF;
    line-height: 26px;
    margin-left: 15px;
  }

  .divider {
    width: 0;
    height: 36px;
    border: 0;
    border-left: 1px solid #00E5FF;
    margin-left: 17px;
    margin-right: 19px;
  }

  .btn-log-out {
    color: #00E5FF;
    font-size: 14px;
    display: flex;
    flex-direction: row;
    justify-content: flex-end;
    align-items: center;
    cursor: pointer;

    &:hover {
      text-shadow: 0 0 3px #FFFFFF;
      svg {
        filter: drop-shadow(0 0 3px #FFFFFF);
      }
    }

    svg {
      width: 16px;
      height: 16px;
      margin-right: 9px;
    }
  }
}
</style>
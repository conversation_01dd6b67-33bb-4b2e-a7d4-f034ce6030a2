<template>
    <div class="custom-form-item-slider">
        <div class="custom-slider-title">{{props.label}}</div>
        <div class="custom-slider-value">
            <el-slider size="small" style="width: 60%;" v-model="sliderValue" :max="props.max" :min="props.min" :step="props.step" />
            <el-input v-model="tmpVal" @change="numberChange"
                :input-style="{textAlign:'center'}"
                style="width: 60px;margin-left: 18px;">
            </el-input>
        </div>
    </div>
</template>
<script setup>
import { ref,computed } from 'vue'
const props = defineProps({
    value:{
        type:Number,
        required:true
    },
    label:{
        type: String,
        default: ''
    },
    max:{
        type: Number,
        default: 100
    },
    min:{
        type: Number,
        default: 0
    },
    step:{
        type: Number,
        default: 1
    }
})
const sliderValue = ref(0)
const emits = defineEmits(['update:value'])
const tmpVal = computed({
    get(){
        return JSON.parse(JSON.stringify(sliderValue.value))
    },
    set(val){
        sliderValue.value = parseFloat(val)
        emits('update:value',sliderValue.value)
        return val
    }
})
const numberChange = ()=> {
    sliderValue.value = parseFloat(tmpVal.value);
}
watch(sliderValue,(newValue)=>{
    emits('update:value',newValue);
})
sliderValue.value = props.value
</script>
<style>
.custom-form-item-slider{
    display: flex;
    flex-direction: row;
    width: 100%;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 5px;
    .custom-slider-title{
        margin-right: 20px;
        width: 120px;
        text-align: left;
        font-size: 12px;
        color: #999999;
    }
    .custom-slider-value{
        width: calc(100% - 120px);
        display: flex;
        flex-direction: row;
    }
}
</style>
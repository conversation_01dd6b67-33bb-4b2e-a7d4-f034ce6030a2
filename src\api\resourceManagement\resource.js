import request from '@/utils/request'

//资源分类列表
export function getClassifyList(params) {
    return request({
        url: '/resource/classify/list',
        method: 'get',
        params
    })
}

//新增资源分类
export function addClassify(data) {
    return request({
        url: '/resource/classify',
        method: 'post',
        data
    })
}

//修改资源分类
export function editClassify(data) {
    return request({
        url: '/resource/classify',
        method: 'put',
        data
    })
}

//删除资源分类
export function delClassify(classifyIds) {
    return request({
        url: '/resource/classify/'+classifyIds,
        method: 'delete'
    })
}

//资源列表
export function getResourceList(params) {
    return request({
        url: '/resource/resource/list',
        method: 'get',
        params
    })
}

//资源上传
export function addResource(data) {
    return request({
        url: '/resource/resource/uniteupload',
        method: 'post',
        data
    })
}

//资源修改
export function editResource(data) {
    return request({
        url: '/resource/resource',
        method: 'put',
        data
    })
}

//删除资源
export function delResource(resourceIds) {
    return request({
        url: '/resource/resource/'+resourceIds,
        method: 'delete'
    })
}

//导出全部
export function resourceExport(data) {
    return request({
        url: '/resource/resource/export',
        method: 'post',
        data
    })
}

<template>
    <div class="session">
        <div class="history" v-if="props.isHistory" :style="{'width':props.isHistory && showDiv?'20%':props.isHistory && !showDiv?'2%':'0'}">
            <div v-if="showDiv" class="show-history">
                <conversation-list ref="conversation"
                    @after-delete="(list) => {console.log('after-delete'); console.log(list)}"
                    @select="conversationSelect"
                />
                <div class="suspended-button">
                    <div class="box">
                        <el-button link text :icon="ArrowLeft" @click="showDiv=!showDiv"></el-button>
                    </div>
                </div>
            </div>
            <div class="hide-history" v-if="!showDiv">
                <div class="box">
                    <el-button link text :icon="ArrowRight" @click="showDiv=!showDiv"></el-button>
                </div>
            </div>
        </div>
        <div class="middle" :style="{'width':props.isHistory && showDiv && props.agentList.length<=0?'80%':
        props.isHistory && !showDiv && props.agentList.length<=0?'98%':
        !props.isHistory && props.agentList.length>0?'85%':
        !props.isHistory && props.agentList.length<=0?'100%':'65%'}">
            <session-records :message-item-manager="props.messageItemManager" :chat-url="props.chatUrl" :keywords="props.keywords" 
            :current-instruct="currentInstruct" ref="messageCtrl"></session-records>
        </div>
        <div class="agent" :style="{'width':props.agentList.length>0?'15%':'0'}">

        </div>
    </div>
</template>
<script setup>
    import { MessageItemManager } from './utils/msgManager';
    import ConversationList from '@/components/Chat/conversationList.vue'
    import { defineProps, nextTick,ref, watch } from 'vue';
    import { ArrowLeft,ArrowRight } from '@element-plus/icons-vue'
    import sessionRecords from './sessionRecords'

    const props = defineProps({
        messageItemManager: {
            type: Object,
            required: false,
            default: new MessageItemManager()
        },
        //服务路径
        chatUrl:{
            type:String,
            required:true
        },
        keywords:{
            type:Object,
            required:false,
            default: () => {
                return {}
            }
        },
        keyList:{
            type:Array,
            required:false,
            default: () => {
                return []
            }
        },
        //团队成员
        agentList:{
            type:Array,
            requeired:false,
            default: () => {
                return []
            }
        },
        //是否展示历史记录
        isHistory:{
            type:Boolean,
            required:false,
            default:false
        },
        //是否是调试
        isDebugging:{
            type:Boolean,
            required:false,
            default:false
        },
        currentInstruct:{
            type:Object,
            required:true
        }
    });

    const currentInstruct = ref(null)
    watch(() => props.currentInstruct, val => {
        if (val) {
            currentInstruct.value = val
            return val
        } else {
            currentInstruct.value = null
            return {}
        }
    },{ deep: true, immediate: true });

    const showDiv = ref(true)
    const messageCtrl = ref(null)
    const sessionInfo = ref({})

    defineExpose({
        // connectWS
    })
</script>
<style lang="scss" scoped>
.session{
    display: flex;
    justify-content: space-between;
    height: calc( 100vh );
    background-color: #F7F7FA;
    .history{
        margin-right: 10px;
        .show-history{
            display: flex;
            justify-content: space-between;
            flex-shrink: 0;
            flex-grow: 0;
            .suspended-button{
                width: 10%;
                display: flex;
                align-items: center;
                padding: 0 5px;
                .box{
                    display: flex;
                    align-items: center;
                    height: 30px;
                    background-color: #EEECF6;
                    border-radius: 5px;
                    ::v-deep .el-button.is-link {
                        padding: 0;
                        color: #909090;
                    }
                }
            }
        }
        .hide-history{
            width: 2%;
            display: flex;
            align-items: center;
            padding: 0 5px;
            height: calc( 100vh );
            .box{
                display: flex;
                align-items: center;
                height: 30px;
                background-color: #EEECF6;
                border-radius: 5px;
                ::v-deep .el-button.is-link {
                    padding: 0;
                    color: #909090;
                }
            }
        }
    }
    .middle{

    }
    .agent{

    }
}
</style>
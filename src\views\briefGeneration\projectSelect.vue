<template>
  <el-select v-model="projectId" placeholder="请选择" style="min-width: 160px" popper-class="junzhiSelectPopper"
    class="junzhiSelect selectContainer">
    <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
  </el-select>
</template>

<script setup>
import { onMounted, ref } from 'vue'
import { useRoute, useRouter } from "vue-router";
import { basicInfoList } from "@/api/briefGeneration/projectMannage";
import { projectId } from "@/views/briefGeneration/list/js/sharedState.js";

const route = useRoute()
const router = useRouter()
const id = route.query.projectId;

const options = ref([])

// 项目列表option
const getOptions = async () => {
  try {
    let res = await basicInfoList();
    if (res.code == '200') {
      options.value = (res.data || []).map(item => {
        return {
          value: String(item.id),
          label: item.projectName
        }
      })
    } else {
      options.value = []
    }
    projectId.value = id || options.value[0]?.value;
  } catch (e) {
    console.error(e);
  }
};

onMounted(() => {
  getOptions()
})

</script>

<style lang="scss" scoped>
.selectContainer {
  :deep(.el-select__wrapper) {
    box-shadow: none !important;
    padding: 4px;
  }

  --el-text-color-regular: #6AF6FF;
  --el-input-text-color: #6AF6FF;
  --el-color-primary: #6AF6FF;
}
</style>

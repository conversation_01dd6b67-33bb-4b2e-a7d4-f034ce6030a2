<template>
    <div class="session">
        <div class="history" v-if="props.isHistory" :style="{'width':props.isHistory && showDiv?'20%':props.isHistory && !showDiv?'2%':'0'}">
            <div v-if="showDiv" class="show-history">
                <conversation-list ref="conversation"
                    @after-delete="(list) => {console.log('after-delete'); console.log(list)}"
                    @select="conversationSelect"
                />
                <div class="suspended-button">
                    <div class="box">
                        <el-button link text :icon="ArrowLeft" @click="showDiv=!showDiv"></el-button>
                    </div>
                </div>
            </div>
            <div class="hide-history" v-if="!showDiv">
                <div class="box">
                    <el-button link text :icon="ArrowRight" @click="showDiv=!showDiv"></el-button>
                </div>
            </div>
        </div>
        <div class="middle" :style="{'width':props.isHistory && showDiv ?'80%':
        props.isHistory && !showDiv ?'98%':'100%'}">
            <div class="right-head" v-if="props.isDebugging">
                <div class="head-title">预览与调试
                    <el-button size="small" :icon="Refresh" @click="closeSession()" style="font-size: 14px;color: #5050e6;">重新开始</el-button>
                </div>
            </div>
            <div>
                <session-records :message-item-manager="props.messageItemManager" :chat-url="props.chatUrl" :keywords="props.keywords" 
                :current-instruct="currentInstruct" :is-debugging="props.isDebugging" ref="messageCtrl"></session-records>
            </div>
        </div>
    </div>
</template>
<script setup>
    import { MessageItemManager } from './utils/msgManager';
    import ConversationList from '@/components/Chat/conversationList.vue'
    import { defineProps, nextTick,ref, watch } from 'vue';
    import { ArrowLeft,ArrowRight,Refresh } from '@element-plus/icons-vue'
    import sessionRecords from './sessionRecords'

    const props = defineProps({
        messageItemManager: {
            type: Object,
            required: false,
            default: new MessageItemManager()
        },
        //服务路径
        chatUrl:{
            type:String,
            required:true
        },
        keywords:{
            type:Object,
            required:false,
            default: () => {
                return {}
            }
        },
        keyList:{
            type:Array,
            required:false,
            default: () => {
                return []
            }
        },
        //是否展示历史记录
        isHistory:{
            type:Boolean,
            required:false,
            default:false
        },
        //是否是调试
        isDebugging:{
            type:Boolean,
            required:false,
            default:false
        },
        currentInstruct:{
            type:Object,
            required:true
        }
    });

    const currentInstruct = ref(null)
    watch(() => props.currentInstruct, val => {
        if (val) {
            currentInstruct.value = val
            return val
        } else {
            currentInstruct.value = null
            return {}
        }
    },{ deep: true, immediate: true });

    const showDiv = ref(true)
    const messageCtrl = ref(null)
    const sessionInfo = ref({})

    const closeSession = () => {
        messageCtrl.value.closeSession()
    }

    defineExpose({
        closeSession
    })
</script>
<style lang="scss" scoped>
.session{
    display: flex;
    justify-content: space-between;
    height: calc( 100vh );
    background-color: #F7F7FA;
    .history{
        margin-right: 10px;
        .show-history{
            display: flex;
            justify-content: space-between;
            flex-shrink: 0;
            flex-grow: 0;
            .suspended-button{
                width: 10%;
                display: flex;
                align-items: center;
                padding: 0 5px;
                .box{
                    display: flex;
                    align-items: center;
                    height: 30px;
                    background-color: #EEECF6;
                    border-radius: 5px;
                    ::v-deep .el-button.is-link {
                        padding: 0;
                        color: #909090;
                    }
                }
            }
        }
        .hide-history{
            width: 2%;
            display: flex;
            align-items: center;
            padding: 0 5px;
            height: calc( 100vh );
            .box{
                display: flex;
                align-items: center;
                height: 30px;
                background-color: #EEECF6;
                border-radius: 5px;
                ::v-deep .el-button.is-link {
                    padding: 0;
                    color: #909090;
                }
            }
        }
    }
    .middle{
        .right-head{
            padding: 15px;
            font-size: 16px;
            font-weight: bold;
            color: #032220;
            background-color: white;
            box-shadow: 0 2px 4px 0 rgba(0, 0, 0, .04), 0 0 1px 0 rgba(0, 0, 0, .08);
            border-radius: 8px;
            .head-title{
                display: flex;
                justify-content: space-between;
            }
        }
    }
    .agent{

    }
}
</style>
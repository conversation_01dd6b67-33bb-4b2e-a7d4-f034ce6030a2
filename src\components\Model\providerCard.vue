<template>
    <div class="card-wrapper">
        <el-card :class="['card', {expandable: models.length > 2} ]" shadow="never">
            <template #header>
                <div class="header-container">
                    <div class="left">
                        <img
                            v-if="provider.icon_large"
                            :src="provider.icon_large.zh_Hans.replace('127.0.0.1',  '180.103.126.11') + '?_token=' + getToken()"
                            class="provider-logo"
                        >
                        <span v-else>{{ provider.label.zh_Hans }}</span>
                        <span
                            v-for="item of provider.supported_model_types"
                            :key="item"
                            class="supported-model-type"
                        >
                            {{ item }}
                        </span>

                    </div>
                    <div class="right">
                        <el-button
                            v-if="provider.configurate_methods.includes('customizable-model')"
                            :icon="Plus"
                            link
                            circle
                            class="btn-add-model"
                            @click="emit('add-model', provider)"
                        />
                        <el-button
                            v-if="provider.configurate_methods.includes('predefined-model')"
                            :icon="Setting"
                            link
                            circle
                            class="btn-set-provider"
                            @click="emit('set-provider', provider)"
                        />
                    </div>
                </div>
            </template>
            <div v-if="models.length > 0" class="model-list">
                <div
                    v-for="item of models"
                    :key="item.model"
                    :class="['item', {hoverable: item.fetch_from === 'customizable-model'}]"
                >
                    <div class="left">
                        <span class="name">{{ item.model }}</span>
                        <el-tag type="info" size="small" class="type">{{ item.model_type }}</el-tag>
                    </div>
                    <el-button
                        v-if="item.fetch_from === 'customizable-model'"
                        :icon="Setting"
                        link
                        circle
                        class="btn-edit-model"
                        @click="emit('edit-model', provider, item)"
                    />
                </div>
            </div>
            <div v-else class="empty">
                无
            </div>
            <div v-if="models.length > 2" class="expander">
                <arrow-down-bold />
            </div>
        </el-card>
    </div>
</template>

<script setup>
import { ArrowDownBold, Plus, Setting } from '@element-plus/icons-vue'
import { getToken } from "@/utils/auth";

const props = defineProps({
    /**
     * 供应商
     */
    provider: {
        type: Object,
        required: true
    },

    /**
     * 模型列表
     */
    models: {
        type: Array,
        required: true
    }
});

const emit=defineEmits(['add-model', 'edit-model', 'set-provider']);
</script>

<style scoped>
.card-wrapper {
    width: 100%;
    height: 190px;
    position: relative;
    overflow: hidden;
}
.card-wrapper:hover {
    overflow: visible;
    z-index: 999;
}
.card {
    height: 170px;
    width: calc(100% - 10px);
    box-shadow: 0 4px 10px 0 #0000000d;
    border:none;
    border-radius: 8px;
}

.card-wrapper:hover .card.expandable {
    height: auto;
}

.card-wrapper:hover .expander {
    display: none;
}

.card :deep(.el-card__header) {
    height: 50px;
}

.card :deep(.el-card__body) {
    position: relative;
}

.header-container {
    width: 100%;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
}

.header-container .left {
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
    flex-grow: 1;
    flex-shrink: 1;
    overflow: hidden;
}

.header-container .left > img {
    flex-grow: 0;
    flex-shrink: 1;
}

.header-container .right {
    display: flex;
    flex-direction: row;
    justify-content: flex-end;
    align-items: center;
}

.header-container .right .btn-set-provider {
    margin-left: 0 !important;
}


.provider-logo {
    height: 1.5em;
}

.supported-model-type {
    display: inline-flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    padding: 0 9px;
    margin: 0 5px;
    background-color: #f7f7f7;
    color: #909399;
    font-size: 12px;
    border-radius: 2px;
}

.empty {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100px;
    color: lightgray;
}
.model-list .item {
    padding: 10px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    height: 42px;
    border-radius: 3px;
    overflow: visible;
}
.model-list .item .left {
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
    flex-grow: 1;
    flex-shrink: 0;
}
.model-list .item .btn-edit-model {
    display: none;
    flex-grow: 0;
    flex-shrink: 0;
}
.model-list .item.hoverable:hover {
    background-color: #fafafa;
}
.model-list .item.hoverable:hover .btn-edit-model {
    display: block;
}
.model-list .item .name {
    margin-right: 1em;
}
.expander {
    width: 100%;
    height: 24px;
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    position: absolute;
    left: 0;
    top: 96px;
    background-color: #fafafa;
    cursor: pointer;
}
.expander svg {
    width: 16px;
    color: lightgray;
}
</style>
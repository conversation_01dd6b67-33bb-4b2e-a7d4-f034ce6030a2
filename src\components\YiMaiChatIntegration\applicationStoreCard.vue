<template>
    <div class="app-item">
        <div class="title-area">
            <div class="icon">
                <template v-if="app.applicationType === 'team'">
                    <img :src="app.applicationIcon ? baseUrl+app.applicationIcon : TeamDefaultIcon" class="img" />
                </template>
                <template v-else>
                    <img :src="app.applicationIcon ? baseUrl+app.applicationIcon : AgentDefaultIcon" class="img" />
                </template>
            </div>
            <div class="text">
                <div class="name">
                    <el-tooltip
                        :content="app.applicationName"
                        placement="top"
                        effect="customized"
                        popper-class="card-pop"
                        >
                        <div style="overflow: hidden;white-space: nowrap;text-overflow: ellipsis;">
                            {{app.applicationName}}
                        </div>
                    </el-tooltip>
                    <div>
                        <inline-svg
                            v-if="!!app.isIndex"
                            :src="thumbtackFilledSvg" @click="pinApp(false)"
                            class="btn-pinned"
                        />
                        <inline-svg
                            v-if="!app.isIndex"
                            :src="thumbtackOutlineSvg" @click="pinApp(true)"
                            class="btn-unpinned hover-show"
                        />
                    </div>
                </div>
                <el-tooltip
                    :content="app.applicationDesc"
                    placement="bottom"
                    effect="customized"
                    popper-class="card-pop"
                    >
                    <div class="desc">
                            {{app.applicationDesc}}
                    </div>
                </el-tooltip>
                <div class="tags">
                    <template v-for="tag in app.classifyNames">
                        <el-tag type="info" size="small">{{tag}}</el-tag>
                    </template>
                </div>
            </div>
        </div>
        <div class="buttons">
            <el-button v-if="app.approveStatus == 1" type="info" link @click="useApp(app)" >使用</el-button>
            <el-button v-else-if="app.approveStatus == 2" type="primary" link disabled >审核中</el-button>
            <el-button v-else type="primary" link @click="applyUseApp(app)" >申请使用</el-button>
        </div>
    </div>
</template>

<script setup>
import AgentDefaultIcon from '@/assets/icons/svg/agent-default.svg'
import TeamDefaultIcon from '@/assets/icons/svg/team-default.svg'
import thumbtackFilledSvg from '@/assets/icons/svg/thumbtack-filled.svg';
import thumbtackOutlineSvg from '@/assets/icons/svg/thumbtack-outline.svg';
import InlineSvg from 'vue-inline-svg';
import {ElMessage, ElMessageBox} from 'element-plus'
import {useRouter} from 'vue-router'
import { addApply } from "@/api/application/apply";
import { setIsIndex } from '@/api/session/session';

const baseUrl = import.meta.env.VITE_APP_BASE_API
const router = useRouter()

const props = defineProps({
    app: {
        type: Object,
        default: () => ({})
    }
})

const emit = defineEmits(['submit','use'])
const useApp = inject('useApp');

const applyUseApp = (app) => {
    const {applicationId,applicationName,applicationType,applicationDesc} = app
    addApply({
        applicationId,
        applicationName,
        applicationType,
        applicationDesc
    }).then(res => {
        if(res.code == 200){
            ElMessage.success('申请成功')
            emit('submit')
        }
    })
}

const pinApp = (isIndex) => {
    if(props.app.approveStatus != 1 && isIndex){
        ElMessageBox.confirm('你暂无应用使用权限，确定要置顶应用?','提示',{
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
        }).then(() => {
            setIsIndex({
                applicationId: props.app.applicationId,
                applicationType: props.app.applicationType,
                isIndex: isIndex?1:0
            }).then(res => {
                if(res.code == 200){
                    ElMessage.success('设置成功')
                    emit('submit')
                }
            })
        })
    }else{
        setIsIndex({
            applicationId: props.app.applicationId,
            applicationType: props.app.applicationType,
            isIndex: isIndex?1:0
        }).then(res => {
            if(res.code == 200){
                ElMessage.success('设置成功')
                emit('submit')
            }
        })
    }
}

</script>

<style lang="scss" scoped>
.app-item{
    min-height: 150px;
    display: flex;
    flex-direction: column;
    grid-column: span 1 / span 1;
    background-color: white;
    border-radius: 8px;
    border: 1px solid rgb(234,236,240);
    border-color: transparent;
    box-shadow: 0 0 1px 0 rgba(0,0,0,.1);
    cursor: pointer;
    transition: all .2s;
    box-shadow: 0 4px 10px 0 #0000000d;
    .title-area{
        display: flex;
        height: 112px;
        max-height: 112px;
        flex-shrink: 0;
        flex-grow: 0;
        gap: .75rem;
        padding: 24px;
        padding-bottom: 4px;
        margin-bottom: 12px;
        .icon{
            position: relative;
            flex-shrink: 0;
            width: 64px;
            height: 64px;
            margin: 4px 0;
            .img{
                height: 64px;
                width: 64px;
                background-size: cover;
                background-position: 50%;
                background-repeat: no-repeat;
                border-radius: .375rem;
            }
        }
        .text{
            width: 0;
            flex-grow: 1;
            padding: 1px 0;
            margin: 4px 0;
            .name{
                font-weight: 600;
                color: rgb(29, 41, 57);
                line-height: 1.5rem;
                font-size: 1.25rem;
                display: flex;
                align-items: center;
                justify-content: space-between;
                margin-bottom: 8px;
            }
            .desc{
                overflow: hidden;
                max-height: 1rem;
                display: -webkit-box;
                -webkit-line-clamp: 1;
                -webkit-box-orient: vertical;
                text-overflow: ellipsis;
                color: rgb(102, 112, 133);
                line-height: 1rem;
                font-weight: 500;
                font-size: 12px;
                align-items: center;
            }
            .tags{
                height: 1.25rem;
                display: flex;
                gap: .25rem;
                margin-top: 8px;
                overflow: hidden;
            }
            .btn-pinned, .btn-unpinned {
                width: 14px;
                height: 14px;
                cursor: pointer;
                fill: #5e6d82;
                &:hover {
                    background-color: #fafafa;
                    fill: black;
                }
            }
        }
    }
    .buttons{
        display: flex;
        justify-content: end;
        align-items: center;
        padding: 0 24px 10px 24px;
    }
    .hover-show{
        display: none;
    }
    &:hover{
        .hover-show{
            display: block;
        }
    }
}
</style>
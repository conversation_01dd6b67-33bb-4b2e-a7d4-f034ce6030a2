<template>
  <div class="container">
    <div class="process-bar-wrapper">
      <span class="process-title">简报生成</span>
      <stepBox />
    </div>
    <div class="content-wrapper" style="display: flex">
      <div class="left">
        <div class="input-row">
          <span class="input-label"
            ><span style="color: red">* </span>简报标题：</span
          >
          <el-input
            v-model="form.title"
            @input="clearError('title')"
            placeholder="请输入简报标题，例如九牧王品牌战略发展研究报告"
            class="input input-main"
          />
          <span v-if="errors.title" class="error">{{ errors.title }}</span>
        </div>
        <div class="input-row">
          <span class="input-label"
            ><span style="color: red">* </span>研究主题：</span
          >
          <el-input
            v-model="form.subject"
            @input="clearError('subject')"
            placeholder="请输入需要AI帮助研究的企业或品牌，例如九牧王品牌发展研究"
            class="input input-main theme-input"
          />
          <span v-if="errors.subject" class="error">{{ errors.subject }}</span>
        </div>
        <div class="input-row">
          <span class="input-label"
            ><span style="color: red">* </span>研究阶段：</span
          >
          <el-select
            v-model="form.stage"
            placeholder="请选择研究的阶段"
            class="input input-main"
            @change="clearError('stage')"
          >
            <el-option
              v-for="opt in researchStageOptions"
              :key="opt.value"
              :label="opt.label"
              :value="opt.value"
            />
          </el-select>
          <span v-if="errors.stage" class="error">{{ errors.stage }}</span>
        </div>
        <div class="input-row">
          <span class="input-label"
            ><span style="color: red">* </span>行业分析框架：</span
          >
          <el-select
            v-model="form.businessVersion"
            placeholder="请选择行业分析框架"
            class="input input-main"
            @change="clearError('businessVersion')"
          >
            <el-option
              v-for="opt in industryAnalysisOptions"
              :key="opt.value"
              :label="opt.label"
              :value="opt.value"
            />
          </el-select>
          <el-button
            class="eye-btn"
            :class="{ 'is-active': activeViewIndexes.includes(3) }"
            @click="handleViewClick(3)"
          >
            <el-icon :class="{ 'is-active': activeViewIndexes.includes(3) }"
              ><View
            /></el-icon>
          </el-button>
          <span v-if="errors.businessVersion" class="error">{{
            errors.businessVersion
          }}</span>
        </div>
        <div class="input-row">
          <span class="input-label"
            ><span style="color: red">* </span>竞争分析框架：</span
          >
          <el-select
            v-model="form.competeVersion"
            placeholder="请选择竞争分析框架"
            class="input input-main"
            @change="clearError('competeVersion')"
          >
            <el-option
              v-for="opt in competitionAnalysisOptions"
              :key="opt.value"
              :label="opt.label"
              :value="opt.value"
            />
          </el-select>
          <el-button
            class="eye-btn"
            :class="{ 'is-active': activeViewIndexes.includes(4) }"
            @click="handleViewClick(4)"
          >
            <el-icon :class="{ 'is-active': activeViewIndexes.includes(4) }"
              ><View
            /></el-icon>
          </el-button>
          <span v-if="errors.competeVersion" class="error">{{
            errors.competeVersion
          }}</span>
        </div>
        <div class="input-row">
          <span class="input-label"
            ><span style="color: red">* </span>顾客认知分析框架：</span
          >
          <el-select
            v-model="form.clientVersion"
            placeholder="请选择顾客认知分析框架"
            class="input input-main"
            @change="clearError('clientVersion')"
          >
            <el-option
              v-for="opt in customerInsightAnalysisOptions"
              :key="opt.value"
              :label="opt.label"
              :value="opt.value"
            />
          </el-select>
          <el-button
            class="eye-btn"
            :class="{ 'is-active': activeViewIndexes.includes(5) }"
            @click="handleViewClick(5)"
          >
            <el-icon :class="{ 'is-active': activeViewIndexes.includes(5) }"
              ><View
            /></el-icon>
          </el-button>
          <span v-if="errors.clientVersion" class="error">{{
            errors.clientVersion
          }}</span>
        </div>
        <div class="input-row">
          <span class="input-label"
            ><span style="color: red">* </span>企业经营分析框架：</span
          >
          <el-select
            v-model="form.manageVersion"
            placeholder="请选择企业经营分析框架"
            class="input input-main"
            @change="clearError('manageVersion')"
          >
            <el-option
              v-for="opt in businessOperationAnalysisOptions"
              :key="opt.value"
              :label="opt.label"
              :value="opt.value"
            />
          </el-select>
          <el-button
            class="eye-btn"
            :class="{ 'is-active': activeViewIndexes.includes(6) }"
            @click="handleViewClick(6)"
          >
            <el-icon :class="{ 'is-active': activeViewIndexes.includes(6) }"
              ><View
            /></el-icon>
          </el-button>
          <span v-if="errors.manageVersion" class="error">{{
            errors.manageVersion
          }}</span>
        </div>
        <div class="input-row">
          <span class="input-label"
            ><span style="color: red">* </span>企业家分析框架：</span
          >
          <el-select
            v-model="form.entrepreneurVersion"
            placeholder="请选择企业家分析框架"
            class="input input-main"
            @change="clearError('entrepreneurVersion')"
          >
            <el-option
              v-for="opt in entrepreneurAnalysisOptions"
              :key="opt.value"
              :label="opt.label"
              :value="opt.value"
            />
          </el-select>
          <el-button
            class="eye-btn"
            :class="{ 'is-active': activeViewIndexes.includes(7) }"
            @click="handleViewClick(7)"
          >
            <el-icon :class="{ 'is-active': activeViewIndexes.includes(7) }"
              ><View
            /></el-icon>
          </el-button>
          <span v-if="errors.entrepreneurVersion" class="error">{{
            errors.entrepreneurVersion
          }}</span>
        </div>
        <div class="switch-row">
          <span>知识库：</span>
          <el-switch v-model="form.knowledge" class="switch-btn" />
        </div>
        <div class="switch-row">
          <span>联网搜索：</span>
          <el-switch v-model="form.internet" class="switch-btn" />
        </div>
        <div class="btn-row">
          <el-button class="cancel-btn" @click="handleCancel">取 消</el-button>
          <el-button :class="['generate-btn']" @click="handleGenerate"
            >一键生成简报</el-button
          >
        </div>
      </div>
      <div class="divider"></div>
      <div class="right">
        <div class="catalog-header">行业研究框架</div>
        <div
          v-for="(idx, index) in activeViewIndexes"
          :key="index"
          class="outline-tree custom-tree"
        >
          <el-tree
            v-if="treeDataMap[getAnalysisStructureType(idx)]?.length"
            :data="treeDataMap[getAnalysisStructureType(idx)]"
            :props="{
              label: 'name',
              children: 'children',
            }"
            node-key="id"
            default-expand-all
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, h } from "vue";
import { ElIcon } from "element-plus";
import { ArrowRight, View } from "@element-plus/icons-vue";
import { useRouter } from "vue-router";
import stepBox from "./step.vue";
import { getCatalogList } from "@/api/briefGeneration/catalog.js";
import {
  form,
  activeStep,
} from "@/views/briefGeneration/add/create/js/sharedState.js";

const router = useRouter();
const outlineLabels = {
  title: "简报标题",
  subject: "研究主题",
  stage: "研究阶段",
  businessVersion: "企业经营分析框架",
  competeVersion: "竞争分析框架",
  clientVersion: "顾客认知分析框架",
  manageVersion: "企业管理分析框架",
  entrepreneurVersion: "企业家分析框架",
};

const errors = ref({
  title: "",
  subject: "",
  stage: "",
  businessVersion: "",
  competeVersion: "",
  clientVersion: "",
  manageVersion: "",
  entrepreneurVersion: "",
  knowledge: false,
  internet: false,
});

const treeDataMap = ref({
  1: [], // 行业分析
  2: [], // 竞争分析
  3: [], // 顾客认知分析
  4: [], // 企业经营分析
  5: [], // 企业家分析
});

const handleCancel = () => {
  router.go(-1);
};

const handleGenerate = () => {
  let valid = true;
  for (const key in form.value) {
    if (
      !key.startsWith("knowledge") &&
      !key.startsWith("internet") &&
      !form.value[key]
    ) {
      errors.value[key] = `${outlineLabels[key]}必填`;
      valid = false;
    }
  }
  if (valid) {
    localStorage.setItem("myForm", JSON.stringify(form.value));
    router.push(`/briefGeneration/add/create/add`);
  }
};

const clearError = (key) => {
  if (form.value[key]) {
    errors.value[key] = "";
  }
};

// 研究阶段选项
const researchStageOptions = [
  { label: "信息收集阶段", value: "0" },
  { label: "简报1.0阶段", value: "1" },
  { label: "简报2.0阶段", value: "2" },
  { label: "简报3.0阶段", value: "3" },
];

// 行业分析框架选项
const industryAnalysisOptions = [{ label: "行业分析框架V1.0", value: "1" }];

// 竞争分析框架选项
const competitionAnalysisOptions = [{ label: "竞争分析框架V1.0", value: "1" }];

// 顾客认知分析框架选项
const customerInsightAnalysisOptions = [
  { label: "顾客认知分析框架V1.0", value: "1" },
];

// 企业经营分析框架选项
const businessOperationAnalysisOptions = [
  { label: "企业经营分析框架V1.0", value: "1" },
];

// 企业家分析框架选项
const entrepreneurAnalysisOptions = [
  { label: "企业家分析框架V1.0", value: "1" },
];
const activeViewIndexes = ref([]);

const handleViewClick = async (index) => {
  // 获取当前选中的分析框架类型
  let analysisStructureType = "";
  switch (index) {
    case 3:
      analysisStructureType = "1"; // 行业分析
      break;
    case 4:
      analysisStructureType = "2"; // 竞争分析
      break;
    case 5:
      analysisStructureType = "3"; // 顾客认知分析
      break;
    case 6:
      analysisStructureType = "4"; // 企业经营分析
      break;
    case 7:
      analysisStructureType = "5"; // 企业家分析
      break;
  }

  // 切换按钮状态
  if (activeViewIndexes.value.includes(index)) {
    // 如果按钮已经是激活状态，则取消激活
    activeViewIndexes.value = activeViewIndexes.value.filter(
      (idx) => idx !== index
    );
  } else {
    // 如果按钮未激活，则激活并调用接口
    activeViewIndexes.value.push(index);

    // 确保analysisStructureType不为空
    if (!analysisStructureType) {
      console.error("分析结构类型不能为空");
      return;
    }

    try {
      const res = await getCatalogList({
        structureVersion: "1",
        analysisStructureType,
      });
      if (res.code === 200) {
        treeDataMap.value[analysisStructureType] = res.data;
      }
    } catch (error) {
      console.error("获取目录列表失败:", error);
    }
  }
};

// 添加一个辅助函数来获取分析结构类型
const getAnalysisStructureType = (index) => {
  switch (index) {
    case 3:
      return "1"; // 行业分析
    case 4:
      return "2"; // 竞争分析
    case 5:
      return "3"; // 顾客认知分析
    case 6:
      return "4"; // 企业经营分析
    case 7:
      return "5"; // 企业家分析
    default:
      return "";
  }
};
onMounted(() => {
  activeStep.value = 1; // 设置当前步骤为配置简报
});
</script>

<style lang="scss" scoped>
.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: #fff;
  .process-bar-wrapper {
    display: flex;
    align-items: center;
    padding: 20px 0;
    background: #fff;
    padding: 13px 25px;
    border-bottom: 1px solid rgba(215, 215, 217, 1);
    .process-title {
      font-size: 16px;
      font-weight: bold;
      color: rgba(100, 94, 94, 1);
      margin-right: 20px;
      letter-spacing: 2px;
      line-height: 23px;
      font-family: SourceHanSansSC-bold;
      flex: none;
    }
  }
  .content-wrapper {
    flex: 1;
    min-height: 0;
    padding: 32px 0;
  }
}
.left {
  flex: 2;
  display: flex;
  flex-direction: column;
  gap: 0;
  background: #fff;
  padding-left: 35px;
}
.input-row {
  display: flex;
  align-items: center;
  margin-bottom: 24px;
  position: relative; /* 添加相对定位以便错误信息显示在输入框下方 */
}
.input-label {
  display: inline-block;
  min-width: 140px;
  color: rgba(132, 132, 132, 1);
  font-size: 14px;
  text-align: left;
  margin-right: 21px;
  line-height: 20px;
  font-family: SourceHanSansSC-regular;
}
.input-main {
  width: 676px;
  height: 36px;
  line-height: 20px;
  border-radius: 4px;
  color: rgba(186, 186, 186, 1);
  font-size: 14px;
  font-family: PingFangSC-regular;
}
.input-main :deep(.el-input__wrapper) {
  border: 1px solid rgba(187, 187, 187, 1);
  box-shadow: none !important;
}
.input-main :deep(.el-select__wrapper) {
  border: 1px solid rgba(224, 224, 224, 1);
  box-shadow: none !important;
  background-color: rgba(224, 224, 224, 0.16) !important;
}
.input-main :deep(.el-input__inner) {
  color: rgba(186, 186, 186, 1);
  font-size: 14px;
  font-family: PingFangSC-regular;
}

/* 下拉框特定样式 */
.el-select.input-main {
  width: 220px !important;
}
.el-select.input-main :deep(.el-input__wrapper) {
  background-color: rgba(224, 224, 224, 0.16) !important;
  border: 1px solid rgba(224, 224, 224, 1);
  border-radius: 6px;
  line-height: 20px;
  text-align: center;
}
.el-select.input-main :deep(.el-input__inner) {
  text-align: center;
}
.eye-btn {
  margin-left: 24px;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(224, 224, 224, 1);
  border: none;
  transition: all 0.3s;
  position: relative;
}
.eye-btn:hover {
  background-color: rgba(224, 224, 224, 0.8);
}
.eye-btn .el-icon {
  font-size: 14px;
  color: rgba(132, 132, 132, 1);
  position: absolute;
  top: 50%;
  left: 55%;
  transform: translate(-50%, -50%) scale(1.2);
  transition: color 0.3s;
}
.eye-btn .el-icon.is-active {
  color: #1890ff;
}
.eye-btn.is-active {
  background-color: rgba(24, 144, 255, 0.1);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}
.divider {
  width: 1px;
  height: 715px;
  background-color: rgba(255, 255, 255, 1);
  border: 1px solid rgba(187, 187, 187, 1);
  margin: auto 0;
  border-radius: 2px;
}
.right {
  width: 456px;
  height: 100%;
  background-color: rgba(255, 255, 255, 1);
  border-radius: 8px;
  padding: 0 20px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  // margin-top: 60px;
  gap: 16px; /* 增加间距以便区分多个树形结构 */
}
.catalog-header {
  font-size: 16px;
  font-weight: bold;
  color: rgba(100, 94, 94, 1);
  letter-spacing: 2px;
  width: 100%;
  line-height: 23px;
  font-family: SourceHanSansSC-bold;
}
.outline-tree {
  width: 100%;
  background: #fff;
  font-size: 15px;
  max-height: 550px;
  overflow-y: auto;
}
.custom-tree ::v-deep .el-tree-node__expand-icon {
  right: 8px !important;
  left: auto !important;
  position: absolute !important;
  height: 32px !important;
  width: 32px !important;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 22px !important;
  background: transparent;
  transition: background 0.2s;
}
.custom-tree ::v-deep .el-tree-node__expand-icon.is-expanded {
  background: #f0f0f0 !important;
  color: #888 !important;
  border-radius: 6px;
}
.custom-arrow {
  font-size: 22px !important;
}
/* 增加树形表每一行高度 */
.custom-tree ::v-deep .el-tree-node__content {
  min-height: 48px !important;
  height: 48px !important;
  line-height: 48px !important;
  font-size: 16px;
}
.switch-row {
  display: flex;
  align-items: center;
  margin-bottom: 28px;
  font-size: 14px;
  color: rgba(132, 132, 132, 1);
  line-height: 20px;
  font-family: SourceHanSansSC-regular;
}
.switch-row span {
  display: inline-block;
  min-width: 140px;
  text-align: left;
  margin-right: 21px;
  padding-left: 10px;
}
.switch-btn {
  position: relative;
  left: 0;
  width: 124px;
  height: 20px;
  line-height: 20px;
  color: rgba(132, 132, 132, 1);
  font-size: 14px;
  text-align: left;
  font-family: SourceHanSansSC-regular;
  padding: 0;
  margin-left: 0;
}
.outline-header {
  margin-bottom: 12px;
  padding: 8px;
  background: #f0f0f0;
  border-radius: 4px;
}
.outline-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
}
.btn-row {
  margin-top: 12px;
  display: flex;
  gap: 107px;
  justify-content: center;
}
.cancel-btn {
  background-color: rgba(224, 224, 224, 1);
  color: rgba(137, 137, 137, 1);
  border: none;
  width: 116px;
  height: 36px;
  border-radius: 6px;
  line-height: 20px;
  font-size: 14px;
  font-family: AlibabaPuHui-medium;
  transition: all 0.3s;
}
.cancel-btn:hover,
.cancel-btn:focus {
  background-color: rgba(146, 32, 245, 1);
  color: #fff;
}
.generate-btn {
  background-color: rgba(146, 32, 245, 1);
  color: #fff;
  border: none;
  width: 116px;
  height: 36px;
  border-radius: 6px;
  line-height: 20px;
  font-size: 14px;
  font-family: AlibabaPuHui-medium;
  transition: all 0.3s;
}
.theme-input {
  height: 70px !important;
}
.theme-input :deep(.el-input__wrapper) {
  height: 70px !important;
}
.theme-input :deep(.el-input__inner) {
  height: 70px !important;
  line-height: 70px !important;
}
.error {
  color: red;
  position: absolute;
  bottom: -15px;
  left: 160px;
  font-size: 12px;
}
</style>
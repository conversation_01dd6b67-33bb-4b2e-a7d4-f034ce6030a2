import request from '@/utils/cleanRequest.js';

/**
 * 上传文档
 * @returns
 */
export function uploadFile(param) {
  return request({
    url: `/previewdish`,
    method: 'post',
    data: param
  });
}

/**
 * 获取脚本列表
 * @param {*} param
 */
export function getScripts(fileName) {
  return request({
    url: `/getsupportscripts`,
    method: 'get',
    params: {
      file_name: fileName
    }
  });
}

/**
 * 清洗
 * @returns
 */
export function clean(data) {
  return request({
    url: `/clean/`,
    method: 'post',
    data: data
  });
}

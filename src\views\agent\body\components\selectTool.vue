<template>

    <div class="select-tool">
        <el-tabs v-model="activeName" type="card" style="position: relative;">
            <el-tab-pane label="自定义" name="custom"></el-tab-pane>
            <el-tab-pane label="内置" name="built-in"></el-tab-pane>
        </el-tabs>
        <div style="position: absolute;right: 10px;top: 3px">
            <el-input placeholder="搜索工具" v-model="queryToolName" style="width: 180px;"
                clearable :prefix-icon="Search"/>
        </div>
        <div class="tool-list">
            <template v-for="apiTool in toolList.apiToolList.filter(item => item.tools.length)">
                <div class="tool-item"  v-show="activeName==='custom' && apiTool.tools.filter(item => {
                            return item.label.zh_Hans.toLowerCase().indexOf(queryToolName.toLowerCase()) != -1 || 
                                item.description.zh_Hans.toLowerCase().indexOf(queryToolName.toLowerCase()) != -1
                        }).length">
                    <div class="item-title">
                        {{ apiTool.label.zh_Hans }}
                    </div>
                    <div class="item-tools">
                        <template v-for="tool in apiTool.tools.filter(item => {
                            return item.label.zh_Hans.toLowerCase().indexOf(queryToolName.toLowerCase()) != -1 || 
                                item.description.zh_Hans.toLowerCase().indexOf(queryToolName.toLowerCase()) != -1
                        })">
                            <select-tool-item
                                :tool="{
                                    provider_id:apiTool.id,
                                    provider_name:apiTool.name,
                                    icon:apiTool.icon,
                                    type:apiTool.type,
                                    label:tool.label.zh_Hans,
                                    name:tool.name,
                                    description:tool.description.zh_Hans,
                                    parameters:tool.parameters
                                }"
                                @select-tool="selectTool"
                                @cancel-select-tool="cancelSelectTool"
                                :selected="selectedToolList.findIndex(item => 
                                    item.provider_id === apiTool.id && 
                                    item.tool_label === tool.label.zh_Hans
                                    ) != -1"/>
                        </template>
                    </div>
                </div>
            </template>
            <template v-for="builtinTool in toolList.builtinToolList.filter(item => item.tools.length)">
                <div class="tool-item"  v-show="activeName==='built-in' && builtinTool.tools.filter(item => {
                            return item.label.zh_Hans.toLowerCase().indexOf(queryToolName.toLowerCase()) != -1 || 
                                item.description.zh_Hans.toLowerCase().indexOf(queryToolName.toLowerCase()) != -1
                        }).length">
                    <div class="item-title">
                        {{ builtinTool.label.zh_Hans }}
                    </div>
                    <div class="item-tools">
                        <template v-for="tool in builtinTool.tools.filter(item => {
                            return item.label.zh_Hans.toLowerCase().indexOf(queryToolName.toLowerCase()) != -1 || 
                                item.description.zh_Hans.toLowerCase().indexOf(queryToolName.toLowerCase()) != -1
                        })">
                            <select-tool-item
                                :tool="{
                                    provider_id:builtinTool.id,
                                    provider_name:builtinTool.name,
                                    icon:builtinTool.icon,
                                    type:builtinTool.type,
                                    label:tool.label.zh_Hans,
                                    name:tool.name,
                                    description:tool.description.zh_Hans,
                                    parameters:tool.parameters
                                }"
                                @select-tool="selectTool"
                                @cancel-select-tool="cancelSelectTool"
                                :selected="selectedToolList.findIndex(item => 
                                    item.provider_id === builtinTool.id && 
                                    item.tool_label === tool.label.zh_Hans
                                    ) != -1"/>
                        </template>
                    </div>
                </div>
            </template>
        </div>
    </div>

</template>

<script setup>
import { 
    getAllBuiltinTools,
    getAllApiTypeTools
} from '@/api/newTool'
import selectToolItem from './selectToolItem'
import {Search} from '@element-plus/icons-vue'

const emits = defineEmits(['change-select-tool'])

const activeName = ref('custom')
const queryToolName = ref('')
const toolList = ref({
    apiToolList:[],
    builtinToolList:[]
})

const selectedToolList = ref([])

const getInitData = () => {
    getAllApiTypeTools().then( res => {
        toolList.value.apiToolList = res
        getAllBuiltinTools().then( res => {
            toolList.value.builtinToolList = res
        })
    })
}

const selectTool = (val) => {
    selectedToolList.value.push({
        enabled:true,
        provider_id:val.provider_id,
        provider_type:val.type,
        provider_name:val.provider_name,
        tool_label:val.label,
        tool_name:val.name,
        tool_parameters:val.parameters
    })
    emits('change-select-tool',selectedToolList.value)
}

const setSelectTool = (val) => {
    selectedToolList.value = val
}

const cancelSelectTool = () => {

}

getInitData()

defineExpose({
    setSelectTool
})

</script>
<style lang="scss" scoped>
.select-tool{
    display: flex;
    flex-direction: column;
    height: 600px;
    position: relative;
}
.tool-list{
    max-height: calc(600px - 100px);
    overflow-y: auto;
    padding: 3px;
    .tool-item{
        .item-title{
            font-size: .75rem;
            color: rgb(102, 112, 133);
        }
        .item-tools{
            display: grid;
            grid-template-columns: repeat(1, minmax(0, 1fr));
            gap: .875rem;
            flex-shrink: 0;
            flex-grow: 1;
            align-content: flex-start;
            padding: 10px 0;
        }
    }
}
.line{
    height: 1px;
    background-color: rgba(0,0,0,.05);
}
</style>
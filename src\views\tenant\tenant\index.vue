<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch"  @submit.native.prevent>
      <el-form-item label="租户域名" prop="tenant">
        <el-input v-model="queryParams.tenant" placeholder="请输入租户域名" clearable @keyup.enter.native="handleQuery"  style="width: 190px;"/>
      </el-form-item>
      <el-form-item label="租户名称" prop="tenantName">
        <el-input v-model="queryParams.tenantName" placeholder="请输入租户名称" clearable @keyup.enter.native="handleQuery" style="width: 190px;" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" :icon="Search" @click="handleQuery">搜索</el-button>
        <el-button :icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          :icon="Plus"
          @click="handleAdd"
          v-hasPermi="['tenant:tenant:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          :icon="Edit"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['tenant:tenant:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          :icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['tenant:tenant:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          :icon="Download"
          @click="handleExport"
          v-hasPermi="['tenant:tenant:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table ref="tables" :border="true" v-loading="loading" :data="tenantList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="编号" prop="id" width="60" />
<!--      <el-table-column label="主键" align="center" prop="id"  width="55" />-->
      <el-table-column label="租户域名" align="left" prop="tenant" width="150" show-overflow-tooltip />
      <el-table-column label="租户名称" align="left" prop="tenantName" width="230" show-overflow-tooltip />
      <el-table-column label="状态" align="center" prop="status" width="80" show-overflow-tooltip>
        <template #default="scope">
          <dict-tag :options="tenantStatusDict" :value="scope.row.status" />
<!--          <span v-if="scope.row.status==1">正常</span>-->
<!--          <span v-else-if="scope.row.status==2">停用</span>-->
        </template>
      </el-table-column>
      <!-- <el-table-column label="同步服务名" align="left" prop="quartzTaskName" width="150" show-overflow-tooltip/> -->
      <el-table-column label="创建时间" align="left" prop="createTime" width="180" show-overflow-tooltip>
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
<!--      <el-table-column label="到期日期" align="left" prop="expirationDate" width="180" show-overflow-tooltip sortable="custom">-->
<!--        <template slot-scope="scope">-->
<!--          <span>{{ parseTime(scope.row.expirationDate, '{y}-{m}-{d}') }}</span>-->
<!--        </template>-->
<!--      </el-table-column>-->
      <el-table-column label="数据库连接URL" align="left" prop="url"  show-overflow-tooltip />
      <el-table-column label="用户名" align="left" prop="username" width="150" show-overflow-tooltip />
      <el-table-column label="密码" align="left" prop="password" width="150" show-overflow-tooltip />
      <el-table-column label="数据库名" align="left" prop="databaseName" width="150" show-overflow-tooltip />
      <el-table-column label="操作" align="center" class-name="small-padding" width="150">
        <template #default="scope">
          <el-button
            size="small"
            type="text"
            :icon="Edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['tenant:tenant:edit']"
          >修改</el-button>
          <el-button
            size="small"
            type="text"
            :icon="Delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['tenant:tenant:remove']"
            class="row-btn-delete"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total>0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />

    <!-- 添加或修改租户管理对话框 -->
    <el-dialog :close-on-click-modal="false" :title="title" v-model="open" style="height: 95vh;" width="40%" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" :label-position="'top'" 
        style="max-height: calc(95vh - 120px);overflow-y: auto;">
        <el-row style="margin: 10px;">
          <el-col :span="24" style="padding: 0 10px">
            <el-divider content-position="left">基础信息</el-divider>
          </el-col>
          <el-col :span="12" style="padding: 0 10px">
            <el-form-item label="租户域名" prop="tenant">
              <el-input v-model="form.tenant" placeholder="请输入租户域名" maxlength="60" />
            </el-form-item>
          </el-col>
          <el-col :span="12" style="padding: 0 10px">
            <el-form-item label="租户名称" prop="tenantName">
              <el-input v-model="form.tenantName" placeholder="请输入租户名称" maxlength="120" />
            </el-form-item>
          </el-col>
          <el-col :span="12" style="padding: 0 10px">
            <el-form-item label="租户标识" prop="tenantIdentity">
              <el-input v-model="form.tenantIdentity" placeholder="请输入租户标识" maxlength="120" />
            </el-form-item>
          </el-col>
          <el-col :span="12" style="padding: 0 10px">
            <el-form-item label="首页名称" prop="loginName">
              <el-input v-model="form.loginName" placeholder="请输入首页名称" maxlength="120" />
            </el-form-item>
          </el-col>
          <el-col :span="24" style="padding: 0 10px">
            <el-form-item label="引导语" prop="clientGuide">
              <el-input v-model="form.clientGuide" type="textarea" :rows="5"  placeholder="请输入引导语" show-word-limit maxlength="500" />
            </el-form-item>
          </el-col>
          <el-col :span="12" style="padding: 0 10px">
            <el-form-item label="状态">
              <el-radio-group v-model="form.status">
                <el-radio
                  v-for="dict in tenantStatusDict"
                  :key="dict.value"
                  :label="dict.value"
                >{{dict.label}}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12" style="padding: 0 10px">
            
          </el-col>
          <el-col :span="12" style="padding: 0 10px">
            <el-form-item label="租户图标" prop="tenantLogo">
              <Image-Upload-Min v-model="form.tenantLogo" :limit="1" ></Image-Upload-Min>
            </el-form-item>
          </el-col>
          <el-col :span="12" style="padding: 0 10px">
            <el-form-item label="登录背景" prop="loginImg">
              <Image-Upload-Min v-model="form.loginImg" :limit="1" ></Image-Upload-Min>
            </el-form-item>
          </el-col>
          <!-- <el-col :span="12" style="padding: 0 10px">
            <el-form-item label="同步服务名" prop="quartzTaskName">
              <el-input v-model="form.quartzTaskName" placeholder="请输入同步服务名" maxlength="120" />
            </el-form-item>
          </el-col> -->
          <el-col :span="24" style="padding: 0 10px">
            <el-divider content-position="left">数据储存</el-divider>
          </el-col>
          <el-col :span="12" style="padding: 0 10px">
            <el-form-item label="数据库名" prop="databaseName">
              <el-input v-model="form.databaseName" placeholder="请输入数据库名" maxlength="60" />
            </el-form-item>
          </el-col>
          <el-col :span="12" style="padding: 0 10px">
            <el-form-item label="数据库连接URL" prop="url">
              <el-input v-model="form.url" placeholder="请输入数据库连接URL" maxlength="250" />
            </el-form-item>
          </el-col>
          <el-col :span="12" style="padding: 0 10px">
            <el-form-item label="用户名" prop="username">
              <el-input v-model="form.username" placeholder="请输入用户名" maxlength="120" />
            </el-form-item>
          </el-col>
          <el-col :span="12" style="padding: 0 10px">
            <el-form-item label="密码" prop="password">
              <el-input v-model="form.password" placeholder="请输入密码" maxlength="120" />
            </el-form-item>
          </el-col>
          <el-col :span="12" style="padding: 0 10px">
            <el-form-item label="文件储存路径" prop="baseFilePath">
              <el-input v-model="form.baseFilePath" placeholder="请输入文件储存路径" maxlength="120" />
            </el-form-item>
          </el-col>
          <el-col :span="12" style="padding: 0 10px">
            <el-form-item label="缓存key" prop="redisKey">
              <el-input v-model="form.redisKey" placeholder="请输入缓存key" maxlength="250" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { listTenant, getTenant, delTenant, addTenant, updateTenant } from "@/api/tenant/tenant";
import { Plus, Edit, Delete, Download, Refresh, Search } from '@element-plus/icons-vue';

export default {
  name: "Tenant",
  setup() {
    return {
      Plus, Edit, Delete, Download, Refresh, Search
    }
  }, 
  data () {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 租户管理表格数据
      tenantList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 默认排序
      // defaultSort: { prop: 'createTime', order: 'descending' },
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        tenant: null,
        tenantName: null
      },
      // 表单参数
      form: {
        status: "1"
      },
      // 表单校验
      rules: {
        tenant: [
          { required: true, trigger: "blur", message: "请输入租户" },
        ],
        tenantName: [
          { required: true, trigger: "blur", message: "请输入租户名称" },
        ],
        url: [
          { required: true, trigger: "blur", message: "请输入数据库连接URL" },
        ],
        username: [
          { required: true, trigger: "blur", message: "请输入用户名" },
        ],
        password: [
          { required: true, trigger: "blur", message: "请输入密码" },
        ],
        databaseName: [
          { required: true, trigger: "blur", message: "请输入数据库名" },
        ],
        baseFilePath: [
          { required: true, trigger: "blur", message: "请输入文件储存路径" },
        ],
        redisKey: [
          { required: true, trigger: "blur", message: "请输入缓存key" },
        ],
      },
      // 租户状态字典
      tenantStatusDict: {}
    };
  },
  created () {
    this.getList();

    const { sys_tenant_status: tenantStatusDict } = this.useDict('sys_tenant_status');
    this.tenantStatusDict = tenantStatusDict;
  },
  watch: {
    form: {
      handler() {
        if (this.form.databaseName && this.form.databaseName.match(/[\u4E00-\u9FA5]+/)) {
          this.form.databaseName = this.form.databaseName.replace(/\[\u4E00-\u9FA5]/g, '');
        }
        if (this.form.redisKey && this.form.redisKey.match(/[\u4E00-\u9FA5]+/)) {
          this.form.redisKey = this.form.redisKey.replace(/[\u4E00-\u9FA5]+/g, '');
        }
        if (this.form.url && this.form.url.match(/[^a-zA-Z0-9_\-.!@#$%^&'()*+,\/:;=?@\[\]]/)) {
          this.form.url = this.form.url.replace(/[^a-zA-Z0-9_\-.!@#$%^&'()*+,\/:;=?@\[\]]/g, '');
        }
        if (this.form.tenant && this.form.tenant.match(/[^a-zA-Z0-9_\-.!@#$%^&'()*+,\/:;=?@\[\]]/)) {
          this.form.tenant = this.form.tenant.replace(/[^a-zA-Z0-9_\-.!@#$%^&'()*+,\/:;=?@\[\]]/g, '');
        }
      },
      deep: true
    }
  },
  methods: {
    /** 查询租户管理列表 */
    getList () {
      this.loading = true;
      listTenant(this.queryParams).then(response => {
        this.tenantList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel () {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset () {
      this.form = {
        id: null,
        tenant: null,
        url: null,
        username: null,
        password: null,
        databaseName: null,
        tenantName: null,
        quartzTaskName: null,
        createTime: null,
        status: "0",
        expirationDate: null,
        // isHospital: '0',
        tenantIdentity:null,
        tenantLogo:null,
        loginImg:null,
        loginName:null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery () {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery () {
      this.resetForm("queryForm");
      // this.$refs.tables.sort(this.defaultSort.prop, this.defaultSort.order)
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange (selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 排序触发事件 */
    // handleSortChange (column, prop, order) {
    //   this.queryParams.orderByColumn = column.prop;
    //   this.queryParams.isAsc = column.order;
    //   this.getList();
    // },
    /** 新增按钮操作 */
    handleAdd () {
      this.reset();
      this.form.status="0";
      this.open = true;
      this.title = "添加租户管理";
    },
    /** 修改按钮操作 */
    handleUpdate (row) {
      this.reset();
      const id = row.id || this.ids
      getTenant(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改租户管理";
      });
    },
    /** 提交按钮 */
    submitForm () {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateTenant(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addTenant(this.form).then(response => {
              // console.log(response);
              if(response.code != 200){
                this.$modal.msgError("租户已存在");
                return false;
              }
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete (row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除租户编号为"' + ids + '"的数据项？').then(function() {
        return delTenant(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {
      });
    },
    /** 导出按钮操作 */
    handleExport () {
      this.download('tenant/tenant/export', {
        ...this.queryParams
      }, `tenant_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
<style lang="scss" scoped>
:deep(.el-dialog:not(.is-fullscreen)) {
	margin-top: 3vh !important;
}

.row-btn-delete {
  margin-left: 0;
}
</style>

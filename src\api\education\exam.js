import request from "@/utils/request"

// 获取试卷详情
export function getPaper (params) {
  return request({
    url: "/paper/student",
    method: "get",
    params,
  })
}

// 试卷查询
export function testPaperQuery (params) {
  return request({
    url: '/paper/list',
    method: 'get',
    params
  })
}
// 获取试卷列表 全部
export function getPaperListAll (params) {
  return request({
    url: '/paper/listAll',
    method: 'get',
    params
  })
}

// 试卷删除
export function testDelete (data) {
  return request({
    url: '/paper/delete',
    method: 'post',
    data
  })
}

// 状态修改
export function testUpdate (data) {
  return request({
    url: '/paper/update/status',
    method: 'post',
    data
  })
}

// 发布修改
export function publishUpdate (data) {
  return request({
    url: '/paper/publish',
    method: 'post',
    data
  })
}


// 试卷详情
export function testPaperDetails (params) {
  return request({
    url: `/paper/one`,
    method: 'get',
    params
  })
}

// 试卷保存
export function savePapers (data) {
  return request({
    url: '/paper/save',
    method: 'post',
    data
  })
}

// 获取学生答题详情
export function getStudentAnswer (params) {
  return request({
    url: "/paper/students/answer",
    method: "get",
    params,
  })
}

// 获取学生答卷分析
export function getStudentAnswerAnalysis (params) {
  return request({
    url: "/paper/student/analysis",
    method: "get",
    params,
  })
}

// 获取学生各综合分析
export function getStudentOverallAnalysis (params) {
  return request({
    url: "/paper/total/analysis",
    method: "get",
    params,
  })
}

// 修改学生某道答题的分数
export function updateStudentAnswerScore (data) {
  return request({
    url: '/paper/score/update',
    method: 'post',
    data
  })
}
//  获取批阅历史  /paper/teacher/read/over?answerId=81
export function getPaperTeacherReadOver (params) {
  return request({
    url: '/paper/teacher/read/over',
    method: 'get',
    params
  })
}
// 获取试卷分析列表  /paper/analyse/list?paperId=65&className=&studentName=
export function getPaperAnalyse (params) {
  return request({
    url: "/paper/analyse/list",  
    method: "get",
    params
  })
}

// 总分析 
export function getPaperAnalyseAll (paperId) {
  return request({
    url: "paper/total/analysis?paperId=" + paperId,
    method: "get",
  })
}

// 提交答案(答题)
export function commitAnswer (data) {
  return request({
    url: '/paper/commit',
    method: 'post',
    data
  })
}

// 学生答题列表 
export function getStudentAnswerList (params) {
  return request({
    url: "/paper/student/answer/list",
    method: "get",
    params
  })
}

// 学生试卷概览
export function getStudentOverview (params) {
  return request({
    url: "/paper/student/overview",
    method: "get",
    params
  })
}
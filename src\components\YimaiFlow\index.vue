<template>
    <div style="width: 100%;height: 100%;">
        <template v-if="iframeVisible">
            <iframe style="border:none" ref="graphIframe" src="" height="100%" width="100%">

            </iframe>
        </template>
    </div>
</template>
<script setup>
import {getTeamInfo} from '@/api/teamManagement/team'
import { getToken } from "@/utils/auth";
const props = defineProps({
    id:{
        type:String,
        required:true
    }
})

const graphIframe = ref(null)

const iframeVisible = ref(null)

const createIframe = () => {
    getTeamInfo(props.id).then(res => {
        const app_id = res.data.appId
        iframeVisible.value = true
        nextTick(() => {
            graphIframe.value.src = 
                '/yimai-graph-engines/graph?app_id='+ 
                    app_id + 
                    '&token=' + getToken()+ 
                    '&agent_id=' + props.id
        })
    })
}

onMounted(() => {
    createIframe()
})

</script>
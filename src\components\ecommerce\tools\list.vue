<template>
    <div>
        <div v-for="stage in stageList">
            <div style="line-height: 48px;font-size: 20px;margin-left: 20px;margin-top: 20px;">
                {{ stage.name }}
            </div>
            <div v-loading="loading">
                <el-row>
                    <div v-for="(item, index) in toolList.filter(item => item.toolStage == stage.key)" :key="item"
                        style="margin: 20px;max-width: 20%;flex: 0 0 20%;" @click="toolUse(item)">
                        <el-card :body-style="{ padding: '0px' }">
                            <div style="height: 320px;">
                                <div slot="header" class="clearfix">
                                    <img :src="baseUr1 + '/' + item.toolImage" style="width: 48px;">
                                    <el-tooltip content="编辑" placement="top" effect="light">
                                        <el-icon style="float: right; padding: 3px 0;cursor: pointer;"
                                            @click="editTool(item)" onclick="event.cancelBubble=true">
                                            <Operation />
                                        </el-icon>
                                    </el-tooltip>
                                </div>

                                <div style="line-height: 48px;font-size: 16px;font-weight: 600;cursor: pointer;">{{
                                    item.toolName }}</div>
                                <div
                                    style="line-height: 24px;font-size: 14px;overflow: hidden;text-overflow: ellipsis;cursor: pointer;">
                                    {{ item.toolDesc }}
                                </div>
                            </div>
                        </el-card>
                    </div>
                </el-row>
            </div>
        </div>

        <el-dialog v-model="editToolStepVisible" width="90%" style="height: calc(90vh);">
            <editStep1 v-if="editToolStep == 1" @nextStep="editToolGo" v-bind:toolInfo="toolInfo"></editStep1>
            <editStep2 v-if="editToolStep == 2" @nextStep="editToolGo" @refreshList="getToolsList"
                v-bind:toolInfo="toolInfo"></editStep2>
        </el-dialog>


        <el-dialog v-model="useToolDisable" width="90%" v-if="useToolDisable">
            <use-tool :userInfo="userToolInfo"></use-tool>
        </el-dialog>
    </div>
</template>

<script setup  name="ecommerceToolEdit1">
import { useRoute, useRouter } from 'vue-router';

const baseUr1 = import.meta.env.VITE_APP_BASE_API

import { ref, reactive, toRefs } from 'vue'
import editStep1 from './toolEditStep1'
import editStep2 from './toolEditStep2'
import { getToolList, toolEdit, getToolInfo } from "@/api/ecommerce/tool";
import AgentDrawer from '@/components/AgentDrawer/index'
import useTool from '@/views/ecommerce/toolAndSceneUse/use'

const { proxy } = getCurrentInstance();

const loading = ref(false)

const router = useRouter()
const query = reactive(router.currentRoute.value.query)

//会话使用
const useToolDisable = ref(false)
//使用会话要用到的参数
const userToolInfo = ref({})

const editToolStepVisible = ref(false)

const editToolStep = ref(1)

const editToolGo = (val) => {
    editToolStep.value = val
}

const stageList = ref([
    {
        key: 0,
        name: '创立阶段'
    },
    {
        key: 1,
        name: '发展阶段'
    },
    {
        key: 2,
        name: '稳定阶段'
    },
    {
        key: 3,
        name: '拓展阶段'
    },
    {
        key: 4,
        name: '其他'
    },
])

//搜索
const queryParams = ref({
    toolName: '',
    pageNum: 1,
    pageSize: 15,
});
const total = ref(0);
//编辑、新增工具弹框开关
const toolVisible = ref(false);
//编辑、新增工具弹框标题
const toolTitle = ref('');
//工具信息
const toolInfo = ref({
    id: 0,
    toolImage: '',
    toolName: '',
    toolDesc: '',
    toolStage: '',
    contentType: '',
    prompt: '',
    toolType: '',
    ecommerceToolKeys: []
});

//工具列表
const toolList = ref([])

//编辑
const editTool = (tool) => {
    editToolStep.value = 1
    editToolStepVisible.value = true
    toolInfo.value = JSON.parse(JSON.stringify(tool))
}

//取消
const cancel = () => {
    toolVisible.value = false;
    reset()
}

//创建工具
const createTool = () => {
    reset()
    toolTitle.value = '新建工具';
    toolVisible.value = true;
}

//工具列表
const getToolsList = () => {
    if (query.shopId && query.shopId > 0) {
        loading.value = true;
        getToolList(1, query.shopId).then(response => {
            toolList.value = response.rows;
            total.value = response.total;
            loading.value = false;
        })
    } else {
        router.push('/shop')
    }
}

//工具使用
const toolUse = (item) => {
    userToolInfo.value = {
        shopId: query.shopId,
        toolId: item.id,
        //1.文字 2.图文 3.视频脚本
        contentType: item.contentType,
        //1.通用工具 2.场景工具
        toolType: item.toolType,
        toolName: item.toolName,
        toolDesc: item.toolDesc,
        toolImage: item.toolImage
    }
    useToolDisable.value = true
}

//获取工具列表
getToolsList()
</script>


<style lang='scss' scoped>
.bottom {
    margin-top: 13px;
    line-height: 12px;
}

.button {
    padding: 0;
    float: right;
}

.image {
    width: 100%;
    display: block;
}

.clearfix:before,
.clearfix:after {
    display: table;
    content: "";
}

.clearfix:after {
    clear: both
}
</style>
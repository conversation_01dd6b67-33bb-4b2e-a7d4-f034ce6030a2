<template>
  <div class="header">
    <div class="left">
      <div class="crumbs-wrapper">
        <div v-for="(crumb, idx) of crumbs" :key="crumb" class="crumb">
          <component :is="crumb.component" v-if="crumb.name == '组件' && crumb.component" />
          <div  v-else>{{ crumb.name }}</div>
          {{ idx < crumbs.length - 1 ? '|' : '' }} 
        </div>
      </div>
    </div>
    <div class="right">
      <user />
    </div>
  </div>
</template>

<script setup>
import { onMounted } from 'vue';
import User from './user.vue'

const props = defineProps({
  crumbs: {
    type: Array,
    default: () => []
  }
})
onMounted(() => {
  console.log(props.crumbs);
})
</script>

<style lang="scss" scoped>
.header {
  height: 80px;
  width: calc(100% - 20px);
  margin-left: auto;
  margin-right: auto;
  padding: 0;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #00E5FF;

  .left {
    font-size: 14px;
    color: #929499;
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;

    .crumbs-wrapper {
      height: 16px;
      display: flex;
      flex-direction: row;
      justify-content: flex-start;
      align-items: center;

      .crumb {
        // display: block;
        height: 16px;
        line-height: 16px;
        color: #EAEAEA;
        font-size: 14px;
        display: flex;
        align-items: center;
      }

      & > div:last-child {
        color: #6AF6FF;
      }
    }
  }

  .right {
    padding-right: 27px;
    display: flex;
    flex-direction: row;
    justify-content: flex-end;
    align-items: center;
  }
}
</style>
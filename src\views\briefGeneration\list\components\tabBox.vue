<template>
  <div class="tab-container">
    <div
      :class="{ 'tab-item': true, active: checked == v.value }"
      v-for="(v, i) in tabs"
      :key="i"
      @click="
        () => {
          handleUpdate(v.value);
        }
      "
    >
      {{ v.icon && v.icon }}
      <div class="tab-name">{{ v.name }}</div>
    </div>
  </div>
</template>
<script setup>
import { ref, reactive, toRefs, onMounted } from "vue";
const props = defineProps({
  tabs: {
    type: Array,
    required: true,
    defaults: [],
  },
  checked: {
    type: Number,
    required: true,
    defaults: 0,
  },
});
const emit = defineEmits(["update:checked"]);
const handleUpdate = (val) => {
  emit("update:checked", val);
};
</script>
<style lang='scss' scoped>
.tab-container {
  display: flex;
  align-items: center;
  border: 1px solid #148d9a;
  border-radius: 4px;
  .tab-item {
    padding: 5px 19px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    color: #4bafff;
    .tab-name {
      font-family: HarmonyOS Sans SC;
      font-weight: 400;
      font-size: 14px;
      height: 20px;
    }
    &:hover {
      background: #148d9a;
      color: #fff;
    }
  }
  .active {
    background: #148d9a;
    color: #fff;
  }
}
</style>
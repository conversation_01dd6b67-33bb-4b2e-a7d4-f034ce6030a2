<template>
  <div class="design-page">
    <div class="content" ref="contentRef">
      <template v-if="paragraphs.length === 0 && competitorStr.trim().length === 0">
        <div style="
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            font-size: 14px;
            color: #fff;
            line-height: 14px;
            margin: 0 atuo;
            text-align: center;
            padding-top: 50px;
          ">
          {{ startGenerating || competitorLoad ? "正在为你生成简报..." : "暂无内容" }}
        </div>
      </template>
      <div class="competitorBox" v-show="competitorStr.trim().length > 0 || ishumanInput">
        <md :cnt="competitorStr" :enableHtml="true" class="markdown-cnt"></md>
        <competitor @scroll-Bottom="scrollToBottom" />
      </div>
      <div ref="scrollRef" class="scrollBox">
        <template
          v-for="(paragraphsItem, paragraphsIndex) in paragraphs"
          :key="paragraphsIndex"
        >
          <Paragraph
            v-for="(item, index) in paragraphsItem"
            :key="index"
            :topId="menuList[paragraphsIndex].id"
            :nodeId="`section${paragraphsIndex}-${index}`"
            :parentIndex="paragraphsIndex"
            :title="item.title"
            :index="`${paragraphsIndex}-${index}`"
            :mode="item.mode"
            :think="item.think"
            :type="item.type"
            v-model:content="item.content"
            v-model:subSections="item.subSections"
            :collapse-status="collapseStatus"
            @update:title="(idx, newTitle) => handleTitleUpdate(idx, newTitle)"
            @add-sibling="handleAddSibling"
            @add-child="handleAddChild"
            @delete-section="handleDeleteSection"
            :readonly="pageType === 'view'"
          >
          </Paragraph>
        </template>
      </div>
    </div>
    <!-- <competitorDialog /> -->

    <div class="btnBg" title="终止生成" @click="stop"
      :style="{ cursor: startGenerating || competitorLoad ? 'pointer' : 'not-allowed' }"
      v-show="pageType == 'add' && (startGenerating || competitorLoad)">
      <i class="ri-stop-fill"></i>
    </div>
  </div>
</template>

<script setup>
import { ref } from "@vue/reactivity";
import { ElMessageBox, ElMessage } from "element-plus";
import Paragraph from "./paragraph.vue";
import { sseRequest } from "@/utils/request.js";
import {
  paragraphs,
  // saveParagraphs,
  startGenerating,
  operatingTextIndex,
  menuList,
  scrollRef,
  // form,
  pageType,
  pageId,
  configData,
  ishumanInput,
  competitorParams,
  top5Options,
  competitorStr,
  competitorLoad,
  workflowtaskId,
  workflowtaskChildId,
  workflowIndex,
  completedWorkflows,
  resetForm
} from "@/views/briefGeneration/add/create/js/sharedState.js";
import competitorDialog from "./competitorDialog.vue";
import competitor from "./competitor.vue";
import { useRoute, useRouter } from "vue-router";
import { onUnmounted, onMounted, nextTick, watch } from "vue";
import { getCatalogList } from "@/api/briefGeneration/catalog.js";
import { getAppSecretKey, workflowtaskStatus } from "@/api/briefGeneration/index.js";
import Md from "@/components/Markdown";

const route = useRoute();
const generatedTaskId = ref("");
const generatedCtrl = ref(null);
const router = useRouter();
const collapseStatus = reactive({});
// 定义content的引用
const contentRef = ref(null);

const appSecretKeyList = ref([]);
let timer = null; //监听竞争分析后台工作流任务状态的定时器
let jingzhengIndex = -1; //竞争索引
let zongjieIndex = -1; //五维总结索引
const onlyjz = computed(() => {
  return menuList.value.length == 1 && jingzhengIndex != -1;
});
function scrollToBottom() {
  // 滚动到底部
  if (contentRef.value) {
    contentRef.value.scrollTop = contentRef.value.scrollHeight;
  }
}

function processMarkdownContent(markdownContent, menuIndex) {
  const lines = markdownContent.split("\n");
  // 不清空paragraphs，而是从lastParagraphCount开始处理新内容
  // paragraphs.value = []; // 注释掉这行，保留之前的数据

  // 移除从lastParagraphCount开始的所有段落，然后重新处理当前content
  paragraphs.value[menuIndex] = [];

  let currentSection = null; // 一级菜单
  let currentSubSection = null; // 二级菜单
  let currentThirdSection = null; // 三级菜单
  let currentFourSection = null; // 四级菜单
  let inThink = false;
  let currentIndex = -1;
  let contentIndex = -1;

  lines.forEach((line) => {
    if (line.includes("<think>")) {
      contentIndex = -1;
      inThink = true;
      paragraphs.value[menuIndex].push({
        title: "",
        think: "",
        mode: "view",
        type: "think",
        subSections: [],
      });
      currentIndex = paragraphs.value[menuIndex].length - 1;
      let afterTag = line.substring(line.indexOf("<think>") + 7);
      // 使用全局索引来生成nodeId，确保ID的连续性
      const nodeId = `section${menuIndex}-${currentIndex}`;
      collapseStatus[nodeId] = ["collapse"];
      if (afterTag.includes("</think>")) {
        paragraphs.value[menuIndex][currentIndex].think =
          (paragraphs.value[menuIndex][currentIndex].think || "") +
          afterTag.substring(0, afterTag.indexOf("</think>"));
        inThink = false;
        collapseStatus[nodeId] = [];
      } else {
        paragraphs.value[menuIndex][currentIndex].think =
          (paragraphs.value[menuIndex][currentIndex].think || "") +
          afterTag;
      }
      return;
    }

    // 2. <think> 内内容
    if (inThink) {
      contentIndex = -1;
      // 使用全局索引来生成nodeId，确保ID的连续性
      const nodeId = `section${menuIndex}-${currentIndex}`;
      if (line.includes("</think>")) {
        paragraphs.value[menuIndex][currentIndex].think =
          (paragraphs.value[menuIndex][currentIndex].think || "") +
          "\n" +
          line.substring(0, line.indexOf("</think>"));
        inThink = false;
        collapseStatus[nodeId] = [];
      } else {
        paragraphs.value[menuIndex][currentIndex].think =
          (paragraphs.value[menuIndex][currentIndex].think || "") +
          "\n" +
          line;
      }
      return;
    }

    if (/^# \s*/.test(line)) {
      contentIndex = -1;
      // 一级菜单，单独一条数据
      const title = line.replace(/^#\s*/, "");
      let thinkContent = "";
      if (currentIndex !== -1) {
        thinkContent =
          paragraphs.value[menuIndex][currentIndex].think.trim();
        paragraphs.value[menuIndex].splice(currentIndex, 1);
        currentIndex = -1;
      }
      currentSection = {
        title,
        think: thinkContent,
        content: null,
        mode: "view",
        type: 1,
        subSections: [],
      };
      paragraphs.value[menuIndex].push(currentSection);
      // 关键：一级菜单后，二级、三级都不再归属于它
      // currentSection = null;
      currentSubSection = null;
      currentThirdSection = null;
      currentFourSection = null;
      return;
    } else if (/^## \s*/.test(line)) {
      contentIndex = -1;
      // 二级菜单，直接 push 到顶层
      const title = line.replace(/^##\s*/, "");
      let thinkContent = "";
      if (currentIndex !== -1) {
        thinkContent =
          paragraphs.value[menuIndex][currentIndex].think.trim();
        paragraphs.value[menuIndex].splice(currentIndex, 1);
        currentIndex = -1;
      }
      currentSubSection = {
        title,
        think: thinkContent,
        content: null,
        mode: "view",
        type: 2,
        subSections: [],
      };
      paragraphs.value[menuIndex].push(currentSubSection);
      currentThirdSection = null;
      currentFourSection = null;
      return;
    } else if (/^### \s*/.test(line)) {
      contentIndex = -1;
      // 三级菜单，还是作为二级的子级
      const subTitle = line.replace(/^###\s*/, "");
      let thinkContent = "";
      if (currentIndex !== -1) {
        thinkContent =
          paragraphs.value[menuIndex][currentIndex].think.trim();
        paragraphs.value[menuIndex].splice(currentIndex, 1);
        currentIndex = -1;
      }
      currentThirdSection = {
        title: subTitle,
        think: thinkContent,
        content: null,
        type: 3,
        mode: "view",
        subSections: [],
      };
      if (currentSubSection) {
        currentSubSection.subSections.push(currentThirdSection);
      } else {
        // 没有二级菜单时，直接挂到顶层
        paragraphs.value[menuIndex].push(currentThirdSection);
      }
      currentFourSection = null;
      return;
    } else if (/^#### \s*/.test(line)) {
      contentIndex = -1;
      // 四级菜单，还是作为三级的子级
      const subTitle = line.replace(/^####\s*/, "");
      let thinkContent = "";
      if (currentIndex !== -1) {
        thinkContent =
          paragraphs.value[menuIndex][currentIndex].think.trim();
        paragraphs.value[menuIndex].splice(currentIndex, 1);
        currentIndex = -1;
      }
      currentFourSection = {
        title: subTitle,
        think: thinkContent,
        content: null,
        type: 4,
        mode: "view",
      };
      if (currentThirdSection) {
        currentThirdSection.subSections.push(currentFourSection);
      } else if (currentSubSection) {
        currentSubSection.subSections.push(currentFourSection);
      } else {
        // 没有二级菜单时，直接挂到顶层
        paragraphs.value[menuIndex].push(currentFourSection);
      }
      return;
    } else {
      // 有标题的情况 - 保留所有行（包括空行）
      if (currentFourSection) {
        contentIndex = -1;
        currentFourSection.content =
          (currentFourSection.content || "") + line + "\n";
      } else if (currentThirdSection) {
        contentIndex = -1;
        currentThirdSection.content =
          (currentThirdSection.content || "") + line + "\n";
      } else if (currentSubSection) {
        contentIndex = -1;
        currentSubSection.content =
          (currentSubSection.content || "") + line + "\n";
      } else if (currentSection) {
        currentSection.content = (currentSection.content || "") + line + "\n";
      } else {
        // 无标题的情况
        if (contentIndex == -1) {
          let thinkContent = "";
          if (currentIndex !== -1) {
            thinkContent =
              paragraphs.value[menuIndex][currentIndex].think.trim();
            paragraphs.value[menuIndex].splice(currentIndex, 1);
            currentIndex = -1;
          }
          paragraphs.value[menuIndex].push({
            title: "",
            think: thinkContent,
            content: null,
            type: null,
            mode: "view",
          });
        }
        contentIndex = paragraphs.value[menuIndex].length - 1;
        paragraphs.value[menuIndex][contentIndex].content =
          (paragraphs.value[menuIndex][contentIndex].content || "") +
          line +
          "\n";
      }
    }
  });
}

function extractType2Sections(paragraphs = [], menuIndex) {
  let arr = paragraphs
    .map((p, idx) => ({
      id: `/section${menuIndex}-${idx}`,
      name: p.title || `段落${idx + 1}`,
      type: p.type,
      children: (p.subSections || [])
        .map((sub, subIdx) => ({
          id: `/section${menuIndex}-${idx}-${subIdx}`,
          name: sub.title || `子段落${subIdx + 1}`,
          type: sub.type,
        }))
        .filter((sub) => sub.type == 3),
    }))
    .filter((p) => p.type == 2);
  return arr || [];
}

// 记录每轮开始前的段落数
const parallelControllers = ref([]); // 存储并行工作流的控制器

// 开始并行工作流处理
function startParallelWorkflows() {
  console.log('开始并行工作流处理');

  // 重置状态
  parallelControllers.value = [];
  completedWorkflows.value = [];

  // 找出战略方向预判的工作流索引（作为最后一项）
  // 获取非战略方向预判与非竞争工作流
  const nonStrategicWorkflows = menuList.value
    .map((item, index) => ({ item, index }))
    .filter(({ item, index }) => index !== zongjieIndex && index !== jingzhengIndex);
  // 并行启动非战略方向预判,竞争的工作流
  nonStrategicWorkflows.forEach(({ index }) => {
    startSingleWorkflow(index);
  });

  // 如果有战略方向预判工作流，等其他工作流完成后再启动
  if (zongjieIndex && zongjieIndex !== -1) {
    console.log(`战略方向预判工作流索引: ${zongjieIndex}, 将在其他工作流完成后启动`);
  }

}

// 启动单个工作流
function startSingleWorkflow(index) {
  console.log(`启动工作流 ${index}: ${menuList.value[index]?.name}`);

  const currentMenuItem = menuList.value[index];
  if (!currentMenuItem) {
    console.error(`工作流 ${index} 不存在`);
    return;
  }

  // 设置状态为加载中
  currentMenuItem.status = 'load';

  // 重置内容
  currentMenuItem.content = '';

  // 创建控制器
  const controller = new AbortController();
  parallelControllers.value[index] = controller;

  // 根据当前menuList项的id获取对应的appSecretKey
  const appSecretKey = getAppSecretKeyById(currentMenuItem.id);
  console.log(`工作流 ${index} 使用 appSecretKey: ${appSecretKey}`);

  const touchesObj = {
    response_mode: "streaming",
    // inputs: JSON.stringify({
    // }),
    query: configData.value.brandName,
    appSecretKey: appSecretKey,
  };
  sseRequest(
    "/proxy/chat",
    touchesObj,
    (event) => onParallelGeneratingMessage(event, index),
    () => onParallelWorkflowClose(index),
    (error) => onParallelWorkflowError(error, index),
    controller.signal
  );
}

// 并行工作流消息处理
function onParallelGeneratingMessage(event, index) {
  if (!startGenerating.value) {
    return;
  }
  const data = event.data && JSON.parse(event.data);

  if (data.event === "message" && data.answer) {
    // 将数据追加到对应menuList项的content字段
    if (menuList.value[index]) {
      menuList.value[index].content += data.answer;
      if (workflowIndex.value == index) {
        processMarkdownContent(menuList.value[workflowIndex.value].content, workflowIndex.value);
      }
    }
  }

  // 人工介入处理
  if (data.event == "node_finished" && data.data.node_type == "human-input-text") {
    competitorParams.value.node_id = data.data.node_id;
    competitorParams.value.conversation_id = data.conversation_id;
    top5Options.value = data.data.inputs.question.split(";").slice(0, 5);
    ishumanInput.value = true;
  }

  scrollToBottom();
}

// 并行工作流关闭处理
function onParallelWorkflowClose(index) {
  console.log(`工作流 ${index} 运行完成`);

  if (menuList.value[index]) {
    menuList.value[index].status = 'end';
    completedWorkflows.value.push(index);

    console.log(`工作流 ${index} 已完成，总完成数: ${completedWorkflows.value.length}`);

    // 检查是否所有非战略工作流都完成了
    menuList.value.length > 1 && checkAndStartStrategicWorkflow();
  }
  if (workflowIndex.value === index) {
    // 更新所有已处理的menuList项的children
    menuList.value.forEach((menuItem, index) => {
      if (paragraphs.value[index] && paragraphs.value[index].length > 0) {
        menuItem.children = extractType2Sections(paragraphs.value[index], index);
      }
    });
    workflowIndex.value = index + 1;
  }
}
watch(workflowIndex, (newValue, oldValue) => {
  if (newValue !== oldValue) {
    if (newValue >= menuList.value.length) {
      console.log('所有工作流内容处理完成', 'menuList.value :>> ', menuList.value, 'paragraphs.value', paragraphs.value);
      startGenerating.value = false;
      return;
    }
    if (newValue && (jingzhengIndex != -1) && newValue == jingzhengIndex) {
      workflowIndex.value = newValue + 1;
      return;
    }
    if (menuList.value[newValue] && menuList.value[newValue].status == 'end') {
      processMarkdownContent(menuList.value[newValue].content, newValue);
      menuList.value.forEach((menuItem, index) => {
        if (paragraphs.value[index] && paragraphs.value[index].length > 0) {
          menuItem.children = extractType2Sections(paragraphs.value[index], index);
        }
      });
      workflowIndex.value = newValue + 1;
    }
  }
});
// 并行工作流错误处理
function onParallelWorkflowError(error, index) {
  console.error(`工作流 ${index} 出现错误:`, error);
  if (menuList.value[index]) {
    menuList.value[index].content = '';
    // menuList.value[index].status = 'wait';
  }
}



// 检查并启动战略工作流
function checkAndStartStrategicWorkflow() {
  if (!zongjieIndex && zongjieIndex === -1) return;

  // 检查所有非战略工作流是否都完成了
  const nonStrategicCompleted = menuList.value.every((item, index) => {
    if (index === zongjieIndex) return true; // 跳过战略工作流本身
    return item.status === 'end';
  });

  if (nonStrategicCompleted && menuList.value[zongjieIndex].status === 'wait') {
    console.log('所有非战略工作流已完成，启动战略方向预判工作流');
    startSingleWorkflow(zongjieIndex);
  }
}


function stop() {
  if (startGenerating.value || competitorLoad.value) {
    // 终止所有并行工作流
    parallelControllers.value.forEach((controller, index) => {
      if (controller) {
        controller.abort();
        console.log(`终止工作流 ${index}`);
      }
    });

    generatedCtrl.value && generatedCtrl.value.abort(); // 终止请求（可选）
    startCtrl.value && startCtrl.value.abort(); // 终止请求（可选）
    generatedTaskId.value = "";
    startGenerating.value = false;
    competitorLoad.value = false;
    competitorStr.value = ``;
    ishumanInput.value = false;
    workflowtaskId.value = null;
    workflowtaskChildId.value=null;
    if (timer) {
      clearInterval(timer);
      timer = null;
    }

    // 重置并行工作流状态
    parallelControllers.value = [];
    completedWorkflows.value = [];
    console.log('menuList.value :>> ', menuList.value, 'paragraphs.value', paragraphs.value);
    // 更新所有已处理的menuList项的children
    menuList.value.forEach((menuItem, index) => {
      if (paragraphs.value[index] && paragraphs.value[index].length > 0) {
        menuItem.children = extractType2Sections(paragraphs.value[index], index);
      }
    });
  }
}
watch(startGenerating, async (newValue) => {
  if (newValue) {
    // 开始并行工作流处理
    startParallelWorkflows();
  }
});
// watch([workflowtaskId, completedWorkflows], ([newA, newB]) => {
//   console.log('已完成的工作流数量', newB.length);

//   if (newA) {
//     if (onlyjz.value) {
//       handleTimer(newA)
//     } else if (menuList.value.length > 1 && (newB.length > 0)) {
//       handleTimer(newA)
//     }
//   }

// }, { immediate: true, deep: true });
watch(workflowtaskId, (newVal) => {
  if (newVal) {
     handleTimer(newVal)
  }
}, { immediate: true, deep: true });
function handleTimer(id) {
  try {
    if (!timer) {
      menuList.value[jingzhengIndex]['status'] = 'load'
      timer = setInterval(async () => {
        console.log('调用后台任务状态啦 :>> ',workflowIndex.value,jingzhengIndex,workflowIndex.value >= jingzhengIndex);
        if (workflowIndex.value >= jingzhengIndex) {
          let res = await workflowtaskStatus({ id: id });
          if (res.data && res.data.status == 2) {
            // 等待所有子任务完成并按顺序拼接内容
            for (const taskId of workflowtaskChildId.value) {
              try {
                const r = await workflowtaskStatus({ id: taskId });
                if (r.data && r.data.content) {
                  menuList.value[jingzhengIndex].content += r.data.content + '\n';
                 console.log('子任务赋值 :>> ');
                }
              } catch (error) {
                console.error(`获取子任务 ${taskId} 状态失败:`, error);
              }
            }
            console.log('结束子任务啦 :>> ');
            menuList.value[jingzhengIndex].content += res.data.content
            menuList.value[jingzhengIndex].status = 'end'
            completedWorkflows.value.push(jingzhengIndex);
            clearInterval(timer);
            processMarkdownContent(menuList.value[jingzhengIndex].content, jingzhengIndex);
            menuList.value.forEach((menuItem, index) => {
              if (paragraphs.value[index] && paragraphs.value[index].length > 0) {
                menuItem.children = extractType2Sections(paragraphs.value[index], index);
              }
            });
            if (onlyjz.value) {
              startGenerating.value = false
            } else {
              checkAndStartStrategicWorkflow()
            }
          }
        }
      }, 10 * 1000);
      console.log('任务状态获取成功');
    }

  } catch (error) {
    console.error('任务状态获取失败:', error);
  }
}
const getMenuList = async (params) => {
  const res = await getCatalogList(params);
  if (res.code === 200) {
    //type: 1行业2竟品3运营4心智5企业家6总结  ----智能体类型
    menuList.value = res.data || [];
    const arr=appSecretKeyList.value.filter(v=>v.type==2);
    jingzhengIndex = menuList.value.findIndex(item => arr[0]&&item.id==arr[0].catalogId);
    const arr2=appSecretKeyList.value.filter(v=>v.type==6)
    zongjieIndex = menuList.value.findIndex(item => arr2[0]&&item.id==arr2[0].catalogId);
  } else {
    return [];
  }
};
const getKeyList = async (params) => {
  const res = await getAppSecretKey(params);
  if (res.code === 200) {
    appSecretKeyList.value = res.data || [];
  } else {
    return [];
  }
};
const init = async () => {
  // 重置数据
  appSecretKeyList.value = [];
  resetForm('return')
  try {
    // 获取appSecretKey配置列表
    await getKeyList({ labelConfigId: configData.value.labelConfigId });
    if (appSecretKeyList.value.length == 0) {
      ElMessage({
        type: "error",
        message: `没有对应目录，无法生成简报！为您返回列表页`,
        duration: 1000,
      });
      setTimeout(() => {
        router.push("/briefGeneration/list");
      }, 1000);
      return;
    }
    // 获取默认目录列表
    await getMenuList({
      structureVersion: configData.value.version,
      catalogIdList: appSecretKeyList.value.map((v) => v.catalogId).join(","),
    });
    console.log("初始化完成", {
      menuCount: menuList.value.length,
      keyCount: appSecretKeyList.value.length,
    });

    // 为menuList添加状态字段
    menuList.value.forEach((item, index) => {
      item.status = 'wait'; // wait, load, end (工作流运行状态)
      item.content = ''; // 存储工作流组合的content
    });
    if (jingzhengIndex != -1) {
      // 包含竞品分析就要跑oneWorkFlow竞品相关工作流
      console.log('1111 :>> ', 1111);
      oneWorkFlow()
    } else {
      startGenerating.value = true;
    }

  } catch (error) {
    console.log("初始化失败:", error);
    ElMessage.error("初始化失败，请重试");
  }
};
const startCtrl = ref(null)
function oneWorkFlow() {
  // 重置内容和状态
  competitorStr.value = "";
  ishumanInput.value=false;
  startCtrl.value = new AbortController();
  const touchesObj = {
    response_mode: "streaming",
    // inputs: JSON.stringify({
    // }),
    query: configData.value.brandName,
    appSecretKey: 'category_definition_competitive_identification',
  };
  const onMessage = (event) => {
    const data = event.data && JSON.parse(event.data);

    if (data.event === "message" && data.answer) {
      competitorStr.value += data.answer;
    }
    scrollToBottom();
  }
  competitorLoad.value = true;
  sseRequest(
    "/proxy/chat",
    touchesObj,
    onMessage,
    () => {
      ishumanInput.value = true;
      competitorLoad.value = false;
      console.log('competitorStr :>> ', {a:competitorStr});
    },
    (error) => {
      competitorStr.value = "";
      console.error("SSE 出现错误:重连并清空本次content", error);
    },
    startCtrl.value.signal
  );
}
// 根据menuList的id获取对应的appSecretKey
function getAppSecretKeyById(id) {
  console.log(`查找 catalogId: ${id} 对应的 configKey`);

  // 在appSecretKeyList中查找对应catalogId的数据，返回其configKey
  const keyItem = appSecretKeyList.value.find((item) => item.catalogId === id);
  if (keyItem && keyItem.configKey) {
    console.log(`找到对应的 configKey: ${keyItem.configKey}`);
    return keyItem.configKey;
  }

  console.log(`未找到 catalogId: ${id} 对应的 configKey，使用默认值`);

  // 如果没有找到对应的配置，根据在menuList中的索引生成默认的appSecretKey
  const menuIndex = menuList.value.findIndex((item) => item.id === id);
  const defaultKey = menuIndex >= 0 ? `workflow${menuIndex + 1}` : `workflow1`;

  console.log(`使用默认 configKey: ${defaultKey}`);
  return defaultKey;
}



// 处理标题更新
const handleTitleUpdate = (index, newTitle) => {
  if (typeof index === "string" && index.includes("-")) {
    const indexParts = index.split("-");
    if (indexParts.length === 2) {
      // 二级标题 (menuIndex-paragraphIndex) - 正式段落
      const menuIndex = parseInt(indexParts[0]);
      const paragraphIndex = parseInt(indexParts[1]);
      if (
        paragraphs.value[menuIndex] &&
        paragraphs.value[menuIndex][paragraphIndex]
      ) {
        paragraphs.value[menuIndex][paragraphIndex].title = newTitle;
      }
    } else if (indexParts.length === 3) {
      // 三级标题 (menuIndex-paragraphIndex-subIndex)
      const menuIndex = parseInt(indexParts[0]);
      const paragraphIndex = parseInt(indexParts[1]);
      const subIndex = parseInt(indexParts[2]);
      if (
        paragraphs.value[menuIndex] &&
        paragraphs.value[menuIndex][paragraphIndex] &&
        paragraphs.value[menuIndex][paragraphIndex].subSections[subIndex]
      ) {
        paragraphs.value[menuIndex][paragraphIndex].subSections[
          subIndex
        ].title = newTitle;
      }
    } else if (indexParts.length === 4) {
      // 四级标题 (menuIndex-paragraphIndex-subIndex-thirdIndex)
      const menuIndex = parseInt(indexParts[0]);
      const paragraphIndex = parseInt(indexParts[1]);
      const subIndex = parseInt(indexParts[2]);
      const thirdIndex = parseInt(indexParts[3]);
      if (
        paragraphs.value[menuIndex] &&
        paragraphs.value[menuIndex][paragraphIndex] &&
        paragraphs.value[menuIndex][paragraphIndex].subSections[subIndex] &&
        paragraphs.value[menuIndex][paragraphIndex].subSections[subIndex]
          .subSections[thirdIndex]
      ) {
        paragraphs.value[menuIndex][paragraphIndex].subSections[
          subIndex
        ].subSections[thirdIndex].title = newTitle;
      }
    }
  }
};

// 处理添加同级内容
const handleAddSibling = (index, type) => {
  const newSection = {
    title: "新建标题",
    content: "",
    mode: "edit",
    type,
    subSections: [],
    think: "",
    aa: "hh",
  };

  let newIndex;
  console.log("index :>> ", index, "type", type);
  if (typeof index === "string" && index.includes("-")) {
    const indexParts = index.split("-");
    const menuIndex = parseInt(indexParts[0]);
    const paragraphIndex = parseInt(indexParts[1]);
    const subIndex = parseInt(indexParts[2]);
    const thirdIndex = parseInt(indexParts[3]);
    if (indexParts.length === 2) {
      // 在二级段落中添加同级 (menuIndex-paragraphIndex)
      paragraphs.value[menuIndex]?.splice(paragraphIndex + 1, 0, newSection);
      newIndex = `${menuIndex}-${paragraphIndex + 1}`;
    } else if (indexParts.length === 3) {
      // 在三级中添加同级 (menuIndex-paragraphIndex-subIndex)
      paragraphs.value[menuIndex][paragraphIndex].subSections?.splice(
        subIndex + 1,
        0,
        newSection
      );
      newIndex = `${menuIndex}-${paragraphIndex}-${subIndex + 1}`;
    } else if (indexParts.length === 4) {
      // 在四级中添加同级 (menuIndex-paragraphIndex-subIndex-thirdIndex)
      paragraphs.value[menuIndex][paragraphIndex].subSections[
        subIndex
      ].subSections?.splice(thirdIndex + 1, 0, newSection);
      newIndex = `${menuIndex}-${paragraphIndex}-${subIndex}-${thirdIndex + 1}`;
    }
  }

  // 设置操作索引
  operatingTextIndex.value = newIndex;

  console.log("paragraphs.value :>> ", paragraphs.value);
};

// 处理添加子级内容
const handleAddChild = (index, type) => {
  const newSection = {
    title: "新建子标题",
    content: "",
    mode: "edit",
    type,
    subSections: [],
    think: "",
  };

  let newIndex;
  console.log("index :>> ", index, "type", type);
  if (typeof index === "string" && index.includes("-")) {
    const indexParts = index.split("-");
    if (indexParts.length === 2) {
      // 在二级段落中添加子级（三级） (menuIndex-paragraphIndex)
      const menuIndex = parseInt(indexParts[0]);
      const paragraphIndex = parseInt(indexParts[1]);
      if (!paragraphs.value[menuIndex][paragraphIndex].subSections) {
        paragraphs.value[menuIndex][paragraphIndex].subSections = [];
      }
      const newSubIndex =
        paragraphs.value[menuIndex][paragraphIndex].subSections.length;
      paragraphs.value[menuIndex][paragraphIndex].subSections.push(newSection);
      newIndex = `${menuIndex}-${paragraphIndex}-${newSubIndex}`;
    } else if (indexParts.length === 3) {
      // 在三级中添加子级（四级） (menuIndex-paragraphIndex-subIndex)
      const menuIndex = parseInt(indexParts[0]);
      const paragraphIndex = parseInt(indexParts[1]);
      const subIndex = parseInt(indexParts[2]);
      if (
        !paragraphs.value[menuIndex][paragraphIndex].subSections[subIndex]
          .subSections
      ) {
        paragraphs.value[menuIndex][paragraphIndex].subSections[
          subIndex
        ].subSections = [];
      }
      const newThirdIndex =
        paragraphs.value[menuIndex][paragraphIndex].subSections[subIndex]
          .subSections.length;
      paragraphs.value[menuIndex][paragraphIndex].subSections[
        subIndex
      ].subSections.push(newSection);
      newIndex = `${menuIndex}-${paragraphIndex}-${subIndex}-${newThirdIndex}`;
    }
  }

  // 设置操作索引
  operatingTextIndex.value = newIndex;

  // 激活新添加内容的编辑模式
};

// 处理删除内容
const handleDeleteSection = (index) => {
  if (typeof index === "string" && index.includes("-")) {
    const indexParts = index.split("-");
    if (indexParts.length === 2) {
      // 删除二级段落 (menuIndex-paragraphIndex)
      const menuIndex = parseInt(indexParts[0]);
      const paragraphIndex = parseInt(indexParts[1]);
      paragraphs.value[menuIndex].splice(paragraphIndex, 1);
    } else if (indexParts.length === 3) {
      // 删除三级 (menuIndex-paragraphIndex-subIndex)
      const menuIndex = parseInt(indexParts[0]);
      const paragraphIndex = parseInt(indexParts[1]);
      const subIndex = parseInt(indexParts[2]);
      paragraphs.value[menuIndex][paragraphIndex].subSections.splice(
        subIndex,
        1
      );
    } else if (indexParts.length === 4) {
      // 删除四级 (menuIndex-paragraphIndex-subIndex-thirdIndex)
      const menuIndex = parseInt(indexParts[0]);
      const paragraphIndex = parseInt(indexParts[1]);
      const subIndex = parseInt(indexParts[2]);
      const thirdIndex = parseInt(indexParts[3]);
      paragraphs.value[menuIndex][paragraphIndex].subSections[
        subIndex
      ].subSections.splice(thirdIndex, 1);
    }
  }
};

onMounted(async () => {
  pageType.value = route.params.type || "add";
  pageId.value = route.params.id;
  if (pageType.value === "add") {
    // const saved = localStorage.getItem("myForm");
    const saved = localStorage.getItem("configData");
    if (saved) {
      // form.value = JSON.parse(saved);
      configData.value = { ...configData.value, ...JSON.parse(saved) };
      init();
    } else {
      ElMessage({
        type: "error",
        message: "当前未配置简报项",
        duration: 300,
      });
      setTimeout(() => {
        router.push("/briefGeneration/list");
      }, 300);
    }
  }
});
onUnmounted(() => {
  generatedTaskId.value = "";
  // 终止所有并行工作流
  parallelControllers.value.forEach((controller, index) => {
    if (controller) {
      controller.abort();
      console.log(`清理时终止工作流 ${index}`);
    }
  });

  generatedCtrl.value && generatedCtrl.value.abort(); // 终止请求（可选）
  startCtrl.value && startCtrl.value.abort(); // 终止请求（可选）

  // 清理并行工作流状态
  parallelControllers.value = [];
  if (timer) {
    clearInterval(timer);
    timer = null;
  }
  resetForm('return')
});
</script>

<style lang="scss" scoped>
.design-page {
  position: relative;
  height: 100%;
  color: #d6f8ff;

  .btnBg {
    position: absolute;
    bottom: 50px;
    right: 100px;
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background: linear-gradient(136.1deg,
        rgba(75, 175, 247, 1) -17.4%,
        rgba(9, 60, 98, 1) 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    color: #ffffff;
    cursor: pointer;

    &:hover {
      box-shadow: 1px 0px 2px 0px rgba(0, 229, 255, 1);
      border: 1px solid rgba(0, 229, 255, 1);
    }
  }
}

.content {
  // width: 798px;
  // margin: 0 auto;
  // padding: 17px 25px;
  max-height: 100%;
  overflow-y: auto;
  position: relative;
  min-height: 500px;

  .think-con {
    padding: 0 10px;
  }

  .think-title {
    color: #515151;
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 6px;
  }

  .think-content {
    color: #a0c7da;
    // color: #426777;
    // :deep(blockquote) {
    //   color: #a0c7da;
    //   margin: 0;
    // }
    // :deep(h2) {
    //   color: #426777;
    //   font-size: 18px;
    //   font-weight: bold;
    // }
  }
}

// 固定Toolbar
// .design-page Toolbar {
//   position: fixed;
//   top: 0;
//   width: 100%;
//   background-color: white;
//   z-index: 1000;
// }
:deep(.collapseBox) {
  // .el-collapse-item__header,
  // .el-collapse-item__wrap {
  //   background-color: transparent;
  // }
  --el-collapse-border-color: transparent;
  --el-collapse-header-bg-color: transparent;
  --el-collapse-content-bg-color: transparent;
}

.competitorBox {
  padding: 16px 10px;

  .markdown-cnt {
    line-height: 22px;
    font-size: 14px;
  }

  .markdown-cnt:deep(ul) {
    list-style: disc;
  }

  .markdown-cnt:deep(li) {
    margin: 8px 0;
  }
}
</style>

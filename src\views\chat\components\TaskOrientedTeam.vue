<template>
    <div class="task-oriented">
        <div class="left" v-if="currentInstruct && !currentInstruct.isHistory">
            <div class="title">
                <img :src="baseUrl+currentInstruct.applicationIcon" v-if="currentInstruct.applicationIcon" class="img" />
                <span>{{ currentInstruct.applicationName }}</span>
            </div>
            <el-tabs v-model="activeName" class="tabs" @tab-click="handleClick">
                <el-tab-pane label="运行一次" name="runOnce" class="run-once">
                    <div v-for="(item,index) in currentInstruct.user_input_form" >
                        <div v-if="Object.keys(item)[0]" class="key-input">
                            <div class="text">{{ item[Object.keys(item)[0]].label }}</div>
                            <div v-if="Object.keys(item)[0] && Object.keys(item)[0] == 'text-input'">
                                <el-input v-model="keyList[item[Object.keys(item)[0]].variable]" :placeholder="item[Object.keys(item)[0]].label" type="text"
                                :maxlength="item[Object.keys(item)[0]].max_length" />
                            </div>
                            <div v-if="Object.keys(item)[0] && Object.keys(item)[0]=='paragraph'">
                                <el-input v-model="keyList[item[Object.keys(item)[0]].variable]" type="textarea" show-word-limit rows="4" :placeholder="item[Object.keys(item)[0]].label"
                                :maxlength="item[Object.keys(item)[0]].max_length" />
                            </div>
                            <div v-if="Object.keys(item)[0] && Object.keys(item)[0]=='select'">
                                <el-select clearable v-model="keyList[item[Object.keys(item)[0]].variable]" :placeholder="item[Object.keys(item)[0]].label" style="width: 100%;">
                                    <el-option v-for="(item,index) in item[Object.keys(item)[0]].options" 
                                    :key="item" :label="item" :value="item" />
                                </el-select>
                            </div>
                            <div v-if="Object.keys(item)[0] && Object.keys(item)[0] == 'number'">
                                <el-input-number v-model="keyList[item[Object.keys(item)[0]].variable]" controls-position="right" style="width: 100%;" />
                            </div>
                        </div>
                    </div>
                </el-tab-pane>
                <el-tab-pane label="批量运行" name="runBatch" class="run-batch">
                    <div v-if="currentInstruct.user_input_form && currentInstruct.user_input_form.length>0">
                        <div class="custom-file-input" @click="csvDataHandle">
                            <input type="file" accept=".csv" ref="fileInput" @change="handleFileUpload" class="hidden-file-input">
                            <div for="file-upload" class="custom-file-label">
                                <div v-if="!csvData" class="no-data">
                                    <img :src="csv" class="img" />
                                    <div class="title">
                                        将您的CSV文件拖放到此处，或<span style="color: blue;">浏览</span>
                                    </div>
                                </div>
                                <div v-else class="have-data">
                                    <img :src="csv" class="img" />
                                    <div class="title">
                                        {{ fileName }}
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="prompt-title">CSV 文件必须符合以下结构：</div>
                        <div class="batch-table">
                            <div class="table-th">
                                <span v-for="(item,index) in currentInstruct.user_input_form"> 
                                    {{ item[Object.keys(item)[0]].label }}
                                </span>
                            </div>
                            <div class="table-tr">
                                <span v-for="(item,index) in currentInstruct.user_input_form"> 
                                    {{ item[Object.keys(item)[0]].label }}
                                </span>
                            </div>
                        </div>
                        <div>
                            <el-link type="primary" :underline="false" class="import-Template" @click="importTemplate">
                                <el-icon style="color: #5050E6;margin-right: 3px;"><Download /></el-icon>
                                <div>下载模板</div>
                            </el-link>
                        </div>
                    </div>
                </el-tab-pane>
            </el-tabs>
            <div class="bottom">
                <el-button @click="empty()" v-if="activeName=='runOnce'">清空</el-button>
                <div style="display: flex;justify-content: right;" :style="{'width':activeName=='runOnce'?0:'100%' }">
                    <el-button type="primary" :icon="CaretRight" @click="run()" 
                    :disabled="activeName=='runOnce'?false:csvData?false:true">运行</el-button>
                </div>
            </div>
        </div>
        <div class="right" :style="{'width':currentInstruct && currentInstruct.isHistory?'100%':'60%',
        'border-top-left-radius':currentInstruct && currentInstruct.isHistory?'10px':'0','border-bottom-left-radius':currentInstruct && currentInstruct.isHistory?'10px':'0'}">
            <div class="title">
                <img :src="aiTask" class="img"/>
                <div>AI 智能书写</div>
            </div>
            <yi-mai-chat ref="messageCtrl" :chat-url="''" :current-instruct="currentInstruct"
            :preface-state="prefaceState" @conversation-refresh="conversationRefresh" >
            </yi-mai-chat>
        </div>
    </div>
</template>
<script setup>
    import { defineProps, nextTick,ref, watch } from 'vue';
    import { CaretRight } from '@element-plus/icons-vue'
    import YiMaiChat from './YiMaiChat/index.vue'
    import { ElMessage } from 'element-plus'
    import aiTask from "@/assets/icons/svg/ai-task.svg"
    import csv from "@/assets/icons/svg/csv.svg"
    import { saveAs } from 'file-saver';
    import * as XLSX from 'xlsx';
    import Papa from 'papaparse';

    const emit = defineEmits();
    const baseUrl = import.meta.env.VITE_APP_BASE_API;

    const props = defineProps({
        currentInstruct:{
            type:Object,
            required:true
        }
    });

    const currentInstruct = ref(null)
    watch(() => props.currentInstruct, val => {
        if (val) {
            currentInstruct.value = val
            return val
        } else {
            currentInstruct.value = null
            return {}
        }
    },{ deep: true, immediate: true });

    //标签页
    const activeName = ref('runOnce')
    const keyList = ref({})
    const messageCtrl = ref(null)

    const csvData  =ref(null)
    const fileName = ref('')
    const handleFileUpload = (event) => {
        const file = event.target.files[0];
        fileName.value = file.name
        if (file) {
            Papa.parse(file, {
                header: true, // 如果有标题行，设置为true
                skipEmptyLines: true, // 跳过空行
                complete: (results) => {
                    csvData.value = results.data; // 将解析后的数据存储在csvData中
                },
                error: (err, file) => {
                    console.error('Error parsing file:', err, file);
                },
                encoding: 'GBK' // 如果需要，可以指定编码
            });
        }
    }

    //用户输入表单验证
    const keyListCheck = () => {
        if(currentInstruct.value.user_input_form && currentInstruct.value.user_input_form.length>0){
            for(let i=0;i<currentInstruct.value.user_input_form.length;i++){
                let singleStrip = (currentInstruct.value.user_input_form[i]['text-input']?currentInstruct.value.user_input_form[i]['text-input'] :null)
                    ||(currentInstruct.value.user_input_form[i]['paragraph']?currentInstruct.value.user_input_form[i]['paragraph'] :null)
                    ||(currentInstruct.value.user_input_form[i]['number']?currentInstruct.value.user_input_form[i]['number'] :null)
                    ||(currentInstruct.value.user_input_form[i]['select']?currentInstruct.value.user_input_form[i]['select'] :null)
                if(singleStrip && !keyList.value[singleStrip.variable] && singleStrip.required){
                    ElMessage.warning(singleStrip.label+'必须填写')
                    return false
                }
                if((Object.keys(currentInstruct.value.user_input_form[i])[0] =='text-input' 
                || Object.keys(currentInstruct.value.user_input_form[i])[0] =='paragraph')
                && keyList.value[singleStrip.variable] 
                && keyList.value[singleStrip.variable].length>singleStrip.max_length){
                    ElMessage.warning(singleStrip.label+'长度不能大于'+singleStrip.max_length)
                    return false
                }
            }
        }
        return true
    }

    const keyListBatch = ref([])
    //批量运行字段验证
    const keyListCheckBatch = () => {
        if(currentInstruct.value.user_input_form && currentInstruct.value.user_input_form.length>0){
            keyListBatch.value = []
            for(let row = 0; row<csvData.value.length; row++){
                let keyListOnce = {}
                for(let i = 0; i<currentInstruct.value.user_input_form.length; i++){
                    let singleStrip = (currentInstruct.value.user_input_form[i]['text-input']?currentInstruct.value.user_input_form[i]['text-input'] :null)
                    ||(currentInstruct.value.user_input_form[i]['paragraph']?currentInstruct.value.user_input_form[i]['paragraph'] :null)
                    ||(currentInstruct.value.user_input_form[i]['number']?currentInstruct.value.user_input_form[i]['number'] :null)
                    ||(currentInstruct.value.user_input_form[i]['select']?currentInstruct.value.user_input_form[i]['select'] :null)
                    let value = Object.values(csvData.value[row])[i]
                    //判断必填
                    if(singleStrip && singleStrip.required && !value){
                        ElMessage.warning('第'+(row+1)+'行'+singleStrip.label+'必须填写')
                        return false
                    }
                    //判断长度
                    if((Object.keys(currentInstruct.value.user_input_form[i])[0] =='text-input' 
                    || Object.keys(currentInstruct.value.user_input_form[i])[0] =='paragraph')
                    && value && value.length>singleStrip.max_length){
                        ElMessage.warning('第'+(row+1)+'行'+singleStrip.label+'长度不能大于'+singleStrip.max_length)
                        return false
                    }
                    //判断下拉框
                    if(Object.keys(currentInstruct.value.user_input_form[i])[0] =='select' && value &&
                    currentInstruct.value.user_input_form[i]['select'].options.indexOf(value) === -1){
                        ElMessage.warning('第'+(row+1)+'行'+singleStrip.label+'内容不正确，请选择表头括号内一项输入')
                        return false
                    }

                    keyListOnce[singleStrip.variable] = value
                }
                keyListBatch.value.push(keyListOnce)
            }
        }
        return true
    }

    //清空
    const empty = () => {
        Object.keys(keyList.value).forEach(element => {
            keyList.value[element]=''
        });
    }

    //运行
    const run = () => {
        //单次
        if(activeName.value =='runOnce'){
            //用户表单校验
            if(!keyListCheck()){
                return
            }
            messageCtrl.value.sendMessageTask(JSON.parse(JSON.stringify(keyList.value)),true)
        }//批量
        else{
            if(!keyListCheckBatch()){
                return
            }
            keyListBatch.value.forEach(element => {
                messageCtrl.value.sendMessageTask(JSON.parse(JSON.stringify(element)),true)
            });
        }
        
    }

    //历史记录刷新
    const conversationRefresh = () => {
        emit('conversationRefresh')
    }

    //历史记录处理
    const openHistorySessionClear = () => {
        messageCtrl.value.openHistorySessionClear()
    }

    //下载模板
    const importTemplate = () => {
        let th = []
        currentInstruct.value.user_input_form.forEach((element) => {
            if(element[Object.keys(element)[0]].label){
                if(element[Object.keys(element)[0]].type=='select'){
                    th.push(element[Object.keys(element)[0]].label+'('+element[Object.keys(element)[0]].options.join(',')+')')
                }else{
                    th.push(element[Object.keys(element)[0]].label)
                }
            }
        })
        // 创建数据
        let data = [ th ];

        // 创建工作簿
        const ws = XLSX.utils.aoa_to_sheet(data);
        // 创建工作簿并添加工作表
        const wb = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(wb, ws, "Sheet1");
        // 生成SCV字符串
        const csv = XLSX.utils.sheet_to_csv(ws);
    
        // 创建blob对象
        const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });

        // 设置文件名
        const fileName = "template.csv";

        // 触发下载
        saveAs(blob, fileName);
    }

    const fileInput = ref(null)
    //处理文件
    const csvDataHandle = () => {
        if(csvData.value){
            csvData.value = null
            fileInput.value.value = ''
        }
    }

    defineExpose({
        openHistorySessionClear
    })
</script>
<style scoped lang="scss">
    .task-oriented{
        display: flex;
        justify-content: space-between;
        padding: 10px 10px 10px 0;
        border-radius: 5px;
        width: 100%;
        height: calc( 100vh );
        .left{
            width: 40%;
            background-color: white;
            border-right: 1px solid #F2F4F7;
            border-top-left-radius: 10px;
            border-bottom-left-radius: 10px;
            padding: 20px 20px 10px 20px;
            .title{
                font-size: 18px;
                font-weight: 600;
                color: #1D2939;
                margin: 10px;
                display: flex;
                justify-content:left;
                align-items: center;
                .img{
                    width: 30px;
                    height: 30px;
                    border-radius: 5px;
                    margin-right: 10px;
                }
            }
            .tabs{
                .run-once{
                    max-height: calc( 100vh - 300px );
                    overflow-y: auto;
                    .key-input{
                        font-size: 13px;
                        margin-bottom: 20px;
                        .text{
                            margin: 10px 0;
                        }
                    }
                }
                .run-batch{
                    .custom-file-input {
                        position: relative;
                        display: inline-block;
                        width: 100%; /* 根据需要设置宽度 */
                        .hidden-file-input {
                            position: absolute;
                            top: 0;
                            left: 0;
                            width: 100%;
                            height: 100%;
                            opacity: 0;
                            cursor: pointer;
                        }
                        .custom-file-label {
                            display: flex;
                            align-items: center;
                            padding: 10px;
                            border: 1px dashed #EAECF0;
                            height: 80px;
                            border-radius: 10px;
                            background-color: #F9FAFB;
                            cursor: pointer;
                            .img{
                                width: 30px;
                                height: 40px;
                            }
                            .title{
                                font-size: 13px;
                                color: #667085;
                                margin-left: 10px;
                                font-weight: 400;
                            }
                            .no-data{
                                display: flex;
                                align-items: center;
                                justify-content: center;
                                width: 100%;
                            }
                            .have-data{
                                display: flex;
                                align-items: center;
                                justify-content: left;
                                width: 100%;
                            }
                        }
                    }
                    .prompt-title{
                        margin: 10px 0;
                        font-weight: 500;
                        font-size: 14px;
                        color: #111928;
                        margin-top: 20px;
                    }
                    .batch-table{
                        border: 1px solid #E5E7EB;
                        border-radius: 5px;
                        min-height: 50px;
                        .table-th{
                            padding: 0 10px;
                            color:#667085;
                            font-size: 12px;
                            line-height: 35px;
                            display: flex;
                            justify-content: space-between;
                        }
                        .table-tr{
                            border-top:1px solid #E5E7EB;
                            padding: 0 10px;
                            color:#D0D5DD;
                            font-size: 12px;
                            line-height: 35px;
                            display: flex;
                            justify-content: space-between;
                        }
                    }
                    .import-Template{
                        font-size:12px;
                        vertical-align: baseline;
                        margin-top: 5px;
                        display: flex;
                        justify-content: left;
                        align-items: center;
                    }
                }
            }
            .bottom{
                margin: 20px 0;
                border-top: 1px solid #F2F4F7;
                padding: 10px 0;
                display: flex;
                justify-content: space-between;
            }
        }
        .right{
            width: 60%;
            background-color: #F9FAFB;
            border-top-right-radius: 10px;
            border-bottom-right-radius: 10px;
            padding: 20px;
            .title{
                font-size: 18px;
                font-weight: 600;
                color: #1D2939;
                margin-bottom: 10px;
                display: flex;
                align-items: center;
                .img{
                    width: 20px;
                    height: 20px;
                    margin-right: 10px;
                }
            }
            

        }
    }
</style>
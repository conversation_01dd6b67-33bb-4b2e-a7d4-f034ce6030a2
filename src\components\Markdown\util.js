import markdownIt from 'markdown-it'
import markdownItMath from "markdown-it-math"
import markdownItLinkAttributes from 'markdown-it-link-attributes'
import temml from "temml"

const macros = {};

const mdit = markdownIt({
  html: false,
  linkify: false,
  typographer: true,
  breaks: true,
})

mdit.use(markdownItMath, {
  inlineOpen: '<formula>',
  inlineClose: '<formula>',
  blockOpen: '$',
  blockClose: '$',
  renderOptions: {},
  inlineRenderer: (src) => {
    return temml.renderToString(src, { macros })
  },
  blockRenderer: (src) => {
    return temml.renderToString(src, { displayStyle: true, macros })
  }
});
mdit.use(markdownItLinkAttributes, {
  attrs: {
    target: "_blank",
    rel: "noopener",
  }
})

export function renderMarkdown(cnt) {
  try {
    return mdit.render(cnt.replace(/(\\\( *| *\\\))/g, '<formula>')).replace(/(<p>|<\/p>)/g, '')
  } catch (e) {
    console.log(e)
    return cnt
  }
}
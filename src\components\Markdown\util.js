import markdownIt from 'markdown-it'
import mditMath from 'markdown-it-math'
import katex from 'katex'
import 'katex/dist/katex.css'
import hljs from "highlight.js"
import "highlight.js/styles/vs2015.css"
import { uniqId } from '@/utils'
import * as echarts from 'echarts'
import mermaid from 'mermaid'
import { nextTick } from 'vue'

// --------------------- 图片加载 ------------------------
const imageLoader = reactive({})

/**
 * 加载图片
 */
function loadImage(url) {
    if (imageLoader[url]) {
        return
    }

    // 设置初始加载状态
    imageLoader[url] = 'loading'

    const img = new Image()
    img.src = url
    img.onload = () => {
        imageLoader[url] = 'finished'
        // 触发响应式更新
        nextTick(() => {
            // 强制重新渲染包含此图片的所有markdown组件
            window.dispatchEvent(new CustomEvent('markdown-image-loaded', { detail: { url } }))
        })
    }
    img.onerror = () => {
        imageLoader[url] = 'failed'
        // 触发响应式更新
        nextTick(() => {
            window.dispatchEvent(new CustomEvent('markdown-image-failed', { detail: { url } }))
        })
    }
}

/**
 * 图片渲染
 * url: 图片url
 */
function renderImage(url, title) {
    if (url === '/markdown-unfinished.png') {
        return '<div class="image-loading"></div>'
    }

    if (!imageLoader[url]) {
        loadImage(url)
    }

    if (imageLoader[url]) {
      switch (imageLoader[url]) {
          case 'loading':
              return '<div class="image-loading"></div>'

          case 'finished':
              return title
                ? `<div class="image-wrapper"><img src="${url}"><span class="image-title">${title}</span></div>`
                : `<img src="${url}">`

          case 'failed':
              return '<div class="image-failed"></div>'
      }
    }

    return '<div class="image-loading"></div>'
}

// --------------- 初始化markdown-it，并生成window.markdownIt实例 ------------------

let componentId = ''
let codes = []

// 创建 markdown-it 实例的函数
function createMarkdownInstance(enableHtml = false) {

const emptyToken = () => {
  return {
    attrs: null,
    block: false,
    children: null,
    content: '',
    hidden: false,
    info: '',
    level: 1,
    map: null,
    markup: '',
    meta: null,
    nesting: 0,
    tag: '',
    type: 'text'
  }
}

mermaid.initialize({ startOnLoad: false })

  const md = markdownIt({
    html: enableHtml,
  linkify: true,
  typographer: true,
  breaks: true,

  // 代码块渲染
  highlight: function (str, lang) {
    const codeIdx = codes.length
    const elId = `${componentId}-${codeIdx}`

    // html
    if (lang === 'html') {
      if (!str.trim().endsWith('</html>')) {
        return `<pre><div class="image-loading"></div></pre>`
      }

      codes.push({
        type: 'html',
        html: str.trim(),
        to: 'html-'+elId
      })

      return `<pre><div id="html-${elId}" class="iframe-html"></div></pre>`
    }

    // echarts
    if (lang === 'echarts') {
      try {
        const option = new Function(`return ${str}`)()

        codes.push({
          type: 'echarts',
          option,
          to: elId
        })

        return `<pre><div id="${elId}" class="echarts-graph"></div></pre>`
      } catch(e) {
        console.log(e)
        return `<pre><div class='echarts-loading'></div></pre>`
      }
    }

    if (lang === 'mermaid') {
      const graphDef = str

      window.setTimeout(() => {
        const el = document.getElementById(elId)
        if (!el) {
          return
        }

        mermaid.parse(graphDef, {suppressErrors: false}).then(() => {
          const el2 = document.getElementById(elId)
          if (el2) {
            mermaid.run({
              nodes: [el],
              suppressErrors: true,
            })
          }
        }).catch(() => {
          const el2 = document.getElementById(elId)
          if (el2) {
            el2.outerHTML = '<div class="image-loading"></div>'
          }
        })
      }, 0)

      return `<pre><div id="${elId}" class="mermaid-graph">${graphDef}</div></pre>`
    }

    // 其它
    if (lang && hljs.getLanguage(lang)) {
      try {
        return '<pre><code class="hljs">' +
              hljs.highlight(str, { language: lang, ignoreIllegals: true }).value +
              '</code></pre>'
      } catch (__) {}
    }

    // 展示原文
    return '<pre><code class="hljs">' + md.utils.escapeHtml(str) + '</code></pre>'
  }
})

// 公式
md.use(mditMath, {
  inlineOpen: '$',
  inlineClose: '$',
  blockOpen: '$$',
  blockClose: '$$',
  renderingOptions: {},
  inlineRenderer: function(str) {
    return katex.renderToString(str, {
      throwOnError: false
    })
  },
  blockRenderer: function(str) {
    return katex.renderToString(str, {
      throwOnError: false
    })
  },
})

// 链接
const orgLinkOpenRenderer = md.renderer.rules.link_open
  ? md.renderer.rules.link_open.bind(md.renderer.rules)
  : md.renderer.renderToken.bind(md.renderer)

md.renderer.rules.image = function(tokens, idx, options, env, self) {
  const url = tokens[idx].attrGet('src')
  const title = tokens[idx].attrGet('title') || tokens[idx].attrGet('alt')

  return renderImage(url, title)
}

md.renderer.rules.link_open = function(tokens, idx, options, env, self) {
  const isSimpleLink =
    tokens[idx + 2] &&
    tokens[idx + 1].type === 'text' &&
    tokens[idx + 2].type === 'link_close'

  if (!isSimpleLink) {
    return orgLinkOpenRenderer(tokens, idx, options, env, self)
  }

  let url = tokens[idx].attrGet('href') || ''
  let ext = url.split('.').pop()
  let text = tokens[idx + 1].content

  // 纯粹的url地址转换成的链接，其文字内容是链接本身
  // 不能直接对比url === text，有可能其中一个是经过编码的，另一个是未经过编码的
  // 也不能用decodeURI等方法解码后再判断url === text, decodeURI可能会抛出异常
  // console.log('text :>> ', text)
  const urlPattern = /^http(s?):\/\//
  if (text.trim().match(urlPattern)) {
    text = ''
  }

  try {
    url = encodeURI(decodeURI(url) || '')
    ext = url.split('.').pop()
  } catch (e) {
    console.log(url)
    console.log(e)
  }

  // 图片链接 - 更精确的匹配，确保是文件扩展名
  if (url.match(/\.(jpg|jpeg|png|gif)(\?.*)?$/i)) {
    tokens[idx] = emptyToken()
    tokens[idx + 1] = emptyToken()
    tokens[idx + 2] = emptyToken()

    return renderImage(url)
  }

  // 视频链接 - 更精确的匹配，确保是文件扩展名而不是域名或路径中的字符串
  if (url.match(/\.(mp4|webm|ogg)(\?.*)?$/i)) {
    tokens[idx] = emptyToken()
    tokens[idx + 1] = emptyToken()
    tokens[idx + 2] = emptyToken()

    return (
      '<br>' +
      '<video controls>' +
      `<source src="${url}" type="video/${ext}"></source>` +
      'Your browser does not support the video tag.' +
      '</video>' +
      '<br>'
    )
  }

  // pdf链接 - 更精确的匹配
  if (url.match(/\.(pdf)(\?.*)?$/i)) {
    tokens[idx] = emptyToken()
    tokens[idx + 1] = emptyToken()
    tokens[idx + 2] = emptyToken()

    const title = text.trim() || '详细信息请参考链接'
    return `<a href="${url}" target='_blank'>${title}</a>`
  }

  // 普通链接
  tokens[idx].attrSet('target', '_blank')
  return orgLinkOpenRenderer(tokens, idx, options, env, self)
}

// 表格
md.renderer.rules.table_open = function(tokens, idx, options, env, self) {
  return '<div class="table-wrapper"><table>'
}

md.renderer.rules.table_close = function(tokens, idx, options, env, self) {
  return '</table></div>'
}

  return md
}

/**
 * markdown预处理
 * 
 * 主要是为了解决3个问题:
 * 1. 一个链接: 【https://www.1688.com/chanpin/-C0CFB0E5BCD2B5E7.html】   渲染后，"】"也会被渲染进链接地址中 
 * 2. 当表格前面没有空行的时候，表格不能被正确渲染
 * 3. 当表格后面没有空行的时候，表格后的内容会被渲染到表格中
 * 
 * @param {*} cnt 
 * @returns 
 */
function preprocess(cnt) {
  // 解决问题1
  const processedCnt = cnt.replace(/【(https?:\/\/[^\s]{1,300}?)】/g, '【[$1]($1)】')

  // 解决问题2和3
  const lines = processedCnt.split('\n')
  const newLines = []

  for (let idx=0; idx<lines.length; idx++) {
    if (idx === 0 || idx >= lines.length - 2) {
      newLines.push(lines[idx])
      continue
    }

    const line = lines[idx].trim()
    const prevLine = lines[idx - 1].trim()
    const nextLine = lines[idx + 1].trim()
    if (isTableRow(line) && !isTableRow(prevLine) && isTableRow(nextLine)) {
      newLines.push('')
      newLines.push(lines[idx])
    } else if (isTableRow(line) && isTableRow(prevLine) && !isTableRow(nextLine)) {
      newLines.push(lines[idx])
      newLines.push('')
    } else  { 
      newLines.push(lines[idx])
    }
  }

  return newLines.join('\n')
}

function isTableRow(cnt) {
  const trimedCnt = cnt.trim()
  return (trimedCnt && trimedCnt.startsWith('|') && trimedCnt.endsWith('|'))
}

export function renderMarkdown(mdComponentId, cnt, enableHtml = false) {
  try {
    componentId = mdComponentId
    codes = []

    // 根据 isHtml 参数动态创建 markdown-it 实例
    const md = createMarkdownInstance(enableHtml)
    const processedCnt = preprocess(cnt)
    const html = md.render(processedCnt)
    // console.log(processedCnt)

    return {
      html,
      codes 
    }
  } catch (e) {
    console.log(e)
    return {
      html: cnt,
      codes: []
    }
  }
}
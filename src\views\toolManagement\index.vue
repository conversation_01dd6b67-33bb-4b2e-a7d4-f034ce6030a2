<template>
    <div>
        <div class="common-container">
            <div class="common-header">
                <div class="title">工具列表</div>
                <div class="header-right">
                    <div style="margin: 0 20px;">
                        <el-input
                            v-model="queryParams.toolName"
                            class="w-50 m-2"
                            placeholder="搜索"
                            :prefix-icon="Search"
                            @keyup.enter="handleQuery"
                        />
                    </div>
                    <el-button type="primary" style="color: white;font-size: 14px;font-weight: 500;" @click="createTool()">新建工具</el-button>
                </div>
            </div>
            <div v-loading="loading" class="common-content">
                <el-row>
                    <el-col :span="8" v-for="(item,index) in toolList" :key="index"  style="margin-bottom: 20px;">
                        <custom-card :img="baseUrl+item.toolImageUrl" :title="item.toolName" :detail="item.toolDesc" :is-need-footer="false">
                            <template #tool>
                                <el-dropdown :hide-on-click="false">
                                    <el-button text :icon="More" @click.stop></el-button>
                                    <template #dropdown>
                                    <el-dropdown-menu>
                                        <el-dropdown-item @click="editTool(item)">编辑</el-dropdown-item>
                                        <el-dropdown-item @click="deleteTool(item)" style="color:red">删除</el-dropdown-item>
                                    </el-dropdown-menu>
                                    </template>
                                </el-dropdown>
                            </template>
                        </custom-card>
                    </el-col>
                </el-row>
            </div>
            <pagination
                v-show="total > 0"
                :total="total"
                v-model:page="queryParams.pageNum"
                v-model:limit="queryParams.pageSize"
                layout="prev, pager, next"
                @pagination="getToolsList"
                style="margin-right:20px"
            />
        </div>
        <!--新建、编辑工具弹框-->
        <el-dialog v-model="toolVisible" :title="toolTitle">
            <div style="height: calc(100vh - 295px);overflow-y: auto;padding:0 20px">
                <el-form ref="toolInfoForm" :model="toolInfo" :rules="rules" label-width="120px"  :label-position="'top'" :validate-on-rule-change="false">
                    <div style="text-align:center">
                        <Image-Upload-Min v-model="toolInfo.toolImageUrl" :limit="1" ></Image-Upload-Min>
                    </div>
                    <el-row>
                        <el-col>
                            <el-form-item label="工具名称" prop="toolName">
                                <el-input v-model="toolInfo.toolName" placeholder="请输入工具名称"/>
                            </el-form-item>
                        </el-col>
                        <el-col>
                            <el-form-item label="工具标识" prop="toolCode">
                                <el-input v-model="toolInfo.toolCode" placeholder="请输入工具标识"/>
                            </el-form-item>
                        </el-col>
                        <el-col>
                            <el-form-item label="工具概述" prop="toolDesc">
                                <el-input v-model="toolInfo.toolDesc" placeholder="请输入工具概述" type="textarea" rows="7" />
                            </el-form-item>
                        </el-col>
                        <el-col>
                            <el-form-item label="工具类型" prop="toolType">
                                <el-select v-model="toolInfo.toolType" placeholder="请选择" style="width:100%" :fit-input-width="true"
                                @change="toolTypeChange">
                                    <el-option :key="1" label="代码块类型" :value="1"/>
                                    <el-option :key="2" label="API类型" :value="2"/>
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col v-if="toolInfo.toolType==1">
                            <el-form-item label="工具代码" prop="toolProcedure">
                                <custom-code-mirror 
                                    v-if="toolVisible"
                                    style="width: 100%;" 
                                    :height="'300px'" 
                                    :value="toolInfo.toolProcedure" 
                                    @update:bindVal="updatetoolProcedure" 
                                    >
                                </custom-code-mirror>
                            </el-form-item>
                        </el-col>
                        <el-col v-if="toolInfo.toolType==2">
                            <el-form-item label="工具URL" prop="toolUrl">
                                <el-input v-model="toolInfo.toolUrl" placeholder="请输入工具URL" />
                            </el-form-item>
                        </el-col>
                        <el-col v-if="toolInfo.toolType==2">
                            <el-form-item label="请求方式" prop="requestMethod">
                                <el-select v-model="toolInfo.requestMethod" placeholder="请选择" style="width:100%" :fit-input-width="true">
                                    <el-option :key="1" label="GET" value="GET"/>
                                    <el-option :key="2" label="POST" value="POST"/>
                                    <el-option :key="3" label="PUT" value="PUT"/>
                                    <el-option :key="4" label="DELETE" value="DELETE"/>
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col v-if="toolInfo.toolType==2">
                            <el-form-item label="Header List" prop="headerListParse">
                                <el-table :data="toolInfo.headerListParse" style="width: 100%">
                                    <el-table-column label="参数名" prop="paramName" align="center">
                                        <template #default="{ row }">
                                            <el-input v-model="row.paramName"></el-input>
                                        </template>
                                    </el-table-column>
                                    <el-table-column label="参数值" prop="paramValues" align="center">
                                        <template #default="{ row }">
                                            <el-input v-model="row.paramValues"></el-input>
                                        </template>
                                    </el-table-column>
                                    <el-table-column width="60" align="center">
                                        <template #header="{ row }">
                                            <span @click="headerListAddRow()" style="font-size: 16px;cursor: pointer;"><el-icon>
                                                    <CirclePlus />
                                                </el-icon></span>
                                        </template>
                                        <template #default="scope">
                                            <span @click="headerListDelRow(scope.$index)"
                                                style="font-size: 16px;cursor: pointer;color: red;"><el-icon color="red">
                                                    <Delete />
                                                </el-icon></span>
                                        </template>
                                    </el-table-column>
                                </el-table>
                            </el-form-item>
                        </el-col>
                        <el-col v-if="toolInfo.toolType==3">
                            <el-form-item label="授权方式" prop="authMet">
                                <el-select v-model="toolInfo.authMet" placeholder="请选择" style="width:100%" :fit-input-width="true"
                                @change="authMetChange">
                                    <el-option :key="1" label="无授权" value="1"/>
                                    <el-option :key="2" label="服务" value="2"/>
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col v-if="toolInfo.toolType==3 && toolInfo.authMet=='2'">
                            <el-form-item label="Location" prop="location">
                                <el-radio-group v-model="toolInfo.location">
                                    <el-radio label="1">Header</el-radio>
                                    <el-radio label="2">Query</el-radio>
                                </el-radio-group>
                            </el-form-item>
                        </el-col>
                        <el-col v-if="toolInfo.toolType==3 && toolInfo.authMet=='2'">
                            <el-form-item label="参数名称" prop="keyName">
                                <el-input v-model="toolInfo.keyName" placeholder="请输入参数名称"/>
                            </el-form-item>
                        </el-col>
                        <el-col v-if="toolInfo.toolType==3 && toolInfo.authMet=='2'">
                            <el-form-item label="服务令牌/API密钥" prop="serviceToken">
                                <el-input v-model="toolInfo.serviceToken" placeholder="请输入服务令牌/API密钥"/>
                            </el-form-item>
                        </el-col>
                        <el-col v-if="toolInfo.toolType==2">
                            <el-form-item label="入参" prop="inKeyParse">
                                <el-table :data="toolInfo.inKeyParse" style="width: 100%">
                                    <el-table-column label="参数名" prop="name" align="center">
                                        <template #default="{ row }">
                                            <el-input v-model="row.name"></el-input>
                                        </template>
                                    </el-table-column>
                                    <el-table-column label="参数描述" prop="desc" align="center">
                                        <template #default="{ row }">
                                            <el-input v-model="row.desc"></el-input>
                                        </template>
                                    </el-table-column>
                                    <el-table-column label="参数类型" prop="type" align="center">
                                        <template #default="{ row }">
                                            <el-select v-model="row.type" placeholder="请选择" style="width:100%" :fit-input-width="true">
                                                <el-option :key="1" label="String" value="String"/>
                                                <el-option :key="2" label="Integrt" value="Integrt"/>
                                                <el-option :key="3" label="Number" value="Number"/>
                                                <el-option :key="4" label="Object" value="Object"/>
                                                <el-option :key="5" label="Array" value="Array"/>
                                                <el-option :key="6" label="Boolen" value="Boolen"/>
                                            </el-select>
                                        </template>
                                    </el-table-column>
                                    <el-table-column label="是否必要" prop="ifNece" align="center">
                                        <template #default="{ row }">
                                            <el-switch v-model="row.ifNece" />
                                        </template>
                                    </el-table-column>
                                    <el-table-column width="60" align="center">
                                        <template #header="{ row }">
                                            <span @click="inKeyAddRow()" style="font-size: 16px;cursor: pointer;"><el-icon>
                                                    <CirclePlus />
                                                </el-icon></span>
                                        </template>
                                        <template #default="scope">
                                            <span @click="inKeyDelRow(scope.$index)"
                                                style="font-size: 16px;cursor: pointer;color: red;"><el-icon color="red">
                                                    <Delete />
                                                </el-icon></span>
                                        </template>
                                    </el-table-column>
                                </el-table>
                            </el-form-item>
                        </el-col>
                        <el-col v-if="toolInfo.toolType==2">
                            <el-form-item label="出参" prop="outKeyParse">
                                <el-table :data="toolInfo.outKeyParse" style="width: 100%">
                                    <el-table-column label="参数名" prop="name" align="center">
                                        <template #default="{ row }">
                                            <el-input v-model="row.name"></el-input>
                                        </template>
                                    </el-table-column>
                                    <el-table-column label="参数描述" prop="desc" align="center">
                                        <template #default="{ row }">
                                            <el-input v-model="row.desc"></el-input>
                                        </template>
                                    </el-table-column>
                                    <el-table-column label="参数类型" prop="type" align="center">
                                        <template #default="{ row }">
                                            <el-select v-model="row.type" placeholder="请选择" style="width:100%" :fit-input-width="true">
                                                <el-option :key="1" label="String" value="String"/>
                                                <el-option :key="2" label="Integrt" value="Integrt"/>
                                                <el-option :key="3" label="Number" value="Number"/>
                                                <el-option :key="4" label="Object" value="Object"/>
                                                <el-option :key="5" label="Array" value="Array"/>
                                                <el-option :key="6" label="Boolen" value="Boolen"/>
                                            </el-select>
                                        </template>
                                    </el-table-column>
                                    <el-table-column label="是否必要" prop="ifNece" align="center">
                                        <template #default="{ row }">
                                            <el-switch v-model="row.ifNece" />
                                        </template>
                                    </el-table-column>
                                    <el-table-column width="60" align="center">
                                        <template #header="{ row }">
                                            <span @click="outKeyAddRow()" style="font-size: 16px;cursor: pointer;"><el-icon>
                                                    <CirclePlus />
                                                </el-icon></span>
                                        </template>
                                        <template #default="scope">
                                            <span @click="outKeyDelRow(scope.$index)"
                                                style="font-size: 16px;cursor: pointer;color: red;"><el-icon color="red">
                                                    <Delete />
                                                </el-icon></span>
                                        </template>
                                    </el-table-column>
                                </el-table>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
            </div>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="cancel">取消</el-button>
                    <el-button type="primary" @click="submitTool">保存</el-button>
                </span>
            </template>
        </el-dialog>
    </div>
</template>
<script setup>
import { ref, reactive, toRefs } from 'vue'
import { Tools,Edit,Delete,Star,StarFilled,Search, Top,More } from '@element-plus/icons-vue'
import CustomCard from '@/components/CustomCard/index'
import CustomCodeMirror from '@/components/CustomCodeMirror/index'
import { getToolList,creationTool,modifyTool,delTool } from "@/api/toolManagement/tool";

const route = useRoute();

const { proxy } = getCurrentInstance();
const baseUrl = import.meta.env.VITE_APP_BASE_API;
const loading = ref(false)
//搜索
const queryParams = ref({
    toolName:'',
    pageNum: 1,
    pageSize: 12,
});

const total = ref(0);
//编辑、新增工具弹框开关
const toolVisible = ref(false);
//编辑、新增工具弹框标题
const toolTitle = ref('');
//工具信息
const toolInfo = ref({
    toolImageUrl:'',
    toolName:'',
    toolCode:'',
    toolDesc:'',
    toolProcedure:'',
    toolType:'',
    toolUrl:'',
    headerListParse:[],
    authMet:'1',
    location:'1',
    keyName:'',
    serviceToken:'',
    inKeyParse:[],
    outKeyParse:[],
});
//规则校验
const rules = ref({
    toolName: [{ required: true, message: "工具名称不能为空", trigger: "blur" }],
    toolCode: [{ required: true, message: "工具标识不能为空", trigger: "blur" }],
    toolDesc: [{ required: true, message: "工具概述不能为空", trigger: "blur" }],
    toolType:[{ required: true, message: "工具类型不能为空", trigger: "change" }]
});
//更新toolProcedure的值
const updatetoolProcedure = (val) => {
    toolInfo.value.toolProcedure = val
}
//工具列表
const toolList = ref([])

//编辑
const editTool = (tool) => {
    reset()
    toolInfo.value = JSON.parse(JSON.stringify(tool));
    toolInfo.value.headerListParse=toolInfo.value.headerList?JSON.parse(toolInfo.value.headerList):[]
    toolInfo.value.inKeyParse=toolInfo.value.inKey?JSON.parse(toolInfo.value.inKey):[]
    toolInfo.value.outKeyParse=toolInfo.value.outKey?JSON.parse(toolInfo.value.outKey):[]
    toolTypeChange()
    authMetChange()
    toolTitle.value = '编辑工具';
    toolVisible.value = true;
}

//取消
const cancel = () => {
    toolVisible.value=false;
    reset()
}

//创建工具
const createTool = () => {
    reset()
    toolTitle.value = '新建工具';
    toolVisible.value = true;
}

/** 表单重置 */
const  reset = () => {
    toolInfo.value={
        toolImageUrl:'',
        toolName:'',
        toolCode:'',
        toolDesc:'',
        toolProcedure:'',
        toolType:'',
        toolUrl:'',
        headerListParse:[],
        authMet:'1',
        location:'1',
        keyName:'',
        serviceToken:'',
        inKeyParse:[],
        outKeyParse:[],
    };
  proxy.resetForm("toolInfoForm");
}

//工具列表
const getToolsList = () => {
    loading.value=true;
    getToolList(queryParams.value).then(response => {
        toolList.value=response.rows;
        total.value=response.total;
        loading.value=false;
    })
}

//保存
const submitTool = () => {
    if(toolInfo.value.toolType==1){
        toolInfo.value.toolUrl=''
        toolInfo.value.headerList=''
        toolInfo.value.authMet=''
        toolInfo.value.location=''
        toolInfo.value.keyName=''
        toolInfo.value.serviceToken=''
        toolInfo.value.inKey=''
        toolInfo.value.outKey=''
        toolInfo.value.requestMethod=''
    }else{
        toolInfo.value.toolProcedure='',
        toolInfo.value.headerList=JSON.stringify(toolInfo.value.headerListParse)
        toolInfo.value.inKey=JSON.stringify(toolInfo.value.inKeyParse)
        toolInfo.value.outKey=JSON.stringify(toolInfo.value.outKeyParse)
    }
    proxy.$refs["toolInfoForm"].validate(valid => {
        if (valid) {
            if(toolInfo.value.id){
                modifyTool(toolInfo.value).then(response => {
                    proxy.$modal.msgSuccess("修改成功");
                    toolVisible.value=false;
                    reset()
                    getToolsList();
                });
            }else{
                creationTool(toolInfo.value).then(response => {
                    proxy.$modal.msgSuccess("新增成功");
                    toolVisible.value=false;
                    reset()
                    getToolsList();
                });
            }
        }
    })
}

//删除工具
const deleteTool = (tool) => {
    proxy.$modal.confirm('是否确认删除工具名称为"' + tool.toolName + '"的数据项？').then(function() {
        return delTool(tool.id);
    }).then(() => {
        getToolsList();
        proxy.$modal.msgSuccess("删除成功");
    }).catch(() => {});
}

//监听工具代码
watch(() => toolInfo.value.toolProcedure, value => changeToolProcedure(value))
const changeToolProcedure = (value) => {
    if(proxy.$refs["toolInfoForm"]){
        proxy.$refs["toolInfoForm"].validateField('toolProcedure');
    }
}

//列表搜索
const handleQuery = () => {
    queryParams.value.pageNum=1;
    getToolsList()
}

//工具类型切换
const toolTypeChange = () => {
    if(toolInfo.value.toolType==1){
        rules.value.toolProcedure=[{ required: true, message: "工具代码不能为空", trigger: "blur" }]
        rules.value.toolUrl=[]
        rules.value.authMet=[]
    }else if(toolInfo.value.toolType==2){
        if(!toolInfo.value.authMet){
            toolInfo.value.authMet='1'
        }
        rules.value.toolUrl=[{ required: true, message: "工具URL不能为空", trigger: "blur" }]
        // rules.value.authMet=[{ required: true, message: "授权方式不能为空", trigger: "change" }]
        rules.value.requestMethod=[{ required: true, message: "请求方式不能为空", trigger: "change" }]
        rules.value.toolProcedure=[]
    }
}
//授权方式切换
const authMetChange = () => {
    if(toolInfo.value.authMet==1){
        rules.value.location=[]
        rules.value.keyName=[]
        rules.value.serviceToken=[]
    }else if(toolInfo.value.authMet==2){
        if(!toolInfo.value.location){
            toolInfo.value.location='1'
        }
        rules.value.location=[{ required: true, message: "Location不能为空", trigger: "blur" }]
        rules.value.keyName=[{ required: true, message: "参数名称不能为空", trigger: "blur" }]
        rules.value.serviceToken=[{ required: true, message: "服务令牌/API密钥不能为空", trigger: "blur" }]
    }
}

//headerList增加
const headerListAddRow = () => {
    if(!toolInfo.value.headerListParse){
        toolInfo.value.headerListParse=[]
    }
    toolInfo.value.headerListParse.push({
        paramName: '',
        paramValues: '',
    })
}
//headerList删除
const headerListDelRow = (index) => {
    toolInfo.value.headerListParse.splice(index, 1)
}

//入参增加
const inKeyAddRow = () => {
    if(!toolInfo.value.inKeyParse){
        toolInfo.value.inKeyParse=[]
    }
    toolInfo.value.inKeyParse.push({
        name: '',
        desc: '',
        type:'',
        ifNece:false
    })
}
//入参删除
const inKeyDelRow = (index) => {
    toolInfo.value.inKeyParse.splice(index, 1)
}

//出参增加
const outKeyAddRow = () => {
    if(!toolInfo.value.outKeyParse){
        toolInfo.value.outKeyParse=[]
    }
    toolInfo.value.outKeyParse.push({
        name: '',
        desc: '',
        type:'',
        ifNece:false
    })
}
//出参删除
const outKeyDelRow = (index) => {
    toolInfo.value.outKeyParse.splice(index, 1)
}

const pageJumpProcessing = () => {
    if(route.query.type && route.query.type=='add'){
        createTool()
    }
}

//获取工具列表
getToolsList()
pageJumpProcessing()
</script>
<style lang='scss' scoped>
.common-container {
    display: flex;
    flex-direction: column;
    background-color: #F7F7FA;
    height: calc(100vh - 52px);
    width: 100%;
    margin: 0;
    padding: 0;
    background-size: cover;
}
.common-header {
    height: 80px;
    padding: 28px 30px;
    line-height: 24px;
    display: flex;
    justify-content: space-between;

    .title {
        color: #032220;
        font-size: 18px;
        font-weight: 500;
    }

    .header-right {
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: row;
    }
}

.common-content {
    margin: 0 20px;
    height: calc(100vh - 250px);
}
::v-deep .el-card__footer {
    padding: calc(var(--el-card-padding) - 15px) var(--el-card-padding);
    border-top: 1px solid var(--el-card-border-color);
    box-sizing: border-box;
}
::v-deep .pagination-container{
    background:#F7F7FA
}

</style>
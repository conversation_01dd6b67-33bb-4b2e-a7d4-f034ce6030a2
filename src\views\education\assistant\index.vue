<template>
  <el-container class="wrapper">
    <el-header class="header-container">
      <common-header />
    </el-header>

    <el-main>
      <div class="container">
        <div class="screenOptionsArea">
          <div class="left-section">
            <div class="header-title">校园助手</div>
          </div>
          <div class="right-section">
            <el-dropdown
              class="filter-dropdown"
              @command="handleCommandKnowledge"
            >
              <span class="el-dropdown-link">
                {{ belongsToTheClassOf }}
                <el-icon class="el-icon--right">
                  <arrow-down />
                </el-icon>
              </span>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="thirdParty"
                    >确认为第三方agent</el-dropdown-item
                  >
                  <el-dropdown-item command="notThirdParty"
                    >不是第三方agent</el-dropdown-item
                  >
                </el-dropdown-menu>
              </template>
            </el-dropdown>

            <el-input
              v-model="searchKeyword"
              class="w-50 m-2 searchInput"
              :maxlength="50"
              placeholder="请输入关键词查询"
              :prefix-icon="Search"
              @keyup.enter="handleQuery"
              clearable
            />
          </div>
        </div>
        <div class="contentArea">
          <!-- AI辅导员 -->
          <div class="card-container" @click="handleOperation('aICounselor')">
            <img class="card-img" src="/icons/aICounselor.png" />
            <div class="card-title">AI辅导员</div>
            <div class="card-desc">
              智能辅导，为学生日常、学业等问题答疑解惑。
            </div>
          </div>
          <!-- 办公助手 -->
          <div
            v-hasPermi="['edu:permission-management', 'edu:personal-center']" 
            class="card-container"
            style="margin-left: 17px"
            @click="handleOperation('officeAssistant')"
          >
            <img class="card-img" src="/icons/officeAssistant.png" />
            <div class="card-title">办公助手</div>
            <div class="card-desc">助力老师的校园办公，高效处理各类事务。</div>
          </div>
          <!-- 就业助手 -->
          <div
            class="card-container"
            style="margin-left: 17px"
            @click="handleOperation('employmentAssistant')"
          >
            <img class="card-img" src="/icons/employmentAssistant.png" />
            <div class="card-title">就业助手</div>
            <div class="card-desc">
              综合能力分析，精准推送岗位，助力学生求职就业。
            </div>
          </div>
          <!-- 网信助手 -->
          <div
            class="card-container"
            style="margin-left: 17px"
            @click="handleOperation('internetAssistant')"
          >
            <img class="card-img" src="/icons/internetAssistant.png" />
            <div class="card-title">网信助手</div>
            <div class="card-desc">
              支持校园网络信息答疑，助力师生高效使用校园网络，获取信息。
            </div>
          </div>
          <!-- 招生助手 -->
          <div
            v-hasPermi="['edu:permission-management', 'edu:personal-center']" 
            class="card-container"
            style="margin-left: 17px"
            @click="handleOperation('admissionsAssistant')"
          >
            <img class="card-img" src="/icons/admissionsAssistant.png" />
            <div class="card-title">招生助手</div>
            <div class="card-desc">提供招生资讯，辅助师生了解政策。</div>
          </div>
        </div>
      </div>
    </el-main>
  </el-container>
</template>

<script setup>
import { ref, provide } from 'vue'
import CommonHeader from '../Header.vue'
import { Search } from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'

const showEditDialog = ref(false)
const belongsToTheClassOf = ref('是否第三方agent')
const searchKeyword = ref('')
const handleCommandKnowledge = (command) => {
  switch (command) {
    case 'thirdParty':
      belongsToTheClassOf.value = '确认为第三方agent'
      break
    case 'notThirdParty':
      belongsToTheClassOf.value = '不是第三方agent'
      break
    default:
      belongsToTheClassOf.value = '是否第三方agent'
  }
}
const tableData = ref([])

const total = ref(tableData.value.length) // 总数据条数
const pageSize = ref(10) // 每页显示的数据条数
const currentPage = ref(1) // 当前页码

const paginatedTableData = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return tableData.value.slice(start, end)
})

const fetchData = () => {

  paginatedTableData.value = tableData.value.slice((currentPage.value - 1) * pageSize.value, currentPage.value * pageSize.value)
}
watch([currentPage, pageSize], fetchData)


const handleQuery = () => {
  console.log(searchKeyword.value)

}
const handleSelectionChange = (selection) => {
  console.log(selection)
}
const router = useRouter()
const handleOperation = (type) => {
  router.push({ path: '/edu/assistant/chat', query: { type: type } })
}
const handlePageChange = (page) => {
  currentPage.value = page
  console.log(`当前页码为 ${page}`)
}
const handlePageSizeChange = (size) => {
  pageSize.value = size
  console.log(`每页显示数据条数为 ${size}`)
}

const handleReturn = () => {
  router.push('/edu/assistant/chat')
}
</script>

<style lang="scss" scoped>
.wrapper {
  background-color: #f5f7fd;
  position: relative;
}

.container {
  display: flex;
  height: 100%;
  // width: 100%;
  width: 1152px;
  margin: 0 auto;
  flex-direction: column;
  .screenOptionsArea {
    width: 100%;
    height: 50px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .left-section {
      display: flex;
      align-items: center;

      .header-title {
        font-family: PingFangSC, PingFang SC;
        font-weight: 600;
        font-size: 16px;
        color: #1e1f24;
      }
    }
    .right-section {
      display: flex;
      align-items: center;
    }
    .el-dropdown-link,
    .filter-dropdown {
      cursor: pointer;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 14px;
      color: #1e1f24 !important;
      margin-right: 10px;
      white-space: nowrap;
      &:focus {
        border: 0px !important;
        outline: 0px !important;
      }
    }
    .searchInput {
      border: none !important;
      outline: none !important;
      :deep(.el-input__wrapper) {
        width: 283px;
        height: 32px;
        background: #ffffff;
        border-radius: 4px;
      }
    }
  }
  .contentArea {
    width: 100%;
    height: 679px;
    margin-top: 15px;
    display: flex;
    .card-container {
      width: 217px;
      height: 122px;
      background: #ffffff;
      // box-shadow: 0px 5px 12px 0px rgba(0, 0, 0, 0.1);
      border-radius: 16px;
      border: 1px solid #eaeaec;
      position: relative;
      cursor: pointer;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      }
      .card-img {
        position: absolute;
        top: 12px;
        left: 12px;
        width: 20px;
        height: 20px;
      }
      .card-title {
        font-family: PingFangSC, PingFang SC;
        font-weight: 600;
        font-size: 14px;
        color: #1e1f24;
        line-height: 14px;
        white-space: normal;
        position: absolute;
        top: 44px;
        left: 12px;
      }
      .card-desc {
        width: 180px;
        height: 32px;
        position: absolute;
        top: 74px;
        left: 12px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 12px;
        color: #848691;
        line-height: 18px;
      }
    }
  }
}
</style>

import request from '@/utils/request'

// 查询生成表数据
export function getOwnList(params) {
  return request({
    url: '/index/getuserstore',
    method: 'get',
    params
  })
}
export function getOwnListOld(params) {
  return request({
    url: '/index/application/ownList',
    method: 'get',
    params
  })
}

/**
 * 设为首页
 */
export function setIsIndex(params) {
   return request({
    url: `/index/application`,
    method: 'post',
    data: params
  })
}

/**
 * 智能体, 获取会话列表
 */
export function getConversationList(title) {
   return request({
    url: `/index/historylist?title=${encodeURIComponent(title)}`,
    method: 'get',
  })
}

/**
 * 删除会话
 */
export function delConversation(params) {
  return request({
    url: `/index/delmeeting`,
    method: 'delete',
    data: params
  })
}

/**
 * 智能体, 重命名会话
 * @param {*} sessionId 
 * @param {*} name 
 * @returns 
 */
export function renameConversation(params) {
  return request({
    url: `/index/titleedit`,
    method: 'put',
    data: params
  })
}

/**
 * 获取会话详情
 */
export function getConversationDetail(params) {
   return request({
    url: `/index/getmeetinginfo`,
    method: 'get',
    params
  })
}

//新增应用申请
export function applicationApply(data) {
  return request({
    url: `/application/apply`,
    method: 'post',
    data
  })
}

//判断应用是否可用
export function checkusestatus(data) {
  return request({
    url: `/index/checkusestatus`,
    method: 'post',
    data
  })
}

//查询租户信息
export function gettenant() {
  return request({
    url: '/tenant/tenant/gettenant',
    method: 'get'
  })
}
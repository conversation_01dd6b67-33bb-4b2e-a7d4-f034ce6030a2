<template>
  <div class="header-content">
    <div class="left">
      <div class="return-btn" @click="handleReturn">
        <img src="/icons/return.png" />
        返回
      </div>
      <div class="name">{{ titleName }}</div>
    </div>
    <div class="right">
      <router-link to="/notice">
        <img src="/icons/notice.png" alt="notice" class="header-icon" />
      </router-link>
      <router-link to="/help">
        <img src="/icons/help.png" alt="help" class="header-icon" />
      </router-link>
      <user />
    </div>
  </div>
</template>

<script setup>
import User from '../../User.vue'
import { useRouter } from 'vue-router'

const router = useRouter()

function handleReturn () {
  router.push('/edu/assistant')
}
const route = useRoute()
const titleName = ref('')
onMounted(() => {
  // AI辅导员
  let type = route.query.type
  console.log('type', type)
  titleName.value = type == 'aICounselor' ? "AI辅导员"
    : type == 'officeAssistant' ? "办公助手"
      : type == 'employmentAssistant' ? "就业助手"
        : type == 'internetAssistant' ? "网信助手"
          : type == 'admissionsAssistant' ? "招生助手"
            : "AI辅导员"

})
</script>

<style lang="scss" scoped>
.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}
.header-icon {
  width: 16px;
  height: 16px;
  cursor: pointer;
  transition: opacity 0.2s;
  margin-right: 24px;
}

.header-icon:hover {
  opacity: 0.8;
}

.left {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;

  .name {
    font-size: 16px;
    color: #1e1f24;
    margin-left: 24px;
  }

  .return-btn {
    width: 68px;
    height: 30px;
    background: #e8edf8;
    border-radius: 8px;
    border: 1px solid rgba(0, 0, 0, 0.05);
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    gap: 2px;
    cursor: pointer;
    width: 68px;
    height: 30px;
    font-size: 12px;
    color: #1e1f24;

    img {
      width: 14px;
      height: 14px;
    }
  }
}

.right {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  align-items: center;
}
</style>
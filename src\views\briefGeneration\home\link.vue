<template>
  <div :class="['link-card', {active: active}]" @click="handleClick">
    <inline-svg :src="icon" alt="" title="" class="icon" />
    <div class="content">
      <div class="title">{{ title }}</div>
      <div class="desc">{{ desc }}</div>
    </div>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'
import InlineSvg from 'vue-inline-svg'

const props = defineProps({
  active: {
    type: Boolean,
    default: false,
  },
  icon: {
    type: String,
    required: true
  },
  title: {
    type: String,
    required: true
  },
  desc: {
    type: String,
    required: true
  }
})

const emit = defineEmits(['choose'])

const handleClick = () => {
  emit('choose')
}
</script>

<style scoped>
.link-card {
  display: flex;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s ease;
  height: 48rem;
  background: #1D3359;
  border-radius: 4rem;
  opacity: 0.7;
  padding: 10rem;
  border: 1rem solid #3F8FB5;
  box-sizing: border-box;
}

.link-card.active, .link-card:hover {
  border-color: #21EDFF;
  box-shadow: 0 0 1rem 1rem #5BC4DA;
}

.icon {
  width: 25rem;
  height: 25rem;
  margin-right: 5rem;
  color: #4BAFFF;
}

.content {
  flex: 1;
  overflow: hidden;
}

.title {
  margin-bottom: 4rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: 12rem;
  color: #4BAFFF;
  text-align: left;
  text-indent: 18rem;
}

.desc {
  font-size: 10rem;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-wrap: nowrap;
  color: #999999;
  text-align: left;
}
</style>

<template>
  <div :class="['btn-list', msg.type]">
    <el-tooltip
      v-for="btn of btnList"
      :key="btn.name"
      :content="btn.tip"
      effect="dark"
      placement="top"
      popper-class="tooltip-adjust"
    >
      <div :class="['btn', btn.name]" @click="btn.handler()">
        <inline-svg :src="btn.icon" :style="btn.style" />
      </div>
    </el-tooltip>
  </div>
</template>

<script>
import InlineSvg from 'vue-inline-svg'
import copy from 'copy-to-clipboard'

export default {
  components: {
    InlineSvg
  },
  props: {
    msg: {
      type: Object,
      required: true
    },
    /**
     * 获取按钮列表的函数
     */
    fnBtnList: {
      type: Function,
      required: false
    },
  },
  data () {
    return {
      copied: false,
      btnMap: {
        'copy': {
          name: 'copy',
          icon: '/icons/copy.svg',
          tip: '复制',
          style: {},
          handler: () => {
            this.copyRenderedContent()
            this.copied = true
            setTimeout(() => {
              this.copied = false
            }, 600)
          }
        },
        'copied': {
          name: 'copied',
          icon: '/icons/check-mark.svg',
          tip: '已复制',
          style: {},
          handler: () => { }
        },
        'thumbs-up': {
          name: 'thumbs-up',
          icon: '/icons/thumbs-up.svg',
          tip: '点赞',
          style: {},
          handler: () => {
            this.$emit('like')
          }
        },
        'thumbs-down': {
          name: 'thumbs-down',
          icon: '/icons/thumbs-up.svg',
          tip: '反对',
          style: {
            transform: 'rotate(180deg)'
          },
          handler: () => {
            this.$emit('dislike')
          }
        },
        'thumbs-up-filled': {
          name: 'thumbs-up-filled',
          icon: '/icons/thumbs-up-filled.svg',
          tip: '取消点赞',
          style: {
            fill: '#ff6600'
          },
          handler: () => {
            this.$emit('revert-like')
          }
        },
        'thumbs-down-filled': {
          name: 'thumbs-down-filled',
          icon: '/icons/thumbs-up-filled.svg',
          tip: '取消反对',
          style: {
            transform: 'rotate(180deg)',
            fill: '#ff6600'
          },
          handler: () => {
            this.$emit('cancel-dislike')
          }
        },
        edit: {
          name: 'edit',
          icon: '/icons/edit.svg',
          tip: '编辑',
          style: {},
          handler: () => {
            this.$emit('edit')
          }
        },
        insert: {
          name: 'insert',
          icon: '/icons/edit.svg',
          tip: '插入',
          handler: () => {
            this.$emit('insert')
          }
        },
        regenerate: {
          name: 'regenerate',
          icon: '/icons/reload.svg',
          tip: '重新生成',
          style: {},
          handler: () => {
            this.$emit('regenerate')
          }
        }

      }
    }
  },
  methods: {
    // 复制渲染后的内容
    copyRenderedContent() {
      try {
        // 查找父组件中的 message 元素
        const messageElement = this.findMessageElement()
        if (!messageElement) {
          // 降级到复制原始文本
          copy(this.msg.content)
          return
        }

        // 获取渲染后的 HTML 内容
        const renderedHTML = this.getCleanHTML(messageElement)
        // 使用 copy-to-clipboard 库复制 HTML 格式内容
        const success = copy(renderedHTML, {
          format: 'text/html',
          debug: false
        })
        if (!success) {
          // 如果 HTML 复制失败，降级到纯文本
          copy(this.msg.content)
        }
      } catch (error) {
        console.warn('复制失败，使用降级方案:', error)
        copy(this.msg.content)
      }
    },

    // 查找包含渲染内容的 message 元素
    findMessageElement() {
      // 从当前组件开始向上查找
      let parent = this.$parent
      while (parent) {
        if (parent.$refs && parent.$refs.message) {
          return parent.$refs.message
        }
        parent = parent.$parent
      }
      return null
    },

    // 获取清理后的 HTML 内容
    getCleanHTML(element) {
      // 克隆元素以避免修改原始DOM
      const clonedElement = element.cloneNode(true)

      // 移除不需要的元素（如按钮、控制元素等）
      const unwantedSelectors = [
        '.btn-list',
        '.message-btn-list',
        '.scroll-mark',
        'button',
        '.loading'
      ]

      unwantedSelectors.forEach(selector => {
        const elements = clonedElement.querySelectorAll(selector)
        elements.forEach(el => el.remove())
      })
      return clonedElement.innerHTML
    },
  },
  computed: {
    justify () {
      if (this.msg.type === 'question') {
        return 'flex-end'
      } else {
        return 'flex-start'
      }
    },
    btnList () {
      // 通过回调，确定展示的按钮列表
      if (this.fnBtnList) {
        const btns = this.fnBtnList(this.msg)
        for (let idx=0; idx<btns.length; idx++) {
          if (btns[idx] === 'copy') {
            btns[idx] = this.copied ? 'copied' : 'copy'
          }
        }

        return btns.map((item) =>{ 
          if (typeof item === 'string') {
            return this.btnMap[item]
          } else if (typeof item === 'object' && item.name && this.btnMap[item.name]) {
            return Object.assign({}, this.btnMap[item.name], item)
          } else if (typeof item === 'object') {
            return item
          }
        })
      }

      // 没有回调，则只在回复下展示
      const btns = []

      if (this.msg.type === 'answer') {
        btns.push(this.copied ? 'copied' : 'copy')
      }

      return btns.map((name) => this.btnMap[name])
    }
  }
};
</script>

<style scoped>
.btn-list {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  align-items: center;
  margin-top: 12px;
}
.btn-list.answer {
  justify-content: flex-start;
  margin-top: 0;
}
.btn-list.answer .btn {
  margin-right: 12px;
  margin-left: 0;
}
.btn {
  display: block;
  width: 26px;
  height: 26px;
  border: 0;
  cursor: pointer;
  border-radius: 12px;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  margin-left: 10px;
  position: relative;
}
.btn:hover svg {
  color: #6AF6FF;
}
.btn svg {
  width: 16px;
  height: 16px;
  color: #6AF6FF;
  pointer-events: none;
}
</style>

<style>
.tooltip-adjust .popper__arrow {
  bottom: -5px !important;
}
</style>

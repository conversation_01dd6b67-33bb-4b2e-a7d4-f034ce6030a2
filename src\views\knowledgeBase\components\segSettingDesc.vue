<template>
    <div class="seg-setting-desc">
        <template v-if="currentSegSetting == 0">
            <h5 class="top-title">“自动分段与清洗”说明</h5>
            <p style="font-size: 14px;">
                <p>支持格式：<b>txt、pdf、docx、xlsx、xls、csv、markdown、md、html</b></p>
                <p>自动分段规则：</p>
                <ul style="padding-left: 25px;list-style: outside;">
                    <li>在处理文本时自动去除多余的空格。</li>
                    <li>自动去除网址和电子邮箱地址的操作。</li>
                    <li>使用“\n”换行符作为分割的标志。</li>
                    <li>每个分割的片段最多包含 500 个标记（取决于具体的标记定义，可能是单词、字符等）。</li>
                </ul>
                <p>适用场景：</p>
                <ul style="padding-left: 25px;list-style: outside;">
                    <li>适用于常规标准化内容，无需精细分段控制的场景。</li>
                    <li>如新闻报道、学术论文、常规文档等，系统自动识别段落、章节结构并清洗，对每个图片、视频转换为链接进行存储。</li>
                </ul>
            </p>
            <h5>示例：</h5>
            <el-row>
                <el-col :span="12">
                    <el-image style="width: 100%;" 
                        :src="knowledge_seg_temp_img['auto-1']" fit="contain" />
                </el-col>
                <el-col :span="12">
                    <el-image style="width: 100%;" 
                        :src="knowledge_seg_temp_img['auto-2']" fit="contain" />
                </el-col>
            </el-row>
        </template>
        <template v-if="currentSegSetting == 1">
            <h5 class="top-title">“自定义”说明</h5>
            <p style="font-size: 14px;">
                <p>支持格式：<b>txt、pdf、docx、xlsx、xls、csv、markdown、md、html</b></p>
                <p>自动分段规则：</p>
                <ul style="padding-left: 25px;list-style: outside;">
                    <li>根据自定义符号作为分割的标志进行分段。</li>
                    <li>可自定义分段最大长度，最大长度可设置为2000。</li>
                    <li>可选择在处理文本时会去除多余的空格、换行符和制表符。</li>
                    <li>可选择是否去除网址和电子邮箱地址的操作。</li>
                    <li>每个分割的片段最多包含自定义个数的标记（这取决于具体的标记定义，可能是单词、字符等）。</li>
                </ul>
                <p>适用场景：</p>
                <ul style="padding-left: 25px;list-style: outside;">
                    <li>适用于特殊格式、需精细控制分段的文档，对docx文档中的图片、视频转换为链接进行存储。</li>
                    <li>如专业术语多、排版特殊或需保留特定格式的文本，提供灵活分段处理。</li>
                </ul>
            </p>
            <h5>示例：</h5>
            <el-row>
                <el-col :span="12">
                    <el-image style="width: 100%;" 
                        :src="knowledge_seg_temp_img['custom-1']" fit="contain" />
                </el-col>
                <el-col :span="12">
                    <el-image style="width: 100%;" 
                        :src="knowledge_seg_temp_img['custom-1']" fit="contain" />
                </el-col>
            </el-row>
        </template>
        <template v-if="currentSegSetting == 2">
            <h5 class="top-title">“智能分段”说明</h5>
            <p style="font-size: 14px;">
                <p>支持格式：<b>pdf、docx、xlsx、xls、csv</b></p>
                <p>自动分段规则：</p>
                <ul style="padding-left: 25px;list-style: outside;">
                    <li>自动识别DOCX格式文件中的目录结构，按照章节进行分段，保留文档的原有组织结构。</li>
                    <li>表格内容按行划分，每行数据划分为一个段落，格式为“列名A: 列名A下第一行内容”。一个包含电影数据的表格将转换为：“电影名: 满江红”，“当年票房（万）: 454434.46”，“票房占比: 0.091”等。</li>
                    <li>可选择是否将嵌入的图片转换为链接存储（仅支持pdf/docx格式文档），支持自定义图片通用描述语。</li>
                    <li>可选择是否增强分段聚合能力，在分段内容前添加分段描述说明，或自定义分段描述说明内容。</li>
                </ul>
                <p>适用场景：</p>
                <ul style="padding-left: 25px;list-style: outside;">
                    <li>适用于处理单行的数据表，如销售数据表、学生成绩表。</li>
                    <li>适用于目录结构清晰的文档，如书籍、报告等自动识别目录。</li>
                </ul>
            </p>
            <h5>示例：</h5>
            <el-row>
                <el-col :span="12">
                    <el-image style="width: 100%;" 
                        :src="knowledge_seg_temp_img['advanced-1']" fit="contain" />
                </el-col>
                <el-col :span="12">
                    <el-image style="width: 100%;" 
                        :src="knowledge_seg_temp_img['advanced-2']" fit="contain" />
                </el-col>
            </el-row>
        </template>
        <template v-if="currentSegSetting == 3">
            <h5 class="top-title">“智能分段(增强)”说明</h5>
            <p style="font-size: 14px;">
                <p>支持格式：<b>pdf、docx</b></p>
                <p>智能分段(增强)规则：</p>
                <ul style="padding-left: 25px;list-style: outside;">
                    <li>将每一页内容转换为图片，自动检测图片区域中文字，将提取的文字内容转换为markdown格式。</li>
                    <li>在处理文本时自动去除多余的空格。</li>
                    <li>自动去除网址和电子邮箱地址的操作。</li>
                    <li>使用“\n”换行符作为文本分割的标志。</li>
                    <li>每个分割的片段最多包含 500 个标记（取决于具体的标记定义，可能是单词、字符等）。</li>
                    <li>可选择是否增强分段聚合能力，在分段内容前添加分段描述说明，或自定义分段描述说明内容。</li>
                </ul>
                <p>适用场景：</p>
                <ul style="padding-left: 25px;list-style: outside;">
                    <li>适用于需对扫描文件进行分析解读的文档。如扫描的数据、报告或合同等文件。</li>
                </ul>
            </p>
            <h5>示例：</h5>
            <el-row>
                <el-col :span="12">
                    <el-image style="width: 100%;" 
                        :src="knowledge_seg_temp_img['advanced_enhance-1']" fit="contain" />
                </el-col>
                <el-col :span="12">
                    <el-image style="width: 100%;" 
                        :src="knowledge_seg_temp_img['advanced_enhance-2']" fit="contain" />
                </el-col>
            </el-row>
        </template>
        <template v-if="currentSegSetting == 4">
            <h5 class="top-title">“分析报告分段”说明-分析报告并自动分段</h5>
            <p style="font-size: 14px;">
                <p>支持格式：<b>pdf、pptx（暂时仅支持不超过10页的文档）</b></p>
                <p>分析报告分段规则：</p>
                <ul style="padding-left: 25px;list-style: outside;">
                    <li>将文档转换为图片并使用多模态模型进行分析总结。</li>
                    <li>支持对模型参数进行自定义配置。</li>
                    <li>支持对自定义配置选项，允许用户设置一次可分析最大页数。若设置为1，则每页内容单独分段并分析；若设置为4，则每四页内容合并为一个段落进行总结性分析。参数设置越小（如1页/段），文档分析解读效果越精细。参数设置越大（如4页/段），文档分析解读效果相对粗略，但分段清洗速度更快。</li>
                    <li>支持是否对文档内容进行总结。选择是，对文档中图表、图像与文字内容进行分析解读，若选择否，则仅读取文档中文字内容，不对图表进行解读。</li>
                    <li>可选择是否增强分段聚合能力，在分段内容前添加分段描述说明，或自定义分段描述说明内容。</li>
                </ul>
                <p>适用场景：</p>
                <ul style="padding-left: 25px;list-style: outside;">
                    <li>适用于图表、图像与文本混合的分析报告或PPT。</li>
                    <li>如市场分析报告、科学研究展示、产品介绍等，需要对页面上数据进行分析或提取。</li>
                </ul>
            </p>
            <h5>示例：</h5>
            <el-row>
                <el-col :span="12">
                    <el-image style="width: 100%;" 
                        :src="knowledge_seg_temp_img['report-1']" fit="contain" />
                </el-col>
                <el-col :span="12">
                    <el-image style="width: 100%;" 
                        :src="knowledge_seg_temp_img['report-2']" fit="contain" />
                </el-col>
            </el-row>
        </template>
    </div>
</template>
<script setup>
import { getDicts } from '@/api/system/dict/data'
const knowledge_seg_temp_img = ref({})
const props = defineProps({
    currentSegSetting:{
        type: Number,
        default:0
    }
})

getDicts('knowledge_seg_temp_img').then((res) => {
    knowledge_seg_temp_img.value = res.data.reduce((cur, dict) => {
        cur[dict.dictValue] = dict.remark
        return cur
    }, {})
})

</script>
<style lang="scss" scoped>
.seg-setting-desc {
    font-size: 15px;
    color: #303133;
    .top-title{
        margin-top: 0;
    }
    h5 {
        font-size: 15px;
        font-weight: 600;
        margin-bottom: .5rem;
        line-height: 1.5;
        
    }
}
</style>
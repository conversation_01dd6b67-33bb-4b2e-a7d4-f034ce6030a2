<template>
  <iframe ref="iframeRef" :srcdoc="processedHtml"></iframe>
</template>

<script setup>
import { ref, computed, onUnmounted } from 'vue'

const props = defineProps({
  html: {
    type: String,
    required: true
  }
})

const replaces = [
  {
    from: /https:\/\/cdn.jsdelivr.net\/npm\/echarts@5.\d+.\d+\/dist\/echarts.min.js/g,
    to: '/echarts/5.6.0.min.js'
  }
]

const onloadScript = `
<script>
  window.onload = () => {
    window.parent.postMessage('loaded', '*')
  }
<\/script>
`
/**
 * html处理
 * 1. 替换一些第三方包的地址
 * 2. 加上onload事件
 */
const processedHtml = computed(() => {
  let proHtml = props.html

  for (const replace of replaces) {
    proHtml = proHtml.replace(replace.from, replace.to)
  }

  return proHtml + onloadScript
})


const iframeRef = ref(null)
const handleMessage = (event) => {
  if (iframeRef.value) {
    iframeRef.value.style.height = iframeRef.value.contentWindow.document.documentElement.scrollHeight + 'px';
  }
}

window.addEventListener('message', handleMessage);

onUnmounted(() => {
  window.removeEventListener('message', handleMessage);
});
</script>

<style scoped>
iframe {
	width: 100%;
	min-height: 300px;
	border: 0;
}
</style>
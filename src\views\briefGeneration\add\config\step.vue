<template>
  <div class="process-flow">
    <div :class="{ 'process-step': true, active: activeStep == 1 }">
      <div class="step-circle">1</div>
      <div class="step-text">配置简报</div>
    </div>
    <div class="step-line"></div>
    <div :class="{ 'process-step': true, active: activeStep == 2 }">
      <div class="step-circle">2</div>
      <div class="step-text">AI生成</div>
    </div>
    <div class="step-line"></div>
    <div :class="{ 'process-step': true, active: activeStep == 3 }">
      <div class="step-circle">3</div>
      <div class="step-text">简报优化</div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, h } from "vue";
import { ElIcon } from "element-plus";
import { ArrowRight, View } from "@element-plus/icons-vue";
import { useRouter } from "vue-router";
import { activeStep } from "@/views/briefGeneration/add/create/js/sharedState.js";
const router = useRouter();
// const props = defineProps({
//   active: {
//     type: String,
//     required: true,
//   },
// });
</script>

<style lang="scss" scoped>
.process-flow {
  display: flex;
  align-items: center;
  flex: 1;
  justify-content: center;
  min-width: 0;
}

.process-step {
  display: flex;
  align-items: center;
  position: relative;
  cursor: pointer;
}

.step-circle {
  width: 32px;
  height: 32px;
  line-height: 32px;
  color: rgba(255, 255, 255, 1);
  font-size: 14px;
  text-align: center;
  font-family: PingFangSC-regular;
  border: 1px solid rgba(231, 231, 231, 1);
  border-radius: 50%;
  z-index: 1;
  background-color: #c9a9e6;
  flex: none;
}
.step-text {
  color: #797a7c;
  flex: none;
  margin-left: 13px;
}
.step-line {
  flex: 1;
  min-width: 0;
  height: 1px;
  background: #1469ea;
  margin: 0 20px;
}
.active {
  .step-circle {
    background-color: #9956cd;
  }
  .step-text {
    color: #4d4e51;
  }
}
</style> 
<template>
    <div class="app-label-filter">
        <el-select v-model="value" placeholder="请选择" 
            @change="categoryChange"
            style="width: 100px;margin-right: 8px;" >
            <el-option
                v-for="item in options"
                :key="item.value"
                :label="item.label"
                :value="item.value">
            </el-option>
            <el-option
                :label="'形态'"
                :value="'mode'">
            </el-option>
        </el-select>
        <div class="tags">
            <el-check-tag :checked="primaryTag.value == currentCheckTag" 
                @click="tagClick(primaryTag)" 
                style="padding: 9px 17px;">
                {{ primaryTag.label }}
            </el-check-tag>
            <template v-for="tag in tags">
                <el-check-tag :checked="tag.value == currentCheckTag" 
                    @click="tagClick(tag)" 
                    style="padding: 9px 17px;">
                    {{tag.label}}
                </el-check-tag>
            </template>
        </div>
    </div>
</template>
<script setup>
import {getClassify} from "@/api/application/store"

const options = ref([])
const value = ref('')
const primaryTag = ref({
    label: '推荐',
    value: 'primary'
})
const tags = ref([])
const currentCheckTag = ref('')

const emits = defineEmits(['change'])

const modeTags = ref([
    {
        label: '聊天助手',
        value: 'advanced-chat'
    },
    {
        label: 'Agent',
        value: 'agent'
    },
    {
        label: '工作流',
        value: 'workflow'
    }
])

const categoryChange = (val) => {
    if (val == 'mode') {
        tags.value = modeTags.value
    } else {
        tags.value = options.value.find(item => item.value == val).tags
    }
    currentCheckTag.value = ''
    emits('change', {value: 'category-change', category:val})
}

const tagClick = (tag) => {
    currentCheckTag.value = tag.value
    emits('change', {value: 'tag-click', tag:tag.value})
}

getClassify().then(res => {
    options.value = res.data.map(item => {
        return {
            value: item.id,
            label: item.classifyName,
            tags: item.children.map(child => {
                return {
                    label: child.classifyName,
                    value: child.id
                }
            })
        }
    })
    value.value = options.value[0].value
    categoryChange(options.value[0].value)
})

</script>
<style lang="scss" scoped>
.app-label-filter{
    padding: 0 30px;
    margin-top: 10px;
    margin-bottom: 20px;
    display: flex;
    .tags{
        display: flex;
        flex-direction: row;
        gap: .5rem;
        align-items: center;
        :deep(.el-check-tag){
            background-color: white;
            font-weight: 400 ;
            color: rgb(96, 98, 102);
        }
        :deep(.el-check-tag.is-checked.el-check-tag--primary){
            background-color: #dcdcfa;
            color: #5050E6;
        }
    }
}
</style>
<template>
    <div class="doc-embed-progress">
        <div class="title">{{ '服务器处理数据中' }}</div>
        <div class="progress-list">
            <template v-for="embedfile in props.embedFiles">
                <div class="item">
                    <div class="process">
                        <el-progress  :text-inside="true" 
                            :percentage="getPercent(embedfile)" 
                            :stroke-width="26" >
                            {{ embedfile.name }}
                        </el-progress>
                    </div>
                    <div class="text">
                        {{ getPercent(embedfile) }}%
                    </div>
                </div>
            </template>
        </div>
        <div class="confirm-bottom">
            <span style="font-size: 12px;color: rgba(28,31,35,.6);margin-right: 10px;">点击确认不影响数据处理，处理完毕后可进行引用</span>
            <el-button @click="confirmSubmit" type="primary">确认</el-button>
        </div>
    </div>
</template>
<script setup>
import { useRouter } from 'vue-router';

const router = useRouter();

const props = defineProps({
    embedFiles: {
        type: Array,
        default: () => []
    },
    embedFilesProcess :{
        type: Array,
        default: () => []
    },
    knowledgeId:{
        type: String,
        default: ''
    }
})

/**
 * 获取百分比进度
 */
function getPercent(embedFile) {
    const process = props.embedFilesProcess.find(item => item.id === embedFile.id);

    if (!process) {
        return 0;
    }

    if (process.indexing_status === 'completed') {
        return 100;
    }

    if (process.indexing_status !== 'indexing') {
        return 0;
    }

    return Math.floor(process.completed_segments / process.total_segments * 100);
}

const confirmSubmit = () => {
    if(props.knowledgeId != ''){
        router.push({
            path: '/knowledgeBase/documentManagement',
            query: {
                id: props.knowledgeId
            }
        })
    }else{
        router.back()
    }
}
</script>
<style scoped lang="scss">
.doc-embed-progress{
    width: 100%;
    padding: 0 15%;
    .title{
        color: #1c1f23;
        font-size: 14px;
        font-style: normal;
        font-weight: 600;
        line-height: 22px;
        margin: 24px 0 17px;
    }
    .progress-list{
        border: 1px solid rgba(29, 28, 35, .12);
        border-radius: 8px;
        max-height: 532px;
        overflow-y: scroll;
        padding: 23px 35px 23px 24px;
        .item{
            display: flex;
            flex-direction: row;
            margin-bottom: 15px;
            .process{
                width: 95%;
            }
            .text{
                padding-left: 10px;
                align-items: center;
                box-sizing: border-box;
                color: rgba(28, 31, 35, .6);
                display: flex;
                flex-shrink: 0;
                font-size: 16px;
                font-weight: bold;
                justify-content: flex-end;
                line-height: 26px;
                padding-right: 6px;
                width: 58px;
            }
        }
    }
    .confirm-bottom{
        display: flex;
        float: right;
        justify-content: flex-end;
        line-height: 32px;
        margin-bottom: 40px;
        margin-top: 40px;
    }
}

::v-deep(.el-progress-bar__inner ){
    text-align: left;
}
</style>
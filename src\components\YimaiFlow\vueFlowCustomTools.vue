<template>
  <div class="vue-flow-custom-tools">
    <el-tooltip content="移动画布" placement="top">
      <el-button size="small" text :style="{'background-color': !isCanDrag?'white':'#D9DCFA'}" @click="clickHand" > 
          <img v-if="!isCanDrag" :src="Hand" alt="" height="12" width="12">
          <img v-else :src="ActiveHand" alt="" height="12" width="12">
      </el-button>
    </el-tooltip>
    <el-divider direction="vertical" />
    <el-tooltip content="缩小" placement="top">
      <el-button :icon="Minus" size="small" :disabled="currentZoom <= 0.5" text @click="customZoomOut(0.1)"></el-button>
    </el-tooltip>
    <div class="zoom-value">{{ (currentZoom*100).toFixed(2) + '%' }}</div>
    <el-tooltip content="放大" placement="top">
      <el-button :icon="Plus" size="small"  :disabled="currentZoom >= 2" text @click="customZoomIn(0.1)"></el-button>
    </el-tooltip>
    <el-divider direction="vertical" />
    <el-tooltip content="自适应视图" placement="top">
      <el-button :icon="FullScreen" size="small" text @click="fitView()" ></el-button>
    </el-tooltip>
    <el-divider direction="vertical" />
    <el-tooltip content="优化布局" placement="top">
      <el-button :icon="MagicStick" size="small" text @click="niceLayout()" ></el-button>
    </el-tooltip>
  </div>
</template>
<script setup>
import Hand from '@/assets/icons/svg/hand.svg'
import ActiveHand from '@/assets/icons/svg/active-hand.svg'
import { Plus,Minus,FullScreen,MagicStick} from '@element-plus/icons-vue'
import { onMounted,watchEffect,watch,defineEmits,defineProps} from 'vue'
import {Graph} from './graph.js'
import { useLayout } from './layout'

import { ElMessage } from 'element-plus';
const props = defineProps({
  currentFlow:{
    type:Object,
    required:true
  },
  canDrag:{
    type:Boolean,
    default:false
  }
})

const emit = defineEmits(['updateCanDrag'])

const isCanDrag = ref(false)
const {zoomTo,fitView} = props.currentFlow
const { graph, layout, previousDirection } = useLayout()
const nodes = ref([])
const edges = ref([])

const currentZoom = ref(1)

const customZoomIn = (val) => {
  zoomTo(currentZoom.value+val)
}

const customZoomOut = (val) => {
  zoomTo(currentZoom.value-val)
}

const clickHand = () => {
  emit('updateCanDrag',!isCanDrag.value)
}

const niceLayout = () => {
  let flowObj = props.currentFlow.toObject()
  nodes.value = flowObj.nodes
  edges.value = flowObj.edges
  flowObj.nodes = layout(nodes.value,edges.value, 'LR')
  props.currentFlow.fromObject(flowObj)
  nextTick(() => {
    fitView()
  })
}

watchEffect(() => {
  currentZoom.value = props.currentFlow.getViewport().zoom
})

watch(() => props.canDrag,(newVal)=>{
  console.log(1);
  isCanDrag.value = newVal
},{deep:true})

</script>

<style scoped lang="scss">
.vue-flow-custom-tools {
  // Add your styles here
  position: absolute;
  bottom: 70px;
  right: 10px;
  z-index: 9;
  padding: 0;
  display: flex;
  flex-direction: row;
  border-radius: 12px;
  align-items: center;
  padding: 8px 16px;
  background-color: white;
  box-shadow: 0 0 1px 0 rgba(0, 0, 0, .3), 0 4px 14px 0 rgba(0, 0, 0, .1) !important;
  .zoom-value {
    margin: 0 8px;
    font-size: 14px;
    color:rgba(29, 28, 35, .8)
  }
}
</style>
<template>
  <el-drawer
    :value="value"
    @update:value="(val) => $emit('update:value', val)"
    direction="rtl"
    title="知识来源"
    size="517px"
  >
    <template #header>
      <div class="popup-header">知识来源</div>
    </template>
    <div class="popup-container">
      <div class="popup-subtitle">标题二可点击知识来源标题二可点击</div>
      <div class="popup-content">{{ content }}</div>
    </div>
  </el-drawer>
</template>

<script setup>
import { ref } from 'vue'
import { ElDrawer } from 'element-plus'

const props = defineProps({
  value: {
    type: Boolean,
    required: true
  },
  title: {
    type: String,
    required: false,
    default: ''
  }
})

const content = ref('知识来源二相关内容文案知识来源二相关内容知识来源二相关内容文案知识来源二相关内容知识来源二相关内容文案知识来源二相关内容知识来源二相关内容文案知识来源二相关内容知识来源二相关内容文案知识来源二相关内容知识来源二相关内容文案知识来源二相关内容知识来源二相关内容文案知识来源二相关内容知识来源二相关内容文案知识来源二相关内容知识来源二相关内容文案知识来源二相关内容知识来源二相关内容文案知识来源二相关内容知识来源二相关内容文案知识来源二相关内容知识来源二相关内容文案知识来源二相关内容知识来源二相关内容文案知识来源二相关内容知识来源二相关内容文案知识来源二相关内容知识来源二相关内容文案知识来源二相关内容知识来源二相关内容文案知识来源二相关内容知识来源二相关内容文案知识来源二相关内容知识来源二相关内容文案知识来源二相关内容')

const emit = defineEmits(['visible'])
const closeThePop = () => {
  emit('toVisible', false)
}
</script>

<style lang="scss" scoped>
:deep(.el-drawer__close) {
  position: relative;
  top: 10px;
}
.el-drawer__header {
  padding: 0;
  margin: 0;
  .popup-header {
    width: 80px;
    height: 20px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 600;
    font-size: 20px;
    color: #848691;
    margin-top: 20px;
    padding-left: 20px;
  }
}
.popup-container {
  padding: 0 40px;
}
.el-drawer__body {
  padding: 0;
  .popup-subtitle {
    // margin-top: 20px;
    margin-top: 8px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 600;
    font-size: 20px;
    color: #1e1f24;
    line-height: 20px;
    cursor: pointer;
  }
  .popup-content {
    margin-top: 40px;
    width: 100%;
    // height: calc(100% - 160px);
    // border: 1px solid red;
    overflow-y: auto;

    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 14px;
    color: #1e1f24;
    line-height: 22px;
    text-indent: 2em;
  }
}
</style>

import request from '@/utils/request'

// 获取文献综述历史记录
export function getLiteratureReviewHistory (params) {
    return request({
        url: '/summarize/list',
        method: 'get',
        params
    })
}

// 获取论文大纲历史记录
export function getOutlineHistory (params) {
    return request({
        url: '/outline/list',
        method: 'get',
        params
    })
}

// 获取学术检索历史记录
export function getScholarHistory (params) {
    return request({
        url: '/paper/search/historylist',
        method: 'get',
        params
    })
}

// 获取智能选题历史记录
export function getSelectionHistory (params) {
    return request({
        url: '/selection/historylist',
        method: 'get',
        params
    })
}

// 文献历史记录删除
export function deleteLiteratureReviewHistory (id) {
    return request({
        url: '/summarize/remove/' + id,
        method: 'delete',
    })
}

// 论文大纲历史记录删除
export function deleteOutlineHistory (data) {
    return request({
        url: '/outline/delete',
        method: 'post',
        data
    })
}
// 学术检索历史记录删除
export function deleteScholarHistory (data) {
    return request({
        url: '/paper/search/delete',
        method: 'post',
        data
    })
}

// 智能选题历史记录删除
export function deleteSelectionHistory (data) {
    return request({
        url: '/selection/delete',
        method: 'post',
        data
    })
}

// AI学术检索
export function saveAIScholar (data) {
    return request({
        url: '/paper/search/save',
        method: 'POST',
        data
    })
}

// AI学术检索历史记录详情
export function detailsAIScholar (params) {
    return request({
        url: '/paper/search/history',
        method: 'get',
        params
    })
}

// AI学术检索
export function getAIOutline (params) {
    return request({
        url: '/paper/search/list',
        method: 'get',
        params
    })
}

// AI学术检索 批量收藏
export function collectAIScholar (data) {
    return request({
        url: '/literature/addBatch',
        method: 'post',
        data
    })
}

// AI学术检索 单次收藏
export function collectAIScholarOnce (data) {
    return request({
        url: '/literature/add',
        method: 'post',
        data
    })
}
// 智能选题 保存历史
export function saveSelectionHistory (data) {
    return request({
        url: '/selection/save',
        method: 'post',
        data
    })
}

// 文献综述 保存历史
export function saveLiteratureReviewHistory (data) {
    return request({
        url: '/summarize/save',
        method: 'post',
        data
    })
}

// 论文大纲 保存历史
export function saveOutlineHistory (data) {
    return request({
        url: '/outline/save',
        method: 'post',
        data
    })
}

// 智能选题 历史记录详情
export function detailsSelectionHistory (params) {
    return request({
        url: '/selection/history',
        method: 'get',
        params
    })
}

// 文献综述 历史记录详情
export function detailsLiteratureReviewHistory (id) {
    return request({
        url: '/summarize/getHistoryInfo/' + id,
        method: 'get',
    })
}


// 论文大纲 历史记录详情
export function detailsOutlineHistory (params) {
    return request({
        url: '/outline/one',
        method: 'get',
        params
    })
}

// 文献综述 编辑历史记录
export function editLiteratureReviewHistory (data) {
    return request({
        url: '/summarize/edit',
        method: 'post',
        data
    })
}

// 论文大纲 编辑历史记录
export function editOutlineHistory (data) {
    return request({
        url: '/outline/update/context',
        method: 'post',
        data
    })
}


// AIGC检测，历史记录，保存
export function aigcSaveHistory (data) {
    return request({
        url: '/aigc/detect/save',
        method: 'post',
        data
    })
}

// AIGC检测，历史记录，删除
export function aigcDelHistory (data) {
    return request({
        url: '/aigc/detect/delete',
        method: 'post',
        data
    })
}

// AIGC检测，历史记录，列表
export function aigcGetHistoryList (params) {
    return request({
        url: '/aigc/detect/list',
        method: 'get',
        params
    })
}

// AIGC检测，历史记录，详情
export function aigcGetHistoryDetail (params) {
    return request({
        url: '/aigc/detect/one',
        method: 'get',
        params
    })
}

// 智能预审，历史记录，保存
export function preReviewSaveHistory (data) {
    return request({
        url: '/pre/review/save',
        method: 'post',
        data
    })
}

// 智能预审，历史记录，删除
export function preReviewDelHistory (id) {
    return request({
        url: `/pre/review/remove/${id}`,
        method: 'delete'
    })
}

// 智能预审，历史记录，列表
export function preReviewGetHistoryList (params) {
    return request({
        url: '/pre/review/list',
        method: 'get',
        params
    })
}

// 智能预审，历史记录，详情
export function preReviewGetHistoryDetail (id) {
    return request({
        url: `/pre/review/query/${id}`,
        method: 'get',
    })
}
// 智能翻译
export function intelligentTranslation (params) {
    return request({
        url: `/paper/search/translate`,
        method: 'get',
        params
    })
}

// pdf解析进度查询
export function pdfProgress (params) {
  return request({
    url: "/pre/review/pdf/parse/task",
    method: "get",
    params,
  })
}

// pdf生成物查询
export function pdfParse(data) {
  return request({
    url: "/pre/review/pdf/parse",
    method: "post",
    data,
  })
}
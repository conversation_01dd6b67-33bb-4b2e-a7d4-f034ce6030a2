<template>
  <el-dialog
    v-model="isVisible"
    :title="title"
    width="350px"
  >
    <el-form :model="form" :rules="rules" ref="formRef" label-position="top">
        <el-form-item label="名称" prop="keyName">
            <el-input v-model="form.keyName" :maxlength="20"></el-input>
        </el-form-item>
        <el-form-item label="过期日期" prop="expirationTime">
            <el-date-picker
                v-model="form.expirationTime"
                type="date"
                placeholder="-"
                :editable="false"
                style="width: 100%;"
                :disabled-date="(date) => date.getTime() <= Date.now() "
            />
        </el-form-item>
    </el-form>
    <template #footer>
        <el-button @click="isVisible = false">取消</el-button>
        <el-button type="primary" @click="save">保存</el-button>
    </template>
  </el-dialog>
</template>

<script setup>
import { updateApiKey, addApi<PERSON>ey } from '@/api/agent/apikey';
import { formatDate } from '@/utils/index.js';
import { nextTick } from 'vue';

const props = defineProps({
    // 智能体ID
    agentId: {
        type: String,
        required: true
    }
});

const emit = defineEmits(['add-success', 'update-success']);


// 是否打开弹窗
const isVisible = ref(false);

// 标题
const title = ref('');

// 表单
const form = ref({
    id: null,
    keyName: '',
    expirationTime: new Date()
});

// 表单组件的引用
const formRef = ref(null);

// 校验规则
const rules = {
    keyName: [
        { type: 'string', required: true, message: '请输入名字', trigger: 'blur' }
    ]
};

/**
 * 打开弹窗，添加
 * @param {*} theKey 
 */
function openAdd() {
    title.value = '添加密钥'

    form.value.id = '';
    form.value.keyName = '';
    form.value.expirationTime = '';

    isVisible.value = true;
    nextTick(() => {
        formRef.value.clearValidate();
    });
}

/**
 * 打开弹窗，编辑
 * @param {*} row 
 */
function openEdit(row) {
    title.value = '修改密钥'

    form.value.id = row.id;
    form.value.keyName = row.keyName;
    form.value.expirationTime = row.expirationTime;

    isVisible.value = true;
    nextTick(() => {
        formRef.value.clearValidate();
    });
}


/**
 * 保存
 */
function save() {
    const expirationTime = form.value.expirationTime
        ? formatDate(new Date(form.value.expirationTime))
        : undefined;

    // 修改
    if (form.value.id) {
        updateApiKey({
            id: form.value.id,
            applicationType:"agent",
            keyName: form.value.keyName,
            expirationTime 
        }).then(() => {
            isVisible.value = false;
            emit('update-success');
        })

    // 添加
    } else {
        addApiKey({
            agentId: props.agentId,
            applicationType:"agent",
            keyName: form.value.keyName,
            expirationTime
        }).then((rs) => {
            isVisible.value = false;
            emit('add-success', rs.data.keyValue);
        });
    }
}

defineExpose({
    openAdd, openEdit
})
</script>

<style scoped>
.icon-key {
    width: 1.5em;
    height: 1.5em;
    color: cadetblue;
}
.title-wrapper {
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
}
.key-wrapper {
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
    width: 100%;
    height: 80px;
    border: 0;
    background-color: #eeeeee;
    border-radius: 5px;
    overflow: hidden;
    margin-top: 10px;
}
.key-wrapper .key {
    text-wrap: wrap;
    word-break: break-all;
    line-height: 1.5;
    padding: 0 5px 0 15px;
}
</style>

<template>
  <el-dialog
    :close-on-click-modal="false"
    :model-value="props.visible"
    :show-close="false"
    width="364px"
    @close="handleClose"
    style="
      width: 500px;
      min-height: 220px;
      border-radius: 8px !important;
      --el-dialog-bg-color: rgba(11, 83, 116, 0.8);
      --el-dialog-box-shadow: 2px 2px 10px 2px rgba(36, 66, 179, 0.4);
      --el-input-border-color: #05c0dc;
      --el-border-color: #05c0dc;
      --el-border-color-hover: #05c0dc;
      --el-fill-color-blank: rgba(0, 229, 255, 0.18);
      --el-text-color-regular: #fff;
    "
  >
    <template #title>
      <div class="titlebk">
        <div class="titletxt">{{ "新建简报" }}</div>
        <i
          class="ri-close-line"
          style="font-size: 18px; cursor: pointer; color: #63e5fe"
          @click="handleClose"
          alt="关闭"
        ></i>
      </div>
    </template>
    <div class="upload-content">
      <div class="upload-title">
        <span class="upload-text" style="color: #4bafff">简报标题</span>
      </div>
      <div class="selectKnowledgePoints">
        <!-- @input=" () => { checkNameUniqueness(); } " -->
        <el-input
          style="width: 100% !important"
          v-model="name"
          placeholder="请输入简报标题"
          maxlength="20"
        />
        <div v-if="errors.name" class="error_tip">{{ errors.name }}</div>
      </div>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <!-- <el-button class="cancel-btn" @click="handleClose">取 消</el-button> -->
        <!-- <el-button class="delete-btn" type="primary" @click="handleConfirm"
          >确 定</el-button
        > -->
        <div class="btn" @click="handleConfirm">确定</div>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, watch, onMounted } from "vue";
import { useRouter } from "vue-router";
import { ElMessage } from "element-plus";
import { configData } from "@/views/briefGeneration/add/create/js/sharedState.js";
const router = useRouter();

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(["update:visible"]);

const name = ref(""); // 简报名称
const errors = ref({
  name: "",
}); // 错误信息对象

// 监听 visible 的变化
watch(
  () => props.visible,
  (newVisible) => {
    if (newVisible) {
      configData.value = {
        name: "",
        brandName: "",
        version: 1, // 简报版本
        dataBank: false,
        internet: false,
        labelConfigId: 1,
      };
    } else {
      // 弹窗关闭时，清空表单
      name.value = "";
      errors.value = {
        name: "",
      };
    }
  },
  { immediate: true }
);

const handleClose = () => {
  emit("update:visible", false);
};

const handleConfirm = () => {
  console.log("简报名称:", name.value);

  // 验证必传项
  let hasErrors = false;

  if (!name.value) {
    // ElMessage.warning('请输入课程名称')
    errors.value.name = "课程名称不能为空";
    hasErrors = true;
  }

  if (errors.value.name) {
    hasErrors = true;
  }

  if (hasErrors) {
    return;
  }
  configData.value.title = name.value;
  localStorage.setItem("configData", JSON.stringify(configData.value));
  router.push("/briefGeneration/add/config");
  emit("update:visible", false);
};
// const courseList = ref([]);

// // 检查课程名称是否唯一
// const checkNameUniqueness = () => {
//   if (props.course?.id) {
//     console.log("you", props.course.id);
//     errors.value.name = "";
//     return;
//   }

//   if (courseList.value.some((course) => course.name == name.value)) {
//     errors.value.name = "课程名称已重复";
//   } else {
//     // 如果需要确保名称唯一，可以考虑清空错误信息
//     errors.value.name = "";
//   }
// };

onMounted(() => {});
</script>

<style lang="scss" scoped>
.selectKnowledgePoints {
  margin-top: 12px;
  position: relative;
}

.titlebk {
  width: 100%;
  height: 24px;
  margin-top: 8px;
  display: flex;
  align-items: center;
  justify-content: space-between;

  .titletxt {
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 16px;
    color: #4bafff;
    line-height: 16px;
  }
}

.error_tip {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 11px;
  color: #f53f3f;
  line-height: 12px;
  text-align: left;
  font-style: normal;
  // margin-top: 8px;
  position: absolute;
  bottom: -16px;
}
.dialog-footer {
  width: 100%;
  text-align: center;
}
.btn {
  background: #2442b3;
  border-radius: 20px;
  border: 1px solid #05c0dc;
  color: #fff;
  padding: 4px 32px;
  display: inline-block;
  margin-top: 35px;
  cursor: pointer;
}
</style>
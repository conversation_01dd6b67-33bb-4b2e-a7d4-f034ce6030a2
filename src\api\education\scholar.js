import request from "@/utils/request";

// 科研收藏论文列表
export function getLiteratureList(params) {
  return request({
    url: "/literature/list",
    method: "get",
    params,
  });
}

// 科研收藏论文删除
export function delLiterature(data) {
  return request({
    url: `/literature/remove/${data.id}`,
    method: "delete",
    data
  });
}

// 收藏目录（树状结构）
export function getResearchcatalog(params) {
  return request({
    url: "/researchcatalog/catalog",
    method: "get",
    params,
  });
}

// 科研收藏上传
export function uploadFile(data,onUploadProgress) {
  return request({
    url: "/literature/uniteupload",
    method: "post",
    data,
    onUploadProgress
  });
}

// 科研收藏上传
export function addBatch(data) {
  return request({
    url: "/literature/addBatch",
    method: "post",
    data,
  });
}

// 论文研读目录树
export function saveResearchCatalog(data) {
  return request({
    url: "/researchcatalog/catalog/save",
    method: "post",
    data,
  });
}
// 论文研读目录树删除
export function delResearchCatalog(data) {
  return request({
    url: "/researchcatalog/catalog/delete",
    method: "post",
    data,
  });
}
// 科研收藏论文详情
export function getLiteratureQueryInfo(params) {
  return request({
    url: `/literature/queryInfo/${params.ids}`,
    method: "get",
  });
}
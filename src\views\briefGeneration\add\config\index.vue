<!-- AI学术检索 -->
<template>
  <div class="container">
    <div class="ask-header">{{ configData.title }}</div>
    <ask :showEmitBtn="true" :maxChars="2000" />
  </div>
</template>

<script setup>
import { ref, watch } from "vue";
import Ask from "./Ask.vue";
import { configData } from "@/views/briefGeneration/add/create/js/sharedState.js";
import { useRouter } from "vue-router";
import { ElMessage } from "element-plus";
const router = useRouter();
onMounted(() => {
  const saved = localStorage.getItem("configData");
  if (saved) {
    configData.value = JSON.parse(saved);
  }
  if (!configData.value.title) {
    ElMessage({
      type: "error",
      message: "当前未配置简报名称",
      duration: 300,
    });
    setTimeout(() => {
      router.push("/briefGeneration/list");
    }, 300);
  }
});
</script>

<style>
.myTooltips {
  max-width: 50%;
}
</style>
<style lang="scss" scoped>
.container {
  display: flex;
  height: 100%;
  width: 750px;
  margin: 0 auto;
  flex-direction: column;
  justify-content: center;
  position: relative;
  // background: #0d1e4f;
  .ask-header {
    text-align: left;
    margin-bottom: 23px;
    color: rgba(97, 207, 220, 1);
    font-size: 20px;
  }
}
</style>

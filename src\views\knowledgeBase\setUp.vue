<template>
    <div class="common-container">
        <div class="single-page-header">
            <div class="left-box">
                <!-- <el-icon style="margin-right: 10px;"><Back /></el-icon> -->
                <el-button :icon="Back" @click="back()" text style="margin-right: 10px;" link></el-button>
                <img :src="knowledgeBaseIcon" />
                <div class="text-box">
                    <div class="title">
                        知识库设置
                        <!-- <el-icon size="20px" style="margin-left: 4px;color: #5050E6;"><Edit /></el-icon> -->
                    </div>
                    <div class="detail">
                        在这里您可以修改知识库的工作方式以及其它设置。
                    </div>
                </div>
            </div>
            <div  class="right-box">
                <el-button type="primary" style="color: white;font-size: 14px;font-weight: 500;"
                @click="preserve()">保存</el-button>
            </div>
        </div>
        <div class="subject">
            <el-row style="padding:30px 70px">
                <el-col>
                    <el-row>
                        <el-col :span="4">
                            <span style="line-height: 35px;">知识库名称</span>
                        </el-col>
                        <el-col :span="12">
                            <el-input v-model="knowledgeBaseInfo.name" placeholder="请输入内容" style="width: 100%;" type="text" :maxlength="40" />
                        </el-col>
                    </el-row>
                    <el-row class="knowledge-base-name">
                        <el-col :span="4">
                            <span class="title">知识库描述</span>
                        </el-col>
                        <el-col :span="12">
                            <el-input show-word-limit v-model="knowledgeBaseInfo.description" placeholder="请输入内容" style="width: 100%;" type="textarea" :rows="6" :maxlength="400" />
                            <!-- <div class="prompt">
                                <el-icon><Reading /></el-icon>
                                了解如何编写更好的知识库描述。
                            </div> -->
                        </el-col>
                    </el-row>
                    <el-row class="visible-permissions">
                        <el-col :span="4">
                            <span class="title">可见权限</span>
                        </el-col>
                        <el-col :span="12">
                            <el-row>
                                <el-col :span="12" style="padding-right:10px">
                                    <div class="radio" @click="knowledgeBaseInfo.permission='only_me'" 
                                    :class="knowledgeBaseInfo.permission=='only_me'?'radio-sel':''">
                                        <el-row>
                                            <el-col :span="5" class="image-box">
                                                <img :src="onlyme" style="background-color:#52acff54;" />
                                            </el-col>
                                            <el-col :span="16">
                                                <span>只有我</span>
                                            </el-col>
                                            <el-col :span="3" style="text-align: end;">
                                                <el-radio-group v-model="knowledgeBaseInfo.permission">
                                                    <el-radio :label="'only_me'">{{ '' }}</el-radio>
                                                </el-radio-group>
                                            </el-col>
                                        </el-row>
                                    </div>
                                </el-col>
                                <el-col :span="12" style="padding-left:10px">
                                    <div class="radio" @click="knowledgeBaseInfo.permission='all_team_members'"
                                    :class="knowledgeBaseInfo.permission=='all_team_members'?'radio-sel':''">
                                        <el-row>
                                            <el-col :span="5" class="image-box">
                                                <img :src="team" style="background-color:#52acff54;" />
                                            </el-col>
                                            <el-col :span="16">
                                                <span>所有团队成员</span>
                                            </el-col>
                                            <el-col :span="3" style="text-align: end;">
                                                <el-radio-group v-model="knowledgeBaseInfo.permission">
                                                    <el-radio :label="'all_team_members'">{{ '' }}</el-radio>
                                                </el-radio-group>
                                            </el-col>
                                        </el-row>
                                    </div>
                                </el-col>
                            </el-row>
                        </el-col>
                    </el-row>
                    <!-- <el-row class="indexing-model">
                        <el-col :span="4">
                            <span class="title">索引模式</span>
                        </el-col>
                        <el-col :span="12">
                            <el-row>
                                <el-col :span="12" style="padding-right:10px">
                                    <div class="radio" @click="knowledgeBaseInfo.indexing_technique=1" 
                                    :class="knowledgeBaseInfo.indexing_technique==1?'radio-sel':''">
                                        <el-row>
                                            <el-col :span="5" class="image-box">
                                                <img :src="hightquality" style="background-color:#ffa15254;" />
                                            </el-col>
                                            <el-col :span="19">
                                                <div class="title">
                                                    <span>高质量</span>
                                                    <el-radio-group v-model="knowledgeBaseInfo.indexing_technique">
                                                        <el-radio :label="1">{{ '' }}</el-radio>
                                                    </el-radio-group>
                                                </div>
                                                <div class="content">
                                                    调用 OpenAl 的嵌入接口进行处理，以在用户查询时提供更高的准确度
                                                </div>
                                            </el-col>
                                        </el-row>
                                    </div>
                                </el-col>
                                <el-col :span="12" style="padding-left:10px">
                                    <div class="radio" @click="knowledgeBaseInfo.indexing_technique=2"
                                    :class="knowledgeBaseInfo.indexing_technique==2?'radio-sel':''">
                                        <el-row>
                                            <el-col :span="5" class="image-box">
                                                <img :src="economic" style="background-color:#52acff54;" />
                                            </el-col>
                                            <el-col :span="19">
                                                <div class="title">
                                                    <span>经济</span>
                                                    <el-radio-group v-model="knowledgeBaseInfo.indexing_technique">
                                                        <el-radio :label="2">{{ '' }}</el-radio>
                                                    </el-radio-group>
                                                </div>
                                                <div class="content">
                                                    使用离线的向量引擎、关键词索引等方式，降低了准确度但无需花费 Token
                                                </div>
                                            </el-col>
                                        </el-row>
                                    </div>
                                </el-col>
                            </el-row>
                        </el-col>
                    </el-row> -->
                    <el-row class="embedding-model">
                        <el-col :span="4">
                            <span class="title">Embedding 模型</span>
                        </el-col>
                        <el-col :span="12">
                            <model-selector
                                type="text-embedding"
                                style="width: 100%;"
                                :enable-default-model="true"
                                v-model:model-name="knowledgeBaseInfo.embedding_model"
                                v-model:model-provider="knowledgeBaseInfo.embedding_model_provider"
                            />
                        </el-col>
                    </el-row>
                    <el-row class="retrieve-settings-box">
                        <el-col :span="4">
                            <span class="title">检索设置</span>
                        </el-col>
                        <el-col :span="12">
                            <retrieve-settings v-model="knowledgeBaseInfo.retrieval_model"></retrieve-settings>
                        </el-col>
                    </el-row>
                </el-col>
            </el-row>
        </div>
    </div>
</template>

<script setup>
import { nextTick, ref } from 'vue'
import {Back,Reading,QuestionFilled,Search } from '@element-plus/icons-vue'
import { getKnowledgeBaseInfo,editKnowledgeBase } from "@/api/knowledgeBase/knowledgeBase";
import router from "@/router";
import documentation from "@/assets/images/knowledgeBase.png";
import hightquality from "@/assets/icons/svg/hightquality.svg";
import economic from "@/assets/icons/svg/economic.svg";
import onlyme from "@/assets/icons/svg/onlyme.svg";
import team from "@/assets/icons/svg/team.svg";
import model from "@/assets/icons/svg/model.svg";
import vectorretrieval from "@/assets/icons/svg/vectorretrieval.svg";
import rerankmodel from "@/assets/icons/svg/rerankmodel.svg";
import warning from "@/assets/icons/svg/warning.svg";
import fullTextSearch from "@/assets/icons/svg/fullTextSearch.svg";
import mixedRetrieval from "@/assets/icons/svg/mixedRetrieval.svg";
import retrieveSettings from '@/components/retrieveSettings/index.vue'
import ModelSelector from '@/components/Model/modelSelector.vue';
import { ElMessage } from 'element-plus'
import knowledgeBaseIcon from "@/assets/icons/svg/knowledgeBaseIcon.svg";

const { proxy } = getCurrentInstance();

const route = useRoute();
const knowledgeId = route.query.knowledgeId

const knowledgeBaseInfo = ref({
    name:'',
    description:'',
    permission: "only_me",
    indexing_technique: "high_quality",
    embedding_model:'',
    embedding_model_provider: '',
    retrieval_model:{
        search_method:'semantic_search',
        reranking_enable:false,
        reranking_model:{
            reranking_provider_name:'',
            reranking_model_name:''
        },
        top_k:3,
        score_threshold_enabled:false,
        score_threshold:null
    }
})

//检索设置列表
const retrievalList = ref([
    {search_method:'semantic_search',title:'向量检索',img:vectorretrieval,content:'通过生成查询嵌入并查询与其向量表示最相似的文本分段'},
    {search_method:'full_text_search',title:'全文检索',img:fullTextSearch,content:'索引文档中的所有词汇，从而允许用户查询任意词汇，并返回包含这些词汇的文本片段'},
    {search_method:'keyword_search',title:'混合检索',img:mixedRetrieval,content:'同时执行全文检索和向量检索，并应用重排序步，从两类查询结果中选择匹配用户问题的最佳结果，需配置 Rerank 模型 API'},
])

//Embedding 模型下拉列表
const embeddingModeList = ref([
    {model:'text-embedding-3-large'},
    {model:'text-embedding-3-small'},
    {model:'text-embedding-ada-002'},
])

const rerankSel = ref('')
//Rerank 模型下拉列表
const rerankModelList = ref([
    {id:1,type:'API Catalog',modelList:[
        {id:1,model:'nv-rerank-ga-mistral-4b:1',img:model}
    ]},
    {id:2,type:'Cohere',modelList:[
        {id:2,model:'rerank-english-v2.0',img:model},
        {id:3,model:'rerank-english-v3.0',img:model},
        {id:4,model:'rerank-multilingual-v2.0',img:model},
        {id:5,model:'rerank-multilingual-v3.0',img:model},
    ]},
    {id:3,type:'Jina',modelList:[
        {id:6,model:'jina-colbert-v1-en',img:model},
        {id:7,model:'jina-reranker-v1-base-en',img:model},
    ]},
])

//返回
const back = () =>{
    if(knowledgeId){
        router.push("/knowledgeBase/documentManagement?id=" + knowledgeId);
    }else{
        router.push("/knowledgeBase");
    }
}

//获取知识库信息
const getKnowledgeBaseInfoMethod = () => {
    if(!knowledgeId){
        router.push("/knowledgeBase");
    }else{
        getKnowledgeBaseInfo(knowledgeId).then(response=>{
            // knowledgeBaseInfo.value=response
            knowledgeBaseInfo.value.id=response.id
            knowledgeBaseInfo.value.name=response.name
            knowledgeBaseInfo.value.permission=response.permission
            knowledgeBaseInfo.value.embedding_model_provider = response.embedding_model_provider
            knowledgeBaseInfo.value.description=response.description
            knowledgeBaseInfo.value.embedding_model=response.embedding_model
            knowledgeBaseInfo.value.retrieval_model=response.retrieval_model_dict
        })
    }
}

//保存
const preserve = () => {
    if(knowledgeBaseInfo.value.retrieval_model.search_method == 'hybrid_search'
    && (knowledgeBaseInfo.value.retrieval_model.reranking_model.reranking_model_name == null
    || knowledgeBaseInfo.value.retrieval_model.reranking_model.reranking_model_name == "")){
        ElMessage.warning('请选择Rerank 模型！')
        return
    }
    if(knowledgeBaseInfo.value.retrieval_model.reranking_enable && knowledgeBaseInfo.value.retrieval_model.search_method!='hybrid_search'
    && (knowledgeBaseInfo.value.retrieval_model.reranking_model.reranking_model_name == null
    || knowledgeBaseInfo.value.retrieval_model.reranking_model.reranking_model_name == "")){
        ElMessage.warning('请选择Rerank 模型或关闭Rerank 模型！')
        return
    }
    editKnowledgeBase(knowledgeBaseInfo.value).then(response=>{
        proxy.$modal.msgSuccess("修改成功");
        getKnowledgeBaseInfoMethod()
    })
}

getKnowledgeBaseInfoMethod()
</script>
<style scoped lang="scss">
    .common-container {
        display: flex;
        flex-direction: column;
        background-color: #FFFFFF;
        // height: 100vh !important; 
        // calc(100vh - 52px);
        width: 100%;
        padding: 0;
        background-size: cover;
        background-color: #F7F7FA;
        height: calc(100vh - 50px);
    }

    .single-page-header{
        height: 80px;
        display: flex;
        background-color: white;
        margin: 0 20px;
        justify-content: space-between;
        align-items: center;
        flex-direction: row;
        border-radius: 8px;
        .left-box{
            margin-left:24px;
            display: flex;
            align-items: center;
            .text-box{
                display: flex;
                flex-direction: column;
                margin-left: 10px;
                .title{
                    display: flex;
                    align-items: center;
                    height: 25px;
                    font-size: 18px;
                    font-weight: 500;
                    color: #032220;
                }
                .detail{
                    height: 17px;
                    font-size: 12px;
                    color: #999999;
                    font-weight: 400;
                    margin-top: 4px;
                }
            }
            img {
                height: 40px;
                width: 40px;
                border-radius: 4px;
                // border: 1px solid #E6E6E6;
            }
        }
        .right-box{
            margin-right: 20px;
        }
    }

    .min-title{
        font-size:18px;
        font-weight: 500;
    }

    ::v-deep .el-dialog {
        display: flex;
        flex-direction: column;
        margin: 0 !important;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        max-height: calc(100% - 30px);
        max-width: calc(100% - 30px);
    }

    ::v-deep(.el-dialog__body) {
        padding: 0;
    }

    ::v-deep(.el-card__body){
        padding:5px !important
    }
    ::v-deep(.el-progress__text){
        min-width:0 !important
    }
    ::v-deep(.el-card__footer){
        padding:5px 0px !important
    }
    
    .subject{
        height: calc(100vh - 150px);
        background-color: white;
        padding: 20px;
        margin: 10px 20px 0 20px;
        border-radius: 8px;
        overflow: auto;
        .knowledge-base-name{
            margin-top: 20px;
            .title{
                line-height: 35px;
            }
            .prompt{
                font-size: 12px;
                color: #999999;
                font-weight: 400;
                display: flex;
                align-items: center;
                margin-top: 5px;
            }
        }
        .visible-permissions{
            margin-top: 20px;
            .title{
                line-height: 35px;
            }
            .radio{
                height:45px;
                line-height:45px;
                border:1px solid #dcdfe6;
                border-radius:10px;
                cursor: pointer;
                .image-box{
                    text-align:center;
                    padding-top:6px;
                    img{
                        height: 25px;
                        width: 25px;
                        border-radius: 4px;
                    }
                }
            }
            .radio-sel{
                border: 1px solid #5050E6;
            }
        }
        .indexing-model{
            margin-top: 20px;
            .title{
                line-height: 35px;
            }
            .radio{
                height:100px;
                // width:45%;
                border:1px solid #dcdfe6;
                border-radius:10px;
                cursor: pointer; 
                .image-box{
                    text-align:center;
                    padding-top:10px;
                    img{
                        height: 25px;
                        width: 25px;
                        border-radius: 4px;
                    }
                }
                .title{
                    margin-top:5px;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;

                }
                .content{
                    font-size: 13px;
                    color:#999999;
                    padding-right: 5px;
                    height:50px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                }
            }
            .radio-sel{
                border: 1px solid #5050E6;
            }
        }
        .embedding-model{
            margin-top: 20px;
            .title{
                line-height: 35px;
            }
            .image-box{
                text-align:center;
                padding-top:10px;
                img{
                    height: 25px;
                    width: 25px;
                    border-radius: 4px;
                }
            }
            .option-title{
                cursor: pointer; 
                line-height: 32px;
                // width:125px;
                background-color:#f3f3f3;
                border-radius: 8px;
                padding:0 5px
            }
        }
        .retrieve-settings-box{
            margin-top: 20px;
            .title{
                line-height: 35px;
            }
        }
    }
</style>
<style lang="scss">
    .popover-my{
        .popover-option{
            cursor: pointer; 
            line-height:20px;
            font-size:12px;
            color: #032220;
            border-radius: var(--radius-sm, 4px);
        }
        .tab_click{
            background: #f3f3f3; 
        }
        .image-box{
            text-align:center;
            padding-top:6px;
            img{
                height: 25px;
                width: 25px;
                border-radius: 4px;
            }
        }
        .option-height{
            height: 330px;
            overflow: auto;
        }
    }

    .tooltip-pop.el-popper{
        max-width: 200px !important;
    }
</style>

import { reactive, ref } from "vue";
import md from "./markdown";

export class MessageItem {
  isUser;
  content;
}

class MsgRecord {
  role;
  content;
}

export class MessageItemManager {
  refMessageItemList = reactive([]);
  nowMessageText = "";
  nowMessageHtml = ref("");
  nowMessageRole = ref({});
  nowMessageLine = ref("");
  nowMessageProcessStatus = ref([])
  nowMessageEvent = ref('')
  nowMessageRelation = ref('')
  nowMessageChoices = ref([])
  nowMessageKnowledgeList = ref([])
  nowMessageAutosuggestion = ref('')
  loading = ref(false);
  waitting = ref(false);
  msgRecords = ref([]);

  pushUserMesage(content) {
    this.refMessageItemList.push({ isUser: true, content: content });
    this.msgRecords.value.push({ role: "user", content: content });
  }

  loadHistoryMessage(messages) {
    messages.forEach((item) => {
      this.refMessageItemList.push({ isUser: true, content: item.userMessage });
      this.refMessageItemList.push({ isUser: false, content: item.agentMessage , roleInfo:this.nowMessageRole.value});
    })
  }

  loadTeamHistoryMessage(messages) {
    messages.forEach((item) => {
      // this.refMessageItemList.push({ isUser: true, content: item.userMessage });
      JSON.parse(item.teamMessage).forEach((item) => {
        this.refMessageItemList.push({ isUser: false, content: item.agentMessage , roleInfo:{key:item.agent,name:''}});
      })
    })
  }

  /**
   * 消息接收结束
   *
   * @param record 是否记录本次接收的消息
   */
  messageReceiveDone(record = true) {
    if (this.nowMessageLine.value !== "") {
      this.nowMessageText += this.nowMessageLine.value;
      this.nowMessageHtml.value = this.nowMessageText;
      this.nowMessageLine.value = "";
    }
    if (record) {
      this.msgRecords.value.push({ role: "assistant", content: this.nowMessageText });
    }
    this.nowMessageText = "";
    if(this.nowMessageEvent.value!='' && this.nowMessageEvent.value){
      if(this.nowMessageProcessStatus.value){
        this.nowMessageProcessStatus.value.isShow=false
        this.nowMessageProcessStatus.value.child_status.forEach(item => {
          item.isShow = false; 
          item.sleep = 0
        } )
        this.delayedDisplayDescription()
      }
    }
    let refMessageItem = {
      isUser: false,
      content: this.nowMessageHtml.value,
      roleInfo: this.nowMessageRole.value,
      processStatus:this.nowMessageProcessStatus.value,
      event:this.nowMessageEvent.value,
      relation:this.nowMessageRelation.value,
      choices:this.nowMessageChoices.value,
      knowledgeList:this.nowMessageKnowledgeList.value,
      autosuggestion:this.nowMessageAutosuggestion.value,
      processStatusShow:false
    } 
    if(this.nowMessageChoices.value.length>0){
      refMessageItem.choicesShow=true
    }
    this.refMessageItemList.push(refMessageItem);
    this.loading.value = false;
    this.nowMessageHtml.value = "";
    // this.nowMessageRole.value = {}
  }

  //延迟展示
  delayedDisplayDescription(){
    for(let y=0; y<=this.nowMessageProcessStatus.value.child_status.length-1;y++){
        let sleep = Math.ceil(Math.random() * 
        (this.nowMessageProcessStatus.value.child_status[y].sleep_end-this.nowMessageProcessStatus.value.child_status[y].sleep_start+1) +
        this.nowMessageProcessStatus.value.child_status[y].sleep_start-1)
        for(let i=y-1; i>=0;i--){
            sleep=sleep+this.nowMessageProcessStatus.value.child_status[i].sleep
        }
        this.nowMessageProcessStatus.value.child_status[y].sleep=sleep
        setTimeout(() => {
          if(this.nowMessageProcessStatus.value){
            this.nowMessageProcessStatus.value.child_status[y].isShow=true
          }
        }, sleep);
    }
  }

  clearNowMessageRole(){
    this.nowMessageRole.value = {}
  }

  do() {
    this.loading.value = true;
  }

  stop(){
    
  }

  //连接断开处理
  disconnected(){
    //会话是否等待状态（否）
    this.waitting.value = false
    this.nowMessageRole.value = {
        name:'小智',
        key:-1
    };
    this.nowMessageHtml.value = "会话已中断，请重新发起新的会话";
    this.nowMessageText = "会话已中断，请重新发起新的会话";
  }

  new() {
    //会话是否等待状态（否）
    this.waitting.value = false
    this.loading.value = false
    //消息内容列表清空
    this.refMessageItemList = []
    this.nowMessageHtml.value = "";
    this.nowMessageText = "";
  }

  waittingStart() {
    this.waitting.value = true;
  }

  waittingEnd() {
    this.waitting.value = false;
  }

  /**
   * 清空消息历史，包含界面显示的和记录的
   */
  cleanHistory() {
    this.cleanRecords();
    if (this.refMessageItemList.length > 0) {
      this.refMessageItemList.splice(0, this.refMessageItemList.length);
    }
    // this.nowMessageRole.value = {}
  }

  /**
   * 清空消息记录
   */
  cleanRecords() {
    if (this.msgRecords.length > 0) {
      this.msgRecords.value = [];
    }
  }

  deleteLastMsgRecord() {
    return this.msgRecords.value.pop();
  }

  /**
   * 向现在的消息中添加新内容
   * TODO 优化md的转换，减少转化次数
   * @param newContent 新的内容
   */
  appendNowMessageContent(newContent,role,processStatus,event,relation , choices,knowledgeList ,autosuggestion, parse = true) {
    try {
      if (parse) {
        newContent = JSON.parse(`"${newContent}"`);
      }
      this.nowMessageText = newContent;
      this.nowMessageLine.value = "";
      this.nowMessageHtml.value = this.nowMessageText;
      this.nowMessageRole.value = role;
      this.nowMessageProcessStatus.value = processStatus;
      this.nowMessageEvent.value = event;
      this.nowMessageRelation.value = relation;
      this.nowMessageChoices.value = choices;
      this.nowMessageAutosuggestion.value = autosuggestion;
      this.nowMessageKnowledgeList.value = knowledgeList;
    } catch (e) {
      console.log(e);
    }
  }
}

import request from "@/utils/request"

// 知识生产
export function produceKnowledge (data) {
    return request({
        url: "/proxy/knowledge/produce",
        method: "post",
        data,
    })

}

// 获取生产知识预估时间
export function getKnowledgeProduceEstimation (data) {
    return request({
        url: "/proxy/knowledge/preview",
        method: "post",
        data
    })
}

// 生成状态查询 
export function getKnowledgeTask (lessonId) {
    return request({
        url: "/knowledge/task/" + lessonId,
        method: "get"
    })
}

// 知识图谱状态查询
export function graphStatus (lessonId) {
    return request({
        url: "/knowledge/graph/status?lessonId=" + lessonId,
        method: "get"
    })
}
// 版本查询
export function knowledgeVersionRelation (data) {
    return request({
        url: `/KnowledgeVersionRecord/list?pageNum=${data.pageNum}&pageSize=${data.pageSize}`,
        method: "post",
        data,
    })

}
// 版本回滚
export function knowledgeVersionRollback (data) {
    return request({
        url: "/knowledge/version/rollback",
        method: "post",
        data,
    })
}

// 发布
export function knowledgeGraph (data) {
    return request({
        url: "/knowledge/graph/publish",
        method: "post",
        data,
    })
}

// 暂停
export function knowledgeGraphPause (data) {
    return request({
        url: "/proxy/knowledge/task/pause",
        method: "post",
        data,
    })
}
// 继续
export function knowledgeGraphContinue (data) {
    return request({
        url: "/proxy/knowledge/task/todo",
        method: "post",
        data,
    })
}
// 取消
export function knowledgeGraphCancel (data) {
    return request({
        url: "/proxy/knowledge/task/cancel",
        method: "post",
        data,
    })
}


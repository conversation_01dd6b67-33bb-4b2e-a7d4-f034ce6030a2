import { ref } from 'vue'

export const chatId = ref('')
export const conversationId = ref('')
export const messageList = ref([])
export const chatList = ref([])
export const chatPopList = ref([])
export const linkTxt = ref('')
export const linkRequest = ref(false)
export const chatType = ref('')
export const chatParameter = ref({})
export const sidebarExpands = ref(true)

export const refresh = () => {
  chatId.value = ''
  messageList.value = []
  conversationId.value = ''
  chatType.value = '新增会话'
}

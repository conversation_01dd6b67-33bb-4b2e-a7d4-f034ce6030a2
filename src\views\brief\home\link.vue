<template>
  <div class="link-card" @click="handleClick">
    <img :src="icon" alt="图标" class="icon" />
    <div class="content">
      <div class="title">{{ title }}</div>
      <div class="desc">{{ desc }}</div>
    </div>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'

const props = defineProps({
  icon: {
    type: String,
    required: true
  },
  title: {
    type: String,
    required: true
  },
  desc: {
    type: String,
    required: true
  },
  url: {
    type: Object,
    required: true
  }
})

const router = useRouter()

const handleClick = () => {
  router.push(props.url)
}
</script>

<style scoped>
.link-card {
  display: flex;
  align-items: center;
  padding: 0 12px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.3s ease;
  height: 45px;
  border-radius: 22px;
  border: 1px solid #ABAAAA;
}

.link-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.icon {
  width: 20px;
  height: 20px;
  margin-right: 5px;
}

.content {
  flex: 1;
  overflow: hidden;
}

.title {
  margin-bottom: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: 12px;
  color: #C7C7C7;
}

.desc {
  font-size: 10px;
  color: #C7C7C7;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-wrap: nowrap;
}
</style>

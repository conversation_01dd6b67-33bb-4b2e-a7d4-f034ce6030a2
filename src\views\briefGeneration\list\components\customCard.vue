<template>
  <div class="agent-team-item">
    <div class="agent-team-item-main">
      <div
        style="
          display: flex;
          flex-direction: row;
          height: 100%;
          align-items: center;
        "
      >
        <div class="img">
          <i class="ri-file-list-3-fill"></i>
        </div>
        <div class="line"></div>
        <div class="item-text">
          <div
            class="text-status"
            :title="props.status"
            :style="{ color: props.status == '已完稿' ? '#FFEA00' : '#0AFCBF' }"
          >
            {{ props.status }}
          </div>
          <div class="text-title" :title="props.title">
            {{ props.title || "--" }}
          </div>
        </div>
      </div>
    </div>
    <div class="agent-team-item-footer" v-if="props.isNeedFooter">
      <slot name="footer"></slot>
    </div>
  </div>
</template>
<script setup>
const props = defineProps({
  title: {
    type: String,
    required: false,
  },
  status: {
    type: String,
    required: false,
  },
  isNeedFooter: {
    type: Boolean,
    required: false,
    default: false,
  },
});
</script>
<style scoped lang="scss">
.agent-team-item {
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background: #0e254c;
  border-radius: 6px;
  border: 1px solid #3f8fb5;
  // &:hover {
  //   box-shadow: 0 4px 10px 0 #0000000d;
  // }
  .agent-team-item-main {
    flex: 1;
    min-height: 0;
    padding: 8px;
    .img {
      font-size: 32px;
      color: #05c0dc;
    }
    .line {
      width: 1px;
      height: 60px;
      background: #3f8fb5;
      margin: 0 15px;
    }
    .item-text {
      flex: 1;
      height: 100%;
      min-width: 100px;
      position: relative;
      display: flex;
      align-items: center;
      .text-status {
        overflow: hidden;
        text-overflow: ellipsis;
        margin-bottom: 6px;
        font-family: Microsoft YaHei;
        font-weight: 400;
        font-size: 14px;
        color: #0af0b7;
        position: absolute;
        top: 0;
        right: 0;
      }
      .text-title {
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        font-family: Microsoft YaHei;
        font-weight: 600;
        font-size: 16px;
        color: #05c0dc;
      }
    }
  }
  .agent-team-item-footer {
    height: 40px;
    display: flex;
    justify-content: space-between;
    flex-direction: row;
    justify-items: center;
    align-items: center;
    border-top: 1px solid #399cd5;
    padding: 0 8px;
  }
}
.right-buttons {
  min-width: 100px;
  display: flex;
  box-sizing: border-box;
  justify-content: end;
  align-items: center;
  max-height: 24px;
  ::v-deep .el-button {
    padding: 0 3px !important;
    height: 20px !important;
  }
}
</style>
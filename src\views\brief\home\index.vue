<template>
  <div class="home-container">
    <!-- Header section (50px height) -->
    <common-header class="header" />
    
    <!-- Main content area -->
    <main class="main-content">
      <div class="content-wrapper">

        <!-- Big title -->
        <h1 class="big-title">君智AI顾问</h1>
        
        <!-- Small title -->
        <h2 class="small-title">首个AI战略咨询顾问</h2>
        
        <!-- Description -->
        <p class="description">数据收集、竞品分析、行业洞察、简报生成，你的专属企业战略规划助手</p>
        
        <!-- Button container -->
        <div class="button-container">
          <div class="prompt-text">你可以这样问:</div>
          <div class="buttons-row">
            <div class="try-buttons-wrapper">
              <div v-for="(button, index) in buttons" :key="index" class="try-button" @click="setAskContent(button.text)">
                {{ button.text }}
                <img src="/icons/home-caret-right.png" >
              </div>
            </div>
            <div class="refresh-button">
              <refresh class="icon" />
              换一批
            </div>
          </div>
        </div>

        <ask
          ref="askRef"
          @ask="handleAsk"
          placeholder="请输入您需要研究的品牌名称，Shift + Enter键换行，Enter键发送。"
        />

        <!-- Link cards using ./link.vue component -->
        <div class="link-cards">
          <Link v-for="(link, index) in links" :key="index" :icon="link.icon" :title="link.title" :desc="link.desc" :url="link.url" />
        </div>
      </div>
    </main>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import Ask from './ask.vue'
import Link from './link.vue'
import { Refresh } from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'
import CommonHeader from './header.vue'

const router = useRouter()
const askRef = ref(null)

const buttons = ref([
  { text: '如何分析市场竞争格局？' },
  { text: '生成行业趋势报告' },
  { text: '制定产品定价策略' }
]);

const links = ref([
  { icon: '/icons/home-link-multidim.png', title: '战略五维分析', desc: '点击展示更多技能',             url: '#' },
  { icon: '/icons/home-link-line.png',     title: '行业洞察',     desc: '多种体裁、润色校对、一键成文', url: '#' },
  { icon: '/icons/home-link-opponent.png', title: '竞品分析',     desc: '双语镜像、一键克隆、知识拓展', url: '#' },
  { icon: '/icons/home-link-img.png',      title: '图形大师',     desc: '点击展示更多技能',             url: '#' },
  { icon: '/icons/home-link-map.png',      title: '心智地图',     desc: '点击展示更多技能',             url: '#' }
]);

function setAskContent(text) {
  askRef.value.setContent(text)
}

function handleAsk(params) {
  const configData = {
    brandName: params.cnt.trim(),
    dataBank: params.knowledge,
    internet: params.network
  }
  localStorage.setItem("configData", JSON.stringify(configData));
  router.push(`/briefGeneration/add/create/add`);
}

</script>

<style scoped>
.home-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: transparent;
}

.header {
  height: 90px;
}

.main-content {
  padding: 40px 20px;
  color: white;
}

.content-wrapper {
  width: 860px;
  margin: 0 auto;
  flex: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.big-title {
  font-size: 52px;
  font-weight: bold;
  margin-bottom: 20px;
}

.small-title {
  font-size: 24px;
  margin-bottom: 15px;
}

.description {
  font-size: 12px;
  margin-bottom: 40px;
  opacity: 0.8;
}

.button-container {
  width: 100%;
  max-width: 800px;
  margin-bottom: 40px;
}

.prompt-text {
  text-align: left;
  margin-bottom: 15px;
  font-size: 12px;
  color: #919398;
}

.buttons-row {
  display: flex;
  justify-content: space-between;
}

.try-buttons-wrapper {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  gap: 22px;
}

.try-button {
  padding: 0 12px;
  background-color: transparent;
  cursor: pointer;
  height: 21px;
  line-height: 21px;
  border-radius: 10px;
  border: 1px solid #D0D0D0;
  font-size: 12px;
  color: #919398;
}

.try-button img {
  width: 7px;
  height: 7px;
  margin-left: 8px;
}

.try-button:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.refresh-button {
  padding: 0;
  border: none;
  cursor: pointer;
  transition: background-color 0.3s;
  font-size: 12px;
  color: #3E3E3E;
  height: 14px;
  line-height: 14px;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
}

.refresh-button:hover {
  color: white;
}

.refresh-button .icon {
  width: 14px;
  height: 14px;
  margin-right: 5px;
}

.link-cards {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 15px;
  width: 100%;
  max-width: 1000px;
  margin-top: 50px;
}
</style>

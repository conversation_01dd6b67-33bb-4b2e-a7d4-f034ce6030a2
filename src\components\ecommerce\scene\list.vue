<template>
    <div>
        <div v-for="sceneType in sceneTypeList">
            <div style="line-height: 48px;font-size: 20px;margin-left: 20px;margin-top: 20px;">
                {{ sceneType.name }}
            </div>
            <div v-loading="loading">
                <el-row>
                    <div v-for="(item, index) in toolList.filter(item => item.sceneType == sceneType.key)" :key="item"
                        style="margin: 20px;max-width: 20%;flex: 0 0 20%;" @click="sceneUse(item)">
                        <el-card :body-style="{ padding: '0px' }">
                            <div style="height: 320px;">
                                <div slot="header" class="clearfix">
                                    <img :src="baseUr1 + '/' + item.toolImage" style="width: 48px;">
                                    <el-tooltip content="编辑" placement="top" effect="light">
                                        <el-icon style="float: right; padding: 3px 0;cursor: pointer;"
                                            @click="editTool(item)" onclick="event.cancelBubble=true">
                                            <Operation />
                                        </el-icon>
                                    </el-tooltip>
                                </div>

                                <div style="line-height: 48px;font-size: 16px;font-weight: 600;cursor: pointer;">{{
                                    item.toolName }}</div>
                                <div
                                    style="line-height: 24px;font-size: 14px;overflow: hidden;text-overflow: ellipsis;cursor: pointer;">
                                    {{ item.toolDesc }}
                                </div>
                            </div>
                        </el-card>
                    </div>
                </el-row>
            </div>
        </div>

        <el-dialog v-model="editToolStepVisible" width="90%">
            <editStep1 v-if="editToolStep == 1" @nextStep="editToolGo" v-bind:sceneInfo="toolInfo"></editStep1>
            <editStep2 v-if="editToolStep == 2" @nextStep="editToolGo" @refreshList="getToolsList"
                v-bind:sceneInfo="toolInfo"></editStep2>
        </el-dialog>

        <el-dialog v-model="useSceneDisable" width="90%" v-if="useSceneDisable">
            <use-tool :userInfo="useSceneInfo"></use-tool>
        </el-dialog>
    </div>
</template>

<script setup>
import { useRoute, useRouter } from 'vue-router';

import { ref, reactive, toRefs } from 'vue'
import editStep1 from './sceneEditStep1'
import editStep2 from './sceneEditStep2'
import { getToolList } from "@/api/ecommerce/tool"
import AgentDrawer from '@/components/AgentDrawer/index'
import useTool from '@/views/ecommerce/toolAndSceneUse/use'

const baseUr1 = import.meta.env.VITE_APP_BASE_API

const { proxy } = getCurrentInstance();

//会话使用
const useSceneDisable = ref(false)
//使用会话要用到的参数
const useSceneInfo = ref({})

const loading = ref(false)

const router = useRouter()
const query = reactive(router.currentRoute.value.query)

const editToolStepVisible = ref(false)

const editToolStep = ref(1)

const editToolGo = (val) => {
    editToolStep.value = val
}

const sceneTypeList = ref([
    {
        key: 2,
        name: '节日和季节性活动'
    },
    {
        key: 0,
        name: '热点事件'
    },
    {
        key: 1,
        name: '营销类型'
    },
    {
        key: 3,
        name: '人物故事（场景工具专属）'
    }
])

//工具信息
const toolInfo = ref({
    id: 0,
    toolImage: '',
    toolName: '',
    toolDesc: '',
    toolStage: '',
    contentType: '',
    prompt: '',
    toolType: '',
    ecommerceToolKeys: []
});

//工具列表
const toolList = ref([])

//编辑
const editTool = (tool) => {
    editToolStep.value = 1
    editToolStepVisible.value = true
    toolInfo.value = JSON.parse(JSON.stringify(tool))
}

//取消
const cancel = () => {
    toolVisible.value = false;
    reset()
}

//创建工具
const createTool = () => {
    reset()
    toolTitle.value = '新建工具';
    toolVisible.value = true;
}

//工具列表
const getToolsList = () => {
    if (query.shopId && query.shopId > 0) {
        loading.value = true;
        getToolList(2, query.shopId).then(response => {
            toolList.value = response.rows;
            loading.value = false;
        })
    }
    else {
        router.push('/shop')
    }
}

//场景使用
const sceneUse = (item) => {
    useSceneInfo.value = {
        shopId: query.shopId,
        toolId: item.id,
        //1.文字 2.图文 3.视频脚本
        contentType: item.contentType,
        //1.通用工具 2.场景工具
        toolType: item.toolType,
        toolName: item.toolName,
        toolDesc: item.toolDesc,
        toolImage: item.toolImage
    }
    useSceneDisable.value = true
}

//获取工具列表
getToolsList()
</script>


<style lang='scss' scoped>
.bottom {
    margin-top: 13px;
    line-height: 12px;
}

.button {
    padding: 0;
    float: right;
}

.image {
    width: 100%;
    display: block;
}

.clearfix:before,
.clearfix:after {
    display: table;
    content: "";
}

.clearfix:after {
    clear: both
}
</style>
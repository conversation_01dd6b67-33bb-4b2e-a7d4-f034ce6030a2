<template>
    <div class="app-item">
        <div class="title-area">
            <div class="icon">
                <template v-if="app.applicationType === 'team'">
                    <img :src="app.applicationIcon ? baseUrl+app.applicationIcon : TeamDefaultIcon" class="img" />
                </template>
                <template v-else>
                    <img :src="app.applicationIcon ? baseUrl+app.applicationIcon : AgentDefaultIcon" class="img" />
                </template>
            </div>
            <div class="text">
                <div class="name">
                    <el-tooltip
                        :content="app.applicationName"
                        placement="top"
                        effect="customized"
                        popper-class="card-pop"
                        >
                        <div style="overflow: hidden;white-space: nowrap;text-overflow: ellipsis;">
                            {{app.applicationName}}
                        </div>
                    </el-tooltip>
                    <div class="hover-show">
                        <el-button link text @click.stop="editTags" style="font-size: 14px;">
                            <svg-icon style="margin-right: 2px;" 
                                :icon-class="'app-store-tag'" 
                                :size="'14px'" />
                            标签
                        </el-button>
                    </div>
                </div>
                <el-tooltip
                    :content="app.applicationDesc"
                    placement="bottom"
                    effect="customized"
                    popper-class="card-pop"
                    >
                    <div class="desc">
                            {{app.applicationDesc}}
                    </div>
                </el-tooltip>
                <div class="tags">
                    <template v-for="tag in app.classifyNames">
                        <el-tag type="info" size="small">{{tag}}</el-tag>
                    </template>
                </div>
            </div>
        </div>
        <div class="buttons">
            <el-button v-if="app.launchStatus" type="danger" @click.stop="changeLaunch(0)">下架</el-button>
            <el-button v-else type="primary" @click.stop="changeLaunch(1)">上架</el-button>
            <el-dropdown placement="bottom" style="margin-left: 8px;" trigger="click" v-if="app.launchStatus">
                <el-button @click.stop>授权管理</el-button>
                <template #dropdown>
                    <el-dropdown-menu>
                        <el-dropdown-item @click="roleAuth">
                            <svg-icon style="margin-right: 4px;" 
                                :icon-class="'app-store-role-auth'" 
                                :size="'14px'" />
                            角色授权
                        </el-dropdown-item>
                        <el-dropdown-item @click="cancelUserAuth">
                            <svg-icon style="margin-right: 4px;" 
                                :icon-class="'app-store-cancel-user-auth'" 
                                :size="'14px'" />
                            取消用户授权
                        </el-dropdown-item>
                    </el-dropdown-menu>
                </template>
            </el-dropdown>
        </div>
    </div>
</template>

<script setup>
import AgentDefaultIcon from '@/assets/icons/svg/agent-default.svg'
import TeamDefaultIcon from '@/assets/icons/svg/team-default.svg'
import { updateApp } from "@/api/application/store"
import {ElMessage} from 'element-plus'
import {useRouter} from 'vue-router'

const baseUrl = import.meta.env.VITE_APP_BASE_API
const router = useRouter()

const props = defineProps({
    app: {
        type: Object,
        default: () => ({})
    }
})

const emit = defineEmits(['edit-tags','submit'])

const editTags = () => {
    emit('edit-tags',JSON.parse(JSON.stringify(props.app)))
}

const changeLaunch = (status) => {
    updateApp({
        applicationId: props.app.applicationId,
        applicationType: props.app.applicationType,
        launchStatus: status
    }).then((res) => {
        ElMessage.success(res.msg)
        emit('submit')
    })
}

const roleAuth = () => {
    if(props.app.applicationType == 'agent'){
        router.push('/application/agent/'+props.app.applicationId)
    }
    if(props.app.applicationType == 'team'){
        router.push('/application/team/'+props.app.applicationId)
    }
}

const cancelUserAuth = () => {
    router.push('/application/cancelUserAuth?applicationId='+props.app.applicationId+'&applicationType='+props.app.applicationType)
}

</script>

<style lang="scss" scoped>
.app-item{
    min-height: 150px;
    display: flex;
    flex-direction: column;
    grid-column: span 1 / span 1;
    background-color: white;
    border-radius: 8px;
    border: 1px solid rgb(234,236,240);
    border-color: transparent;
    box-shadow: 0 0 1px 0 rgba(0,0,0,.1);
    cursor: pointer;
    transition: all .2s;
    box-shadow: 0 4px 10px 0 #0000000d;
    .title-area{
        display: flex;
        height: 112px;
        max-height: 112px;
        flex-shrink: 0;
        flex-grow: 0;
        gap: .75rem;
        padding: 24px;
        padding-bottom: 4px;
        margin-bottom: 12px;
        .icon{
            position: relative;
            flex-shrink: 0;
            width: 64px;
            height: 64px;
            margin: 4px 0;
            .img{
                height: 64px;
                width: 64px;
                background-size: cover;
                background-position: 50%;
                background-repeat: no-repeat;
                border-radius: .375rem;
            }
        }
        .text{
            width: 0;
            flex-grow: 1;
            padding: 1px 0;
            margin: 4px 0;
            .name{
                font-weight: 600;
                color: rgb(29, 41, 57);
                line-height: 1.5rem;
                font-size: 1.25rem;
                display: flex;
                align-items: center;
                justify-content: space-between;
                margin-bottom: 8px;
            }
            .desc{
                overflow: hidden;
                max-height: 1rem;
                display: -webkit-box;
                -webkit-line-clamp: 1;
                -webkit-box-orient: vertical;
                text-overflow: ellipsis;
                color: rgb(102, 112, 133);
                line-height: 1rem;
                font-weight: 500;
                font-size: 12px;
                align-items: center;
            }
            .tags{
                height: 1.25rem;
                display: flex;
                gap: .25rem;
                margin-top: 8px;
                overflow: hidden;
            }
        }
    }
    .buttons{
        display: flex;
        justify-content: end;
        align-items: center;
        padding: 0 24px 10px 24px;
    }
    .hover-show{
        display: none;
    }
    &:hover{
        .hover-show{
            display: block;
        }
    }
}
</style>
<style lang="scss">
    .card-pop.el-popper{
        max-width: 450px !important;
    }
    .el-popper.is-customized {
    /* Set padding to ensure the height is 32px */
        // padding: 6px 12px;
        background: #E6E6FA
        //#E6E6FA
        //linear-gradient(90deg, rgb(159, 229, 151), rgb(204, 229, 129));
    }
    .el-popper.is-customized .el-popper__arrow::before {
        background: #E6E6FA;
        right: 0;
    }
</style>
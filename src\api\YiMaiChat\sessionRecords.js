import request from '@/utils/conversationClientRequest'

//开启会话
export function conversationGenerate(data) {
    return request({
        url: '/conversation/generate',
        method: 'post',
        data
    })
}

//获取会话历史记录
export function getConversationList(size,query) {
    return request({
        url: 'uniform/tk/records?size='+size+'&query='+query,
        method: 'get',
    })
}

//删除历史记录
export function delConversation(data) {
    return request({
        url: '/uniform/history/delete',
        method: 'delete',
        data
    })
}

//重命名历史记录
export function renameConversation(data) {
    return request({
        url: '/uniform/history/rename',
        method: 'put',
        data
    })
}


import request from '@/utils/conversationClientRequest'

//开启会话
export function conversationGenerate(data) {
    return request({
        url: '/conversation/generate',
        method: 'post',
        data
    })
}

//获取会话历史记录
export function getConversationList(size,query) {
    return request({
        url: 'uniform/tk/records?size='+size+'&query='+query,
        method: 'get',
    })
}

//删除历史记录
export function delConversation(data) {
    return request({
        url: '/uniform/history/delete',
        method: 'delete',
        data
    })
}

//重命名历史记录
export function renameConversation(data) {
    return request({
        url: '/uniform/history/rename',
        method: 'put',
        data
    })
}

//对文档进行索引documents
export function documents(data) {
    return request({
        url: '/dataset/documents',
        method: 'post',
        data
    })
}

//获取索引进度接口
export function indexingStatus(dataset_id,batch,conversationId) {
    return request({
        url: '/dataset/'+(dataset_id?dataset_id:conversationId)+'/'+batch+'/indexing-status'+(dataset_id?'':'?is_conversation=true'),
        method: 'get'
    })
}

//获取团队编排相关信息
export function deleteDocuments(dataset_id,document_id,conversationId) {
    return request({
        url: '/dataset/'+(dataset_id?dataset_id:conversationId)+'/documents/'+document_id+(dataset_id?'':'?is_conversation=true'),
        method: 'delete',
    })
}
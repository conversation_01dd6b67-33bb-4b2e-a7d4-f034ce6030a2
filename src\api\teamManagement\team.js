import request from '@/utils/request'

// 团队类型选项列表
export function getTeamTypeList() {
    return request({
        url: '/team/type/list',
        method: 'get'
    })
}

// 团队新增
export function addTeam(data) {
    return request({
        url: '/team/team',
        method: 'post',
        data
    })
}

// 团队新增
export function newAddTeam(data) {
    return request({
        url: '/team/team/addTeam',
        method: 'post',
        data
    })
}

//修改
export function editTeam(data) {
    return request({
        url: '/team/team/revise',
        method: 'put',
        data
    })
}

//团队列表
export function getTeamInfoList(params) {
    return request({
        url: '/team/team/list',
        method: 'get',
        params
    })
}

//收藏
export function collect(query) {
    return request({
        url: '/team/collect',
        method: 'post',
        data:query
    })
}

//删除
export function delTeam(id) {
    return request({
        url: '/team/team/'+id,
        method: 'delete',
    })
}

//复制
export function copyTeam(data) {
    return request({
        url: '/proxy/copyapp',
        method: 'post',
        data
    })
}

//详情
export function getTeamInfo(id) {
    return request({
        url: '/team/team/selectinfo/'+id,
        method: 'get',
    })
}
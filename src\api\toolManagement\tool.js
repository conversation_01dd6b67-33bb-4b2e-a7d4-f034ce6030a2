import request from '@/utils/request'

// 工具列表
export function getToolList(query) {
    return request({
        url: '/system/tool/list',
        method: 'get',
        params:query
    })
}

//工具创建
export function creationTool(query) {
    return request({
        url: '/system/tool',
        method: 'post',
        data:query
    })
}

//工具修改
export function modifyTool(query) {
    return request({
        url: '/system/tool',
        method: 'put',
        data:query
    })
}

//工具删除
export function delTool(id) {
    return request({
        url: '/system/tool/'+id,
        method: 'delete',
    })
}

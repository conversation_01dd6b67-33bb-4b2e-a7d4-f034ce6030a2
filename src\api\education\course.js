import request from "@/utils/request"

// 获取微课列表
export function getCourseList (params) {
  return request({
    url: "/course/list",
    method: "get",
    params,
  })
}

// 获取微课详情
export function getCourse (params) {
  return request({
    url: "/course/one",
    method: "get",
    params,
  })
}

// 修改微课名称
export function renameCourse (data) {
  return request({
    url: "/course/updateName",
    method: "post",
    data,
  })
}

// 删除微课
export function delCourse (data) {
  return request({
    url: "/course/delete",
    method: "post",
    data,
  })
}

// 保存微课
export function saveCourse (data) {
  return request({
    url: "/course/save",
    method: "post",
    data,
  })
}

// 上传文档，获取分段结果
export function parseDocument (data) {
  return request({
    url: "/course/parse/word",
    method: "post",
    data,
  })
}

// 获取2D数字人物列表
export function get2DHumanList (params) {
  return request({
    url: "/course/2dHuman/list",
    method: "get",
    params,
  })
}

// 获取3D数字人物列表
export function get3DHumanList (params) {
  return request({
    url: "/course/2dHuman/list",
    method: "get",
    params,
  })
}

// 获取3D精品数字人物列表
export function get3DPrimeHumanList (params) {
  return request({
    url: "/course/2dHuman/list",
    method: "get",
    params,
  })
}

// 获取音色列表
export function getVoiceList (params) {
  return request({
    url: "/course/2dHuman/voice",
    method: "get",
    params,
  })
}

// 生成视频
export function genVideo (data) {
  return request({
    url: "/course/2dHuman/produce",
    method: "post",
    data,
  })
}

// 解析ppt
export function parsePPT (data) {
  return request({
    url: "/FileResource/ppt2Img",
    method: "post",
    data,
  })
}
// ppt解析进度查询
export function pptProgress (data) {
  return request({
    url: "/FileResource/getPptParseInfo",
    method: "post",
    data,
  })
}

// word解析进度查询
export function wordProgress (params) {
  return request({
    url: "/course/word/result",
    method: "get",
    params,
  })
}

// ppt生成物查询
export function pptParselist (data) {
  return request({
    url: "/FileResource/pptParselist",
    method: "post",
    data,
  })
}

// 批量生成视频
export function batchGenVideo (data) {
  return request({
    url: "/course/2dHuman/produce/batch",
    method: "post",
    data,
  })
}

<template>
  <el-dialog
    v-model="isVisible"
    :title="title"
    width="600px"
    style="max-height:80vh;"
  >
    <el-form
      ref="formRef"
      :model="formItems"
      label-position="top"
      style="max-height:calc(80vh - 120px);overflow-y: auto;padding-right: 4px;"
      >
      <template
        v-for="(item, idx) of formItems"
        :key="item.variable"
      >
        <el-form-item
          v-if="!item.hide"
          :label="item.label.zh_Hans"
          :prop="`${idx}.value`"
          :rules="item.rules"
          :show-message="false"
        >
          <el-input
            v-if="item.type === 'text-input'"
            v-model="item.value"
            :placeholder="item.placeholder.zh_Hans"
            :maxlength="item.max_length || 200"
            :disabled="item.disabled"
          />
          <el-input
            v-else-if="item.type === 'secret-input'"
            v-model="item.value"
            :placeholder="item.placeholder.zh_Hans"
            :maxlength="item.max_length || 200"
            :disabled="item.disabled"
            type="password"
            show-password
          />
          <el-select
            v-else-if="item.type === 'select'"
            v-model="item.value"
            :disabled="item.disabled"
            placeholder="请选择"
            class="field-select"
          >
            <template
              v-for="option of item.options"
              :key="option.label.zh_Hans"
            >
              <el-option
                v-if="!option.hide"
                :label="option.label.zh_Hans"
                :value="option.value"
              />
            </template>
          </el-select>
          <el-radio-group 
            v-else-if="item.type === 'radio'"
            v-model="item.value"
            :disabled="item.disabled"
            class="field-radio"
          >
            <template
              v-for="option of item.options"
              :key="option.label.zh_Hans"
            >
              <el-radio
                v-if="!option.hide"
                :label="option.value"
                :value="option.value"
                :border="true"
                class="field-radio-option"
              >
              {{ option.label.zh_Hans }}
              </el-radio>
            </template>
          </el-radio-group>
        </el-form-item>
      </template>

    </el-form>
    <template #footer>
      <el-button
        v-if="mode === 'editModel' || (mode === 'setProvider' && provider.custom_configuration.status !== 'no-configure')"
        type="danger"
        @click="remove"
      >移除</el-button>
      <el-button @click="isVisible=false">取消</el-button>
      <el-button
        type="primary"
        :disabled="isSaveBtnDisabled"
        @click="save"
      >保存</el-button>
    </template>
  </el-dialog>
</template>

<script setup>
import { nextTick, watch } from 'vue';
import { 
  getModelCredentials,
  getProviderCredentials,
  addOrUpdateModel,
  configureProvider,
  delProvider,
  delModel
} from '@/api/model';
import { ElMessage, ElMessageBox } from 'element-plus';

// 是否展示弹窗
const isVisible = ref(false);

// 表单内容, key是字段名，val是字段值
const form = ref({});

// 表单引用
const formRef = ref(null);

// 表单输入项
const formItems = ref([]);

// 模式: addModel添加模型 editModel编辑模型 setProvider配置供应商
const mode = ref('addModel')

// 标题
const title = computed(() => {
  switch(mode.value) {
    case 'addModel':
      return '添加 ' + provider.value.label.zh_Hans + ' 模型';

    case 'editModel':
      return '配置 ' + provider.value.label.zh_Hans + ' 模型';

    case 'setProvider':
      return '设置' + provider.value.label.zh_Hans
  }
});

// 是否禁用保存按钮
const isSaveBtnDisabled = computed(() => {
  let disabled = false;
  for (const item of formItems.value) {
    if (item.required && !item.value) {
      disabled = true;
      break;
    }
  }

  return disabled;
});

// 供应商信息
const provider = ref({
  provider: '',
  label: {
      zh_Hans: '',
      en_US: ''
  },
  description: {
      zh_Hans: '',
      en_US: ''
  },
  icon_small: {
      zh_Hans: '',
      en_US: ''
  },
  icon_large: {
      zh_Hans: '',
      en_US: ''
  },
  supported_model_types: [],
  configurate_methods: [
      'predefined-model',
      'customizable-model'
  ],
  provider_credential_schema: {
      credential_form_schemas: []
  },
  model_credential_schema: {
      model: {
          label: {
              zh_Hans: '\u6a21\u578b\u540d\u79f0',
              en_US: '模型名称'
          },
          placeholder: {
              zh_Hans: '输入模型名称',
              en_US: 'Enter your model name'
          }
      },
      credential_form_schemas: []
  },
  preferred_provider_type: 'custom',
  custom_configuration: {
      status: 'no-configure'
  },
  system_configuration: {
      enabled: false,
      current_quota_type: null,
      quota_configurations: []
  }
});

// 当前编辑的模型
const model = ref({});

// 定义事件
const emit = defineEmits(['save-success']);

// 根据show_on属性，展示/隐藏字段，展示/隐藏下拉选项框
watch(formItems, () => {
  const map = {};

  formItems.value.forEach((item) => {
    map[item.variable] = item.value;
  });

  formItems.value.forEach((item) => {
    if (Array.isArray(item.show_on) && item.show_on.length > 0) {
      item.hide = !item.show_on.find((showItem) => map[showItem.variable] === showItem.value);
    } else {
      item.hide = false;
    }
    if (item.type === 'select' || item.type === 'radio') {
      item.options.forEach((option) => {
        if (Array.isArray(option.show_on) && option.show_on.length > 0) {
          option.hide = !option.show_on.find((showItem) => map[showItem.variable] === showItem.value);
        } else {
          option.hide = false;
        }
      });
    }
  });

}, {deep: true, immediate: true});


/**
 * 添加模型
 */
function openAddModel(options) {
  mode.value = 'addModel';
  provider.value = options.provider;
  model.value = null;
  formItems.value = generateFormItemsForAddModel();

  isVisible.value = true;
  nextTick(() => {
    formRef.value.clearValidate();
  });
}

/**
 * 编辑模型
 */
async function openEditModel(options) {
  mode.value = 'editModel';
  provider.value = options.provider;
  model.value = options.model;
  formItems.value = await generateFormItemsForEditModel();

  isVisible.value = true;
  nextTick(() => {
    formRef.value.clearValidate();
  });
}

/**
 * 设置供应商
 */
async function openSetProvider(options) {
  mode.value = 'setProvider';
  provider.value = options.provider;
  model.value = null;
  formItems.value = await generateFormItemsForSetProvider();

  isVisible.value = true;
}

/**
 * 生成添加/修改模型的表单项
 */
function generateFormItemsForAddModel() {
  const fitems = [];

  fitems.push({
    type: 'radio',
    required: true,
    variable: '__model_type',
    label: {
      zh_Hans: '模型类型'
    },
    options: provider.value.supported_model_types.map((item) => {
      return {
        label: {
          zh_Hans: item
        },
        value: item
      }
    }),
    show_on: [],
    value: provider.value.supported_model_types[0],
    disabled: false,
    hide: false,
    rules: []
  });
  fitems.push({
    type: 'text-input',
    required: true,
    variable: '__model',
    label: provider.value.model_credential_schema.model.label,
    placeholder: provider.value.model_credential_schema.model.placeholder || { zh_Hans: ''},
    show_on: [],
    value: '',
    disabled: false,
    hide: false,
    rules: [{required: true, trigger:'blur'}]
  })
  provider.value.model_credential_schema.credential_form_schemas.forEach((item) => {
    let val = '';
    
    if (item.default) {
      val = item.default;
    } else if (item.type === 'select' || item.type === 'radio') {
      val = item.options[0].value;
    }

    fitems.push(Object.assign({}, item, {
      value: val,
      disabled: false,
      hide: false,
      rules: item.required
        ? [{required: true, trigger: 'blur'}]
        : [],
      placeholder: item.placeholder || {zh_Hans: ''}
    }));
  });

  return fitems;
}

/**
 * 生成编辑模型的表单项
 */
async function generateFormItemsForEditModel() {
  const res = await getModelCredentials(provider.value.provider, {
    model: model.value.model,
    model_type: model.value.model_type
  });
  const { credentials } = res;

  const fitems = generateFormItemsForAddModel();
  fitems.forEach((item) => {
    switch (item.variable) {
      case '__model':
        item.disabled = true;
        item.value = model.value.model;
        break;

      case '__model_type':
        item.value = model.value.model_type;
        break;

      default:
        if (credentials[item.variable]) {
          item.value = credentials[item.variable];
        }
    }
  });

  return fitems;
}

/**
 * 生成设置供应商的表单项
 */
async function generateFormItemsForSetProvider() {
  const fitems = [];
  
  provider.value.provider_credential_schema.credential_form_schemas.forEach((item) => {
    fitems.push(Object.assign({
      hide: false,
      value: '',
      disabled: false,
      rules: item.required
        ? [{required: true, trigger: 'blur'}]
        : []
    }, item));
  });

  const res = await getProviderCredentials(provider.value.provider);
  const credentials = res.credentials
    ? res.credentials
    : {};
  fitems.forEach((item) => {
    if (credentials[item.variable]) {
      item.value = credentials[item.variable];
    }
  });

  return fitems;
}

/**
 * 添加模型
 */
async function addModel() {
  const credentials = {};
  let modelVal = '';
  let modelTypeVal = '';

  formItems.value.forEach((item) => {
    if (!item.variable.startsWith('__')) {
      credentials[item.variable] = item.value;
    } else if (item.variable === '__model') {
      modelVal = item.value;
    } else if (item.variable === '__model_type') {
      modelTypeVal = item.value;
    }
  });

  await addOrUpdateModel(provider.value.provider, {
    model: modelVal,
    model_type: modelTypeVal,
    credentials
  });

  isVisible.value = false;
  ElMessage({
    type: 'success',
    message: '保存成功'
  });

  emit('save-success');
}


/**
 * 修改模型
 */
async function updateModel() {
  const credentials = {};
  formItems.value.forEach((item) => {
    if (!item.variable.startsWith('__')) {
      credentials[item.variable] = item.value;
    }
  });

  await addOrUpdateModel(provider.value.provider, {
    model: model.value.model,
    model_type: model.value.model_type,
    credentials
  });

  isVisible.value = false;
  ElMessage({
    type: 'success',
    message: '保存成功'
  });

  emit('save-success');
}

/**
 * 保存供应商配置
 */
async function saveProvider() {
  const credentials = {};
  formItems.value.forEach((item) => {
    credentials[item.variable] = item.value;
  });

  await configureProvider(provider.value.provider, {
    credentials
  });

  isVisible.value = false;
  ElMessage({
    type: 'success',
    message: '保存成功'
  });

  emit('save-success');
}

/**
 * 保存
 */
async function save() {
  if (mode.value === 'addModel') {
    await addModel();
  } else if (mode.value === 'editModel') {
    await updateModel();
  } else if (mode.value === 'setProvider') {
    await saveProvider();
  }
}

/**
 * 移除模型
 */
function removeModel() {
  ElMessageBox.confirm(
    '确定要移除吗?',
    '系统提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    delModel(provider.value.provider, {
      model: model.value.model,
      model_type: model.value.model_type
    }).then(() => {
      ElMessage({
        type: 'success',
        message: '移除成功'
      });

      isVisible.value = false;
      emit('remove-success');
    });
  });
}

/**
 * 移除供应商配置
 */
async function removeProvider() {
  ElMessageBox.confirm(
    '确定要移除吗?',
    '系统提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    delProvider(provider.value.provider).then(() => {
      ElMessage({
        type: 'success',
        message: '移除成功'
      });

      isVisible.value = false;
      emit('remove-success');
    });
  })
}

/**
 * 移除
 */
function remove() {
  if (mode.value === 'editModel') {
    removeModel();
  } else if (mode.value === 'setProvider') {
    removeProvider();
  }
}

defineExpose({
  openAddModel,
  openEditModel,
  openSetProvider
});

</script>

<style scoped>
.field-select {
  width: 100%;
}
.field-radio {
  width: 100%;
  gap: .25rem;
  flex-direction: row;
}
.field-radio :deep(label) {
  flex-grow: 1;
  flex-shrink: 1;
  margin-right: 0 !important;
}
.field-radio-option{
  width:100%;
  margin-bottom: 4px;
}
</style>
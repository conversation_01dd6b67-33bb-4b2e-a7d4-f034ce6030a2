<template>
    <div class="fixed-ai-chat-box" :class="fixedAIChatBoxClass">
        <div style="height: 100%;width: 100%;position: relative;">
            <el-tooltip
                :content="'点击打开智能助手'"
                placement="top" v-if="!isOpen" 
            >
                <div class="circle-not-open" @click="openOrHideWindow('open-ai-chat')" >
                    <img :src="AIChatImage" width="30" height="30" />
                    <!-- <span style="color:#FFFFFF;line-height:30px;padding:0 7px;">AI</span> -->
                </div>
            </el-tooltip>
            <el-tooltip
                :content="'点击放大窗口'"
                placement="top" v-if="isOpen && !isBigChat"
            >
                <el-button class="open-big-ai-chat-btn" text :icon="FullScreen" @click="openOrHideWindow('open-big-ai-chat')"></el-button>
            </el-tooltip>
            <el-tooltip
                :content="'点击缩小窗口'"
                placement="top" v-if="isOpen && isBigChat"
            >
                <el-button class="open-big-ai-chat-btn" text :icon="CopyDocument" @click="openOrHideWindow('open-small-ai-chat')"></el-button>
            </el-tooltip>
            <el-tooltip
                :content="'点击关闭智能助手'"
                placement="top" v-if="isOpen"
            >
                <el-button class="circle-open" text :icon="SemiSelect" @click="openOrHideWindow('hide-ai-chat')"></el-button>
            </el-tooltip>
            <iframe src="/insetChatPrompt" 
                style="border: none;border-left: 1px solid #eee;box-shadow: -15px 0 10px -12px #eee; border-radius: 8px;" 
                width="100%" height="100%" v-show="isOpen">
            </iframe>
        </div>
    </div>
</template>
<script setup>
import {SemiSelect,CopyDocument,FullScreen} from '@element-plus/icons-vue'
import AIChatImage from './svg/ai_chat.svg'
const isOpen = ref(false);
const isBigChat = ref(false);
const fixedAIChatBoxClass = ref({
    'open-ai-chat': false,
    'hide-ai-chat': true,
    'open-big-ai-chat': false,
    'open-small-ai-chat': false,
})

const openOrHideWindow = (className) => {
    if(className == 'hide-ai-chat'){
        isOpen.value = false;
    }
    if(className == 'open-ai-chat'){
        isOpen.value = true;
        if(isBigChat.value){
            openOrHideWindow('open-big-ai-chat')
            return;
        }
    }
    if(className == 'open-big-ai-chat'){
        isBigChat.value = true
    }
    if(className == 'open-small-ai-chat'){
        isBigChat.value = false
    }
    Object.keys(fixedAIChatBoxClass.value).forEach(key => {
        fixedAIChatBoxClass.value[key] = false;
    })
    fixedAIChatBoxClass.value[className] = true;
}

</script>
<style scoped lang="scss">
@keyframes openAIChatAnimation {
    from {
        width: 50px;
        height: 50px;
        border-radius: 25px;
    }

    to {
        bottom: 10px;
        right: 50px;
        width: 20vw;
        height: 93vh;
        border-radius: 8px;
    }
}

@keyframes openBigAIChatAnimation {
    from {
        bottom: 10px;
        right: 50px;
        width: 20vw;
        height: 93vh;
        border-radius: 8px;
    }

    to {
        bottom: 10px;
        right: 50px;
        width: 50vw;
        height: 93vh;
        border-radius: 8px;
    }
}

@keyframes openSmallAIChatAnimation {
    from {
        bottom: 10px;
        right: 50px;
        width: 50vw;
        height: 93vh;
        border-radius: 8px;
    }

    to {
        bottom: 10px;
        right: 50px;
        width: 20vw;
        height: 93vh;
        border-radius: 8px;
    }
}

@keyframes closeAIChatAnimation {
    from {
        width: 0;
        height: 0;
        border-radius: 8px;
    }

    to {
        width: 50px;
        height: 50px;
        border-radius: 25px;
    }

    
}

.circle-not-open{
    background-color: #5050E6;
    width:100%;
    height:100%;
    border-radius: 50%;
    margin-right: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
}

.circle-open{
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    position: absolute;
    top: 10px;
    right: 14px;
}

.open-big-ai-chat-btn{
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    position: absolute;
    top: 10px;
    right: 64px;
}

.fixed-ai-chat-box {
    width: 50px;
    height: 50px;
    position: fixed;
    bottom: 100px;
    right: 50px;
    border-radius: 25px;
    z-index: 99;
}

.open-ai-chat {
    animation: openAIChatAnimation 0.25s forwards;
}

.hide-ai-chat {
    animation: closeAIChatAnimation 0.25s forwards;
}

.open-big-ai-chat{
    animation: openBigAIChatAnimation 0.25s forwards;
}

.open-small-ai-chat{
    animation: openSmallAIChatAnimation 0.25s forwards;
}

</style>

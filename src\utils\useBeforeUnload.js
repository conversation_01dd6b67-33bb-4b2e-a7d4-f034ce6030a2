/**
 * Vue 3 组合式函数 - 页面退出保护
 * 用于防止用户意外关闭页面导致正在生成的内容丢失
 */

import { ref, onMounted, onUnmounted } from 'vue'
import { enableBeforeUnload, disableBeforeUnload, isBeforeUnloadEnabled, updateBeforeUnloadMessage } from './index'

/**
 * 页面退出保护组合式函数
 * @param {Object} options 配置选项
 * @param {string} options.message 自定义提示消息
 * @param {boolean} options.autoEnable 是否自动启用保护（默认false）
 * @returns {Object} 返回控制函数和状态
 */
export function useBeforeUnload(options = {}) {
  const {
    message = '如果退出，可能会导致生成内容失败，请确认是否要退出？',
    autoEnable = false
  } = options

  // 响应式状态
  const isEnabled = ref(false)

  /**
   * 启用页面退出保护
   * @param {string} customMessage 自定义消息
   */
  const enable = (customMessage) => {
    const msg = customMessage || message
    enableBeforeUnload(msg)
    isEnabled.value = true
  }

  /**
   * 禁用页面退出保护
   */
  const disable = () => {
    disableBeforeUnload()
    isEnabled.value = false
  }

  /**
   * 切换页面退出保护状态
   * @param {string} customMessage 自定义消息
   */
  const toggle = (customMessage) => {
    if (isEnabled.value) {
      disable()
    } else {
      enable(customMessage)
    }
  }

  /**
   * 更新提示消息
   * @param {string} newMessage 新的提示消息
   */
  const updateMessage = (newMessage) => {
    updateBeforeUnloadMessage(newMessage)
  }

  /**
   * 检查当前状态
   */
  const checkStatus = () => {
    isEnabled.value = isBeforeUnloadEnabled()
    return isEnabled.value
  }

  // 生命周期钩子
  onMounted(() => {
    // 同步状态
    checkStatus()
    
    // 如果设置了自动启用
    if (autoEnable) {
      enable()
    }
  })

  onUnmounted(() => {
    // 组件卸载时禁用保护，避免内存泄漏
    disable()
  })

  return {
    isEnabled,
    enable,
    disable,
    toggle,
    updateMessage,
    checkStatus
  }
}

/**
 * 用于内容生成场景的特定组合式函数
 * @param {Object} options 配置选项
 * @param {Ref} options.isGenerating 是否正在生成的响应式引用
 * @param {Ref} options.hasContent 是否有内容的响应式引用
 * @param {string} options.generatingMessage 生成中的提示消息
 * @param {string} options.contentMessage 有内容时的提示消息
 * @returns {Object} 返回控制函数和状态
 */
export function useContentGenerationProtection(options = {}) {
  const {
    isGenerating,
    hasContent,
    generatingMessage = '正在生成内容，退出可能导致生成失败，请确认是否要退出？',
    contentMessage = '存在未保存的内容，退出可能导致内容丢失，请确认是否要退出？'
  } = options

  const { enable, disable, updateMessage, isEnabled } = useBeforeUnload()

  /**
   * 根据状态自动管理保护
   */
  const autoManage = () => {
    if (isGenerating?.value) {
      updateMessage(generatingMessage)
      enable()
    } else if (hasContent?.value) {
      updateMessage(contentMessage)
      enable()
    } else {
      disable()
    }
  }

  /**
   * 手动启用生成保护
   */
  const enableGeneratingProtection = () => {
    updateMessage(generatingMessage)
    enable()
  }

  /**
   * 手动启用内容保护
   */
  const enableContentProtection = () => {
    updateMessage(contentMessage)
    enable()
  }

  return {
    isEnabled,
    enable,
    disable,
    updateMessage,
    autoManage,
    enableGeneratingProtection,
    enableContentProtection
  }
}

/**
 * 用于表单编辑场景的特定组合式函数
 * @param {Object} options 配置选项
 * @param {Ref} options.isDirty 表单是否有变更的响应式引用
 * @param {string} options.message 提示消息
 * @returns {Object} 返回控制函数和状态
 */
export function useFormProtection(options = {}) {
  const {
    isDirty,
    message = '表单有未保存的更改，退出可能导致数据丢失，请确认是否要退出？'
  } = options

  const { enable, disable, isEnabled } = useBeforeUnload({ message })

  /**
   * 根据表单状态自动管理保护
   */
  const autoManage = () => {
    if (isDirty?.value) {
      enable()
    } else {
      disable()
    }
  }

  return {
    isEnabled,
    enable,
    disable,
    autoManage
  }
}

<template>
    <teleport :disabled="!teleport" :to="teleport">
        <div class="teleportable" :class="{actived: currentActivedNodeId == id}" @click="activedNode">
            <node-header :title="nodeInfo.title" :desc="nodeInfo.desc" :node-id="id"></node-header>
            <template v-if="nodeInfo.type == 'start'">
                <div style="height: .5px;background-color: rgba(0, 0, 0, 0.05);margin: 5px 0;"></div>
                <start-node :node-info="nodeInfo" :id="id"></start-node>
            </template>
            <template v-if="nodeInfo.type == 'end'">
                <end-node :node-info="nodeInfo" :id="id"></end-node>
            </template>
            <next-step v-if="nodeInfo.type != 'end'"></next-step>
        </div>
    </teleport>
</template>

<script setup>
import { useTeleport } from '../../useTeleport.js'
import StartNode from './startNode'
import EndNode from './endNode'
import NodeHeader from '../common/nodeHeader'
import NextStep from '../common/nextStep'

const baseUrl = import.meta.env.VITE_APP_BASE_API

const emit = defineEmits(['change-actived-node-id'])

const props = defineProps({
    id: {
        type: String,
        required: true,
    },
    nodeInfo: {
        type: Object,
        required: false,
    },
    currentActivedNodeId:{
        type: String,
        required: false,
    }
})

const { teleport } = useTeleport(props.id)

const activedNode = () => {
    emit('change-actived-node-id', props.id)
}

console.log(props);

</script>

<style scoped>

.teleportable {
    background: white;
    border: 2px solid #eee;
    border-radius: 24px;
    color: #000;
}
.actived{
    border: 2px solid #5050E6;
}
</style>
<template>
    <div v-html="segContent" style="min-height: 420px;">
    </div>
</template>
<script setup >

const props = defineProps({
    seg: {
        type: String,
        default: ''
    }
})

const segContent = ref('')

//获取所有链接并标注为何种元素
const getSpecialSthFromSeg = (seg) => {
    const specialSths = []
    //找出段落中所有的图片链接
    const imageReg = /(http|https):\/\/.*?\.(gif|png|jpeg|jpe|jpg|webp|bmp)/g
    const imageMatches = seg.match(imageReg)
    if (imageMatches) {
        imageMatches.forEach(imageMatche => {
            specialSths.push({
                url:imageMatche,
                type: 'image'
            })
        })
    }
    //找出段落中所有的视频链接
    const videoReg = /(http|https):\/\/.*?\.(mp4|rmvb|avi|mov|wmv|mkv)/g
    const videoMatches = seg.match(videoReg)
    if (videoMatches) {
        videoMatches.forEach(videoMatche => {
            specialSths.push({
                url:videoMatche,
                type: 'video'
            })
        })
    }
    return specialSths
}

//根据链接找到字符串中的链接并在其后插入指定的html标签
const getHtmlFromSpecialSth = (specialSth) => {
    const { url, type } = specialSth
    let html = ''
    if (type === 'image') {
        html = `<img src="${url}" style="width: 100%;padding:10px;" />`
    } else if (type === 'video') {
        html = `<video src="${url}" controls style="width: 100%;padding:10px;" />`
    }
    return html
}

const getSegContent = (seg) => {
    const specialSths = getSpecialSthFromSeg(seg)
    specialSths.forEach(specialSth => {
        seg = seg.replace(specialSth.url, getHtmlFromSpecialSth(specialSth))
    })
    seg = seg.replace(/\n/g, '<br>')
    return seg
}

onMounted(() => {
    segContent.value = getSegContent(props.seg)
})

watch(() => props.seg , newVal => {
    segContent.value = getSegContent(newVal)
})

</script>
<template>
    <div class="start-node-area">
        <Handle type="target" :position="Position.Left" style="background-color: #5050E6;height: 8px;width: 8px;"/>
    </div>
</template>
<script setup>
import { Handle, Position,useVueFlow } from '@vue-flow/core'
import NoBorderInput from '../common/noBorderInput'
import NoBorderTextarea from '../common/noBorderTextarea'
import NodeVariables from '../common/nodeVariables'

const props = defineProps({
    id: {
        type: String,
        required: true,
    },
    nodeInfo: {
        type: Object,
        required: false,
    },
})

const { removeNodes } = useVueFlow()

function handleCommand(command){
    if(command.command == 'remove'){
        removeNodes([command.data])
    }
}

function beforeHandleCommand(data,command){
    return {
        command,
        data
    }
}

</script>
<style lang="scss" scoped>
.start-node-area{
    height: 100%;
    display: flex;
    flex-direction: column;
    .header{
        display: flex;
        align-items: center;
        .mock-icon{
            height: 24px;
            width: 24px;
            background-color: #5050E6;
            border-radius: 5px;
        }
    }
}
</style>
<template>
  <div class="container" :style="{
    '--current-color': '#3793aa',
    '--el-color-primary': '#3793aa',
    '--el-fill-color': 'transparent',
    '--el-fill-color-blank': 'transparent',
    '--el-fill-color-light': 'transparent',
    '--el-disabled-bg-color': 'transparent',
    '--el-text-color-primary': '#fff',
    '--el-mask-color': 'transparent'
  }">
    <div class="header">
      <el-input v-model="name" style="max-width: 480px" placeholder="请输入项目名称" class="junzhiInput" size="large">
        <template #append>
          <el-button :icon="Search" @click="getList" style="color: white;font-size: 16px;" />
        </template>
      </el-input>
      <div class="box">
        <div class="title">项目列表</div>
        <div @click="handleAdd" class="addBtn">
          <el-icon style="font-size: 18px; margin-right: 10px">
            <Plus />
          </el-icon>新建
        </div>
      </div>
    </div>
    <div v-loading="loading" class="common-content">
      <div class="row" v-if="teamList && teamList.length > 0">
        <el-table :data="teamList" stripe>
          <el-table-column label="项目ID" prop="id" align="center">
            <template #default="{ row }">
              {{ row.id || '--' }}
            </template>
          </el-table-column>
          <el-table-column label="项目名称" prop="title" align="center">
            <template #default="{ row }">
              {{ row.title || '--' }}
            </template>
          </el-table-column>
          <el-table-column label="项目负责人" prop="name" align="center">
            <template #default="{ row }">
              {{ row.name || '--' }}
            </template>
          </el-table-column>
          <el-table-column label="项目成员" prop="name" align="center">
            <template #default="{ row }">
              {{ row.name || '--' }}
            </template>
          </el-table-column>
          <el-table-column label="创建日期" prop="createTime" align="center">
            <template #default="{ row }">
              {{ formatDate(row.createTime) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" prop="name" align="center" width="250">
            <template #default="{ row }">
              <div class="btnBox">
                <div class="button blue">空间</div>
                <div class="button yellow">编辑</div>
                <div class="button red" @click="handleDelete(row)">删除</div>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div v-else class="empty">暂无内容</div>
      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize" layout="prev, pager, next"
        style="margin:16px 0 0 0; background: transparent;padding: 0 !important;" />
    </div>

    <CreateDialog v-model:visible="modalShow" @openUpload="loadModalShow = true" />
    <UploadDialog v-model:visible="loadModalShow" />
  </div>
</template>
<script setup>
import { ref, reactive, toRefs, onMounted, watch } from "vue";
import {
  Tools,
  Edit,
  Delete,
  Star,
  StarFilled,
  Search,
  Check,
} from "@element-plus/icons-vue";
import { useRouter } from "vue-router";
import CreateDialog from "./components/CreateDialog.vue";
import UploadDialog from "./components/UploadDialog.vue";
import { deleteBriefReport, reportList } from "@/api/briefGeneration/list";
import { ElMessage, ElMessageBox } from "element-plus";
import { formatDate } from "@/utils/index.js";
const { proxy } = getCurrentInstance();
const router = useRouter();

const loading = ref(false);
const tabActive = ref(0);

const teamList = ref([]);

const modalShow = ref(false);
const loadModalShow = ref(false);
//搜索
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
});
const name = ref('');
const total = ref(0);
watch(
  tabActive,
  () => {
    queryParams.value.pageNum = 1;
  },
  { immediate: true, deep: true }
);
const handleAdd = () => {
  modalShow.value = true;
  // router.push("/briefGeneration/add/config");
};
// 简报操作
const handleItem = (item, action) => {
  if (action === "edit") {
    router.push(`/briefGeneration/add/create/${action}/${item.id}`);
  } else if (action === "view") {
    router.push(`/briefGeneration/add/create/${action}/${item.id}`);
  }
};
// 删除简报
const handleDelete = async (item) => {
  try {
    await ElMessageBox.confirm("确认要删除该项目吗？", "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      customClass: 'junzhiMessage',
      type: "warning",
    });

    await deleteBriefReport(item.id);
    ElMessage.success("删除成功");
    getList();
  } catch (e) {
    if (e !== "cancel") {
      ElMessage.error("删除失败：" + (e.message || "未知错误"));
    }
  }
};
// 简报列表
const getList = async () => {
  try {
    loading.value = true;
    let res = await reportList({ ...queryParams.value, name: name.value });
    teamList.value = res.rows || [];
    total.value = res.total || 0;
    loading.value = false;
  } catch (e) {
    console.error(e);
  }
};
watch(
  () => queryParams.value,
  () => {
    getList();
  },
  { immediate: true, deep: true }
);
</script>
<style lang='scss' scoped>
.container {
  padding: 20px 24px;
  font-family: HarmonyOS Sans SC;
  height: 100%;
  // background: #0d1e4f;
  display: flex;
  flex-direction: column;

  .header {
    flex: none;

    .box {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin: 18px 0;

      .title {
        font-size: 18px;
        color: #00EDFF;
        line-height: 25px;
      }

      .addBtn {
        cursor: pointer;
        padding: 5px 9px;
        font-weight: 400;
        font-size: 14px;
        line-height: 20px;
        display: flex;
        align-items: center;
        border-radius: 5px;
        background-color: rgba(53, 81, 238, 1);
        color: rgba(255, 255, 255, 1);
        font-size: 14px;
        box-shadow: 0px 2px 6px 0px rgba(0, 229, 255, 0.4);
        font-family: SourceHanSansSC-regular;
        border: 1px solid rgba(0, 229, 255, 1);
      }
    }
  }
}

.common-content {
  flex: 1;
  min-height: 0;
  overflow-y: auto;

  .row {
    box-sizing: border-box;

    .btnBox {
      display: flex;
      justify-content: center;

      .button {
        border-radius: 4px;
        padding: 8px 10px;
        color: black;
        cursor: pointer;

        &:not(:last-child) {
          margin-right: 17px;
        }
      }

      .blue {
        background: #00E5FF;
      }

      .yellow {
        background: #FFF101;
      }

      .red {
        background: #FF3D00;
      }
    }
  }
}


.empty {
  color: #86909c;
  text-align: center;
  padding: 40px 0;
}
</style>
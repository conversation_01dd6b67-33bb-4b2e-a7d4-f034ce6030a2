<template>
  <el-dialog class="junzhiDialog" :close-on-click-modal="false" :model-value="props.visible" :show-close="false"
    width="565" @close="handleClose" style="
      min-height: 288px;
      border-radius: 8px !important;
    ">
    <template #title>
      <div class="titlebk">
        <div class="titletxt">{{ "上传知识" }}</div>
        <i class="ri-close-line" style="font-size: 18px; cursor: pointer; color: #63e5fe" @click="handleClose"
          alt="关闭"></i>
      </div>
    </template>
    <div class="body">
      <div class="marquee">
        <p class="marquee-text">
          这是滚动字幕文本
        </p>
      </div>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <!-- <el-button class="cancel-btn" @click="handleClose">取 消</el-button> -->
        <!-- <el-button class="delete-btn" type="primary" @click="handleConfirm"
          >确 定</el-button
        > -->
        <div class="btn orange" @click="handleConfirm('submit')">完成创建</div>
        <div class="btn blue" @click="handleConfirm('save')">保存，上传知识</div>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, watch, onMounted, reactive } from "vue";
import { useRouter } from "vue-router";
import { ElMessage } from "element-plus";
import { configData } from "@/views/briefGeneration/add/create/js/sharedState.js";
const router = useRouter();

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
});
const options = [
  {
    value: 1,
    label: 'Asia',
    children: [
      {
        value: 2,
        label: 'China',
        children: [
          { value: 3, label: 'Beijing' },
          { value: 4, label: 'Shanghai' },
          { value: 5, label: 'Hangzhou' },
        ],
      },
      {
        value: 6,
        label: 'Japan',
        children: [
          { value: 7, label: 'Tokyo' },
          { value: 8, label: 'Osaka' },
          { value: 9, label: 'Kyoto' },
        ],
      },
      {
        value: 10,
        label: 'Korea',
        children: [
          { value: 11, label: 'Seoul' },
          { value: 12, label: 'Busan' },
          { value: 13, label: 'Taegu' },
        ],
      },
    ],
  },
  {
    value: 14,
    label: 'Europe',
    children: [
      {
        value: 15,
        label: 'France',
        children: [
          { value: 16, label: 'Paris' },
          { value: 17, label: 'Marseille' },
          { value: 18, label: 'Lyon' },
        ],
      },
      {
        value: 19,
        label: 'UK',
        children: [
          { value: 20, label: 'London' },
          { value: 21, label: 'Birmingham' },
          { value: 22, label: 'Manchester' },
        ],
      },
    ],
  },
  {
    value: 23,
    label: 'North America',
    children: [
      {
        value: 24,
        label: 'US',
        children: [
          { value: 25, label: 'New York' },
          { value: 26, label: 'Los Angeles' },
          { value: 27, label: 'Washington' },
        ],
      },
      {
        value: 28,
        label: 'Canada',
        children: [
          { value: 29, label: 'Toronto' },
          { value: 30, label: 'Montreal' },
          { value: 31, label: 'Ottawa' },
        ],
      },
    ],
  },
]
const emit = defineEmits(["update:visible"]);
const ruleFormRef = ref()
const form = reactive({
  name: '',
  member: null
});
const rules = reactive({
  name: [
    { required: true, message: '请输入项目名称', trigger: 'blur' },
  ],
  // member: [
  //   {
  //     required: true,
  //     message: '请选择项目成员',
  //     trigger: 'change',
  //   },
  // ],
})
// 监听 visible 的变化
watch(
  () => props.visible,
  (newVisible) => {
    if (newVisible) {
      // 弹窗关闭时，清空表单
      form.name = ''
      form.member = null
    }
  },
  { immediate: true }
);

const handleClose = () => {
  emit("update:visible", false);
};

const handleConfirm = async (type) => {
  await ruleFormRef.value.validate((valid, fields) => {
    if (valid) {
      console.log('submit!', form)
      emit("update:visible", false);
      if (type == 'save') {

      }
    } else {
      console.log('error submit!', fields)
      return;
    }
  })
};

onMounted(() => { });
</script>

<style lang="scss" scoped>
.selectKnowledgePoints {
  margin-top: 12px;
  position: relative;
}

.titlebk {
  width: 100%;
  height: 46px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid #00EDFF;

  .titletxt {
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 16px;
    color: #fff;
    line-height: 16px;
  }
}

.body {
  .marquee {
    position: relative;

    .marquee-text {
      animation: scrollx 10s linear infinite：
    }
  }
}

@keyframes scrollx {
  0% {
    transform: translateX(100%);
  }

  50% {
    transform: translateX(-100%);
  }

  100% {
    transform: translateX(100%);
  }
}

.dialog-footer {
  width: 100%;
  text-align: center;
  margin-bottom: 30px;
  margin-top: 30px;
}

.btn {
  background: #2442b3;
  border-radius: 20px;
  border: 1px solid #05c0dc;
  color: #fff;
  padding: 0 32px;
  height: 40px;
  line-height: 40px;
  text-align: center;
  display: inline-block;
  cursor: pointer;

  &:not(:last-child) {
    margin-right: 20px;
  }
}

.blue {
  background: #2442b3;
}

.orange {
  background: #FF9100;
}
</style>
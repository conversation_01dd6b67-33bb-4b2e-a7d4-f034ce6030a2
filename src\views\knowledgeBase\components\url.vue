<template>
    <div class="doc-main">
        <div class="steps">
            <el-steps :active="stepActive" finish-status="success" 
                align-center style="min-width: 100%">
                <el-step :title="'新增url'" />
                <el-step :title="'数据处理'" />
            </el-steps>
        </div>
        <template v-if="stepActive == 0">
            <div class="doc-url">
                <div class="item">
                    <div class="label">网址 URL</div>
                    <div>
                        <el-input placeholder="请输入内容"></el-input>
                    </div>
                </div>
                <div class="item" style="margin-top: 40px;">
                    <div class="label">更新频率</div>
                    <div>
                        <el-select v-model="updateRate" style="width: 100%;">
                            <template v-for="updateReateOption in updateReateOptions">
                                <el-option :label="updateReateOption.label" :value="updateReateOption.value"></el-option>
                            </template>
                        </el-select>
                    </div>
                </div>
                <div class="button"></div>
            </div>
        </template>
        <template v-if="stepActive == 1">
            <embed-progress :embed-files="embedFiles"/>
        </template>
        <div class="doc-next">
            <el-button @click="preStep" v-if="stepActive != 0 && stepActive != 1" style="margin-right: 10px;">上一步</el-button>
            <el-button @click="nextStep" v-if="stepActive != 1" type="primary">下一步</el-button>
        </div>
    </div>
</template>
<script setup>
import EmbedProgress from './embedProgress.vue'

const stepActive = ref(0)

const updateRate = ref(1)
const updateReateOptions = ref([
    {label:'不自动更新',value:1},
    {label:'每天',value:2},
    {label:'每3天',value:3},
    {label:'每7天',value:4},
    {label:'每30天',value:5},
])

const props = defineProps({
    docId:{
        type:String,
        default:''
    }
})

const embedFiles = ref([
    {process:70,fileName:'sample_file_name'}
])

const nextStep = () => {
    if (stepActive.value++ > 2) stepActive.value = 0
}

const preStep = () => {
    if (stepActive.value-- < 0) stepActive.value = 0
}

const confirmSubmit = () => {

}

</script>]
<style scoped lang="scss">
.doc-main{
    margin: 40px;
}
.steps{
    width: 100%;
    margin-bottom: 40px;
    ::v-deep(.el-steps .el-step .is-success)  {
        color: #5050E6;
        border-color: #5050E6;
    }
    ::v-deep(.el-steps .el-step .is-process)  {
        color: #5050E6;
        border-color: #5050E6;
    }
}
.doc-next{
    display: flex;
    float: right;
    justify-content: flex-end;
    line-height: 32px;
    margin-bottom: 40px;
    margin-top: 40px;
    padding: 0 15%;
}

.doc-url{
    width: 100%;
    padding: 0 15%;
    .item{
        .label{
            box-sizing: border-box;
            color: rgba( 56,55,67 ,1);
            flex-shrink: 0;
            font-weight: 600;
            line-height: 22px;
            padding: 0 12px 4px 4px;
            font-size: 14px;
        }
    }
    .button{
        margin-top: 40px;
    }
}
</style>
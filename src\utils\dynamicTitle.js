import store from '@/store'
import defaultSettings from '@/settings'
import useSettingsStore from '@/store/modules/settings'
import { getTenantInfo } from "@/api/login";

/**
 * 动态修改标题
 */
export function useDynamicTitle() {
  const settingsStore = useSettingsStore();
  getTenantInfo().then(res => {
    if (settingsStore.dynamicTitle) {
      document.title = settingsStore.title + ' - ' + (res.data.loginName || defaultSettings.title);
    } else {
      document.title = (res.data.loginName || defaultSettings.title);
    }
  })
}
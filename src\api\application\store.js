import request from '@/utils/request'

// 查询应用申请列表
export function listApp(query) {
  return request({
    url: '/store/getamdinstore',
    method: 'get',
    params: query
  })
}

export function listUserApp(query) {
  return request({
    url: '/index/getuserstore',
    method: 'get',
    params: query
  })
}

export function getClassify() {
    return request({
        url: '/store/getclassify',
        method: 'get'
    })
}

export function updateApp(data) {
    return request({
        url: '/store/storeedit',
        method: 'put',
        data
    })
}
<template>
    <div style="display: flex;">
        <div class="common-container">
            <div class="common-header">
                <div class="title">API密钥管理</div>
                <div class="header-right">
                </div>
            </div>
            <div v-loading="loading" class="common-content">
                <div class="tool">
                    <div>API根地址:</div> 
                    <el-input disabled modelValue="http://172.16.1.19/yimai-agent/" style="width: 300px; margin: 0 10px;"/>
                    <el-button :icon="Plus" type="primary" @click="handleAddBtnClick">新建</el-button>
                </div>
                <el-table :data="list" size="default" >
                    <el-table-column
                        prop="keyName"
                        label="名称"
                    />
                    <el-table-column
                        prop="keyValue"
                        label="API KEY"
                        >
                        <template #default="scope">
                        {{ mask(scope.row.keyValue) }}
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="createdTime"
                        label="创建时间"
                        >
                        <template #default="scope">
                        {{ formatDate(scope.row.createdTime) }}
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="expirationTime"
                        label="过期日期"
                        >
                        <template #default="scope">
                        {{ scope.row.expirationTime ? scope.row.expirationTime.replace(/ .*$/, '') : '-' }}
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="lastUseTime"
                        label="最后使用时间"
                        >
                        <template #default="scope">
                        {{ scope.row.lastUseTime ? formatDate(scope.row.lastUseTime) : '未使用' }}
                        </template>
                    </el-table-column>
                    <el-table-column
                        label="操作"
                        align="center"
                        width="150px"
                        >
                        <template #default="scope">
                        <el-button
                            :icon="CopyDocument"
                            link
                            circle
                            class="btn-copy"
                            @click="handleCopyBtnClick(scope.row)"
                        />
                        <el-button
                            :icon="Edit"
                            link
                            circle
                            class="btn-edit"
                            @click="handleEditBtnClick(scope.row)"
                        />
                        <el-button
                            :icon="Delete"
                            link
                            circle
                            class="btn-remove"
                            @click="handleDelBtnClick(scope.row)"
                        />
                        </template>
                    </el-table-column>
                </el-table>
                <div style="display: flex;align-items: center;justify-content: flex-end;margin-top: 20px;">
                    <el-pagination
                        v-model:current-page="curPage"
                        :total="total"
                        layout="prev, pager, next"
                        @current-change="getList"
                    />
                </div>
            </div>
        </div>
        <el-dialog v-model="dialogVisible" :title="editRow?'修改密钥':'添加密钥'" width="480px" style="height: 320px;">
            <div style="height: 170px;display: flex;flex-direction: column;">
                <edit-api-key ref="editKeyApiRef" />
            </div>
            <div style="display: flex;flex-direction: row;justify-content: flex-end;margin-top: 20px;">
                <el-button @click="dialogVisible = false">取 消</el-button>
                <el-button type="primary" @click="toSave" v-if="!editRow">保 存</el-button>
                <el-button type="primary" @click="toUpdate" v-if="editRow">更 新</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script setup>
import { Plus, Delete, CopyDocument, Edit } from '@element-plus/icons-vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { getApiKeyList, updateApiKey, addApiKey, delApiKey, getApiKeyDetail } from '@/api/agent/apikey';
import EditApiKey from './components/editApiKey'
import copy from 'copy-to-clipboard';
import { nextTick } from 'vue';
import { formatDate } from '@/utils/index';

const route = useRoute()
const id =route.query.id

const loading = ref(false)
const dialogVisible = ref(false)
const list = ref([])
const editRow = ref(null)
const total  = ref(0)
const curPage = ref(1)

const editKeyApiRef = ref(null)

const getList = () => {
    loading.value = true
    getApiKeyList({
        agentId: id,
        pageNum: curPage.value,
        applicationType: 'team'
    }).then((rs) => {
        list.value = rs.rows;
        total.value = rs.total;
        loading.value = false
    });
}

const handleAddBtnClick = () => {
    editRow.value = null
    dialogVisible.value = true
    nextTick(() => {
        editKeyApiRef.value.setFormData({
            keyName: '',
            expirationTime: ''
        })
    })
}


/**
 * 掩码显示，如:   *******abde
 */
 function mask(key) {
  return '********' + key.substring(key.length - 4, key.length);
}

/**
 * 点击复制按钮
 */
 function handleCopyBtnClick(row) {
  ElMessageBox.alert(`您要复制的密钥是 <br><div class="agent-copy-key-dialog-key-wrapper">${row.keyValue}</div>`, '复制密钥', {
    confirmButtonText: '复制',
    dangerouslyUseHTMLString: true,
  }).then((res) => {
    if(res != 'confirm') return;
    copy(row.keyValue);
    ElMessage({
      type: 'success',
      message: '已复制到剪贴板'
    })
  })
}

/**
 * 点击编辑按钮
 */
 function handleEditBtnClick(row) {
    editRow.value = row
    dialogVisible.value = true
    nextTick(() => {
        editKeyApiRef.value.setFormData(editRow.value || {
            keyName: '',
            expirationTime: ''
        })
    })
}

/**
 * 点击删除按钮
 */
 function handleDelBtnClick(row) {
  ElMessageBox.confirm('确定删除该API密钥? 删除后该密钥立即失效，正在使用中的应用会受影响，请确认!', '系统提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
  })
    .then(() => {
      delApiKey(row.id).then(() => {
        getList();
        ElMessage({
          type: 'success',
          message: `已删除`,
        });
      });
    })
    .catch((e) => {
      console.log(e);
    })
}

const toSave = () => {
    let {keyName,expirationTime} = editKeyApiRef.value.formData;
    expirationTime = expirationTime
        ? formatDate(new Date(expirationTime))
        : undefined;
    addApiKey({
        agentId:id,
        applicationType:"team",
        keyName,
        expirationTime
    }).then((res) => {
        dialogVisible.value = false
        nextTick(() => {
            ElMessage({
                type: 'success',
                message: res.msg,
            });
            getList()
        })
    })
}

const toUpdate = () => {
    const {keyName,expirationTime} = editKeyApiRef.value.formData;
    updateApiKey({
        id:editRow.value.id,
        applicationType:"team",
        keyName,
        expirationTime:formatDate(new Date(expirationTime))
    }).then((res) => {
        dialogVisible.value = false
        nextTick(() => {
            ElMessage({
                type: 'success',
                message: res.msg,
            });
            getList()
        })
    })
}

getList()

</script>

<style lang='scss' scoped>
.common-container {
    display: flex;
    flex-direction: column;
    background-color: white;
    height: calc(100vh - 52px);
    width: 100%;
    margin: 0;
    padding: 0;
    background-size: cover;
}
.common-header {
    height: 80px;
    padding: 28px 30px;
    line-height: 24px;
    display: flex;
    justify-content: space-between;

    .title {
        color: #032220;
        font-size: 18px;
        font-weight: 500;
    }

    .header-right {
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: row;
    }
}

.common-content {
    margin: 0 30px;
    height: calc(100vh - 250px);
    display: flex;
    flex-direction: column;
    overflow: hidden;
    .tool{
        display: flex;
        margin-bottom: 20px;
        align-items: center;
        justify-content: flex-end;
    }
}
</style>
<style>
.agent-copy-key-dialog-key-wrapper {
  background-color:#eeeeee;
  padding: 10px;
  border-radius: 5px;
  text-wrap:wrap;
  word-break: break-word;
}
</style>
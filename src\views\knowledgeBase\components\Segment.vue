<template>
  <div class="segments-item">
    <div class="header">
      <div class="left">{{ segmentTitle }}</div>
      <div class="right">
        <el-button
          v-if="!editing"
          type="primary"
          icon="Edit"
          circle
          @click="startEditing"
          size="mini"
        ></el-button>
        <el-button
          v-else
          type="success"
          icon="Check"
          circle
          @click="saveContent"
          size="mini"
        ></el-button>
        <el-button
          v-if="!editing"
          type="danger"
          icon="Delete"
          circle
          @click="deleteSegment"
          size="mini"
        ></el-button>
        <el-button
          v-else
          icon="Close"
          circle
          @click="cancelSave"
          size="mini"
        ></el-button>
      </div>
    </div>
    <div class="content" v-if="!editing" @click="startEditing">
      {{ editedContent }}
    </div>
    <div class="textcontent" v-else>
      <el-input
        v-model="editedContent"
        type="textarea"
        :rows="5"
        placeholder=""
        class="input-style"
      ></el-input>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    segmentData: Object,
    segmentNum: Number,
  },
  watch: {
    segmentData: {
      handler(val) {
        console.log("val", val);
        if (val.content) {
          this.segmentTitle = "第" + (this.segmentNum + 1) + "段";
          this.editedContent = this.segmentData?.content;
        }
      },
      immediate: true, // 页面开始立即执行
    },
  },
  data() {
    return {
      editing: false,
      segmentTitle: null,
      editedContent: null,
      tempEditedContent: null,
    };
  },
  methods: {
    startEditing() {
      this.tempEditedContent = this.editedContent;
      this.editing = true;
    },
    saveContent() {
      this.editing = false;
      this.$emit("saveContent", {
        content: this.editedContent,
        modified_at: this.segmentData.meta.modified_at,
      });
    },
    cancelSave() {
      this.editedContent = this.tempEditedContent;
      this.editing = false;
    },
    deleteSegment() {
      this.$emit("deleteSegment", this.segmentData.meta.modified_at);
    },
  },
};
</script>

<style scoped>
.segments-item {
  background-color: #fff;
  padding: 20px;
  margin-bottom: 20px;
  margin-left: 20px;
  flex-basis: calc(33% - 20px);
  box-sizing: border-box;
  height: 240px;
  position: relative;
  cursor: pointer;
  border-radius: 5px;
  overflow: auto;
  overflow-x: hidden;
  line-height: 22px;
  background-color: #f9fafb;
  border: 1px solid #f9fafb;
  word-break: break-all;
}
.segments-item:hover {
  background-color: #fff;
  box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.1);
  border: 1px solid #d3d3d3;
}
.segments-item:hover .right {
  opacity: 1;
  visibility: visible;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.left {
  font-weight: bold;
  font-size: 14px;
}

.right {
  display: flex;
  opacity: 0;
  visibility: hidden;
  align-items: center;
}

.content {
  color: #333;
  cursor: pointer;
  font-size: 12px;
}
.textcontent {
  color: #333;
  cursor: pointer;
  width: 100%;
}
.input-style {
  font-size: 12px;
  padding: 0 !important;
}
.input-style :deep(.el-textarea__inner) {
  font-size: 12px;
  padding: 3px;
  height: 160px;
  line-height: 20px;
}
</style>

<template>
    <div class="common-container">
        <div class="single-page-container">
            <div class="single-page-header">
                <div class="left-box">
                    <!-- <el-icon style="margin-right: 10px;"><Back /></el-icon> -->
                    <el-button :icon="Back" @click="back()" text style="margin-right: 10px;" link></el-button>
                    <img :src="knowledgeBaseIcon" />
                    <div class="text-box">
                        <div class="title">
                            {{knowledgeBaseInfo.name}}
                        </div>
                        <!-- <div class="detail">
                            {{editForm.agentCode}}
                        </div> -->
                    </div>
                </div>
                <div  class="right-box">
                    <el-button type="primary" style="color: white;font-size: 14px;font-weight: 500;"
                    @click="setUp()">设置</el-button>
                    <el-button type="primary" style="color: white;font-size: 14px;font-weight: 500;"
                    @click="recallTesting()">召回测试</el-button>
                    <el-button type="primary" style="color: white;font-size: 14px;font-weight: 500;"
                    @click="addDocument()">添加文档</el-button>
                </div>
            </div>
        </div>
        <div class="whole">
            <div class="table-tool-wrapper">
                <h3>文档</h3>
                <el-input
                    v-model="queryParams.keyword"
                    placeholder="搜索"
                    :suffix-icon="!queryParams.keyword ? Search : null"
                    :clearable="true"
                    @keyup.enter="getDocumentListMethod"
                    @clear="getDocumentListMethod"
                    class="input-keyword"
                />
            </div>
            <el-table v-loading="loading" :data="documentList" @row-click="rowClick" max-height="calc(100vh - 330px)">
               <el-table-column label="文档名称" align="center" prop="name" :show-overflow-tooltip="true" />
               <el-table-column label="字符数" align="center" prop="word_count" :show-overflow-tooltip="true" width="150px"/>
               <el-table-column label="召回次数" align="center" prop="hit_count" :show-overflow-tooltip="true" width="100px"/>
               <el-table-column label="创建时间" align="center" prop="cjsj" width="200px">
                <template #default="scope">
                     <span>{{ scope.row.created_at?formatDate(scope.row.created_at):'' }}</span>
                  </template>
               </el-table-column>
               <el-table-column label="状态" align="center" prop="display_status" width="100px">
                    <template  #default="scope">
                        <dict-tag :options="sys_knowledge_doc_status" :value="scope.row.display_status" />
                    </template>
               </el-table-column>
               <el-table-column label="是否启用" align="center" width="100px">
                  <template #default="scope">
                     <el-switch
                        v-model="scope.row.enabled"
                        :active-value="true"
                        :inactive-value="false"
                        @click.stop
                        @change="enabledChange(scope.row)"
                     ></el-switch>
                  </template>
               </el-table-column>
               <el-table-column label="操作" align="center" width="150" class-name="small-padding fixed-width">
                  <template #default="scope">
                     <el-tooltip content="删除" placement="top">
                        <el-button link type="primary" icon="Delete" @click.stop="delDocumentMethod(scope.row)"></el-button>
                     </el-tooltip>
                  </template>
               </el-table-column>
            </el-table>
            <pagination
               v-show="total > 0"
               :total="total"
               v-model:page="queryParams.page"
               v-model:limit="queryParams.limit"
               @pagination="getDocumentListMethod"
            />
        </div>

        <!-- 编辑名称 -->
        <el-dialog v-model="addDocumentVisible" title="添加文档" >
            <div class="doc-type-select">
                <el-radio-group v-model="documentTypeSel" class="ml-4">
                    <el-radio v-for="documentType in documentTypeList" :label="documentType.id" 
                        :key="documentType.id" border :disabled="documentType.disabled">
                        <div class="doc-seg-option-title">{{ documentType.type }}</div>
                        <div class="doc-seg-option-desc">{{ documentType.desc }}</div>
                    </el-radio>
                </el-radio-group>
            </div>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="addDocumentVisible=false">取消</el-button>
                    <el-button type="primary" @click="addDocumentNext">下一步</el-button>
                </span>
            </template>
        </el-dialog>

        <!-- 设置 -->
        <el-dialog v-model="setUpVisible" title="知识库设置" >
            <div class="subject">
                <el-row class="embedding-model">
                    <el-col :span="5">
                        <span class="title">Embedding 模型</span>
                    </el-col>
                    <el-col :span="19">
                        <model-selector
                            type="text-embedding"
                            style="width: 100%;"
                            :enable-default-model="true"
                            v-model:model-name="setUpInfo.embedding_model"
                            v-model:model-provider="setUpInfo.embedding_model_provider"
                        />
                    </el-col>
                </el-row>
                <el-row class="retrieve-settings-box">
                    <el-col :span="5">
                        <span class="title">检索策略</span>
                    </el-col>
                    <el-col :span="19">
                        <retrieve-settings v-model="setUpInfo.retrieval_model"></retrieve-settings>
                    </el-col>
                </el-row>
            </div>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="setUpVisible=false">取消</el-button>
                    <el-button type="primary" @click="setUpSubmit">保存</el-button>
                </span>
            </template>
        </el-dialog>
    </div>
</template>

<script setup>
import { nextTick, ref } from 'vue'
import { Back, Search } from '@element-plus/icons-vue'
import { useRoute } from 'vue-router';
import { getKnowledgeBaseInfo,getDocumentList,disableDocumentd,availableDocumentd,delDocument,editKnowledgeBase } from "@/api/knowledgeBase/knowledgeBase";
import router from "@/router";
import documentation from "@/assets/images/knowledgeBase.png";
import knowledgeBaseIcon from "@/assets/icons/svg/knowledgeBaseIcon.svg";
import ModelSelector from '@/components/Model/modelSelector.vue';
import retrieveSettings from '@/components/retrieveSettings/index.vue'
import { ElMessage } from 'element-plus'

const { proxy } = getCurrentInstance();

const { sys_knowledge_doc_status } = proxy.useDict("sys_knowledge_doc_status");

const route = useRoute();
const baseUrl = import.meta.env.VITE_APP_BASE_API;

const id =route.query.id
const knowledgeBaseInfo = ref({})

const queryParams = ref({
    page:1,
    limit:10,
    keyword: ''
})
const loading = ref(false)
//列表
const documentList = ref([])
const total = ref(0)

const addDocumentVisible = ref(false)
const documentTypeList = ref([
    {id:1,type:'本地文档',desc:'上传 TXT , MARKDOWN , PPTX , PDF , HTML , XLSX , XLS , DOCX , CSV 格式的本地文件'},
    {id:2,type:'在线文档(待上线)',desc:'获取在线网页内容',disabled:true}
])
const documentTypeSel = ref(1)

//
const getKnowledgeBaseInfoMethod = () => {
    if(!id){
        router.push("/knowledgeBase");
    }else{
        getKnowledgeBaseInfo(id).then(response=>{
            knowledgeBaseInfo.value=response
        })
    }
}

//返回
const back  = () => {
    router.push("/knowledgeBase");
}

//
const rowClick = (row) => {
    router.push("/knowledgeBase/documentSegmentation?id=" + row.id+'&knowledgeId='+id);
}

//添加文档
const addDocument = () => {
    addDocumentVisible.value=true
}

//下一步
const addDocumentNext = () => {
    router.push('/knowledgeBase/upload?type='+ (documentTypeSel.value == 1 ?'doc':'url')+'&knowledge_id='+id)
}

const getDocumentListMethod = () => {
    loading.value=true;
    getDocumentList(queryParams.value,id).then(response => {
        documentList.value=response.data;
        total.value=response.total;
        loading.value=false;
    })
}

//时间戳转时间
const formatDate = (time) => {
    const date = new Date(time*1000);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0'); // 月份从0开始，所以加1，并用0填充
    const day = String(date.getDate()).padStart(2, '0'); // 用0填充
    const hours = String(date.getHours()).padStart(2, '0'); // 用0填充
    const minutes = String(date.getMinutes()).padStart(2, '0'); // 用0填充
    return `${year}-${month}-${day} ${hours}:${minutes}`;
}

//启用禁用
const enabledChange = (row) => {
    let data={
        id:row.id,
        knowledgeId:id
    }
    if(row.enabled){
        availableDocumentd(data).then(response => {
            proxy.$modal.msgSuccess("启用成功");
            getDocumentListMethod()
        })
    }else{
        disableDocumentd(data).then(response => {
            proxy.$modal.msgSuccess("禁用成功");
            getDocumentListMethod()
        })
    }
}

//删除文档
const delDocumentMethod = (row) => {
    let data={
        id:row.id,
        knowledgeId:id
    }
    proxy.$modal.confirm('是否确认删除文档名称为"' + row.name + '"的数据项？').then(function() {
        return delDocument(data);
    }).then(() => {
        proxy.$modal.msgSuccess("删除成功");
        getDocumentListMethod();
    }).catch(() => {});
    
}
//召回测试
const recallTesting = () =>{
    router.push("/knowledgeBase/recallTesting?knowledgeId="+id)
}

//设置
const setUpVisible = ref(false)
const setUpInfo = ref(null)
const setUp = () =>{
    setUpInfo.value = JSON.parse(JSON.stringify(knowledgeBaseInfo.value))
    setUpInfo.value.retrieval_model = setUpInfo.value.retrieval_model_dict
    setUpVisible.value = true 
}
const setUpSubmit = () => {
    if(setUpInfo.value.retrieval_model.search_method == 'hybrid_search'
    && (setUpInfo.value.retrieval_model.reranking_model.reranking_model_name == null
    || setUpInfo.value.retrieval_model.reranking_model.reranking_model_name == "")){
        ElMessage.warning('请选择Rerank 模型！')
        return
    }
    if(setUpInfo.value.retrieval_model.reranking_enable && setUpInfo.value.retrieval_model.search_method!='hybrid_search'
    && (setUpInfo.value.retrieval_model.reranking_model.reranking_model_name == null
    || setUpInfo.value.retrieval_model.reranking_model.reranking_model_name == "")){
        ElMessage.warning('请选择Rerank 模型或关闭Rerank 模型！')
        return
    }
    editKnowledgeBase(setUpInfo.value).then(response=>{
        proxy.$modal.msgSuccess("修改成功");
        getKnowledgeBaseInfoMethod()
        setUpVisible.value = false
    })
}


getKnowledgeBaseInfoMethod()
getDocumentListMethod()
</script>
<style scoped lang="scss">
    .common-container {
        display: flex;
        flex-direction: column;
        background-color: #F7F7FA;
        height: calc(100vh - 50px);
        width: 100%;
        margin: 0;
        padding: 0;
        background-size: cover;
    }

    .single-page-container{
        background-color: #F7F7FA;
        .single-page-header{
            height: 80px;
            display: flex;
            background-color: white;
            margin: 0 20px;
            justify-content: space-between;
            align-items: center;
            flex-direction: row;
            border-radius: 8px;
            .left-box{
                margin-left:24px;
                display: flex;
                align-items: center;
                .text-box{
                    display: flex;
                    flex-direction: column;
                    margin-left: 10px;
                    .title{
                        display: flex;
                        align-items: center;
                        height: 25px;
                        font-size: 18px;
                        font-weight: 500;
                        color: #032220;
                    }
                    .detail{
                        height: 17px;
                        font-size: 12px;
                        color: #999999;
                        font-weight: 400;
                        margin-top: 4px;
                    }
                }
                img {
                    height: 40px;
                    width: 40px;
                    border-radius: 4px;


                }
            }
            .right-box{
                margin-right: 20px;
            }
        }
        .single-page-content{
            display: flex;
            flex-direction: row;
            height: calc(100vh - 80px);
            overflow-y: auto;
            .content-left-box{
                width: calc(60% - 48px);
                height: calc(100% - 60px);
                border-right: 1px solid #ccc;
                background-color: white;
                margin-top: 30px;
                .content-left-box-title{
                    padding-left: 24px;
                    font-size: 14px;
                    font-weight: bold;
                    color: #032220;
                    margin-bottom: 16px;
                }
                .left-box-half-top{
                    border-bottom: 1px solid #ccc;
                }
                .custom-form-item-slider{
                    display: flex;
                    flex-direction: row;
                    width: 100%;
                    align-items: center;
                    justify-content: space-between;
                    margin-bottom: 5px;
                    .custom-slider-title{
                        margin-right: 20px;
                        width: 120px;
                        text-align: left;
                        font-size: 12px;
                        color: #999999;
                    }
                    .custom-slider-value{
                        width: calc(100% - 120px);
                        display: flex;
                        flex-direction: row;
                    }
                }
            }
            .content-right-box-title{
                color: #032220;
                font-size: 14px;
                font-weight: 500;
                margin-bottom: 10px;
            }
            
            
        }
    }

.doc-type-select{
    width: 100%;
    min-height: 500px;
    .doc-seg-option-title{
        font-size: 14px;
        height: 40px;
        line-height: 50px;
        font-weight: 600;
    }
    .doc-seg-option-desc{
        font-size: 14px;
        font-weight: 400;
        line-height: 20px;
        margin-bottom: 10px;
        padding-left: 0;
        color: rgba(56, 55, 67,0.6);
    }
    .custom-config{
        margin-bottom: 15px;
        margin-top: 16px;
        padding-top: 16px;
        border-top: 1px solid #E6E6E9;
        .item{
            margin-bottom: 20px;
            display: flex;
            flex-direction: column;
            .label{
                color:#383743;
                display: block;
                font-size: 14px;
                font-weight: 600;
                line-height: 22px;
                margin-bottom: 8px;
            }
            .requrie{
                &:after{
                    color: #FF441E;
                    content: "*";
                    font-weight: 600;
                    margin-left: 4px;
                }
            }
        }
    }
    ::v-deep(.el-radio-group){
        margin-right: 0 !important;
        width: 100%;
    }
    ::v-deep(.el-radio-group .el-radio){
        margin-right: 0 !important;
        margin: 5px 0;
        width: 100%;
        height: auto;
    }
    ::v-deep(.el-radio-group .el-radio .el-radio__label){
        width: 100%;
    }
}
    .whole{
        height: calc(100vh - 150px);
        background-color: white;
        padding: 20px;
        margin: 10px 20px 0 20px;
        border-radius: 8px;
    }

.table-tool-wrapper {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
}
.input-keyword {
    width: 200px;
}
::v-deep.el-table .el-table__row:hover {
    cursor: pointer;
}

.subject{
    min-height: 550px;
    background-color: white;
    border-radius: 8px;
    overflow: auto;
    padding: 20px 30px;
    .knowledge-base-name{
        margin-top: 20px;
        .title{
            line-height: 35px;
        }
        .prompt{
            font-size: 12px;
            color: #999999;
            font-weight: 400;
            display: flex;
            align-items: center;
            margin-top: 5px;
        }
    }
    .visible-permissions{
        margin-top: 20px;
        .title{
            line-height: 35px;
        }
        .radio{
            height:45px;
            line-height:45px;
            border:1px solid #dcdfe6;
            border-radius:10px;
            cursor: pointer;
            .image-box{
                text-align:center;
                padding-top:6px;
                img{
                    height: 25px;
                    width: 25px;
                    border-radius: 4px;
                }
            }
        }
        .radio-sel{
            border: 1px solid #5050E6;
        }
    }
    .indexing-model{
        margin-top: 20px;
        .title{
            line-height: 35px;
        }
        .radio{
            height:100px;
            // width:45%;
            border:1px solid #dcdfe6;
            border-radius:10px;
            cursor: pointer; 
            .image-box{
                text-align:center;
                padding-top:10px;
                img{
                    height: 25px;
                    width: 25px;
                    border-radius: 4px;
                }
            }
            .title{
                margin-top:5px;
                display: flex;
                justify-content: space-between;
                align-items: center;

            }
            .content{
                font-size: 13px;
                color:#999999;
                padding-right: 5px;
                height:50px;
                overflow: hidden;
                text-overflow: ellipsis;
            }
        }
        .radio-sel{
            border: 1px solid #5050E6;
        }
    }
    .embedding-model{
        margin-top: 20px;
        .title{
            line-height: 35px;
        }
        .image-box{
            text-align:center;
            padding-top:10px;
            img{
                height: 25px;
                width: 25px;
                border-radius: 4px;
            }
        }
        .option-title{
            cursor: pointer; 
            line-height: 32px;
            // width:125px;
            background-color:#f3f3f3;
            border-radius: 8px;
            padding:0 5px
        }
    }
    .retrieve-settings-box{
        margin-top: 20px;
        .title{
            line-height: 35px;
        }
    }
}

</style>
<template>
  <div class="ask-container">
    <textarea
      class="input-area"
      v-model="cnt"
      :placeholder="placeholder"
      @keydown.enter="handleEnter"
      :maxlength="maxChars > 0 ? maxChars : undefined"
    ></textarea>
    <div class="fileView" v-show="uploadedFiles.length > 0">
      <img src="/icons/pdf-icon.png" class="file-icon" />
      <span class="file-name" :title="uploadedFiles[0]?.name">{{
        uploadedFiles[0]?.name
      }}</span>
      <span class="file-size">{{
        formatFileSize(uploadedFiles[0]?.size)
      }}</span>
    </div>
    <div class="settings-area">
      <div class="handleBox">
        <div class="switch-wrapper" v-show="isDataBank">
          <el-switch v-model="network" class="switch" />资料库
        </div>
        <div class="switch-wrapper" v-show="isNetWork">
          <el-switch v-model="knowledge" class="switch"/>联网搜索
        </div>
        <el-upload
          :action="uploadFileUrl"
          :headers="{ Authorization: 'Bearer ' + getToken() }"
          :on-change="handleFileChange"
          :auto-upload="true"
          :show-file-list="false"
          :before-upload="beforeUpload"
          :disabled="isUploading"
          :accept="validFileTypes.join(',')"
        >
          <div class="file" v-show="isFile">
            <i class="ri-attachment-2"></i></div
        ></el-upload>
      </div>
      <el-button
        v-if="!isResponsing"
        class="send-btn"
        @click="ask"
        link
        :disabled="!cnt.trim()"
      >
        生成简报
      </el-button>
      <el-tooltip
        v-else
        class="box-item"
        effect="dark"
        content="停止生成"
        placement="top"
      >
        <div class="stop-btn" @click="stop"></div>
      </el-tooltip>
    </div>
  </div>
</template>

<script setup>
import { ref } from "vue";
import { ElMessage } from "element-plus";
import { getToken } from '@/utils/auth';
// import { uploadFile } from '@/api/knowledge/point.js'

const props = defineProps({
  /**
   * 是否在响应中
   */
  isResponsing: {
    type: Boolean,
    required: false,
    default: false,
  },
  /**
   * 是否展示资料库
   */
  isDataBank: {
    type: Boolean,
    required: false,
    default: true,
  },
  /**
   * 是否展示联网搜索
   */
  isNetWork: {
    type: Boolean,
    required: false,
    default: true,
  },
  /**
   * 是否展示上传文件
   */
  isFile: {
    type: Boolean,
    required: false,
    default: true,
  },
  maxChars: {
    type: Number,
    required: false,
    default: 0,
  },

  placeholder: {
    type: String,
    default: ''
  }
});

const uploadFileUrl = ref(import.meta.env.VITE_APP_BASE_API + "/common/upload");
const network = ref(false);
const knowledge = ref(false);
const uploadedFiles = ref([]);
const cnt = ref("");

const emit = defineEmits(["ask", "stop"]);
const handleEnter = (event) => {
  if (event.ctrlKey) {
    const start = event.target.selectionStart;
    const end = event.target.selectionEnd;
    event.target.value =
      event.target.value.substring(0, start) +
      "\n" +
      event.target.value.substring(end);
    event.target.selectionStart = event.target.selectionEnd = start + 1;
    return;
  }
  event.preventDefault();
  ask();
};
function ask() {
  if (!cnt.value.trim()) return;
  let params = { cnt: cnt.value };
  if (props.isDataBank) {
    params.network = network.value;
  }
  if (props.isNetWork) {
    params.knowledge = knowledge.value;
  }
  if (props.isFile) {
    params.files = uploadedFiles.value;
  }
  emit("ask", params);
  cnt.value = "";
}

function stop() {
  emit("stop");
}
const validFileTypes = [".pdf"];
const maxFileSize = 10 * 1024 * 1024; // 500MB
const isUploading = ref(false);
const beforeUpload = (file) => {
  const fileType = "." + file.name.split(".").pop().toLowerCase();
  if (!validFileTypes.includes(fileType)) {
    ElMessage.error(
      `不支持的文件格式，请上传${validFileTypes.join("，")}格式文件`
    );
    return false;
  }
  if (file.size > maxFileSize) {
    ElMessage.error("文件大小不能超过10M");
    return false;
  }
  return true;
};
const handleFileChange = async (file) => {
  if (file.raw && beforeUpload(file.raw)) {
    const isDuplicate = uploadedFiles.value.some((f) => f.name === file.name);
    if (!isDuplicate) {
      isUploading.value = true;
      try {
        // const formData = new FormData();
        // formData.append("file", file.raw);
        // const res = await uploadFile(formData);
        // const uploadedFile = Object.assign({}, file, {
        //   resourceId: res.resourceId,
        // });
        // uploadedFiles.value.push(uploadedFile);
        uploadedFiles.value = [file];
        ElMessage.success(`文件 ${file.name} 上传成功`);
      } catch (error) {
        console.error("上传文件失败:", error);
        ElMessage.error(`文件 ${file.name} 上传失败`);
      } finally {
        isUploading.value = false;
      }
    } else {
      ElMessage.warning("文件已存在");
    }
  }
};
// 格式化文件大小
function formatFileSize(bytes) {
  if (bytes < 1024) {
    return `${bytes} B`;
  } else if (bytes < 1024 * 1024) {
    return `${(bytes / 1024).toFixed(1)} KB`;
  } else {
    return `${(bytes / (1024 * 1024)).toFixed(1)} MB`;
  }
}

function setContent(text) {
  cnt.value = text
}

defineExpose({ setContent })

</script>

<style lang="scss" scoped>
.ask-container {
  width: 100%;
  height: 160px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  overflow: hidden;
  background-color: rgba(0, 0, 0, 0.13);
  border-radius: 20px;
  border: 2px solid #00C152;
}

.input-area {
  height: 80px;
  flex: none;
  width: 100%;
  padding: 0 12px;
  margin-top: 16px;
  box-sizing: border-box;
  border: 0;
  border-radius: 0;
  resize: none;
  background-color: transparent;
  color: #C7C7C7;
}

.input-area:focus {
  outline: 0;
}
.fileView {
  flex: 1;
  min-height: 0;
  display: flex;
  align-items: center;
  padding: 0 12px;
  margin-top: 10px;
  margin-bottom: 8px;
  .file-icon {
    flex: none;
    width: 16px;
    margin-right: 4px;
  }
  .file-name {
    font-size: 12px;
    line-height: 12px;
    color: #1e1f24;
    margin-right: 7px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    max-width: 240px;
  }

  .file-size {
    flex: none;
    font-size: 12px;
    line-height: 12px;
    color: #848691;
  }
}
.settings-area {
  height: 42px;
  flex: none;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 0 12px 10px 12px;
  .handleBox {
    display: flex;
    align-items: center;
    .switch-wrapper {
      display: flex;
      column-gap: 4px;
      line-height: 20px;
      color: #c7c7c7;
      font-size: 14px;
      display: flex;
      align-items: center;
      user-select: none;
      margin-right: 50px;

      .switch {
        --el-switch-on-color: #13ce66;
        --el-switch-off-color: rgba(215, 215, 215, 0.3);
        margin-right: 5px;
      }
    }
    .file {
      color: #737171;
      font-size: 12px;
      width: 24px;
      height: 24px;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-left: 7px;
      border-radius: 50%;
      &:hover {
        background: #e9e9e9;
      }
    }
  }
}

.send-btn {
  width: 100px;
  height: 27px;
  padding: 0;
  background-color: #000000;
  border-radius: 20px;
  color: white;
  font-size: 12px;
  position: relative;
  box-shadow: 0px 1px 0px 0px #00c152;
  cursor: pointer;
}

.send-btn:hover {
  color: white;
  background-color: #000000;
}

.send-btn.is-disabled {
  color: rgb(76, 74, 74);
  cursor: not-allowed;
  box-shadow: 0px 1px 0px 0px #015e28;
}

.stop-btn {
  width: 32px;
  height: 32px;
  padding: 0;
  background: #6236ed;
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 16px;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  cursor: pointer;
}

.stop-btn::before {
  content: " ";
  display: block;
  width: 12px;
  height: 12px;
  background: #ffffff;
  border-radius: 3px;
}
</style>

<style lang="scss">
.ask-type-popper {
  width: 161px;
}
</style>

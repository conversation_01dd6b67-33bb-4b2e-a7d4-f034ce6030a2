<template>
    <div v-if="data.length > 0" class="citation-list-container">
        <div class="cita-title-wrapper">
            <div class="cita-title">
                引用
            </div>
        </div>
        <div v-for="(citation, idx) of data" class="document" @click="showCitationDialog(citation)">
            <img :src="docSvg" class="doc-icon">
            <div class="doc-title">{{ citation.doc_name }}</div>
        </div>
        <citation-dialog ref="citationDialog" />
    </div>
</template>

<script setup>
import CitationDialog from './citationDialog.vue';
import docSvg from '@/assets/icons/svg/doc.svg';

const props = defineProps({
    data: {
        type: Array,
        default: function() {
            return [];
        }
    }
});

const citationDialog = ref(null);
function showCitationDialog(data) {
    citationDialog.value.open(data)
}


</script>

<style scoped>
.citation-list-container {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: stretch;
  margin-bottom: 10px;
  font-size: 14px;
}
.citation-list-container > .cita-title-wrapper {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
}

.citation-list-container > .cita-title-wrapper > .cita-title {
  padding-right: 1em;
  color: dimgray;
}

.citation-list-container > .cita-title-wrapper::after {
  display: block;
  flex-grow: 1;
  flex-shrink: 1;
  content: ' ';
  height: 1px;
  border: 0;
  border-bottom: 1px solid lightgray;
}
.citation-list-container > .document {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  cursor: pointer;
}
.citation-list-container > .document:hover > .doc-title {
  text-decoration: underline;
}
.citation-list-container > .document .doc-icon {
  width: 1.5em;
  height: 2em;
  object-fit: contain;
  margin-right: 0.5em;
}
</style>

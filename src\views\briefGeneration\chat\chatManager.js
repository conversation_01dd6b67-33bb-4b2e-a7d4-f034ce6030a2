import { ref, isRef, computed } from 'vue'
import { sseRequest } from '@/utils/request'
import { uniqId } from '@/utils'

class ChatManager {
  requestUrl = ''
 
  funcCreateParams = null

  conversationId = ref(null)

  messageList = ref([])

  prologue = ''

  ctrl = null

  // 是否正在响应中
  isResponsing = computed(() => {
    if (this.messageList.value.length === 0) {
      return false
    }

    const answering = this.messageList.value.find((item) => item.type === 'answering')
    return answering ? true : false
  })

  // 设置请求的url
  setRequestUrl(url) {
    this.requestUrl = url
  }

  // 设置构建请求参数的方法
  setFuncCreateParams(funcCreateParams) {
    this.funcCreateParams = funcCreateParams
  }

  // 设置开场白
  setPrologue(cnt) {
    this.prologue = cnt
    this.addPrologueToMessageList()
  }

  // 将开场白添加进消息列表
  addPrologueToMessageList() {
    if (!this.prologue) {
      return
    }

    if (this.messageList.value.length === 0 || (this.messageList.value.length > 0 && this.messageList.value[0].type !== 'prologue')) {
      this.messageList.value.splice(0, 0, {
        id: uniqId(),
        type: 'prologue',
        content: this.prologue 
      })
    }
  }

  // 设置历史
  setHistory(history) {
    this.clear()
    this.messageList.value.push(...history)
  }

  // 清空
  clear() {
    this.messageList.value = []
    this.addPrologueToMessageList()
  }

  /**
   * 发送消息，重新生成 
   * @param question 问题，它是一个对像
   * @param idx      索引，如果idx不为空，则是重新生成某个回答 
   * @returns 
   */
  send(question, idx) {
    if (this.isResponsing.value) {
      return
    }

    if (!this.funcCreateParams) {
      return
    }

    if (idx && idx > this.messageList.value.length - 1) {
      return
    }

    // 发送消息
    if (!idx) {
      this.messageList.value.push({
        id: uniqId(),
        type: 'question',
        content: question.cnt,
        fileObj: question.files.length > 0 ? question.files[0] : undefined,
        originalQuestion: question
      })

      this.messageList.value.push({
        id: uniqId(),
        type: 'answering',
        content: ''
      })

      idx = this.messageList.value.length - 1

    // 重新生成
    } else {
      this.messageList.value[idx].type = 'answering'
      this.messageList.value[idx].content = ''
    }

    this.ctrl = new AbortController()
    sseRequest(
      this.requestUrl,
      this.funcCreateParams({question, converstaionId: this.conversationId.value}),
      (msg) => {
        const data = JSON.parse(msg.data)
        if (data.event === 'message' && data.answer && this.messageList.value[idx].type === 'answering') {
          if (!this.conversationId.value && data.conversationId) {
            this.conversationId.value = data.conversation_id
          }
          this.messageList.value[idx].content += data.answer
        }
      }, () => {
        this.messageList.value[idx].type = 'answer'
        this.ctrl = null
      }, (e) => {
        this.messageList.value[idx].type = 'answer-error'
        this.ctrl = null
        throw (e)
      },
      this.ctrl.signal
    )
  }

  // 重新生成
  regenerate(idx) {
    console.log(`-----regenerate--${idx}----`)
    const originalQuestion = this.messageList.value[idx - 1].originalQuestion
    console.log(isRef(originalQuestion))
    this.send(originalQuestion, idx)
  }

  // 停止
  stop() {
    if (this.isResponsing && this.ctrl) {
      this.ctrl.abort()
    }

    this.messageList.value.forEach((item) => {
      if (item.type === 'answering') {
        item.type = 'answer'
      }
    })
  }
}

export default ChatManager
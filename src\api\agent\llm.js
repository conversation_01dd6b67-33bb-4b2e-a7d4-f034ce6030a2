import request from '@/utils/request'

// 获取列表
export function getList(params) {
    return request({
        url: '/system/model/list',
        method: 'get',
        params
    })
}

// 获取详情
export function getDetail(id) {
    return request({
        url: '/system/model/'+id,
        method: 'get'
    })
}
//添加智能体
export function addModel(data) {
    return request({
        url: '/system/model/',
        method: 'post',
        data
    })
}

//删除智能体
export function delModel(id) {
    return request({
        url: '/system/model/'+id,
        method: 'delete'
    })
}

//编辑智能体
export function updateModel(data) {
    return request({
        url: '/system/model',
        method: 'put',
        data
    })
}
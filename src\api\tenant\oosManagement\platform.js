import request from '@/utils/request'

//存储平台列表
export function getPlatformList(params) {
    return request({
        url: '/tenant/platform/list',
        method: 'get',
        params
    })
}

//平台下拉框列表
export function getPlatforms() {
    return request({
        url: '/tenant/platform/getplatformkey',
        method: 'get'
    })
}

//新增平台
export function addPlatform(data) {
    return request({
        url: '/tenant/platform',
        method: 'post',
        data
    })
}

//修改
export function editPlatform(data) {
    return request({
        url: '/tenant/platform',
        method: 'put',
        data
    })
}

//删除
export function delPlatform(id) {
    return request({
        url: '/tenant/platform/'+id,
        method: 'delete'
    })
}

<template>
    <div>
        <div>
            <div class="label">
                鉴权方法
            </div>
            <div class="credentils-area" @click="openConfigCredentils">
                <div class="auth-type">
                    {{ props.credentials.auth_type == 'none'?'无':'API Key'}}
                </div>
                <div class="setting-icon">
                    <el-icon><Setting /></el-icon>
                </div>
            </div>
        </div>
        <el-dialog title="鉴权方法" v-model="dialogVisible" width="600px" append-to-body>
            <div>
                <div class="label">
                    鉴权类型
                </div>
                <el-radio-group v-model="credentials.auth_type">
                    <el-radio value="none" border style="width: 120px;">无    </el-radio>
                    <el-radio value="api_key" border  style="width: 120px;">API Key</el-radio>
                </el-radio-group>
            </div>
            <div v-show="credentials.auth_type == 'api_key'">
                <div class="label">
                    鉴权头部前缀
                </div>
                <el-radio-group v-model="credentials.api_key_header_prefix">
                    <el-radio value="basic" border style="width: 120px;">Basic</el-radio>
                    <el-radio value="bearer" border  style="width: 120px;">Bearer</el-radio>
                    <el-radio value="custom" border  style="width: 120px;">Custom</el-radio>
                </el-radio-group>
            </div>
            <div v-show="credentials.auth_type == 'api_key'">
                <div class="label">
                    键
                </div>
                <el-input v-model="credentials.api_key_header" placeholder="HTTP头部名称, 用于传递API Key" />
            </div>
            <div v-show="credentials.auth_type == 'api_key'">
                <div class="label">
                    值
                </div>
                <el-input v-model="credentials.api_key_value" placeholder="输入API Key" />
            </div>
            <div style="display: flex;justify-content: center;align-items: center;margin-top: 20px;">
                <el-button type="primary" @click="confrimCredentials">确定</el-button>
                <el-button @click="dialogVisible = false">取消</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script setup>
import { Setting } from '@element-plus/icons-vue'

const credentials = ref({
    auth_type: 'none',
    api_key_header_prefix: 'basic',
    api_key_header: 'Authorization',
    api_key_value: ''
})
const dialogVisible = ref(false)

const emits = defineEmits(['change:credentials'])

const props = defineProps({
    credentials: {
        type: Object,
        default: () => ({})
    }
})

const confrimCredentials = () => {
    emits('change:credentials', credentials.value)
    dialogVisible.value = false
}

const openConfigCredentils = () => {
    credentials.value = { ...props.credentials }
    dialogVisible.value = true
}

</script>
<style scoped lang="scss">
.credentils-area{
    display: flex;
    align-items: center;
    height: 2.25rem;
    justify-content: space-between;
    cursor: pointer;
    border-radius: .5rem;
    background-color: rgb(242, 244, 247);
    padding-right: .625rem;
    padding-left: .625rem;
    .auth-type{
        font-size: .875rem;
        line-height: 1.25rem;
        font-weight: 400;
        color: rgb(16, 24, 40);
    }
    .setting-icon{
        display: flex;
        align-items: center;
    }
}
.label{
    padding: .5rem 0;
    font-size: .875rem;
    font-weight: 500;
    color: rgb(16, 24, 40);
    line-height: 1.25rem;
}

</style>
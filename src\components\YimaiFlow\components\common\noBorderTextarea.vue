<template>
    <div class="node-desc" :class="{'hide-border': !isEditLabel}">
        <el-input ref="labelInput" v-model="_modelValue" size="small" type="textarea" :rows="2"
            @click="isEditLabel = true" @blur="isEditLabel = false" v-bind="$attrs"/>
    </div>
</template>
<script setup>

const props = defineProps({
    modelValue:{
        type:String
    }
})

const emit = defineEmits(['update:modelValue']);

const isEditLabel = ref(false)

const _modelValue = ref(props.modelValue);

// 监听 fromData 变化，并在变化时触发事件更新父组件的数据
watch(_modelValue, (newValue) => {
  emit('update:modelValue', newValue);
});



</script>
<style lang="scss" scoped>

.node-desc{
    margin-top: 5px;
    ::v-deep(.el-textarea__inner){
        color: black;
        font-size: .65rem;
        padding: 2px;
    }
}
.hide-border{
    ::v-deep(.el-textarea__inner){
        box-shadow: none !important;
    }
}

</style>
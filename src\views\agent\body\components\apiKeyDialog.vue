<template>
  <el-dialog
    title="API密钥管理"
    v-model="isVisible"
    width="1020px"
  >
    <template #header>
      <div class="title-wrapper">
        <span class='title'>API密钥管理</span>
        <span class='sub-title'>这些Key已有当前应用标识，如果不想被滥用，请保护好你的API Key。</span>
      </div>
    </template>
    <div class="tool">
      <div>API根地址:</div> 
      <el-input disabled modelValue="http://172.16.1.19/yimai-agent/" style="width: 300px; margin: 0 10px;"/>
      <el-button :icon="Plus" type="primary" @click="handleAddBtnClick">新建</el-button>
    </div>
    <el-table :data="list" size="default">
      <el-table-column
        prop="keyName"
        label="名称"
        width="130px"
      />
      <el-table-column
        prop="keyValue"
        label="API KEY"
        width="150px"
      >
        <template #default="scope">
          {{ mask(scope.row.keyValue) }}
        </template>
      </el-table-column>
      <el-table-column
        prop="createdTime"
        label="创建时间"
        width="180"
      >
        <template #default="scope">
          {{ formatDate(scope.row.createdTime) }}
        </template>
      </el-table-column>
      <el-table-column
        prop="expirationTime"
        label="过期日期"
        width="150"
      >
        <template #default="scope">
          {{ scope.row.expirationTime ? scope.row.expirationTime.replace(/ .*$/, '') : '-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="lastUseTime"
        label="最后使用时间"
        width="180px"
      >
        <template #default="scope">
          {{ scope.row.lastUseTime ? formatDate(scope.row.lastUseTime) : '未使用' }}
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
      >
        <template #default="scope">
          <el-button
            :icon="CopyDocument"
            link
            circle
            class="btn-copy"
            @click="handleCopyBtnClick(scope.row)"
          />
          <el-button
            :icon="Edit"
            link
            circle
            class="btn-edit"
            @click="handleEditBtnClick(scope.row)"
          />
          <el-button
            :icon="Delete"
            link
            circle
            class="btn-remove"
            @click="handleDelBtnClick(scope.row)"
          />
        </template>
      </el-table-column>
    </el-table>
    <div class="pagination-wrapper">
      <el-pagination
        v-model:current-page="curPage"
        :total="total"
        layout="prev, pager, next"
        @current-change="getList"
      />
    </div>
  </el-dialog>
  <new-api-key-dialog ref="newApiKeyDialogRef" />
  <add-or-update-api-key-dialog
    :agent-id="props.agentId"
    ref="addOrUpdateApiKeyDialogRef"
    @add-success="(val) => {newApiKeyDialogRef.open(val); getList();}"
    @update-success="getList"
  />
</template>

<script setup>
import { Plus, Delete, CopyDocument, Edit } from '@element-plus/icons-vue';
import { onMounted, watch } from 'vue';
import copy from 'copy-to-clipboard';
import { ElMessage, ElMessageBox } from 'element-plus';
import { getApiKeyList, updateApiKey, addApiKey, delApiKey, getApiKeyDetail } from '@/api/agent/apikey';
import { formatDate } from '@/utils/index.js';
import NewApiKeyDialog from './newApiKeyDialog.vue';
import addOrUpdateApiKeyDialog from './addOrUpdateApiKeyDialog.vue';

const props = defineProps({
  /**
   * 是否打开弹窗
   */
  modelValue: {
    type: Boolean,
    required: true,
    default: false
  },

  /**
   * 智能体ID
   */
  agentId: {
    type: String,
    required: true,
  }
})

// 表格数据
const list = ref([]);

// 密钥总数量
const total = ref(1);

// 当前页
const curPage = ref(1);

// 展示新密钥的弹窗的引用
const newApiKeyDialogRef = ref(null);

// 添加/修改弹窗的引用
const addOrUpdateApiKeyDialogRef = ref(null);

const emit = defineEmits(['update:modelValue']);

// 是否显示对话框
const isVisible = ref(props.modelValue);
watch(isVisible, () => {
  if (isVisible.value !== props.modelValue) {
    emit('update:modelValue', isVisible.value);
  }
  if (isVisible.value) {
    getList();
  }
})
watch(() => props.modelValue, () => {
  if (isVisible.value !== props.modelValue) {
    isVisible.value = props.modelValue;
  }
})

/**
 * 添加key
 */
function handleAddBtnClick() {
  addOrUpdateApiKeyDialogRef.value.openAdd();
}

/**
 * 获取密钥列表
 */
function getList() {
  getApiKeyList({
    agentId: props.agentId,
    pageNum: curPage.value,
    applicationType: 'agent'
  }).then((rs) => {
    list.value = rs.rows;
    total.value = rs.total;
  });
}

/**
 * 掩码显示，如:   *******abde
 */
function mask(key) {
  return '********' + key.substring(key.length - 4, key.length);
}

/**
 * 点击复制按钮
 */
function handleCopyBtnClick(row) {
  ElMessageBox.alert(`您要复制的密钥是 <br><div class="agent-copy-key-dialog-key-wrapper">${row.keyValue}</div>`, '复制密钥', {
    confirmButtonText: '复制',
    dangerouslyUseHTMLString: true,
  }).then((res) => {
    if(res != 'confirm') return;
    copy(row.keyValue);
    ElMessage({
      type: 'success',
      message: '已复制到剪贴板'
    })
  })
}

/**
 * 点击编辑按钮
 */
function handleEditBtnClick(row) {
  addOrUpdateApiKeyDialogRef.value.openEdit(row);
}

/**
 * 点击删除按钮
 */
function handleDelBtnClick(row) {
  ElMessageBox.confirm('确定删除该API密钥? 删除后该密钥立即失效，正在使用中的应用会受影响，请确认!', '系统提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
  })
    .then(() => {
      delApiKey(row.id).then(() => {
        getList();
        ElMessage({
          type: 'success',
          message: `已删除`,
        });
      });
    })
    .catch((e) => {
      console.log(e);
    })
}

</script>

<style scoped>
.tool {
  margin-bottom: 20px;
}
.pagination-wrapper {
  width: 100%;
  height: 40px;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  margin-top: 10px;
}
.btn-remove, .btn-edit {
  margin-left: 0;
}
.title-wrapper {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
}
.title {
  display: block;
}
.sub-title {
  display: block;
  font-size: 14px;
  color: gray;
  margin-top: 5px;
}
.tool {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  align-items: center;
}
</style>
<style>
.agent-copy-key-dialog-key-wrapper {
  background-color:#eeeeee;
  padding: 10px;
  border-radius: 5px;
  text-wrap:wrap;
  word-break: break-word;
}
</style>

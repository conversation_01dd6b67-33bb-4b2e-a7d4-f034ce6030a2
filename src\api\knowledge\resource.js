import request from "@/utils/request"

// 查询资源首页列表
export function queryResourceHomePageList (query) {
    return request({
        url: '/FileResource/getFileCount',
        method: 'get',
        params: query
    })
}

// 查询资源列表
export function queryResourceList (data) {
    return request({
        url: '/FileResource/getFileList?pageNum=' + data.pageNum + '&pageSize=' + data.pageSize,
        method: 'post',
        data
    })
}

// 资源删除
export function deleteFile (id) {
    return request({
        url: '/FileResource/deleteFile/' + id,
        method: 'delete'
    })
}

// 查询课程
export function getClassList (query) {
    return request({
        url: '/FileResource/getClassList',
        method: 'get',
        params: query
    })
}
<template>
    <div class="session">
        <div class="middle" :style="{}">
            <!--智能体会话-->
            <session-records :message-item-manager="props.messageItemManager" :chat-url="props.chatUrl" :current-instruct="currentInstruct" 
            :preface-state="prefaceState" :preface-height="prefaceHeight" :is-title="props.isTitle" :is-key-list="props.isKeyList" ref="messageCtrl"
            @update-preface-state="updatePrefaceState" @conversation-unselect="conversationUnselect" @conversation-refresh= "conversationRefresh"
            v-if="!currentInstruct || currentInstruct.applicationType == 'agent'" :is-debugging = "props.isDebugging" ></session-records>
            <!--会话型团队-->
            <session-records-team :message-item-manager="props.messageItemManagerTeam" :chat-url="props.chatUrl" :current-instruct="currentInstruct" 
            :preface-state="prefaceState" :preface-height="prefaceHeight" :is-title="props.isTitle" :is-key-list="props.isKeyList" ref="messageCtrl"
            @update-preface-state="updatePrefaceState" @conversation-unselect="conversationUnselect" @conversation-refresh= "conversationRefresh"
            v-if="currentInstruct && currentInstruct.applicationType == 'team' && !currentInstruct.isTaskOrientedTeam" :is-debugging = "props.isDebugging">
            </session-records-team>
            <!--任务型团队-->
            <session-records-team-task :message-item-manager="props.messageItemManagerTeamTask" :chat-url="props.chatUrl" :current-instruct="currentInstruct" 
            :preface-state="prefaceState" ref="messageCtrl"
            @update-preface-state="updatePrefaceState" @conversation-unselect="conversationUnselect" @conversation-refresh= "conversationRefresh"
            v-if="currentInstruct && currentInstruct.applicationType == 'team' && currentInstruct.isTaskOrientedTeam" :is-debugging = "props.isDebugging"
            :run-disabled="runDisabled" @update-run-disabled="updateRunDisabled">
            </session-records-team-task>
        </div>
        <div class="agent" :style="{'width':isTeamRoleShow?'15%':'0'}" v-if="isTeamRoleShow && !prefaceState">
        </div>
    </div>
</template>
<script setup>
    import { MessageItemManager } from './utils/msgManager';
    import { MessageItemManagerTeam } from './utils/msgManagerTeam';
    import { MessageItemManagerTeamTask } from './utils/msgManagerTeamTask';
    import { defineProps, nextTick,ref, watch } from 'vue';
    import { ArrowLeft,ArrowRight,Refresh } from '@element-plus/icons-vue'
    import sessionRecords from './sessionRecords'
    import sessionRecordsTeam from './sessionRecordsTeam'
    import sessionRecordsTeamTask from './sessionRecordsTeamTask'
    

    const emit = defineEmits();
    //baseUrl
    const baseUrl = import.meta.env.VITE_APP_BASE_API;

    const props = defineProps({
        messageItemManager: {
            type: Object,
            required: false,
            default: new MessageItemManager()
        },
        messageItemManagerTeam: {
            type: Object,
            required: false,
            default: new MessageItemManagerTeam()
        },
        messageItemManagerTeamTask: {
            type: Object,
            required: false,
            default: new MessageItemManagerTeamTask()
        },
        //服务路径
        chatUrl:{
            type:String,
            required:true
        },
        keyList:{
            type:Array,
            required:false,
            default: () => {
                return []
            }
        },
        currentInstruct:{
            type:Object,
            required:true
        },
        //是否展示前言
        prefaceState:{
            type:Boolean,
            required:false,
            default:false
        },
        //前言高度
        prefaceHeight:{
            type:Number,
            required:false,
            default:0
        },
        //是否展示title
        isTitle:{
            type:Boolean,
            required:false,
            default:false
        },
        //是否展示用户输入表单
        isKeyList:{
            type:Boolean,
            required:false,
            default:false
        },
        //是否是调试
        isDebugging:{
            type:Boolean,
            required:false,
            default:false
        },
        //是否可以运行
        runDisabled:{
            type:Boolean,
            required:false,
            default:false
        },
        //是否是嵌入网页
        isPublicVisit:{
            type:Boolean,
            required:false,
            default:false
        }
    });

    const currentInstruct = ref(null)
    //是否展示会话参与成员
    const isTeamRoleShow = ref(false)
    watch(() => props.currentInstruct, val => {
        if (val) {
            currentInstruct.value = null
            currentInstruct.value = val
            // isTeamRoleShow.value = currentInstruct.value.applicationType=='team'?true:false
            return val
        } else {
            currentInstruct.value = null
            // isTeamRoleShow.value = false
            return {}
        }
    },{ deep: true, immediate: true });

    //是否展示前言
    const prefaceState = ref(false)
    watch(() => props.prefaceState, val => {
        if (val) {
            prefaceState.value = val
            return val
        } else {
            prefaceState.value = false
            return false
        }
    },{ deep: true, immediate: true });

    //前言高度
    const prefaceHeight = ref(0)
    watch(() => props.prefaceHeight, val => {
        if (val) {
            prefaceHeight.value = val
            return val
        } else {
            prefaceHeight.value = 0
            return 0
        }
    },{ deep: true, immediate: true });

    //是否可运行
    const runDisabled = ref(false)
    watch(() => props.runDisabled, val => {
        if (val) {
            runDisabled.value = true
            return true
        } else {
            runDisabled.value = false
            return false
        }
    },{ deep: true, immediate: true });
    
    //修改prefaceState
    const updatePrefaceState = (newValue) => {
        prefaceState.value = newValue
        emit("updatePrefaceState", prefaceState.value);
    }

    const updateRunDisabled = (newValue) => {
        runDisabled.value = newValue
        emit("updateRunDisabled", runDisabled.value);
    }
    

    const messageCtrl = ref(null)
    const sessionInfo = ref({})

    const conversation = ref(null)
    //历史记录取消选中
    const conversationUnselect = () => {
        //历史记录取消选中
        emit('conversationUnselect')
    }
    //历史记录刷新
    const conversationRefresh = () => {
        emit('conversationRefresh')
    }
    
    //新会话
    const openNewSessionClear = () => {
        messageCtrl.value.openNewSessionClear()
    }

    //历史记录处理
    const openHistorySessionClear = () => {
        messageCtrl.value.openHistorySessionClear()
    }

    const closeSession = () => {
        messageCtrl.value.closeSession()
    }
    const sendMessageTask = (keyList,isClearHistory) => {
        messageCtrl.value.sendMessageTask(keyList,isClearHistory)
    }

    defineExpose({
        closeSession,
        openNewSessionClear,
        openHistorySessionClear,
        sendMessageTask
    })
</script>
<style lang="scss" scoped>
.session{
    display: flex;
    justify-content: space-between;
    width: 100%;
    .middle{
        width: 100%;
        .right-head{
            padding: 15px;
            font-size: 16px;
            font-weight: bold;
            color: #032220;
            background-color: white;
            box-shadow: 0 2px 4px 0 rgba(0, 0, 0, .04), 0 0 1px 0 rgba(0, 0, 0, .08);
            .head-title{
                display: flex;
                justify-content: space-between;
            }
        }
    }
    .agent{
        background-color: #F7F7FA;
        padding: 50px 0;
        .team-agent{
            text-align: center;
            display:flex;
            align-items:center;
            justify-content:center;
            margin-top: 20px;
            .team-agent-box{
                width: 90px;
                height:90px;
                display:flex;
                align-items:center;
                justify-content:center;
                flex-direction: column;
                img{
                    width:50px;
                    border-radius: 10px;
                    background-color: #dfdfff;
                }
                .name{
                    font-size: 12px;
                    color:#909399;
                    margin-top: 5px;
                    line-height: 14px;
                }
            }
            .say{
                border-color: #5050E6;
                box-shadow: 0 0 10px rgba(80,80,230,.6), inset 0 0 10px rgba(80,80,230,.4), 0 1px 0 #ffffff;
            }
        }
    }
}

</style>
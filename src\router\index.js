import { createWebHistory, createRouter } from 'vue-router'
/* Layout */
import Layout from '@/layout'

/**
 * Note: 路由配置项
 *
 * hidden: true                     // 当设置 true 的时候该路由不会再侧边栏出现 如401，login等页面，或者如一些编辑页面/edit/1
 * alwaysShow: true                 // 当你一个路由下面的 children 声明的路由大于1个时，自动会变成嵌套的模式--如组件页面
 *                                  // 只有一个时，会将那个子路由当做根路由显示在侧边栏--如引导页面
 *                                  // 若你想不管路由下面的 children 声明的个数都显示你的根路由
 *                                  // 你可以设置 alwaysShow: true，这样它就会忽略之前定义的规则，一直显示根路由
 * redirect: noRedirect             // 当设置 noRedirect 的时候该路由在面包屑导航中不可被点击
 * name:'router-name'               // 设定路由的名字，一定要填写不然使用<keep-alive>时会出现各种问题
 * query: '{"id": 1, "name": "ry"}' // 访问路由的默认传递参数
 * roles: ['admin', 'common']       // 访问路由的角色权限
 * permissions: ['a:a:a', 'b:b:b']  // 访问路由的菜单权限
 * meta : {
    noCache: true                   // 如果设置为true，则不会被 <keep-alive> 缓存(默认 false)
    title: 'title'                  // 设置该路由在侧边栏和面包屑中展示的名字
    icon: 'svg-name'                // 设置该路由的图标，对应路径src/assets/icons/svg
    breadcrumb: false               // 如果设置为false，则不会在breadcrumb面包屑中显示
    activeMenu: '/system/user'      // 当路由设置了该属性，则会高亮相对应的侧边栏。
  }
 */

// 公共路由
export const constantRoutes = [
  {
    path: '/redirect',
    component: Layout,
    hidden: true,
    children: [
      {
        path: '/redirect/:path(.*)',
        component: () => import('@/views/redirect/index.vue')
      }
    ]
  },
  {
    path: '/login',
    component: () => import('@/views/login'),
    hidden: true
  },
  /*
  {
    path: '/permissions',
    component: () => import('@/views/permissions/index.vue'),
    redirect: '/permissions/role',
    hidden: true,
    children: [
      {
        path: 'role',
        component: () => import('@/views/permissions/role/index.vue'),
        hidden: true
      },
      {
        path: 'user',
        component: () => import('@/views/permissions/user/index.vue'),
        hidden: true
      },
      {
        path: 'class',
        component: () => import('@/views/permissions/class/index.vue'),
        hidden: true
      },
    ]
  },
  */
  /*
  {
    path: '/edu',
    component: () => import('@/views/education'),
    hidden: true,
    children: [
      {
        path: '',
        component: () => import('@/views/education/portal'),
        hidden: true
      },
      {
        path: 'portal',
        component: () => import('@/views/education/portal'),
        hidden: true
      },
      {
        path: 'home',
        component: () => import('@/views/education/home'),
        hidden: true
      },
      {
        path: 'full-dify',
        component: () => import('@/views/education/train/full-dify'),
        hidden: true
      },
      {
        path: 'teach',
        component: () => import('@/views/education/teach'),
        hidden: true,
        children: [
          {
            path: 'plan',
            component: () => import('@/views/education/teach/plan'),
            hidden: true,
          },
          {
            path: 'course',
            component: () => import('@/views/education/teach/course'),
            hidden: true
          },
          {
            path: 'courseware',
            component: () => import('@/views/education/teach/courseware'),
            hidden: true
          },
          {
            path: 'exam',
            component: () => import('@/views/education/teach/exam'),
            hidden: true
          }
        ],
      },
      {
        path: 'knowledge',
        component: () => import('@/views/education/knowledge'),
        hidden: true,
        children: [
          {
            path: 'point',
            component: () => import('@/views/education/knowledge/point'),
            hidden: true,
          },
          {
            path: 'graph',
            component: () => import('@/views/education/knowledge/graph'),
            hidden: true
          },
          {
            path: 'resource',
            component: () => import('@/views/education/knowledge/resource'),
            hidden: true,
          }
        ]
      },
      {
        path: 'course',
        component: () => import('@/views/education/course'),
        hidden: true,
        children: [
          {
            path: 'teaching',
            component: () => import('@/views/education/course/teaching'),
            hidden: true,
          },
          {
            path: 'learning',
            component: () => import('@/views/education/course/learning'),
            hidden: true
          },
        ]
      },
      {
        path: 'train',
        component: () => import('@/views/education/train'),
        hidden: true,
        children: [
          {
            path: 'my-apps',
            component: () => import('@/views/education/train/my-apps'),
            hidden: true,
          },
          {
            path: 'my-knowledge',
            component: () => import('@/views/education/train/my-knowledge'),
            hidden: true
          },
          {
            path: 'my-plugins',
            component: () => import('@/views/education/train/my-plugins'),
            hidden: true
          },
          {
            path: 'explore',
            component: () => import('@/views/education/train/explore'),
            hidden: true
          },
        ]
      },
      {
        path: 'scholar',
        component: () => import('@/views/education/scholar'),
        hidden: true,
        children: [
          {
            path: 'search',
            component: () => import('@/views/education/scholar/search'),
            hidden: true,
          },
          {
            path: 'read',
            component: () => import('@/views/education/scholar/read'),
            hidden: true,
            children: [
              {
                path: '',
                component: () => import('@/views/education/scholar/read/list'),
                hidden: true,
              },
              {
                path: 'doc',
                component: () => import('@/views/education/scholar/read/document'),
                hidden: true,
              }
            ]
          },
          {
            path: 'write',
            component: () => import('@/views/education/scholar/write'),
            hidden: true,
            children: [
              {
                path: '',
                component: () => import('@/views/education/scholar/write/list'),
                hidden: true
              },
            ]
          }
        ]
      },
      {
        path: 'profile',
        component: () => import('@/views/education/profile'),
        hidden: true,
      },
      {
        path: 'resource-list',
        component: () => import('@/views/education/knowledge/resource/list'),
        hidden: true,
      },
      {
        path: 'course-editor',
        component: () => import('@/views/education/teach/course/editor'),
        hidden: true
      },
      {
        path: 'course-play',
        component: () => import('@/views/education/teach/course/play'),
        hidden: true
      },
      {
        path: 'plan-design',
        component: () => import('@/views/education/teach/plan/design'),
        hidden: true
      },
      {
        path: 'plan-detail',
        component: () => import('@/views/education/teach/plan/detail'),
        hidden: true
      },
      {
        path: 'exam-editor',
        component: () => import('@/views/education/teach/exam/editor'),
        hidden: true
      },
      {
        path: 'exam-analyse',
        component: () => import('@/views/education/teach/exam/analyse'),
        hidden: true
      },
      {
        path: 'exam-student',
        component: () => import('@/views/education/teach/exam/student'),
        hidden: true
      },
      {
        path: 'exam-detail',
        component: () => import('@/views/education/teach/exam/detail'),
        hidden: true
      },
      {
        path: 'exam-fill',
        component: () => import('@/views/education/teach/exam/fill'),
        hidden: true
      },
      {
        path: 'exam-result',
        component: () => import('@/views/education/teach/exam/result'),
        hidden: true
      },
      {
        path: 'student-result',
        component: () => import('@/views/education/teach/exam/student-result'),
        hidden: true
      },
      {
        path: 'chat',
        component: () => import('@/views/education/chat'),
        hidden: true
      },
      {
        path: 'assistant',
        component: () => import('@/views/education/assistant'),
        hidden: true
      },
      {
        path: 'assistant-chat',
        component: () => import('@/views/education/assistant/chat'),
        hidden: true
      },
      {
        path: 'course-task-list-teacher',
        component: () => import('@/views/education/course/task/list.vue'),
        hidden: true
      },
      {
        path: 'course-task-list-student',
        component: () => import('@/views/education/course/task/list.vue'),
        hidden: true
      },
      {
        path: 'course-guide',
        component: () => import('@/views/education/course/guide.vue'),
        hidden: true
      },
      {
        path: 'ai-student-train',
        component: () => import('@/views/education/course/task/detail/ai-student-training/index.vue'),
        hidden: true,
      },
      {
        path: 'ai-teacher-train',
        component: () => import('@/views/education/course/task/detail/ai-training/index.vue'),
        hidden: true,
      },
      {
        path: 'course-task-detail',
        component: () => import('@/views/education/course/task/detail'),
        hidden: true,
      },
      {
        path: 'student-train',
        component: () => import('@/views/education/course/task/detail/student-train'),
        hidden: true
      },
      {
        path: 'student-train-full',
        component: () => import('@/views/education/course/task/detail/student-train-full'),
        hidden: true
      },
      {
        path: 'student-train-experience',
        component: () => import('@/views/education/course/task/detail/student-train-experience'),
        hidden: true
      },
      {
        path: 'train-app-detail',
        component: () => import('@/views/education/train/detail'),
        hidden: true
      },
      {
        path: 'train-app-experience',
        component: () => import('@/views/education/train/experience'),
        hidden: true
      },
      {
        path: 'scholar-write',
        component: () => import('@/views/education/scholar/write/tool'),
        hidden: true,
        children: [
          {
            path: 'select-topic',
            component: () => import('@/views/education/scholar/write/select-topic'),
            hidden: true
          },
          {
            path: 'literature-review',
            component: () => import('@/views/education/scholar/write/literature-review'),
            hidden: true
          },
          {
            path: 'outline',
            component: () => import('@/views/education/scholar/write/outline'),
            hidden: true
          },
          {
            path: 'polish',
            component: () => import('@/views/education/scholar/write/polish'),
            hidden: true
          },
          {
            path: 'aigc-detection',
            component: () => import('@/views/education/scholar/write/aigc-detection'),
            hidden: true
          },
          {
            path: 'pre-review',
            component: () => import('@/views/education/scholar/write/pre-review'),
            hidden: true
          },
          {
            path: 'ppt',
            component: () => import('@/views/education/scholar/write/ppt'),
            hidden: true
          },
        ]
      }
    ]
  },
  */
  {
    path: '/insetChatPrompt',
    component: () => import('@/views/insetChatPrompt/index'),
    hidden: true
  },
  /**客户端会话 */
  {
    path: '/session',
    component: () => import('@/views/session/index'),
    hidden: true
  },
  /**客户端会话新 */
  {
    path: '/chat',
    component: () => import('@/views/chat/index'),
    hidden: true
  },
  {
    path: '/register',
    component: () => import('@/views/register'),
    hidden: true
  },
  {
    path: "/:pathMatch(.*)*",
    component: () => import('@/views/error/404'),
    hidden: true
  },
  {
    path: '/401',
    component: () => import('@/views/error/401'),
    hidden: true
  },
  {
    path: '',
    component: Layout,
    redirect: '/edu/portal',
    children: [
      {
        path: '/team',
        component: () => import('@/views/teamManagement/index'),
        name: 'team',
        meta: { title: '团队', icon: 'team1', affix: true }
      }
    ]
  },
  // {
  //   path: '',
  //   component: Layout,
  //   redirect: '/shop',
  //   children: [
  //     {
  //       path: '/shop',
  //       component: () => import('@/views/ecommerce/shop/index'),
  //       name: 'shop',
  //       meta: { title: '店铺', icon: 'shopping', affix: true }
  //     }
  //   ]
  // },
  {
    path: '/user',
    component: Layout,
    hidden: true,
    redirect: 'noredirect',
    children: [
      {
        path: 'profile',
        component: () => import('@/views/system/user/profile/index'),
        name: 'Profile',
        meta: { title: '个人中心', icon: 'user' }
      }
    ]
  }
]

// 动态路由，基于用户权限动态去加载
export const dynamicRoutes = [
  {
    path: '/system/user-auth',
    component: Layout,
    hidden: true,
    permissions: ['system:user:edit'],
    children: [
      {
        path: 'role/:userId(\\d+)',
        component: () => import('@/views/system/user/authRole'),
        name: 'AuthRole',
        meta: { title: '分配角色', activeMenu: '/system/user' }
      }
    ]
  },
  {
    path: '/system/role-auth',
    component: Layout,
    hidden: true,
    permissions: ['system:role:edit'],
    children: [
      {
        path: 'user/:roleId(\\d+)',
        component: () => import('@/views/system/role/authUser'),
        name: 'AuthUser',
        meta: { title: '分配用户', activeMenu: '/system/role' }
      }
    ]
  },
  {
    path: '/system/role-app',
    component: Layout,
    hidden: true,
    permissions: ['system:role:edit'],
    children: [
      {
        path: 'app/:roleId(\\d+)',
        component: () => import('@/views/system/role/roleApp'),
        name: 'RoleApp',
        meta: { title: '分配应用', activeMenu: '/system/role' }
      }
    ]
  },
  {
    path: '/application',
    component: Layout,
    hidden: true,
    permissions: ['store:store:getadminstore'],
    children: [
      {
        path: 'cancelUserAuth',
        component: () => import('@/views/application/auth/cancelUser'),
        name: 'CancelUser',
        meta: { title: '取消用户授权', activeMenu: '/store' }
      },
      {
        path: 'team/:teamId(\\d+)',
        component: () => import('@/views/teamManagement/teamRole'),
        name: 'AppTeamRole',
        meta: { title: '分配角色', activeMenu: '/store' }
      },
      {
        path: 'agent/:agentId(\\d+)',
        component: () => import('@/views/agent/body/agentRole'),
        name: 'AppAgentRole',
        meta: { title: '分配角色', activeMenu: '/store' }
      }
    ]
  },
  {
    path: '/team/team-role',
    component: Layout,
    hidden: true,
    permissions: ['team:team:edit'],
    children: [
      {
        path: 'team/:teamId(\\d+)',
        component: () => import('@/views/teamManagement/teamRole'),
        name: 'TeamRole',
        meta: { title: '分配角色', activeMenu: '/team' }
      },
      {
        path: 'statistics',
        component: () => import('@/views/teamManagement/statistics'),
        name: 'TeamStatistics',
        meta: { title: '统计分析', activeMenu: '/team' }
      }
    ]
  },
  {
    path: '/agent/agent-role',
    component: Layout,
    hidden: true,
    permissions: ['agent:aiagent:edit'],
    children: [
      {
        path: 'agent/:agentId(\\d+)',
        component: () => import('@/views/agent/body/agentRole'),
        name: 'AgentRole',
        meta: { title: '分配角色', activeMenu: '/bodyManger' }
      }
    ]
  },
  {
    path: '/system/dict-data',
    component: Layout,
    hidden: true,
    permissions: ['system:dict:list'],
    children: [
      {
        path: 'index/:dictId(\\d+)',
        component: () => import('@/views/system/dict/data'),
        name: 'Data',
        meta: { title: '字典数据', activeMenu: '/system/dict' }
      }
    ]
  },
  {
    path: '/monitor/job-log',
    component: Layout,
    hidden: true,
    permissions: ['monitor:job:list'],
    children: [
      {
        path: 'index/:jobId(\\d+)',
        component: () => import('@/views/monitor/job/log'),
        name: 'JobLog',
        meta: { title: '调度日志', activeMenu: '/monitor/job' }
      }
    ]
  },
  {
    path: '/tool/gen-edit',
    component: Layout,
    hidden: true,
    permissions: ['tool:gen:edit'],
    children: [
      {
        path: 'index/:tableId(\\d+)',
        component: () => import('@/views/tool/gen/editTable'),
        name: 'GenEdit',
        meta: { title: '修改生成配置', activeMenu: '/tool/gen' }
      }
    ]
  }
]

const router = createRouter({
  history: createWebHistory('/'),
  routes: constantRoutes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  },
})

export default router

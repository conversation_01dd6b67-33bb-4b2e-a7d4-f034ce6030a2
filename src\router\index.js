import { createWebHistory, createRouter } from "vue-router"
/* Layout */
import Layout from "@/layout"
// import BriefLayout from '@/views/briefGeneration/index.vue'

/**
 * Note: 路由配置项
 *
 * hidden: true                     // 当设置 true 的时候该路由不会再侧边栏出现 如401，login等页面，或者如一些编辑页面/edit/1
 * alwaysShow: true                 // 当你一个路由下面的 children 声明的路由大于1个时，自动会变成嵌套的模式--如组件页面
 *                                  // 只有一个时，会将那个子路由当做根路由显示在侧边栏--如引导页面
 *                                  // 若你想不管路由下面的 children 声明的个数都显示你的根路由
 *                                  // 你可以设置 alwaysShow: true，这样它就会忽略之前定义的规则，一直显示根路由
 * redirect: noRedirect             // 当设置 noRedirect 的时候该路由在面包屑导航中不可被点击
 * name:'router-name'               // 设定路由的名字，一定要填写不然使用<keep-alive>时会出现各种问题
 * query: '{"id": 1, "name": "ry"}' // 访问路由的默认传递参数
 * roles: ['admin', 'common']       // 访问路由的角色权限
 * permissions: ['a:a:a', 'b:b:b']  // 访问路由的菜单权限
 * meta : {
    noCache: true                   // 如果设置为true，则不会被 <keep-alive> 缓存(默认 false)
    title: 'title'                  // 设置该路由在侧边栏和面包屑中展示的名字
    icon: 'svg-name'                // 设置该路由的图标，对应路径src/assets/icons/svg
    breadcrumb: false               // 如果设置为false，则不会在breadcrumb面包屑中显示
    activeMenu: '/system/user'      // 当路由设置了该属性，则会高亮相对应的侧边栏。
  }
 */

// 公共路由
export const constantRoutes = [
  {
    path: "/redirect",
    component: Layout,
    hidden: true,
    children: [
      {
        path: "/redirect/:path(.*)",
        component: () => import("@/views/redirect/index.vue"),
      },
    ],
  },
  {
    path: "/login",
    component: () => import("@/views/login"),
    hidden: true,
  },
  {
    path: "/insetChatPrompt",
    component: () => import("@/views/insetChatPrompt/index"),
    hidden: true,
  },
  /**客户端会话 */
  {
    path: "/session",
    component: () => import("@/views/session/index"),
    hidden: true,
  },
  /**客户端会话新 */
  {
    path: "/chat",
    component: () => import("@/views/chat/index"),
    hidden: true,
  },
  /**嵌入网站智能体会话 */
  {
    path: "/web/chat/:token(\\d+)",
    component: Layout,
    hidden: true,
    component: () => import("@/views/web/chat"),
  },
  /**嵌入网站团队会话 */
  {
    path: "/web/team/:token(.*)",
    component: Layout,
    hidden: true,
    component: () => import("@/views/web/team"),
  },
  /**嵌入网站团队任务型会话 */
  {
    path: "/web/workflow/:token(.*)",
    component: Layout,
    hidden: true,
    component: () => import("@/views/web/workflow"),
  },
  /**嵌入网站测试 */
  {
    path: "/web/testIframe",
    component: Layout,
    hidden: true,
    component: () => import("@/views/web/testIframe"),
  },
  {
    path: "/register",
    component: () => import("@/views/register"),
    hidden: true,
  },
  {
    path: "/:pathMatch(.*)*",
    component: () => import("@/views/error/404"),
    hidden: true,
  },
  {
    path: "/401",
    component: () => import("@/views/error/401"),
    hidden: true,
  },
  {
    path: "/appliedStatistics",
    component: () => import("@/views/appliedStatistics/index"),
    name: "appliedStatistics",
    meta: { title: "应用统计", icon: "chart", affix: true },
    hidden: true,
  },
  // {
  //   path: "",
  //   component: Layout,
  //   redirect: "/appliedStatistics",
  //   children: [
  //     {
  //       path: "/appliedStatistics",
  //       component: () => import("@/views/appliedStatistics/index"),
  //       name: "appliedStatistics",
  //       meta: { title: "应用统计", icon: "chart", affix: true },
  //     },
  //   ],
  // },
  {
    path: '',
    redirect: '/briefGeneration/home',
    hidden: true
  },
  // {
  //   path: '',
  //   component: Layout,
  //   redirect: '/team',
  //   children: [
  //     {
  //       path: '/team',
  //       component: () => import('@/views/teamManagement/index'),
  //       name: 'team',
  //       meta: { title: '团队', icon: 'team1', affix: true }
  //     }
  //   ]
  // },
  // {
  //   path: '',
  //   component: Layout,
  //   redirect: '/shop',
  //   children: [
  //     {
  //       path: '/shop',
  //       component: () => import('@/views/ecommerce/shop/index'),
  //       name: 'shop',
  //       meta: { title: '店铺', icon: 'shopping', affix: true }
  //     }
  //   ]
  // },
  {
    path: "/user",
    component: Layout,
    hidden: true,
    redirect: "noredirect",
    children: [
      {
        path: "profile",
        component: () => import("@/views/system/user/profile/index"),
        name: "Profile",
        meta: { title: "个人中心", icon: "user" },
      },
    ],
  },
  {
    path: "/briefGeneration",
    component: () => import("@/views/briefGeneration/index"),
    hidden: true,
    children: [
      {
        path: "projectMannage",
        component: () => import("@/views/briefGeneration/projectMannage"),
        hidden: true,
      }
    ]
  },
  /*
  {
    path: "/brief",
    component: () => import("@/views/brief"),
    hidden: true,
    children: [
      {
        path: "home",
        component: () => import("@/views/brief/home"),
        hidden: true,
        meta: { activeMenu: "/brief/home" },
      },
    ]
  },
  */
  /*
  {
    path: "/briefGeneration",
    component: BriefLayout,
    hidden: true,
    meta: {
      title: "简报生成",
      icon: "dict",
      affix: true,
      newIcon: "ri-article-line",
    },
    redirect: "/briefGeneration/home",
    children: [
      {
        path: "home",
        component: () => import("@/views/briefGeneration/home"),
        hidden: true,
        meta: { activeMenu: "/briefGeneration/home" }, // 关键
      },
      {
        path: "list",
        component: () => import("@/views/briefGeneration/list"),
        hidden: true,
        meta: { activeMenu: "/briefGeneration" }, // 关键
      },
      {
        path: "add",
        component: () => import("@/views/briefGeneration/add"),
        hidden: true,
        children: [
          {
            path: "config",
            component: () =>
              import("@/views/briefGeneration/add/config/index.vue"),
            hidden: true,
            meta: { activeMenu: "/briefGeneration" }, // 关键
          },
          {
            path: "create/:type/:id?",
            component: () =>
              import("@/views/briefGeneration/add/create/index.vue"),
            hidden: true,
            meta: { activeMenu: "/briefGeneration" }, // 关键
          },
        ],
      },
    ],
  },
  */
]

// 动态路由，基于用户权限动态去加载
export const dynamicRoutes = [
  {
    path: "/system/user-auth",
    component: Layout,
    hidden: true,
    permissions: ["system:user:edit"],
    children: [
      {
        path: "role/:userId(\\d+)",
        component: () => import("@/views/system/user/authRole"),
        name: "AuthRole",
        meta: { title: "分配角色", activeMenu: "/system/user" },
      },
    ],
  },
  {
    path: "/system/role-auth",
    component: Layout,
    hidden: true,
    permissions: ["system:role:edit"],
    children: [
      {
        path: "user/:roleId(\\d+)",
        component: () => import("@/views/system/role/authUser"),
        name: "AuthUser",
        meta: { title: "分配用户", activeMenu: "/system/role" },
      },
    ],
  },
  {
    path: "/system/role-app",
    component: Layout,
    hidden: true,
    permissions: ["system:role:edit"],
    children: [
      {
        path: "app/:roleId(\\d+)",
        component: () => import("@/views/system/role/roleApp"),
        name: "RoleApp",
        meta: { title: "分配应用", activeMenu: "/system/role" },
      },
    ],
  },
  {
    path: "/application",
    component: Layout,
    hidden: true,
    permissions: ["store:store:getadminstore"],
    children: [
      {
        path: "cancelUserAuth",
        component: () => import("@/views/application/auth/cancelUser"),
        name: "CancelUser",
        meta: { title: "取消用户授权", activeMenu: "/store" },
      },
      {
        path: "team/:teamId(\\d+)",
        component: () => import("@/views/teamManagement/teamRole"),
        name: "AppTeamRole",
        meta: { title: "分配角色", activeMenu: "/store" },
      },
      {
        path: "agent/:agentId(\\d+)",
        component: () => import("@/views/agent/body/agentRole"),
        name: "AppAgentRole",
        meta: { title: "分配角色", activeMenu: "/store" },
      },
    ],
  },
  {
    path: "/team/team-role",
    component: Layout,
    hidden: true,
    permissions: ["team:team:edit"],
    children: [
      {
        path: "team/:teamId(\\d+)",
        component: () => import("@/views/teamManagement/teamRole"),
        name: "TeamRole",
        meta: { title: "分配角色", activeMenu: "/team" },
      },
      {
        path: "statistics",
        component: () => import("@/views/teamManagement/statistics"),
        name: "TeamStatistics",
        meta: { title: "统计分析", activeMenu: "/team" },
      },
    ],
  },
  {
    path: "/agent/agent-role",
    component: Layout,
    hidden: true,
    permissions: ["agent:aiagent:edit"],
    children: [
      {
        path: "agent/:agentId(\\d+)",
        component: () => import("@/views/agent/body/agentRole"),
        name: "AgentRole",
        meta: { title: "分配角色", activeMenu: "/bodyManger" },
      },
    ],
  },
  {
    path: "/system/dict-data",
    component: Layout,
    hidden: true,
    permissions: ["system:dict:list"],
    children: [
      {
        path: "index/:dictId(\\d+)",
        component: () => import("@/views/system/dict/data"),
        name: "Data",
        meta: { title: "字典数据", activeMenu: "/system/dict" },
      },
    ],
  },
  {
    path: "/monitor/job-log",
    component: Layout,
    hidden: true,
    permissions: ["monitor:job:list"],
    children: [
      {
        path: "index/:jobId(\\d+)",
        component: () => import("@/views/monitor/job/log"),
        name: "JobLog",
        meta: { title: "调度日志", activeMenu: "/monitor/job" },
      },
    ],
  },
  {
    path: "/tool/gen-edit",
    component: Layout,
    hidden: true,
    permissions: ["tool:gen:edit"],
    children: [
      {
        path: "index/:tableId(\\d+)",
        component: () => import("@/views/tool/gen/editTable"),
        name: "GenEdit",
        meta: { title: "修改生成配置", activeMenu: "/tool/gen" },
      },
    ],
  },
]

const env = import.meta.env.VITE_APP_ENV
const router = createRouter({
  history: createWebHistory(env === "production" ? "/yimai-agent/" : "/"),
  routes: constantRoutes,
  scrollBehavior (to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  },
})

export default router

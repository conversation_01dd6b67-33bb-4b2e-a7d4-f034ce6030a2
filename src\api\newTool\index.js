import request from '@/utils/difyRequest'

// 获取所有工具
export function getAllTool() {
    return request({
        url: '/console/api/workspaces/current/tool-providers',
        method: 'get'
    })
}

// 获取内置工具内的tools
export function getBuiltinToolsByProvider(id) {
    return request({
        url: `/console/api/workspaces/current/tool-provider/builtin/${id}/tools`,
        method: 'get'
    })
}

// 获取用户自定义的工具内的tools
export function getCustomToolsByProvider(params) {
    return request({
        url: '/console/api/workspaces/current/tool-provider/api/tools',
        method: 'get',
        params
    })
}

//提取schema中的可用工具
export function getSchemaToolsByProvider(data) {
    return request({
        url: '/console/api/workspaces/current/tool-provider/api/schema',
        method: 'post',
        data
    })
}

//获取所有工具可以选择的标签
export function getAllTags() {
    return request({
        url: '/console/api/workspaces/current/tool-labels',
        method: 'get'
    })
}

//新增自定义工具
export function addCustomTool(data) {
    return request({
        url: '/console/api/workspaces/current/tool-provider/api/add',
        method: 'post',
        data
    })
}

//测试工具
export function testTool(data) {
    return request({
        url: 'console/api/workspaces/current/tool-provider/api/test/pre',
        method: 'post',
        data
    })
}

//根据provider获取工具详情
export function getToolDetailByProvider(params) {
    return request({
        url: `/console/api/workspaces/current/tool-provider/api/get`,
        method: 'get',
        params
    })
}

//更新工具
export function updateTool(data) {
    return request({
        url: `/console/api/workspaces/current/tool-provider/api/update`,
        method: 'post',
        data
    })
}

//删除工具
export function deleteTool(data) {
    return request({
        url: `/console/api/workspaces/current/tool-provider/api/delete`,
        method: 'post',
        data
    })
}

//获取系统内置工具的授权字段
export function getBuiltinToolAuthFields(id) {
    return request({
        url: `/console/api/workspaces/current/tool-provider/builtin/${id}/credentials_schema`,
        method: 'get'
    })
}

//获取系统内置工具的授权字段的值
export function getBuiltinToolAuthFieldValues(id) {
    return request({
        url: `/console/api/workspaces/current/tool-provider/builtin/${id}/credentials`,
        method: 'get'
    })
}

//给系统内置工具需要授权的工具进行授权
export function authBuiltinTool(id, data) {
    return request({
        url: `/console/api/workspaces/current/tool-provider/builtin/${id}/update`,
        method: 'post',
        data
    })
}

//给系统内置工具一集授权的工具进行删除授权
export function deleteAuthBuiltinTool(id) {
    return request({
        url: `/console/api/workspaces/current/tool-provider/builtin/${id}/delete`,
        method: 'post'
    })
}

//从url导入openapi schema
export function importOpenApiSchema(params) {
    return request({
        url: '/console/api/workspaces/current/tool-provider/api/remote',
        method: 'get',
        params
    })
}

//获取所有api类型的工具
export function getAllApiTypeTools() {
    return request({
        url: '/console/api/workspaces/current/tools/api',
        method: 'get'
    })
}

//获取所有内置工具
export function getAllBuiltinTools() {
    return request({
        url: '/console/api/workspaces/current/tools/builtin',
        method: 'get'
    })
}

//获取所有api工具的详情列表
export function getAllApiToolsDetailList() {
    return request({
        url: 'console/api/workspaces/current/tools/api/details',
        method: 'get'
    })
}
<template>
    <div style="padding: 32px;">
        <el-tabs v-model="activeName" class="demo-tabs">
            <el-tab-pane label="工具" name="first">
                <toolList></toolList>
            </el-tab-pane>
            <el-tab-pane label="场景" name="second">
                <sceneList></sceneList>
            </el-tab-pane>
        </el-tabs>
    </div>
</template>
<script lang="ts" setup>
import toolList from '@/components/ecommerce/tools/list'

import sceneList from '@/components/ecommerce/scene/list'

import type { TabsPaneContext } from 'element-plus'

const activeName = ref('first')

</script>
<style>
.demo-tabs>.el-tabs__content {
    color: #6b778c;
    font-size: 32px;
    font-weight: 600;
}</style>
  
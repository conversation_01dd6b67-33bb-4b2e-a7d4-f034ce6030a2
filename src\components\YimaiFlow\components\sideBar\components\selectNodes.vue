<template>
    <div class="yimai-flow-select-nodes">
        <el-tabs v-model="activeName" class="custom-tabs-item">
            <template v-for="tabPane in tabPanes">
                <el-tab-pane :name="tabPane.name">
                    <template #label>
                        <span style="width: 100px;text-align: center;">{{ tabPane.label }}</span>
                    </template>
                    <el-collapse v-model="activeCollapse" class="custom-collapse-item">
                        <template v-for="paneCollapseItem in tabPane.paneCollapseItems">
                            <el-collapse-item :title="paneCollapseItem.title" :name="paneCollapseItem.name">
                                <template v-for="item in paneCollapseItem.nodes">
                                    <custom-card :img="baseUrl + item.icon" :draggable="true"
                                        @dragstart="onDragStart($event, paneCollapseItem.name, item)"
                                        :title="item.name" :detail="item.detail" style="margin-bottom: 10px;"
                                        :is-del-tool="true">
                                    </custom-card>
                                </template>
                            </el-collapse-item>
                        </template>
                    </el-collapse>
                </el-tab-pane>
            </template>
        </el-tabs>
    </div>
</template>
<script setup>
import CustomCard from '@/components/CustomCard/index'

const baseUrl = import.meta.env.VITE_APP_BASE_API;

const activeName = ref('node')

const tabPanes = ref([
    { 
        label: '节点', 
        name: 'node',
        paneCollapseItems:[
            {
                title:'系统工具',
                name:'system',
                nodes:[
                    {name:'LLM',icon:'',detail:''},
                    {name:'知识检索',icon:'',detail:''},
                    {name:'直接回复',icon:'',detail:''}
                ]
            },
            {
                title:'问题解答',
                name:'question',
                nodes:[
                    {name:'问题分类器',icon:'',detail:''},
                ]
            },
            {
                title:'逻辑',
                name:'logic',
                nodes:[
                    {name:'条件分支',icon:'',detail:''},
                    {name:'迭代',icon:'',detail:''},
                ]
            },
            {
                title:'转换',
                name:'transform',
                nodes:[
                    {name:'代码执行',icon:'',detail:''},
                    {name:'模板转换',icon:'',detail:''},
                    {name:'变量聚合器',icon:'',detail:''},
                    {name:'参数提取器',icon:'',detail:''},
                ]
            },
            {
                title:'工具',
                name:'tool',
                nodes:[
                    {name:'HTTP请求',icon:'',detail:''},
                ]
            }

        ]
    }
])

const activeCollapse = ref('')

function onDragStart(event, nodeType ,nodeInfo) {
    if (event.dataTransfer) {
        let info = {
            type:nodeType,
            data:nodeInfo
        }
        event.dataTransfer.setData('application/vueflow', JSON.stringify(info))
        event.dataTransfer.effectAllowed = 'move'
    }
}

</script>
<style lang="scss" scoped>
.yimai-flow-select-nodes {
    margin-top: 20px;
  
  .title{
      font-size: 18px;
      font-weight: bold;
      margin-bottom: 20px;
  }
  .field{
      display: flex;
      margin-bottom: 10px;
  }
  .ai-options-content{
      height: 80%;
      display: flex;
      .ai-option{
          border: 1px solid #ccc;
          margin: 1%;
          height: 48%;
          width: 48%;
          display: flex;
          justify-content: center;
          align-items: center;
          flex-direction: column;
          .option-icon{
              display: flex;
              justify-content: center;
              align-items: center;
              height: 50px;
          }
          .option-title{
              height: 26px;
              line-height: 26px;
              font-size: 16px;
              font-weight: bold;
          }
          .option-detail{
              height: 16px;
              line-height: 16px;
              font-size: 12px;
          }
      }
  }
}
</style>
<style scoped>
.custom-collapse-item{
    height: calc( 100vh - 280px);
    /* max-height: 540px; */
    overflow: auto;
}
.custom-collapse-item ::v-deep.el-collapse{
    border-top:none ;
}
.custom-tabs-item ::v-deep.el-tabs__item{
    padding: none;
}
</style>
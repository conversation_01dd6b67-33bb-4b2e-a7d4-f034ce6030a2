<template>
    <el-dialog 
        title="配置标签"
        v-model="isVisible"
        width="400px"
        >
        <div style="height: calc(-120px + 80vh);display: flex;flex-direction: column;">
            <el-tree
                :data="options"
                show-checkbox
                node-key="value"
                default-expand-all
                :expand-on-click-node="false"
                :props="{ children: 'children', label: 'label' }"
                :default-checked-keys="[]"
                ref="treeRef"
                style="flex: 1;overflow-y: auto;"
            />
        </div>
        <div style="display: flex;flex-direction: row;justify-content: flex-end;margin-top: 20px;">
            <el-button @click="isVisible = false">取 消</el-button>
            <el-button type="primary" @click="toSave">保 存</el-button>
        </div>
    </el-dialog>
</template>
<script setup>
import {getClassify, updateApp} from "@/api/application/store"
import {ElMessage} from 'element-plus'

const emits = defineEmits(["submit"])

const isVisible = ref(false)
const treeRef = ref(null)
const options = ref([])

const appInfo = ref({})

const open = () => {
    isVisible.value = true
}

const toSave = () => {
    let checkedKeys = treeRef.value.getCheckedKeys().filter(key => 
        !options.value.find(item => item.value == key)
    )
    updateApp({
        applicationId: appInfo.value.applicationId,
        applicationType: appInfo.value.applicationType,
        classifyIds: checkedKeys.join(",")
    }).then(res => {
        if (res.code == 200) {
            ElMessage.success("保存成功")
            isVisible.value = false
            emits("submit")
        } else {
            ElMessage.error(res.msg)
        }
    })
}

const setCheckedTags = (checkedTags) => {
    isVisible.value = true
    nextTick(() => {
        treeRef.value.setCheckedKeys(checkedTags)
    })
}

const setAppInfo = (app) => {
    appInfo.value = app
    setCheckedTags(appInfo.value.classifyIds.split(","))
}

getClassify().then(res => {
    options.value = res.data.map(item => {
        return {
            value: item.id,
            label: item.classifyName,
            children: item.children.map(child => {
                return {
                    value: child.id,
                    label: child.classifyName
                }
            })
        }
    })
})

defineExpose({
    open,
    setAppInfo
})

</script>
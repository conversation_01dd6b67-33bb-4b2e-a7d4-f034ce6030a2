<template>
    <div class="doc-main">
        <div class="steps">
            <el-steps :active="stepActive" finish-status="success" 
                align-center style="min-width: 100%">
                <el-step :title="'上传'" />
                <el-step :title="'分段设置'" />
                <el-step :title="'字段处理'" />
            </el-steps>
        </div>
        <template v-if="stepActive == 0">
            <div class="doc-upload">
                <DocUpload v-model="docFileUploads" />
            </div>
        </template>
        <template v-if="stepActive == 1">
            <div class="doc-seg-setting">
                <div style="width: 50%;max-height: 500px;overflow-y: auto;padding-right: 8px;">
                    <seg-setting-options :current-seg-setting="segSetting" ref="segSettingOptionsRef"
                        @update:currentSegSetting="updateSegSetting" />
                </div>
                <div style="width: 50%;padding-left: 30px;max-height: 500px;overflow-y: auto;">
                    <seg-setting-desc :current-seg-setting="segSetting" />
                </div>
            </div>
        </template>
        <template v-if="stepActive == 2">
            <embed-progress :embed-files="embedFiles" :embed-files-process="embedFilesProcess"/>
        </template>
        <div class="doc-next">
            <el-button @click="preStep" v-if="stepActive != 0 && stepActive != 2" style="margin-right: 10px;">上一步</el-button>
            <el-button @click="nextStep" v-if="stepActive != 2" type="primary" :disabled="docFileUploads.length == 0">下一步</el-button>
        </div>
    </div>
</template>
<script setup>
import DocUpload from '@/components/DocUpload/index.vue'
import EmbedProgress from './embedProgress'
import segSettingDesc from './segSettingDesc'
import {embedingDoc,getEmbedingStatus} from '@/api/knowledgeBase/upload'
import segSettingOptions from './segSettingOptions.vue';

const docFileUploads = ref([])
const segSetting = ref(0)
const segSettingOptionsRef = ref(null)

const embedFiles = ref([])
const embedFilesProcess = ref([])
const stepActive = ref(0)

const props = defineProps({
    docId:{
        type:String,
        default:''
    },
    knowledgeId:{
        type:String,
        requrie:true
    }
})

const updateSegSetting = (val) => {
    segSetting.value = val
}

const nextStep = () => {
    if(stepActive.value == 1 && !segSettingOptionsRef.value.validateSegSetting()){
        return;
    }
    if(stepActive.value++ == 1){
        const processRule = segSettingOptionsRef.value.getProcessRule()
        const params = {
            data_source: {
                type: "upload_file",
                info_list: {
                    data_source_type: "upload_file",
                    file_info_list: {
                        file_ids: docFileUploads.value.map(item => item.id)
                    }
                }
            },
            indexing_technique: "high_quality",
            process_rule: processRule,
            doc_form: "text_model",
            doc_language: "Chinese",
            retrieval_model: {
                search_method: "semantic_search",
                reranking_enable: false,
                reranking_model: {
                reranking_provider_name: null,
                reranking_model_name: null
                },
                top_k: 3,
                score_threshold_enabled: false,
                score_threshold: 0.5
            }
        }
        embedingDoc(props.knowledgeId,params).then((res) => {
            getEmbedingStatus(props.knowledgeId,res.batch).then((res1) => {
                embedFiles.value = res.documents
                embedFilesProcess.value = res1.data
                const inter = setInterval(()=>{
                    getEmbedingStatus(props.knowledgeId,res.batch).then((res1) => {
                        embedFilesProcess.value = res1.data
                        if(res1.data.filter((item) => item.indexing_status === 'completed' || item.indexing_status === 'error').length == res1.data.length){
                            clearInterval(inter)
                        }
                    })
                },2000)
            })
        })
    }
}

const preStep = () => {
    stepActive.value--
}

</script>
<style scoped lang="scss">
.doc-main{
    margin: 40px;
}
.steps{
    width: 100%;
    margin-bottom: 40px;
    ::v-deep(.el-steps .el-step .is-success)  {
        color: #5050E6;
        border-color: #5050E6;
    }
    ::v-deep(.el-steps .el-step .is-process)  {
        color: #5050E6;
        border-color: #5050E6;
    }
}
.doc-next{
    display: flex;
    align-items: center;
    justify-content: flex-end;
    line-height: 32px;
    margin-bottom: 40px;
    margin-top: 40px;
    padding: 0 120px;
}

.doc-upload{
    width: 100%;
    padding: 0 15%;
}
.doc-seg-setting{
    width: 100%;
    padding: 0 120px;
    display: flex;
    .doc-seg-option-title{
        font-size: 14px;
        height: 40px;
        line-height: 50px;
        font-weight: 600;
    }
    .doc-seg-option-desc{
        font-size: 14px;
        font-weight: 400;
        line-height: 20px;
        margin-bottom: 10px;
        padding-left: 0;
        color: rgba(56, 55, 67,0.6);
    }
    .custom-config{
        margin-bottom: 15px;
        margin-top: 16px;
        padding-top: 16px;
        border-top: 1px solid #E6E6E9;
        .item{
            margin-bottom: 20px;
            display: flex;
            flex-direction: column;
            .label{
                color:#383743;
                display: block;
                font-size: 14px;
                font-weight: 600;
                line-height: 22px;
                margin-bottom: 8px;
            }
            .requrie{
                &:after{
                    color: #FF441E;
                    content: "*";
                    font-weight: 600;
                    margin-left: 4px;
                }
            }
        }
    }
    ::v-deep(.el-radio-group){
        margin-right: 0 !important;
        width: 100%;
    }
    ::v-deep(.el-radio.is-bordered){
        padding: 0 15px 0 19px;
    }
    ::v-deep(.el-radio-group .el-radio){
        margin-right: 0 !important;
        margin: 5px 0;
        width: 100%;
        height: auto;
    }
    ::v-deep(.el-radio-group .el-radio .el-radio__label){
        width: 100%;
    }
    ::v-deep(.el-radio__input){
        position: absolute;
        top: 18px;
        left: 7px
    }
}

</style>
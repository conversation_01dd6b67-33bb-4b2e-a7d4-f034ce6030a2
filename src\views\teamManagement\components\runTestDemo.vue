<template>
    <div class="common-container">
        <div class="single-page-container">
            <div class="single-page-header">
                <div class="left-box">
                    <!-- <el-icon style="margin-right: 10px;"><Back /></el-icon> -->
                    <el-button :icon="Back" @click="closeDrawer" text style="margin-right: 10px;"></el-button>
                    <img :src="baseUrl+props.editModel.teamImgUrl" />
                    <div class="text-box">
                        <div class="title">
                            {{props.editModel.teamName}}
                        </div>
                        <!-- <div class="detail">
                            {{editForm.agentCode}}
                        </div> -->
                    </div>
                </div>
                <div  class="right-box" v-if="props.isEdit">
                    <el-button type="primary" style="color: white;font-size: 14px;font-weight: 500;"
                    @click="publicTeam()">发布</el-button>
                </div>
            </div>
        </div>
        <!--content-->
        <div class="common-content" v-loading="isLoading">
            <!--half top-->
            <div class="team-agents">
                <div class="title">团队成员</div>
                <div class="agents-items">
                    <template v-for="teamAgent in teamAgentList">
                        <div class="agents-item" :class="{'active':false}">
                            <img :src="baseUrl+teamAgent.agentIconUrl" />
                            <div class="agent-name">{{teamAgent.agentName}}</div>
                            <div class="agent-desc">{{teamAgent.goal}}</div>
                        </div>
                    </template>
                </div>
                <!-- <div class="selected-agents">
                    已选择{{teamDetail.selectedAgent?.length}}位成员
                    <template v-for="(item,index) in teamDetail.selectedAgent">
                        <el-tag closable style="margin: 0 5px;" @close="teamDetail.selectedAgent.splice(index,1)">{{teamAgentList.find(teamAgent => teamAgent.agentId == item)?.agentName}}</el-tag>
                    </template>
                </div> -->
            </div>
            <!--half bottom-->
            <div class="team-keys">
                <div class="title">需求</div>
                <div style="margin-top: 20px;height: calc(100vh - 400px);overflow-y:auto;">
                    <el-form :label-width="'100px'" :label-position="'top'">
                        <el-row>
                            <el-col :span="8" v-for="(key) in keyList" :key="key.keyCode" :style="key.keyType=='image'?{minHeight:'190px'}:''">
                                <el-form-item  :label="key.keyName">
                                    <el-input v-model="testPamrams[key.keyCode]" style="width: 80%;" v-if="key.keyType=='text'" type="text"></el-input>
                                    <Image-Upload v-model="testPamrams[key.keyCode]" :limit="1" :isShowTip="false" v-if="key.keyType=='image'"></Image-Upload>
                                    <file-upload-frame :limit="1" v-model="testPamrams[key.keyCode]" v-if="key.keyType=='file'"></file-upload-frame>
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </el-form>
                </div>
            </div>
        </div>
        <div class="common-footer">
            <el-button style="font-size: 14px;font-weight: 500;" size="large" type="primary"
                    @click="runTest()">开始协作</el-button>
            <el-input
            v-model="speechRecognition"
            size="large"
            style="max-width: 800px;margin-left: 10px;" 
            >
                <template #prepend>
                    <el-button :icon="Microphone" :style="{color:microphoneState?'#5050E6':''}" @click="microphoneClick()"/>
                </template>
            </el-input>
            <el-button style="font-size: 14px;font-weight: 500;;margin-left: 10px" size="large" type="primary"
                    @click="sending()">需求分析</el-button>
        </div>
        <el-dialog v-model="dialogVisible" :title="props.editModel.teamName+'协作'" width="70%" v-if="dialogVisible" style="background-color: #F7F7FA;"
        :close-on-click-modal="false" :close-on-press-escape="false" :before-close="handleClose">
            <el-row style="border-top:1px solid #E6E6E6">
                <el-col style="background-color: #ffffff;">
                    <yimai-chat-team 
                    :chat-url="props.editModel.id == 54 ? 'ws://***********:29998' :props.editModel.id == 115 ? 'ws://***********:29992':'ws://***********:29997'" 
                    :is-disabled-send-set="true" :team-agent-selected-list="teamAgentSelectedList"
                    ref="testTeamChat">
                    </yimai-chat-team>
                </el-col> 
            </el-row>
        </el-dialog>
    </div>
</template>
<script setup>
import { nextTick, ref } from 'vue'
import {getAnsislyDate} from '@/api/teamManagement/invoice'
import YimaiChatTeam from '@/components/YimaiChatTeam/index';
import {Back,Edit,Plus,Remove,Promotion,Microphone} from '@element-plus/icons-vue'
import { useRoute } from 'vue-router';
import {publishTeam} from '@/api/teamManagement/configFlow'
import {v4 as uuidv4} from 'uuid'
import {ElMessage} from 'element-plus'
import { getToken } from "@/utils/auth";
import useUserStore from '@/store/modules/user'
import FileUploadFrame from '@/components/FileUploadFrame/index.vue'

const props = defineProps({
    editModel:{
        required:true,
        type:Object
    },
    editDrawer:{
        required:false,
        type:Object
    },
    isEdit:{
        type: Boolean,
        required: false,
        default: true
    }
})

const baseUrl = import.meta.env.VITE_APP_BASE_API;

const isLoading = ref(false)
const keyList = ref([])
const teamAgentList = ref([])
const teamDetail = ref({})
const testPamrams = ref({})
const dialogVisible = ref(false)
const testTeamChat = ref(null)

const microphoneState = ref(false)
const speechRecognition = ref('')

//选中的智能体列表
const teamAgentSelectedList = ref([])
const userStore = useUserStore()
const runTest = () => {
    dialogVisible.value = true
    // console.log(testPamrams.value)
    nextTick(() => {
        handleTeamAgentList(props.editModel.arrangeJson,props.editModel.id == 54)
        let params = null;
        // console.log(teamDetail.value.selectedAgent);
        if(props.editModel.id == 54){
            const selectedAgents = JSON.parse(props.editModel.arrangeJson).nodes.filter(node => node.data.type == 'agent').map(node => node.data.data.agentId + '')
            params = {
                teamId: props.editModel.id,
                agentIds:selectedAgents,
                sessionId:uuidv4(),
                keywords:testPamrams.value,
                userName:userStore.id
            }
        }else{
            params = {
                teamId: props.editModel.id,
                keywords:testPamrams.value,
                arrangeJson:JSON.parse(props.editModel.arrangeJson),
                prompt:props.editModel.prompt,
                sessionId:uuidv4(),
                token:getToken(),
                userName:userStore.id
            }
        }
        testTeamChat.value.initChat(params)
    })
}

const publicTeam = () => {
    const {id,prompt,aiTeamKeys,teamType,arrangeJson} = teamDetail.value
    let params = {
        id,
        prompt,
        teamType,
        aiTeamKeys,
        arrangeJson
    }
    publishTeam(params).then((res) => {
        ElMessage.success(res.msg)
    })
}

const init = () => {
    isLoading.value = true
    // let teamId = props.editModel.teamId
    teamAgentList.value = JSON.parse(props.editModel.arrangeJson).nodes.filter(item => item.data.type == 'agent').map(item => item.data.data)
    keyList.value = props.editModel.aiTeamKeys
    props.editModel.aiTeamKeys.forEach(key => {
        testPamrams.value[key.keyCode] = ''
    })
    nextTick(() => {
        isLoading.value = false
    })
}

const closeDrawer = () => {
    props.editDrawer.close()
}

//获取智能体列表  智能团队编排
const handleTeamAgentList = (arrangeJson,is54) =>{
    if(arrangeJson){
        let arrangeJsonNodes = JSON.parse(arrangeJson).nodes
        //处理智能体
        teamAgentSelectedList.value=[]
        arrangeJsonNodes.forEach(element => {
            if(element.data.type=='agent'){
                let teamAgentSelected=element.data.data
                // teamAgentSelected.agentId=!is54 ? element.id : teamAgentSelected.agentId
                teamAgentSelectedList.value.push(teamAgentSelected)
            }
        });
    }
}

const handleClose = (done) => {
    testTeamChat.value.clearTimeres()
    done()
}
const isMicrophone = ref(true)
//点击麦克风
const microphoneClick = () => {
    if(isMicrophone.value){
        isMicrophone.value = false
        microphoneState.value=!microphoneState.value
        let speechRecognitionTxt = ''
        if(props.editModel.id==54){
            speechRecognitionTxt = '设计一款面向年轻人办公室场景耳机'
        }
        if(props.editModel.id==55){
            speechRecognitionTxt = '张三的海信电视机出现了黑屏的故障'
        }
        let txt = speechRecognitionTxt.split('')
        let tempTxt = ''
        let timeres = []
        let timer = setTimeout(() => {
            txt.forEach((item,index) => {
                let timer = setTimeout(() => {
                    speechRecognition.value=tempTxt += item
                    if(index == txt.length-1){
                        microphoneState.value=false
                        isMicrophone.value = true
                    }
                }, 30 * index );
                timeres.push(timer) 
            })
        }, 3000 );
    }
}

//发送
const sending = () => {
    if(speechRecognition.value!='' && !microphoneState.value){
        // console.log(props.editModel)
        if(props.editModel.id==54){
            testPamrams.value.product='耳机'
            testPamrams.value.user_situation='我们的用户主要是一群年轻，充满活力的人群，他们年龄在18到35岁之闹，有稳定的经济收入，每月收入在5000到10000元，这些用户注重生活品质，追求时尚和品牌，同时也非常注重产品的便携性。'
            testPamrams.value.usage_scenario='在办公室工作时'
            testPamrams.value.product_type='头戴式耳机'
            testPamrams.value.function='提供高品质的音乐体验,具有降噪功能，有效去除环境噪音'
            testPamrams.value.use_flow='用户在办公室工作时，通过连接到手机或电脑，享受高品质音乐或清晰会议通话体验。用户可以通过耳机上的控制按钮调节音量、切换歌曲。'
            testPamrams.value.product='耳机'
            testPamrams.value.proposals_number='2'
            testPamrams.value.supplement='耳机的舒适设计和便携性使用户能够长时间佩戴'
        }
        if(props.editModel.id==55){
            testPamrams.value.homeAppliance='电视机'
            testPamrams.value.customer='张三'
            testPamrams.value.brand='海信'
            testPamrams.value.pastDesc='电视机黑屏'
        }
        if(props.editModel.id==91){
            getAnsislyDate({message:speechRecognition.value}).then(res => {
                testPamrams.value.beforeDate = res.beforeDate
                testPamrams.value.sinceDate = res.sinceDate
            })
        }
        if(props.editModel.id==115){
            testPamrams.value.userQuestion='与A公司签订了哪些协议？协议的许可费条款内容是什么？最近的履约日期是什么时候？'
        }
    }
}

init()
</script>
<style scoped lang="scss">
.common-container {
    display: flex;
    flex-direction: column;
    background-color: #F7F7FA;
    height: 100vh !important; 
    // calc(100vh - 52px);
    width: 100%;
    margin: 0;
    padding: 0;
    background-size: cover;
}

.single-page-container{
    // background-color: white;
    // height: 100vh;
    .single-page-header{
        height: 80px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-direction: row;
        border-bottom: 1px solid #ccc;
        .left-box{
            margin-left:24px;
            display: flex;
            align-items: center;
            .text-box{
                display: flex;
                flex-direction: column;
                margin-left: 10px;
                .title{
            display: flex;
            align-items: center;
                    height: 25px;
                    font-size: 18px;
                    font-weight: 500;
                    color: #032220;
                }
                .detail{
                    height: 17px;
                    font-size: 12px;
                    color: #999999;
                    font-weight: 400;
                    margin-top: 4px;
                }
            }
            img {
                height: 40px;
                width: 40px;
                border-radius: 4px;
                border: 1px solid #E6E6E6;
            }
        }
        .right-box{
            margin-right: 20px;
        }
    }
    .single-page-content{
        display: flex;
        flex-direction: row;
        height: calc(100vh - 80px);
        overflow-y: auto;
        .content-left-box{
            width: calc(60% - 48px);
            height: calc(100% - 60px);
            border-right: 1px solid #ccc;
            background-color: white;
            margin-top: 30px;
            .content-left-box-title{
                padding-left: 24px;
                font-size: 14px;
                font-weight: bold;
                color: #032220;
                margin-bottom: 16px;
            }
            .left-box-half-top{
                border-bottom: 1px solid #ccc;
            }
            .custom-form-item-slider{
                display: flex;
                flex-direction: row;
                width: 100%;
                align-items: center;
                justify-content: space-between;
                margin-bottom: 5px;
                .custom-slider-title{
                    margin-right: 20px;
                    width: 120px;
                    text-align: left;
                    font-size: 12px;
                    color: #999999;
                }
                .custom-slider-value{
                    width: calc(100% - 120px);
                    display: flex;
                    flex-direction: row;
                }
            }
        }
        .content-right-box-title{
            color: #032220;
            font-size: 14px;
            font-weight: 500;
            margin-bottom: 10px;
        }
        
        
    }
}

.common-header {
    height: 60px;
    padding: 20px 30px;
    line-height: 24px;
    display: flex;
    justify-content: space-between;
    border-bottom: 1px solid #E6E6E6;
    margin-bottom: 10px;
    .title {
        color: #032220;
        font-size: 18px;
        font-weight: bold;
    }

    .header-right {
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: row;
    }
}

.common-content {
    margin: 0 20px;
    height: calc(100vh - 150px);
}

.common-footer{
    // max-height: calc(100vh - 900px);
    // display: flex;
    // justify-content: center;
    // align-items: center;
    text-align: center;
}

.team-agents{
    // height: 400px;
    width: 100%;
    display: flex;
    flex-direction: column;
    padding: 10px 10px;
    border-bottom: 1px solid #E6E6E6;
    margin-bottom: 20px;
    .title{
        color: #032220;
        font-size: 18px;
        font-weight: 500;
    }
    .agents-items{
        display: flex;
        flex-direction: row;
        margin-top: 15px;
        margin-bottom: 20px;
        .agents-item{
            width: 160px;
            height: 210px;
            margin-bottom: 20px;
            background-color: #f5f5ff;
            border-radius: 10px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
            padding: 10px 20px;
            margin: 0 10px;
            &:hover{
                cursor: pointer;
            }
            img{
                width: 70px;
                height: 90px;
                // margin-bottom: 10px;
                border-radius: 10px;
                background-color: #dfdfff;
                border: none;
                overflow:hidden; 
            }
            img[src=""],img:not([src]){
                opacity: 0;
                border:none;
                visibility: hidden;
                max-width: none;
            }
            .agent-name{
                height: 50px;
                color: #032220;
                font-size: 16px;
                font-weight: 500;
                line-height: 60px;
                white-space: nowrap;
            }
            .agent-desc{
                height: 80px;
                max-height: 80px;
                overflow: hidden;
                color: #999999;
                font-size: 12px;
                font-weight: 400;
            }
        }
        .active{
            // background-color: #94bff1;
            border: 2px solid #5050E6;
        }
    }
}
.team-keys{
    max-height: calc(100vh - 500px);
    width: 100%;
    display: flex;
    flex-direction: column;
    padding: 0 10px;
    .title{
        color: #032220;
        font-size: 18px;
        font-weight: 500;
    }
}
::v-deep(.el-dialog__body) {
  padding: 0;
}
</style>

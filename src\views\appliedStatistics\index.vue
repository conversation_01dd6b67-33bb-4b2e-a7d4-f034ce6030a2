<template>
    <div class="whole">
        <div class="left">
            <div class="label">总览</div>
            <div class="overview-box">
                <div v-for="(item,index) in overviewList" class="overview">
                    <!--图标-->
                    <div>
                        <img class="icon" :src="item.icon">
                    </div>
                    <!--名称-->
                    <div class="title">
                        <div>{{ item.name }}</div>
                        <el-tooltip :content="item.desc" effect="light" placement="top" popper-class="tooltip-pop" v-if="item.desc">
                            <el-icon style="margin-left:5px;cursor: pointer"><QuestionFilled /></el-icon>
                        </el-tooltip>
                    </div>
                    <!--值-->
                    <div class="value">
                        {{ item.code=='tokens_usage_count'?Math.ceil(overview[item.code]/1000)+'K':overview[item.code] }}
                    </div>
                </div>
            </div>
            <div class="container">
                <div class="date">
                    <el-date-picker
                    v-model="dateRange"
                    value-format="YYYY-MM-DD"
                    type="daterange"
                    range-separator="-"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    :shortcuts="shortcuts"
                    :clearable="false"
                    ></el-date-picker>
                </div>
                <div style="display: flex;justify-content: space-between;flex:1">
                    <div class="line-chart-box-left">
                        <div>
                            <span style="font-size: 12px;font-weight: 600;">活跃用户数</span>
                            <span style="font-size: 12px;color:#9A9A9A">（合计：{{userChartData.total}}）</span>
                        </div>
                        <div ref="userChart" style="height: 100%;"/>
                    </div>
                    <div class="line-chart-box-right">
                        <div>
                            <span style="font-size: 12px;font-weight: 600;">使用数</span>
                            <span style="font-size: 12px;color:#9A9A9A">（合计：{{useChartData.total}}）</span>
                        </div>
                        <div ref="useChart" style="height: 100%;"/>
                    </div>
                </div>
                <div style="display: flex;justify-content: space-between;flex:1">
                    <div class="line-chart-box-left">
                        <div>
                            <span style="font-size: 12px;font-weight: 600;">消息数</span>
                            <span style="font-size: 12px;color:#9A9A9A">（合计：{{messagesChartData.total}}）</span>
                        </div>
                        <div ref="messagesChart" style="height: 100%;"/>
                    </div>
                    <div class="line-chart-box-right">
                        <div>
                            <span style="font-size: 12px;font-weight: 600;">Token消耗</span>
                            <span style="font-size: 12px;color:#9A9A9A">（合计：{{Math.ceil(TokenConsumeChartData.total/1000)}}K）</span>
                        </div>
                        <div ref="TokenConsumeChart" style="height: 100%;"/>
                    </div>
                </div>
            </div>
        </div>
        <div class="right">
            <div class="label"> 当前Token消耗Top 10(合计: {{ Math.ceil(tokenConsumeTop.total/1000) }}K)</div>
            <div class="token-consume-box">
                <div style="flex: 1;">
                    <div ref="pie" style="height: 100%"></div>
                </div>
                <div class="token-consume-list">
                    <div v-for="(item,index) in tokenConsumeTop.list" class="token-consume">
                        <div class="token-consume-left">
                            <div class="index" :style="{'color':pieColor[index],
                            'background-color':`rgba(${parseInt(pieColor[index].slice(1, 3), 16)}, ${parseInt(pieColor[index].slice(3, 5), 16)}, ${parseInt(pieColor[index].slice(5, 7), 16)}, 0.3)`}">
                                {{ index<9?'0'+(index+1):(index+1) }}
                            </div>
                            <div>
                                <div style="font-size: 14px;">{{ item.name }}</div>
                                <div style="font-size: 12px;color:#6C6C6C">消息数：{{ item.messageNum }}</div>
                            </div>
                        </div>
                        <div class="token-consume-right">
                            {{ item.tokens_usage_count_str }} 
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
<script setup>
    import { nextTick, ref,onMounted } from 'vue'
    import { dayjs } from 'element-plus';
    import {Search,Refresh,QuestionFilled} from '@element-plus/icons-vue'
    import appliedApplication from '@/assets/icons/svg/applied-application.svg'
    import appliedUser from '@/assets/icons/svg/applied-user.svg'
    import appliedAppVisits from '@/assets/icons/svg/applied-app-visits.svg'
    import appliedMessages from '@/assets/icons/svg/applied-messages.svg'
    import appliedToken from '@/assets/icons/svg/applied-token.svg'
    import * as echarts from 'echarts';
    import 'echarts-wordcloud';
    import { getUserChartData,getUseChartData,getMessagesChartData,getTokenConsumeChartData,
        getOverviewData,getTokensRankingData
     } from "@/api/appliedStatistics/statistics"

    //总览数据
    const overviewList = ref([
        {code:'application_count',name:'应用总数',icon:appliedApplication},
        {code:'active_users_count',name:'总活跃用户数',icon:appliedUser,desc:'使用过应用的用户数。'},
        {code:'conversations_count',name:'应用总使用数',icon:appliedAppVisits,desc:'用户开启一次新的会话算一次使用数，调式的消息不计入。'},
        {code:'chat_messages_count',name:'总消息数',icon:appliedMessages,desc:'每回答用户一个问题算一条Mssage，调式的消息不计入。'},
        {code:'tokens_usage_count',name:'Token消耗总数',icon:appliedToken,desc:'应用请求大模型的Token消耗数，调式的消息不计入。'}
    ])
    //总览数据值
    const overview = ref({
        application_count:0,
        active_users_count:0,
        conversations_count:0,
        chat_messages_count:0,
        tokens_usage_count:0
    })

    //获取用户总览数据
    const getOverviewDataMethod = () => {
        getOverviewData().then(response => {
            overview.value = response
        })
    }

    //日期选择器处理
    const shortcuts = [
        {
            text: '近1周',
            value: () => {
                const fourWeeksAgo = dayjs().subtract(6, 'day').startOf('day');
                const today = dayjs().endOf('day');
                return [fourWeeksAgo.format('YYYY-MM-DD'), today.format('YYYY-MM-DD')]
            },
        },
        {
            text: '近一月',
            value: () => {
                const threeMonthsAgo = dayjs().subtract(1, 'month');
                const date_end = dayjs().endOf('day'); 
                return [threeMonthsAgo.format('YYYY-MM-DD'), date_end.format('YYYY-MM-DD')]
            },
        },
        {
            text: '近半年',
            value: () => {
                const twelveMonthsAgo = dayjs().subtract(6, 'month');
                const date_end = dayjs().endOf('day'); 
                return [twelveMonthsAgo.format('YYYY-MM-DD'), date_end.format('YYYY-MM-DD')]
            },
        },
        {
            text: '近一年',
            value: () => {
                const twelveMonthsAgo = dayjs().subtract(12, 'month');
                const date_end = dayjs().endOf('day'); 
                return [twelveMonthsAgo.format('YYYY-MM-DD'), date_end.format('YYYY-MM-DD')]
            },
        },
    ];
    //日期
    const dateRange = ref([dayjs().subtract(6, 'day').startOf('day'),dayjs().endOf('day')])
    const fourWeeksAgo = dayjs().subtract(6, 'day').startOf('day');
    const today = dayjs().endOf('day');
    const queryParams = ref({
        date_start:fourWeeksAgo.format('YYYY-MM-DD')+' 00:00:00',
        date_end:today.format('YYYY-MM-DD')+' 23:59:59'
    })

    //活跃用户数图表
    const userChart = ref(null)
    //用户数图表数据
    const userChartData = ref({})
    //获取用户数图表数据
    const getUserChartDataMethod = () => {
        getUserChartData(queryParams.value).then(response => {
            userChartData.value = {
                total:response.total,
                data:response.data.y_axis,
                xData:response.data.x_axis
            }
            getUserChart()
        })
    }
    const getUserChart = () => {
        const userChartInstance = echarts.init(userChart.value);
        userChartInstance.setOption({
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                type: 'cross'
                }
            },
            xAxis: {
                type: 'category',
                data: userChartData.value.xData
            },
            yAxis: {
                type: 'value'
            },
            grid: {
                top: '15%',    // 上方的距离
                bottom: '15%', // 下方的距离
            },
            series: [{
                data: userChartData.value.data,
                type: 'line'
            }]
        })

        window.addEventListener("resize", () => {
            userChartInstance.resize();
        });
    }

    //应用总使用数图表
    const useChart = ref(null)
    //应用总使用数图表数据
    const useChartData = ref({})
    //获取应用总使用数图表数据
    const getUseChartDataMethod = () => {
        getUseChartData(queryParams.value).then(response => {
            useChartData.value = {
                total:response.total,
                data:response.data.y_axis,
                xData:response.data.x_axis
            }
            getUseChart()
        })
    }
    const getUseChart = () => {
        const useChartInstance = echarts.init(useChart.value);
        useChartInstance.setOption({
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                type: 'cross'
                }
            },
            xAxis: {
                type: 'category',
                data: useChartData.value.xData
            },
            yAxis: {
                type: 'value'
            },
            grid: {
                top: '15%',    // 上方的距离
                bottom: '15%', // 下方的距离
            },
            series: [{
                data: useChartData.value.data,
                type: 'line'
            }]
        })

        window.addEventListener("resize", () => {
            useChartInstance.resize();
        });
    }

    //总消息数图表
    const messagesChart = ref(null)
    //总消息数图表数据
    const messagesChartData = ref({})
    //获取总消息数图表数据
    const getMessagesChartDataMethod = () => {
        getMessagesChartData(queryParams.value).then(response => {
            messagesChartData.value = {
                total:response.total,
                data:response.data.y_axis,
                xData:response.data.x_axis
            }
            getMessagesChart()
        })
    }
    const getMessagesChart = () => {
        const messagesChartInstance = echarts.init(messagesChart.value);
        messagesChartInstance.setOption({
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                type: 'cross'
                }
            },
            xAxis: {
                type: 'category',
                data: messagesChartData.value.xData
            },
            yAxis: {
                type: 'value'
            },
            grid: {
                top: '15%',    // 上方的距离
                bottom: '15%', // 下方的距离
            },
            series: [{
                data: messagesChartData.value.data,
                type: 'line'
            }]
        })

        window.addEventListener("resize", () => {
            messagesChartInstance.resize();
        });
    }

    //Token消耗总数图表
    const TokenConsumeChart = ref(null)
    //Token消耗总数图表数据
    const TokenConsumeChartData = ref({})
    //获取Token消耗总数图表数据
    const getTokenConsumeChartDataMethod = () => {
        getTokenConsumeChartData(queryParams.value).then(response => {
            TokenConsumeChartData.value = {
                total:response.total,
                data:response.data.y_axis,
                xData:response.data.x_axis
            }
            getTokenConsumeChart()
        })
    }
    const getTokenConsumeChart = () => {
        const TokenConsumeChartInstance = echarts.init(TokenConsumeChart.value);
        TokenConsumeChartInstance.setOption({
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                type: 'cross'
                }
            },
            xAxis: {
                type: 'category',
                data: TokenConsumeChartData.value.xData
            },
            yAxis: {
                type: 'value'
            },
            grid: {
                top: '15%',    // 上方的距离
                bottom: '15%', // 下方的距离
            },
            series: [{
                data: TokenConsumeChartData.value.data,
                type: 'line'
            }]
        })

        window.addEventListener("resize", () => {
            TokenConsumeChartInstance.resize();
        });
    }



    const getChartData = () => {
        getUserChartDataMethod()
        getUseChartDataMethod()
        getMessagesChartDataMethod()
        getTokenConsumeChartDataMethod()
    }

    

    //Token消耗Top 10 数据值
    const tokenConsumeTop = ref({
        total:908,
        list:[]
    })
    //
    const getTokensRankingDataMethod = () => {
        getTokensRankingData().then(response => {
            let tokenConsumeTopList = []
            let totalL = 0
            response.forEach(element => {
                let tokenConsumeTop = {
                    value:element.tokens_usage_count,
                    name:element.app_name,
                    tokens_usage_count_str: element.tokens_usage_count_str,
                    messageNum:element.chat_messages_count
                }
                tokenConsumeTopList.push(tokenConsumeTop)
                totalL+=element.tokens_usage_count
            });
            tokenConsumeTop.value.total = totalL
            tokenConsumeTop.value.list = tokenConsumeTopList
            getPie()
        })
    }
    //饼图颜色
    const pieColor = [
        '#5470c6','#91cc75','#fac858','#ee6666','#73c0de','#3ba272','#fc8452','#9a60b4','#ea7ccc','#93f4ff'
    ]
    //饼图
    const pie = ref(null)
    const getPie = () => {
        const pieInstance = echarts.init(pie.value);
        pieInstance.setOption({
            tooltip: {
            trigger: 'item'
        },
        // legend: {
        //     top: '5%',
        //     left: 'center'
        // },
        color:pieColor,
        series: [{
                name: 'Token消耗',
                type: 'pie',
                radius: ['40%', '70%'],
                avoidLabelOverlap: false,
                label: {
                    show: false,
                    position: 'center'
                },
                emphasis: {
                    label: {
                        show: true,
                        fontSize: 18,
                        fontWeight: 'bold'
                    }
                },
                labelLine: {
                    show: false
                },
                data: tokenConsumeTop.value.list
            }]
        })

        window.addEventListener("resize", () => {
            pieInstance.resize();
        });
    }

    watch(() => dateRange.value, val => {
        if(dateRange.value && dateRange.value.length>0){
            queryParams.value.date_start = dateRange.value[0] + ' 00:00:00'
            queryParams.value.date_end = dateRange.value[1] + ' 23:59:59'
            getChartData()
        }else{
            queryParams.value.date_start = null
            queryParams.value.date_end = null
            getChartData()
        }
    });

    onMounted(() => {
        getOverviewDataMethod()
        getChartData()
        getTokensRankingDataMethod()
    });
</script>
<style scoped lang="scss">
    .whole{
        height: calc( 100vh - 50px );
        display: flex;
        justify-content: space-between;
        background-color: #F7F7FA;
        .label{
            font-size: 14px;
        }
        .left{
            height: 100%;
            width: 75%;
            margin: 0 10px;
            .overview-box{
                background-color: white;
                border-radius: 5px;
                padding: 10px 50px;
                margin: 10px 0 5px 0;
                display: flex;
                justify-content: space-between;
                align-items: center;
                .overview{
                    display: flex;         
                    flex-direction: column; 
                    align-items: center;
                    .icon{
                        width:40px;
                        height: 40px;
                    }
                    .title{
                        font-size: 14px;
                        color:#6C6C6C;
                        margin-top: 5px;
                        margin-bottom: 5px;
                        display: flex;
                        align-items: center;
                    }
                    .value{
                        font-size: 16px;
                        font-weight: 600;
                        color:black
                    }
                }
            }
            .container{
                display: flex;
                flex-direction: column;
                height: calc( 100vh - 235px );
                .date{
                    text-align: right;
                }
                .line-chart-box-left{
                    flex: 1;
                    background-color: white;
                    border-radius: 5px;
                    padding: 10px;
                    margin: 5px 5px 5px 0;
                    position: relative;
                }
                .line-chart-box-right{
                    flex: 1;
                    background-color: white;
                    border-radius: 5px;
                    padding: 10px;
                    margin: 5px 0 5px 5px;
                    position: relative;
                }
            }
        }
        .right{
            height: 100%;
            width: 25%;
            .token-consume-box{
                background-color: white;
                border-radius: 5px;
                padding: 10px;
                margin-top: 10px;
                margin-right: 10px;
                position: relative;
                height: calc(100vh - 88px);
                display: flex;         
                flex-direction: column; 
                .token-consume-list{
                    flex: 1.5;
                    height: 100%;
                    overflow-y: auto;
                    .token-consume{
                        padding: 5px;
                        display: flex;         
                        flex-direction: row;
                        justify-content: space-between;
                        margin-bottom: 5px;
                        .token-consume-left{
                            display: flex;         
                            flex-direction: row;
                            justify-content: center;
                            align-items: center;
                            .index{
                                padding: 10px;
                                border-radius: 50%;
                                width: 40px;
                                height: 40px;
                                display: flex;
                                justify-content: center;
                                align-items: center;
                                margin-right: 10px;
                            }
                        }
                        .token-consume-right{
                            display: flex;         
                            flex-direction: row;
                            justify-content: center;
                            align-items: center;
                            font-size: 14px;
                            margin-right:5px;
                        }
                    }
                }
            }
        }
    }
</style>
<style>
    .el-tabs__content {
        height: 100%;
    }
</style>
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>AiPPT</title>
    <script src="https://api-static.aippt.cn/aippt-iframe-sdk.js"></script>
    <style>
      html,
      body {
        margin: 0;
        padding: 0;
        width: 100%;
        height: 100%;
      }
      html {
        overflow: hidden;
      }
      #container {
        width: 100%;
        height: 100%;
      }
    </style>
  </head>
  <body>
    <div id="container">
      <div id="formDisplay"></div>
    </div>
    <script>
      let receivedCode = null

      window.addEventListener("message", (event) => {
        console.log("event", event)

        if (event.data.action === "updateData") {
          let obj = JSON.parse(event.data.data)
          receivedCode = JSON.parse(obj.code)
          let content = JSON.parse(obj.paragraphs)
          console.log("接收到的 数据", receivedCode)
          console.log("content", content)

          if (receivedCode && receivedCode.apiKey && receivedCode.code) {
            initAipptIframe(receivedCode, content)
          }
        }
      })

      const initAipptIframe = async (code, content) => {
        const hasContent = content && content.length > 0

        let options = {
          fc_plate: [2003, 2011, 2014, 2024]
        }

        if (hasContent) {
          const concatenatedContent = content
            .map((item) => `${item.title}\n${item.content}`)
            .join("\n")
          options = {
            custom_generate: {
              type: 11,
              content: concatenatedContent,
            },
            fc_plate: [2003, 2011, 2014, 2024]
          }
        }

        try {
          await AipptIframe.show({
            appkey: code.apiKey,
            channel: null,
            code: code.code,
            container: document.getElementById("container"),
            editorModel: true,
            routerOptions: {
              editor: {
                showLogo: 3
              }
            },
            options,
            onMessage(eventType, data) {
              console.log(eventType, data)
            },
          })
        } catch (e) {
          console.log("错误", e)
        }
      }
    </script>
  </body>
</html>

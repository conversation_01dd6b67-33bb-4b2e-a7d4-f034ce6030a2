<template>
    <div style="padding: 16px 16px 0 16px;">
        <div class="header">
            <div style="display: flex;align-items: center">
                <div class="mock-icon"></div>
                <NoBorderInput v-model="_title" :placeholder="'添加标题...'" />
            </div>
            <div>
                <el-dropdown @command="handleCommand" size="small" trigger="click">
                    <span class="el-dropdown-link">
                        <el-icon>
                            <MoreFilled />
                        </el-icon>
                    </span>
                    <template #dropdown>
                        <el-dropdown-menu>
                            <el-dropdown-item :command="beforeHandleCommand(nodeId, 'remove')">删除节点</el-dropdown-item>
                        </el-dropdown-menu>
                    </template>
                </el-dropdown>
            </div>
        </div>
        <no-border-textarea v-model="_desc" :placeholder="'添加描述...'" />
    </div>
</template>

<script setup>
import { useVueFlow } from '@vue-flow/core'
import NoBorderInput from './noBorderInput'
import NoBorderTextarea from './noBorderTextarea'
const { removeNodes } = useVueFlow()

const props = defineProps({
    title:{
        type:String,
        default:''
    },
    desc:{
        type:String,
        default:''
    },
    nodeId:{
        type:String,
        default:''
    }
})

const _title = ref('')
const _desc = ref('')

// watch(_title, (newVal) => {
//     emit('update:title', newVal)
// })

// watch(_desc, (newVal) => {
//     emit('update:desc', newVal)
// })

const emit = defineEmits(['update:title', 'update:desc'])

function handleCommand(command){
    if(command.command == 'remove'){
        removeNodes([command.data])
    }
}

const beforeHandleCommand = (data, command) => {
    return {
        command,
        data
    }
}

_title.value = props.title
_desc.value = props.desc

</script>

<style lang="scss" scoped>
.header{
    display: flex;
    align-items: center;
    justify-content: space-between;
    .mock-icon{
        height: 24px;
        width: 24px;
        background-color: #5050E6;
        border-radius: 5px;
    }
}
</style>
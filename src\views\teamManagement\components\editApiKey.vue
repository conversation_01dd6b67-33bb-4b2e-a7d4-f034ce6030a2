<template>
    <div>
        <el-form :model="formData" ref="formRef" :rules="rules" label-position="top">
            <el-form-item label="名称" prop="keyName">
                <el-input v-model="formData.keyName" :maxlength="20"></el-input>
            </el-form-item>
            <el-form-item label="过期日期" prop="expirationTime">
                <el-date-picker
                    v-model="formData.expirationTime"
                    type="date"
                    placeholder="-"
                    :editable="false"
                    style="width: 100%;"
                    :disabled-date="(date) => date.getTime() <= Date.now() "
                />
            </el-form-item>
        </el-form>
    </div>
</template>
<script setup>

const formData = ref({
    keyName: '',
    expirationTime: ''
})

const formRef = ref(null)

// 校验规则
const rules = {
    keyName: [
        { type: 'string', required: true, message: '请输入名称', trigger: 'blur' }
    ]
};

const setFormData = (val) => {
    const {keyName,expirationTime} = JSON.parse(JSON.stringify(val))
    formData.value.keyName = keyName
    formData.value.expirationTime = expirationTime
    formRef.value.clearValidate()
}

defineExpose({
    formData,
    setFormData
})

</script>
<style scoped lang="scss">

</style>
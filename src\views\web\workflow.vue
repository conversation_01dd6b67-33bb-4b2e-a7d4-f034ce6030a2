<template>
    <div v-if="currentInstruct">
        <!--任务型团队-->
        <task-oriented-team ref="messageCtrl" :current-instruct="currentInstruct" :is-public-visit="true"></task-oriented-team>
    </div>
</template>
<script setup>
    import {ref,onMounted, onUnmounted, nextTick} from 'vue'
    import { useRoute } from 'vue-router';
    import TaskOrientedTeam from '@/components/YiMaiChatIntegration/TaskOrientedTeam.vue'
    import { getCurrentInstructTeam } from '@/api/YiMaiChat/sessionRecordsDify'

    const route = useRoute();

    const teamUrl = import.meta.env.VITE_APP_TEAM_CHAT
    
    // 获取路径参数
    const token = route.params.token; 
    console.log(token)//cbc96127-aa79-4ed4-b76e-92eca7306e66

    //会话组件
    const messageCtrl = ref(null)

    const currentInstruct = ref(null)

    //获取团队详情
    async function getCurrentInstructTeamMethod(){
        await getCurrentInstructTeam(token).then(response => {
            let currentInstructStaging = {}
            // currentInstructStaging = {...instruct,...response}
            currentInstructStaging.appId = token
            currentInstructStaging.chatUrl = teamUrl+'/console/api/apps/'+token+'/workflows/run'
            currentInstructStaging.isTaskOrientedTeam = true
            currentInstruct.value = currentInstructStaging
        })
    }

    getCurrentInstructTeamMethod()
    
</script>
<template>
  <div :class="['container', isBatchDelete ? 'batch-delete': 'not-batch-delete']">
    <div class="top">
      <el-divider content-position="left" class="divider">
        历史记录
      </el-divider>
      <div class="search-and-clear" v-if="conversationList.length > 0">
        <el-input
          v-model="keyword"
          :suffix-icon="keyword.length > 0 ? null : Search"
          :clearable="keyword.length > 0"
          class="input-keyword"
          placeholder="搜索历史记录"
          @keyup.enter="getList"
        />
        <el-button
          type="info"
          :icon="Delete"
          circle
          class="btn-clear"
          @click="enterBatchDeleteMode"
        />
      </div>
      <div class="empty" v-if="conversationList.length === 0">
        无搜索结果
      </div>
      <div class="toolbar-del" v-if="conversationList.length > 0 && isBatchDelete">
        <el-button @click="toggleCheckAll" link>
          <input type="checkbox" :checked="isAllChecked" style="cursor: pointer;"/> 
          <span style="margin-left: 2px">全选</span>
        </el-button>
        <el-button @click="del(undefined)" :icon="Delete" link>删除</el-button>
        <el-button @click="quitBatchDeleteMode" :icon="Close" link>退出</el-button>
      </div>
    </div>

    <div class="conversation-list-wrapper">
      <el-scrollbar>
        <div class="conversation-list">
            <div
              v-for="(conversation, index) of conversationList"
              :key="conversation.type + conversation.id"
              :class="[
                'conversation',
                { active: conversation === curConversation },
              ]"
              @click="select(conversation)"
            >
              <input
                type="checkbox"
                v-model="conversation.checked"
                @click.stop="conversation.checked=!conversation.checked"
                class="checkbox"
              />
              <span class="conversation-title">
                {{ conversation.name }}
              </span>
              <span class="operation">
                <Delete class="btn" @click.stop="del(conversation)" />
                <Edit class="btn" @click.stop="rename(conversation)" />
              </span>
            </div>
        </div>
      </el-scrollbar>
    </div>
  </div>
</template>

<script setup>
import { Edit, Delete, Search, Close } from "@element-plus/icons-vue";
import { onMounted, watch, computed } from "vue";
import { ElMessageBox, ElMessage } from "element-plus";
import { getConversationList,delConversation,renameConversation } from "@/api/YiMaiChat/sessionRecords"

// 搜索的关键词
const keyword = ref("");

// 会话列表
const conversationList = ref([]);

// 当前会话ID
const curConversation = ref(null);

// 是否正在批量删除
const isBatchDelete = ref(false);

// 是否全选
const isAllChecked = ref(false)

// 事件定义
const emit = defineEmits(['select', 'after-delete','newChat']);

// 全选状态计算
function calcIsAllChecked() {
  isAllChecked.value = !conversationList.value.find((item) => !item.checked);
}

/**
 * 获取历史会话记录, 并选中第一个
 */
async function getList() {
  const rs = await getConversationList(25,keyword.value);
  conversationList.value = rs.map((item) => {
    return {
      ...item,
      checked: false
    }
  });
}

/**
 * 重命名会话
 */
function rename(conversation) {
  ElMessageBox.prompt("请输入新的会话名称:", "重命名会话", {
    inputValue: conversation.name,
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    inputPattern: /^.{1,15}$/,
    inputErrorMessage: "会话名称不能为空，且不能超过10个字符"
  })
    .then(({ value }) => {
      let data = {
        conversation_id: conversation.id,
        app_id: conversation.app_id,
        name: value,
        type:conversation.type
      }
      renameConversation(data).then((res) => {
        conversation.name = value;
        ElMessage({
          type: "success",
          message: "操作成功!",
        });
      });
    })
    .catch(() => {});
}

/**
 * 删除会话
 */
function del(conversation) {
  if(!conversation && !conversationList.value.find(item=>item.checked)){
    ElMessage({
      type: "warning",
      message: "请先选择会话！",
    });
    return
  }
  var text = ''
  if(conversation){
    text = "请问是否确定删除["+conversation.name+"]会话?"
  }else{
    text = "请问是否确定删除选中的会话?"
  }
  ElMessageBox.confirm(text, "删除会话", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    let data = []
    if(conversation){
      data.push({
        conversation_id: conversation.id,
        app_id: conversation.app_id,
        type:conversation.type
      })
    }else{
      conversationList.value.forEach(element => {
        if(element.checked){
          data.push({
            conversation_id: element.id,
            app_id: element.app_id,
            type:element.type
          })
        }
      });
    }
    delConversation(data).then((res) => {
      ElMessage({
        type: "success",
        message: "操作成功!",
      });
      if(conversation == curConversation.value){
        newChat()
      }
      refresh()
    });
  })
  .catch((e) => {console.log(e)});
}


/**
 * 选中会话
 */
function select(conversation, fireEvent = true) {
  curConversation.value = conversation;
  /*
  const prom = getConversationDetail({
    sessionId: curConversation.value.sessionId,
    applicationType: curConversation.value.applicationType
  });
  */
  if (fireEvent) {
    emit('select', curConversation.value);
  }
}

function newChat(){
  emit('newChat');
}

/**
 * 选中第一个会话
 */
function selectFirst(fireEvent = true) {
  if (conversationList.value.length > 0) {
    select(conversationList.value[0], fireEvent);
  }
}

/**
 * 取消选中
 */
function unselect() {
  curConversation.value = null;
}

/**
 * 创建并选中会话
 */
function createAndSelect() {
  if (
    conversationList.value.length > 0 &&
    conversationList.value[0].sessionId === "new"
  ) {
    return;
  }

  const newConversation = {
    applicationId: curConversation.applicationId,
    applicationType: curConversation.applicationType,
    sessionId: "new",
    title: "新的对话",
    createdTime: new Date().toISOString(),
  };

  conversationList.value = [newConversation, ...conversationList.value];
}

/**
 * 进入批量删除模式
 */
function enterBatchDeleteMode() {
  conversationList.value.forEach((item) => {
    item.checked = false;
  });
  isBatchDelete.value = true;
  calcIsAllChecked()
}

/**
 * 退出批量删除模式
 */
function quitBatchDeleteMode() {
  isBatchDelete.value = false;
}

/**
 * 复选框 全选/全不选
 */
function toggleCheckAll(e) {
  // 全选
  if (!isAllChecked.value) {
    conversationList.value.forEach((item) => {
      item.checked = true;
    });

  // 全不选
  } else {
    conversationList.value.forEach((item) => {
      item.checked = false;
    });
  }
  calcIsAllChecked();
}

/**
 * 刷新
 */
async function refresh(isSelectFirst = false) {
  await getList()
  if(isSelectFirst){
    select(conversationList.value[0], false);
  }
}

onMounted(() => {
  getList();
});

defineExpose({
  refresh,
  selectFirst,
  unselect
});
</script>

<style scoped lang="scss">

.container.not-batch-delete {
  .conversation {
    input {
      display: none;
    }

    &:hover {
      background-color: white;

      .operation {
        display: flex !important;
      }
    }
  }
}

.container.batch-delete {
  .conversation {
    justify-content: flex-start !important;
  }
}

.container {
  width: 100%;
  height: 100vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: stretch;
  border-right: 1px solid lightgray;

  .top {
    width: 100%;
    height: auto;
    flex-grow: 0;
    flex-shrink: 0;
    .search-and-clear {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
      padding: 0 10px;

      .input-keyword {
        width: calc(100% - 40px);

        :deep(.el-input__wrapper) {
          background-color: #e3e0f0;
          border-radius: 20px;
        }
      }

      .btn-clear {
        background-color: #e3e0f0;
        border-color: #e3e0f0;
        color: #9494b0;
      }
    }

    .divider {
      width: calc(100% - 20px);
      position: relative;
      left: 10px;

      :deep(.el-divider__text) {
        background-color: #F7F7FA;
        left: 0;
        padding-left: 5px;
        color: #8587aa;
      }
    }

    .empty {
      width: 100%;
      height: 50px;
      display: flex;
      flex-direction: row;
      justify-content: center;
      align-items: center;
      flex-grow: 0;
      flex-shrink: 0;
      color: #9c9cb8;
      font-size: 14px;
    }

    .toolbar-del {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
      padding: 0 18px;
      height: 40px;
      background-color: #E3E0F0;
      position: relative;
      margin-top: 5px;

      svg {
        width: 14px;
        height: 14px;
        margin-right: 3px;
      }

      .check-all {
        display: flex;
        flex-direction: row;
        justify-content: flex-start;
        cursor: pointer;

        input {
          margin-right: 5px;
        }
      }

      .del, .quit {
        cursor: pointer;
      }
    }
  }

  .conversation-list-wrapper {
    width: 100%;
    overflow: hidden;
    flex-grow: 1;
    flex-shrink: 1;

    .conversation-list {
      width: 100%;
      height: 100%;
      overflow: visible;
      display: flex;
      flex-direction: column;
      justify-content: flex-start;
      align-items: center;
      padding-top: 5px;

      .conversation.active {
        background-color: #dedeea;
      }
      .conversation {
        width: calc(100% - 20px);
        height: 40px;
        border-radius: 20px;
        position: relative;
        padding: 0 10px;
        font-size: 14px;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        cursor: pointer;

        input {
          margin-right: 5px;
        }

        .conversation-title {
          display: block;
          text-overflow: ellipsis;
          text-wrap: nowrap;
          white-space: nowrap;
          overflow: hidden;
        }

        .operation {
          flex-direction: row;
          justify-content: flex-end;
          align-items: center;
          padding: 0 5px;
          display: none;

          .btn {
            width: 14px;
            height: 14px;
            color: gray;
            margin-left: 5px;

            &:hover {
              color: black;
            }
          }
        }
      }
    }
  }

  .suspended-button {
    width: 10%;
    display: flex;
    align-items: center;
    padding: 0 5px;
    flex-grow: 0;
    flex-shrink: 0;

    .box {
      display: flex;
      align-items: center;
      height: 30px;
      background-color: #eeecf6;
      border-radius: 5px;
      ::v-deep .el-button.is-link {
        padding: 0;
        color: #909090;
      }
    }
  }
}

</style>

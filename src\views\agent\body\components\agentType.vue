<template>
    <el-radio-group v-model="_modelValue" class="team-type">
        <template v-for="option in options">
            <el-radio :label="option.value" border :disabled="disabled && option.value != _modelValue">
                <img height="30" width="30" :src="option.value == 1?chatBotImg:agent"/>
                <div style="margin-left: 10px;">
                    {{option.label}}
                </div>
            </el-radio>
        </template>
    </el-radio-group>
</template>
<script setup>
import { onMounted } from 'vue';
import chatBotImg from '@/assets/images/chat_bot.png'
import agent from "@/assets/icons/svg/agent.svg"

const props = defineProps({
    modelValue:{
        type:Number
    },
    disabled:{ 
        type:Boolean,
        default:false
    }
})

const options = [
    {
        label:'聊天助手',
        value:1
    },
    {
        label:'Agent',
        value:2
    }
]

const emit = defineEmits(['update:modelValue']);

const _modelValue = ref('');

watch(() => props.modelValue, newValue => {
    _modelValue.value = newValue?newValue:1;
    emit('update:modelValue', _modelValue.value);
},{ deep: true, immediate: true });

watch(_modelValue, (newValue) => {
    emit('update:modelValue', newValue);
});

// onMounted(() => {
   
//     emit('update:modelValue', _modelValue.value);
// })

</script>
<style lang="scss" scoped>

.team-type{
    width: 100%;
    ::v-deep(.el-radio){
        height: 60px;
        width: calc(50% - 16px);
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
    }
    ::v-deep(.el-radio__input){
        display: none;
    }
    ::v-deep(.el-radio__label){
        display: flex;
        flex-direction: row;
        justify-content: start;
        align-items: center;
        color: rgb(96, 98, 102) !important;
        width: 100px;
    }
    .item{

    }
}

</style>
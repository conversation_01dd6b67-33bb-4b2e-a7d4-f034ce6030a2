<template>
    <div>
        <el-row>
            <el-col :span="14" style="border: 1px solid #f0f0f0;padding: 20px;height: calc(100vh - 210px);">
                <div style="font-size: 18px;font-weight: 800;margin-bottom: 10px;">
                    编辑
                </div>
                <el-form label-width="80px" style="height:calc(100vh - 320px);" label-position="top">
                    <el-form-item label="提示词" prop="prompt">
                        <el-input type="textarea" rows="3" v-model="toolInfo.prompt"></el-input>
                    </el-form-item>
                    <el-form-item label="参数" prop="ecommerceToolKeys">
                        <div style="overflow-y: scroll;height: calc(100vh - 500px);width: 100%;">
                            <el-table :data="toolInfo.ecommerceToolKeys" style="width: 100%">
                                <el-table-column label="字段名" prop="keyName" width="120">
                                    <template #default="{ row }">
                                        <el-input v-model="row.keyName"></el-input>
                                    </template>
                                </el-table-column>
                                <el-table-column label="Key" prop="keyCode" width="120">
                                    <template #default="{ row }">
                                        <el-input v-model="row.keyCode"></el-input>
                                    </template>
                                </el-table-column>
                                <el-table-column label="字段描述" prop="keyDesc">
                                    <template #default="{ row }">
                                        <el-input v-model="row.keyDesc"></el-input>
                                    </template>
                                </el-table-column>
                                <el-table-column label="是否必要" prop="isCare" width="90">
                                    <template #default="{ row }">
                                        <el-switch v-model="row.isCare" active-color="#13ce66"
                                            inactive-color="#ff4949"></el-switch>
                                    </template>
                                </el-table-column>
                                <el-table-column width="60">
                                    <template #header="{ row }">
                                        <span @click="addRow()" style="font-size: 16px;cursor: pointer;"><el-icon>
                                                <CirclePlus />
                                            </el-icon></span>
                                    </template>
                                    <template #default="scope">
                                        <span @click="delRow(scope.$index)"
                                            style="font-size: 16px;cursor: pointer;color: red;"><el-icon color="red">
                                                <Delete />
                                            </el-icon></span>
                                    </template>
                                </el-table-column>
                            </el-table>
                        </div>
                    </el-form-item>
                </el-form>
                <div>
                    <div style="float: left;">
                        <el-button @click="goLast()">上一步</el-button>
                    </div>
                    <div style="float: right;">
                        <el-button @click="save()">保存</el-button>
                    </div>
                </div>
            </el-col>
            <el-col :span="10" style="border: 1px solid #f0f0f0;padding: 20px;">
                <div style="font-size: 18px;font-weight: 800;margin-bottom: 20px;padding-left: 10px;">
                    预览
                </div>

                <div>
                    <el-card :body-style="{ padding: '0px' }" style="height: calc(100vh - 295px);overflow: scroll;">
                        <div slot="header" class="clearfix">
                            <img :src="baseUr1 + '/' + props.toolInfo.toolImage" style="width: 88px;float: left;">
                            <div>
                                <div style="font-size: 16px;font-weight: 600;padding-top: 10px;">{{ toolInfo.toolName }}
                                </div>
                                <div style="font-size: 14px;padding: 10px 0px;">
                                    {{ toolInfo.toolDesc }}
                                </div>
                            </div>
                        </div>

                        <el-row v-for="item in toolInfo.ecommerceToolKeys">
                            <el-col :span="24">
                                <div style="padding: 10px 0px;">
                                    {{ item.keyName }}</div>
                                <div>
                                    <el-input type="textarea" v-model="testText" :placeholder="item.keyDesc"></el-input>
                                </div>
                            </el-col>
                        </el-row>
                    </el-card>
                </div>
            </el-col>
        </el-row>
    </div>
</template>

<script setup name="toolEditStep2">

const baseUr1 = import.meta.env.VITE_APP_BASE_API

import { toolEdit, getToolInfo } from "@/api/ecommerce/tool";

const { proxy } = getCurrentInstance();
const loading = ref(false)

const props = defineProps({
    toolInfo: {
        type: Object,
        default: {
            id: 0,
            toolImage: '',
            toolName: '',
            toolDesc: '',
            toolStage: '',
            contentType: '',
            prompt: '',
            toolType: '',
            ecommerceToolKeys: []
        }
    },
})

const ecommerceToolKeys = props.toolInfo.ecommerceToolKeys
ecommerceToolKeys.forEach(element => {
    element.testText = ''
});

if (props.toolInfo && props.toolInfo.ecommerceToolKeys && props.toolInfo.ecommerceToolKeys.length > 0) {
    props.toolInfo.ecommerceToolKeys.forEach(element => {
        element.isCare = element.isCare == 1
    });
}


//编辑
const editTool = (tool) => {
    reset()
    toolInfo.value = tool;
    toolTitle.value = '编辑工具';
    toolVisible.value = true;
}

const addRow = () => {
    if (!props.toolInfo.ecommerceToolKeys) {
        props.toolInfo.ecommerceToolKeys = new Array()
    }
    props.toolInfo.ecommerceToolKeys.push({
        id: 0,
        toolId: props.toolInfo.id,
        keyName: "",
        keyCode: "",
        keyDesc: "",
        testText: '',
        isCare: 1
    })
}

const delRow = (index) => {
    props.toolInfo.ecommerceToolKeys.splice(index, 1)
}


let $emit = defineEmits(["nextStep", "click"]);


const goLast = () => {
    $emit("nextStep", 1);
}

//取消
const cancel = () => {
    toolVisible.value = false;
    reset()
}

//创建工具
const save = () => {

    let info = {
        id: 0,
        toolImage: '',
        toolName: '',
        toolDesc: '',
        toolStage: '',
        contentType: '',
        prompt: '',
        toolType: '',
        ecommerceToolKeys: []
    }

    if (props.toolInfo) {
        info.id = props.toolInfo.id
        info.toolImage = props.toolInfo.toolImage
        info.toolName = props.toolInfo.toolName
        info.toolDesc = props.toolInfo.toolDesc
        info.toolStage = props.toolInfo.toolStage
        info.contentType = props.toolInfo.contentType
        info.prompt = props.toolInfo.prompt
        info.toolType = props.toolInfo.toolType

        if (props.toolInfo.ecommerceToolKeys) {
            props.toolInfo.ecommerceToolKeys.forEach(element => {
                let toolKey = {
                    id: element.id,
                    toolId: element.toolId,
                    keyName: element.keyName,
                    keyCode: element.keyCode,
                    keyDesc: element.keyDesc,
                }
                toolKey.isCare = element.isCare ? 1 : 2
                info.ecommerceToolKeys.push(toolKey)
            });
        }
    }
    if (props.toolInfo.id == 0) {
        //新增
    } else {
        //修改
        toolEdit(info).then(response => {
            proxy.$modal.msgSuccess("修改成功");
            $emit("refreshList");
        });
    }

    reset()
}

/** 表单重置 */
const reset = () => {
    proxy.resetForm("toolInfoForm");
}

</script>


<style lang='scss' scoped>
.toolInfoForm {
    padding: 0px;
}

.toolInfoForm li {
    list-style: none;
    line-height: 40px;
    padding: 0px;
}

.bottom {
    margin-top: 13px;
    line-height: 12px;
}

.button {
    padding: 0;
    float: right;
}

.image {
    width: 100%;
    display: block;
}

.clearfix:before,
.clearfix:after {
    display: table;
    content: "";
}

.clearfix:after {
    clear: both
}
</style>
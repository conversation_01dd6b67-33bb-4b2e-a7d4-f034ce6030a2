<template>
    <div>
        <div class="common-container">
            <!--header-->
            <div class="common-header">
                <div class="title">
                    {{ mainTitle }}
                </div>
                <div class="header-right">
                    <el-button type="primary" size="small" @click="listTypeChange(1)" text style="margin-right:-25px">
                        <img :src="IconParkMargin" style="height: 80%;width: 80%;border-radius: 4px;border: 1px solid #E6E6E6;"/>
                    </el-button>
                    <el-button type="primary" size="small"  @click="listTypeChange(2)" text>
                        <img :src="IfCompany" style="height: 85%;width: 85%;border-radius: 4px;border: 1px solid #E6E6E6;"/>
                    </el-button>
                    <select-switch name="类型" :tabs="searchTypes" tabSelectLable="所有智能体" tabSelect="0" @changeSelect="changeType"></select-switch>
                    <div style="margin: 0 20px;">
                        <!--按下enter时触发getContentList-->
                        <el-input :placeholder="'搜索'" v-model="searchText" :prefix-icon="Search"
                            @keyup.enter="searchContent" maxlength="100" clearable></el-input>
                    </div>
                    <el-button type="primary" style="color: white;font-size: 14px;font-weight: 500;"
                        @click="toCreateSmartBody()">新建智能体</el-button>
                </div>
            </div>
            <!--content-->
            <div class="common-content" v-loading="isLoading" v-if="ListType=='1'">
                <!--列表-->
                <el-row style="max-height: 90%;overflow: auto;">
                    <template v-for="content in contentList">
                        <el-col :span="8" style="margin-bottom: 20px;">
                            <custom-card :img="content.agentIconUrl ? baseUrl+content.agentIconUrl : AgentDefaultIcon" 
                                :title="content.agentName"
                                :detail="content.agentDescribe" :is-need-footer="false" :jump-url="'/agent/bodyEdit?id='+content.id">
                                <template #tool>
                                    <el-dropdown :hide-on-click="false">
                                        <el-button text :icon="More" @click.stop></el-button>
                                        <template #dropdown>
                                        <el-dropdown-menu>
                                            <el-dropdown-item @click="editBody(content.id)" icon="Edit">编辑</el-dropdown-item>
                                            <el-dropdown-item @click="roleAuth(content.id)" v-if="content.publishStatus != 0">
                                                <svg-icon style="margin-right: 5px;" 
                                                    :icon-class="'app-auth'" 
                                                    :size="'1em'" />
                                                应用授权
                                            </el-dropdown-item>
                                            <el-dropdown-item @click="apiKeyAuth(content.id)" v-if="content.publishStatus != 0">
                                                <svg-icon style="margin-right: 5px;" 
                                                    :icon-class="'api-key-auth'" 
                                                    :size="'1em'" />
                                                API授权
                                            </el-dropdown-item>
                                            <el-dropdown-item @click="delBody(content.id)" icon="Delete" style="color:red">删除</el-dropdown-item>
                                        </el-dropdown-menu>
                                        </template>
                                    </el-dropdown>
                                    <!-- <el-button text :icon="Edit" @click.stop="editBody(content.id)" />
                                    <el-button text :icon="Delete" @click.stop="delBody(content.id)" /> -->
                                </template>
                            </custom-card>
                        </el-col>
                    </template>
                </el-row>
                <!--分页-->
                <el-row style="height: 10%;float: right;">
                    <el-pagination background layout="prev, pager, next" :total="page.total" @current-change="getContentList"
                        v-model:current-page="page.current" v-model:page-size="page.size" class="mt-4" />
                </el-row>
            </div>

            <div class="common-content" style="cursor: pointer;" v-loading="isLoading" v-if="ListType=='2'">
                <div style="height: 97%;overflow: auto;text-align: center;">
                    <vue3-tree-org
                        :data="treeList.data"
                        center
                        :toolBar="treeList.toolBar"
                        :horizontal="treeList.horizontal"
                        :collapsable="treeList.collapsable"
                        :label-style="treeList.style"
                        :node-draggable="false"
                        :scalable="false"
                        :only-one-node="treeList.onlyOneNode"
                        :default-expand-level="1"
                        :clone-node-drag="treeList.cloneNodeDrag"
                    >
                        <!-- 自定义节点内容 -->
                        <template v-slot="{node}">
                            <div class="tree-org-node__text node-label" v-if="node.$$data.isAgent && node.$$data.isAgent==0">
                                <div>{{node.label}}{{ node.$$data.isAgent }}</div>
                            </div>
                            <div class="tree-org-node__text node-label" v-if="node.$$data.isAgent && node.$$data.isAgent==1" style="width:200px">
                                <el-row>
                                    <el-col :span="7">
                                        <img :src="node.$$data.url?baseUrl+node.$$data.url:AgentDefaultIcon" 
                                        style="height: 85%;width: 85%;"/>
                                    </el-col>
                                    <el-col :span="17">
                                        <el-row>
                                            <el-col :span="19" style="text-align: left;margin-left: 15px;white-space: nowrap;overflow: hidden;">
                                            {{node.label}}</el-col>
                                            <el-col :span="24" style="text-align: left;margin-left: 12px;width:150px;">
                                                <!-- <el-button text :icon="ChatDotRound" @click="initiateConversationTree(node.$$data.id)"></el-button> -->
                                                <el-tooltip content="设置" effect="light" placement="top" popper-class="tooltip-pop">
                                                    <el-button text :icon="Tools" @click="router.push('agent/bodyEdit?id='+node.$$data.id);" link></el-button>
                                                </el-tooltip>
                                                <el-tooltip content="编辑" effect="light" placement="top" popper-class="tooltip-pop">
                                                    <el-button text :icon="Edit" @click="editBody(node.$$data.id)" link/>
                                                </el-tooltip>
                                            </el-col>
                                        </el-row>
                                    </el-col>
                                </el-row>
                            </div>
                        </template>
                        <!-- 自定义展开按钮 -->
                        <template v-slot:expand="{node}">
                            <div>{{node.children.length}}</div>
                        </template>
                    </vue3-tree-org>
                </div> 
            </div>
        </div>
        <!--新增智能体弹出框-->
        <el-dialog v-model="startChatDialogShow" width="600px" height="800px" @close="startChatDialogClose()" v-if="startChatDialogShow">
            <template #header>
                <span style="font-size: 20px;font-weight: 600;">{{ bodyTitle }}</span>
            </template>
            <div class="agent-dialog-content">
                <div style="text-align:center">
                    <cropper-image-upload :imageUrl="addForm.agentIconUrl" @update:imageUrl="uploadAgentIconUrl" />
                    <!-- <Image-Upload-Min v-model="addForm.agentIconUrl" :limit="1" ></Image-Upload-Min> -->
                </div>
                <el-form class="agent-form" :label-position="'top'" :model="addForm" ref="addBodyForm" :rules="addBodyRules" >
                    <el-form-item style="width: 560px;" prop="agentName" label="智能体名称">
                        <el-input v-model="addForm.agentName" />
                    </el-form-item>
                    <el-form-item style="width: 560px;" prop="agentCode" label="智能体英文标识">
                        <el-input v-model="addForm.agentCode" @input="validateAgentCode" />
                    </el-form-item>
                    <el-form-item style="width: 560px;" prop="agentType" label="智能体分类">
                        <agent-type-radio v-model="addForm.agentType" 
                            :disabled="!!addForm.agentId">
                        </agent-type-radio>
                    </el-form-item>
                    <el-form-item label="智能体所属部门">
                        <el-tree-select
                            v-model="addForm.deptId"
                            :data="deptList"
                            :props="{ value: 'id', label: 'label', children: 'children' }"
                            value-key="id"
                            placeholder="选择所属部门"
                            style="width:100%"
                        />
                    </el-form-item>
                    <el-form-item style="width: 560px;" prop="agentDescribe" label="智能体概述">
                        <el-input v-model="addForm.agentDescribe" type="textarea" :rows="4" />
                    </el-form-item>
                    <!-- <el-form-item style="width: 560px;" prop="agentTemplate" v-if="!addForm.agentId && addForm.agentId==null" label="智能体模版类型">
                        <el-radio-group v-model="addForm.agentTemplate" style="width: 100%;">
                            <el-radio-button :label="1">
                                <div class="custom-radio-button">
                                    <el-icon size="32px" style="margin-right: 10px;">
                                        <ChatDotRound />
                                    </el-icon>对话型
                                </div>
                            </el-radio-button>
                            <el-radio-button :label="2">
                                <div class="custom-radio-button">
                                    <el-icon size="32px" style="margin-right: 10px;">
                                        <Document />
                                    </el-icon>文本型
                                </div>
                            </el-radio-button>
                        </el-radio-group>
                    </el-form-item> -->
                </el-form>
            </div>
            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="startChatDialogClose">取消</el-button>
                    <el-button type="primary" @click="createSmartBody">保存</el-button>
                </div>
            </template>
        </el-dialog>
        <!--点击修改时弹出抽屉，覆盖整个页面-->
        <!-- <agent-drawer ref="agentEditDrawer">
            <template #content>
                <edit-body-container :edit-model="editForm" :edit-drawer="agentEditDrawer" @searchContentChild="searchContent"></edit-body-container>
            </template>
        </agent-drawer> -->

        <!--开始协作-->
        <el-dialog v-model="sessionVisible" :title="sessionDetail.agentName+'协作'" width="70%" v-if="sessionVisible" style="background-color: #F7F7FA;height:75vh"
            :close-on-click-modal="false" :close-on-press-escape="false" @close="sessionClose">
            <el-row style="border-top:1px solid #E6E6E6;height: calc(75vh - 54px);">
                <el-col style="background-color: #ffffff;height: 100%;" v-if="sessionContentVisible">
                    <yimai-chat-bot style="height: 100%;" :chat-url="'ws://180.103.126.11:29991'" 
                    :is-disabled-send-set="false" :keywords="sessionPamrams" :key-list="keyList"
                    ref="sessionTeamChat" :agent-info="sessionDetail" >
                    </yimai-chat-bot>
                </el-col> 
                <el-col v-if="sessionKeyVisible" style="width: 100%;height: 100%;display: flex;align-items: center;flex-direction: row;justify-content: center;" >
                    <div class="start-input" >
                        <div style="background-color: rgb(245 248 255);width: 100%;padding: 16px 24px;font-size: 24px;font-weight: bold;color: rgb(31 42 55);">
                            {{ sessionDetail.agentName+'协作' }}
                        </div>
                        <el-row style="padding: 20px;padding-bottom: 0;">
                            <el-col :span="24">
                                <el-form>
                                    <el-row>
                                        <el-col :span="24" v-for="(key) in keyList" :key="key.keyCode">
                                            <el-form-item  :label="key.keyName" >
                                                <el-input v-model="sessionPamrams[key.keyCode]" style="width: 80%;"></el-input>
                                            </el-form-item>
                                        </el-col>
                                    </el-row>
                                </el-form>
                            </el-col>
                        </el-row>
                        <div style="display: flex;align-items: center;justify-content: center;padding-bottom: 20px;">
                            <el-button @click="cancelSessionKey">取消</el-button>
                            <el-button type="primary" @click="runSession">开始协作</el-button>
                        </div>
                    </div>
                </el-col>
            </el-row>
            <!-- <el-dialog v-model="sessionKeyVisible" title="开始协作" width="50%" :align-center="true" v-if="sessionKeyVisible" style="background-color: #F7F7FA;"
                :close-on-click-modal="false" :close-on-press-escape="false" :show-close="false" center>
                <div style="padding: 20px;">
                        <el-form :label-width="'100px'" :label-position="'top'">
                            <el-row>
                                <el-col :span="8" v-for="(key) in keyList" :key="key.keyCode">
                                    <el-form-item  :label="key.keyName" >
                                        <el-input v-model="sessionPamrams[key.keyCode]" style="width: 80%;"></el-input>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                        </el-form>
                    </div>
                    <template #footer>
                        <span class="dialog-footer">
                            <el-button @click="cancelSessionKey">取消</el-button>
                            <el-button type="primary" @click="runSession">开始协作</el-button>
                        </span>
                    </template>
            </el-dialog> -->
        </el-dialog>

        <!--历史记录-->
        <el-dialog v-model="historyVisible" :title="'历史记录'" width="70%" style="background-color: #F7F7FA;height:75vh"
            :close-on-click-modal="false" :close-on-press-escape="false" @close="hideHistory">
            <history style="width: 100%;height: calc(75vh - 54px);" :agent-info="sessionDetail"></history>
        </el-dialog>

        <api-key-dialog :agentId="agentId" v-model="showApiKeyDialog"/>
    </div>
</template>
<script setup>
import { nextTick, ref } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, ChatDotRound, Document, Delete, Edit,Tools,Menu,Operation,More } from '@element-plus/icons-vue'
import { getList, getDetail, addAgent, delAgent,updateAgent,getAiagentTreeList,getDeptListTree } from '@/api/agent/body'
import CustomCard from '@/components/CustomCard/index'
import AgentDrawer from '@/components/AgentDrawer/index'
import SelectSwitch from '@/components/SelectSwitch/index'
import EditBodyContainer from './edit'
import IfCompany from '@/assets/images/if-company.svg'
import IconParkMargin from '@/assets/images/iconPark-all-application.svg'
import AgentDefaultIcon from '@/assets/icons/svg/agent-default.svg'
import YimaiChatBot from '@/components/YimaiChatBot/index';
import { getToken } from '@/utils/auth'
import useUserStore from '@/store/modules/user'
import {v4 as uuidv4} from 'uuid'
import History from './components/history'
import router from "@/router";
import agentTypeRadio from './components/agentType'
import apiKeyDialog from './components/apiKeyDialog.vue'
import CropperImageUpload from '@/components/CropperImageUpload/index'

const baseUrl = import.meta.env.VITE_APP_BASE_API;

const userStore = useUserStore()

//1列表 2 视图
const ListType = ref('1')

const treeList =ref({
        data: {},
        props:{id: 'id', pid: 'pid', label: 'label', expand: 'expand',children: 'children',isAgent:'isAgent' },
        toolBar:{
            scale: false, 
            restore: false, 
            expand: false, 
            zoom: false, 
            fullscreen: false
        },
        horizontal: false,
        collapsable: false,
        onlyOneNode: false,
        cloneNodeDrag: true,
        expandAll: true,
        noDragging:false,
        style: {
        background: "#fff",
        color: "#5e6d82",
    },
})

//抽屉ref
// const agentEditDrawer = ref(null)
//搜索下拉options
const searchTypes = [
    { label: '所有智能体', value: 0 },
    { label: '聊天助手', value: 1 },
    { label: 'Agent', value: 2 }
]
const page = ref({
    current: 1,
    size: 12,
    total: 0
})
//搜索下拉绑定的值
const searchType = ref('0')
//列表数据
const contentList = ref([])
//isloading
const isLoading = ref(false)

const showApiKeyDialog = ref(false)

const agentId = ref('')
//搜索框绑定的值
const searchText = ref('')
//页面标题
const mainTitle = ref('智能体列表')
//新增智能体dialog是否显示绑定的值
const startChatDialogShow = ref(false)
//新增智能体表单ref
const addBodyForm = ref(null)
const bodyTitle = ref('')
//新增智能体表单绑定的对象
const addForm = ref({
    agentName: '',
    agentCode: '',
    agentType: 1,
    agentDescribe: '',
    agentTemplate: 1,
    agentIconUrl: ''
})

const addBodyRules = ref({
    agentName: [{ required: true, message: "智能体名称不能为空", trigger: "blur" }],
    agentCode: [{ required: true, message: "智能体英文标识不能为空", trigger: "blur" }],
})
//智能体分类
const agentTypeList = ref([
    {id:1,agentType:'聊天型智能体'},
    {id:2,agentType:'文本型智能体'},
    {id:3,agentType:'开发型智能体'}
])

//智能体所属部门
const deptList = ref([])

//编辑智能体的详细信息
const editForm = ref({});

//会话弹框
const sessionVisible = ref(false)
const sessionKeyVisible = ref(false)
const sessionContentVisible = ref(false)
const historyVisible = ref(false)
//会话内容
const sessionDetail = ref({})
const sessionPamrams = ref({})
const keyList = ref([])
const sessionTeamChat = ref(null)

//打开新增智能体dialog
const toCreateSmartBody = () => {
    addForm.value = {
        agentName: '',
        agentCode: '',
        agentType: 1,
        agentDescribe: '',
        agentTemplate: '1',
        agentIconUrl: ''
    }
    bodyTitle.value='新增智能体'
    startChatDialogShow.value = true
    nextTick(() => {
        addBodyForm.value.resetFields()
    })
}
//搜索列表
const searchContent = () => {
    page.value.current = 1
    getContentList()
    if(ListType.value!=1){
        ListType.value=1
    }
}

//列表状态切换
const listTypeChange = (type) => {
    ListType.value=type
    page.value.current = 1
    if(ListType.value==1){
        getContentList()
    }else{
        getTreeList()
    }
}

//
const changeType = (type) => {
    searchType.value=type.value;
    searchContent();
}
//获取列表数据
const getContentList = () => {
    isLoading.value = true
    let params = {
        agentName: searchText.value,
        agentType: searchType.value=='0'?undefined:searchType.value,
        pageNum: page.value.current,
        pageSize: page.value.size
    }
    // 请求列表数据
    getList(params).then((resp) => {
        contentList.value = resp.rows
        page.value.total = resp.total
        isLoading.value = false
    })
}

// 获取部门数据
const getDeptList = () => {
    getDeptListTree().then((resp)=>{
        deptList.value=resp.data
    })
}

//视图列表数据
const getTreeList = () =>{
    getAiagentTreeList().then((resp)=>{
        treeList.value.data=resp.data[0]
    })
}

//提交新增智能体表单，目前无接口
const createSmartBody = () => {
    addBodyForm.value.validate(valid => {
        if(valid){
            if(addForm.value.agentId){
                delete addForm.value['publishStatus']
                updateAgent(addForm.value).then((resp) => {
                    if(resp.code == 200){
                        ElMessage.success('修改成功')
                        searchContent()
                        nextTick(() => {
                            startChatDialogShow.value = false
                        })
                    }else{
                        ElMessage.success('修改失败')
                    }
                })
            }else{
                addAgent(addForm.value).then(() => {
                    ElMessage.success('新增成功')
                    searchContent()
                    nextTick(() => {
                        startChatDialogShow.value = false
                    })
                })
            }
        }
    })
}

const uploadAgentIconUrl = (url) =>{
    addForm.value.agentIconUrl = url
}
//打开修改智能体抽屉
// const settingBody = (id) => {
//     //先获取详情，暂无接口
//     getDetail(id).then((resp) => {
//         agentEditDrawer.value.open()
//         editForm.value = resp.data
//     })
// }

//编辑智能体
const editBody = (id) => {
    bodyTitle.value='修改智能体'
    startChatDialogShow.value = true
    //先获取详情，暂无接口
    getDetail(id).then((resp) => {
        addForm.value = resp.data
        // nextTick(() => {
        //     addBodyForm.value.resetFields()
        // })
    })
}
//API授权
const apiKeyAuth = (id) => {
    agentId.value = id
    nextTick(() => {
        showApiKeyDialog.value = true
    })
}

const roleAuth = (id) => {
    router.push('/agent/agent-role/agent/'+id)
}
//删除智能体
const delBody = (id) => {
    ElMessageBox.confirm(
        '确认删除该智能体吗？',
        '系统提示',
        {
            confirmButtonText: '确认',
            cancelButtonText: '取消',
            type: 'warning',
        }
    ).then(() => {
        delAgent(id).then(() => {
            ElMessage.success('删除成功')
            getContentList()
        })
    }).catch(() => {
    })
}

const startChatDialogClose = () =>{
    addForm.value = {
        agentName: '',
        agentCode: '',
        agentType: '',
        agentDescribe: '',
        agentTemplate: 1,
        agentIconUrl: ''
    }
    startChatDialogShow.value=false
}

//架构图发起会话
const initiateConversationTree = (id) => {
    //先获取详情，暂无接口
    getDetail(id).then((resp) => {
        initiateConversation(resp.data)
    })
    
}

//开始协作
const initiateConversation = (item) =>{
    sessionDetail.value=item
    sessionVisible.value=true;
    if(sessionDetail.value.aiAgentKeyList && sessionDetail.value.aiAgentKeyList.length>0){
        keyList.value = sessionDetail.value.aiAgentKeyList
        let paramsObj = {}
        keyList.value.forEach((item) => {
            paramsObj[item.keyCode] = ''
        })
        sessionPamrams.value=paramsObj;
        sessionKeyVisible.value=true;
    }else if(sessionDetail.value.keyList){
        keyList.value = sessionDetail.value.keyList
        let paramsObj = {}
        keyList.value.forEach((item) => {
            paramsObj[item.keyCode] = ''
        })
        sessionPamrams.value=paramsObj;
        sessionKeyVisible.value=true;
    }else{
        keyList.value = []
        sessionPamrams.value={};
        runSession()
    }
}

//历史记录
const showHistory = (item) => {
    sessionDetail.value=item
    historyVisible.value=true;
}

//关闭历史记录弹窗
const hideHistory = () => {
    historyVisible.value=false;
}

//取消参数弹框
const cancelSessionKey = () => {
    sessionKeyVisible.value=false;
    sessionVisible.value=false;
    sessionContentVisible.value=false;
}
//关闭弹窗
const sessionClose = () => {
    sessionKeyVisible.value=false;
    sessionVisible.value=false;
    sessionContentVisible.value=false;
}

//开始协作
const runSession = () => {
    sessionKeyVisible.value=false;
    sessionContentVisible.value=true;
    nextTick(() => {
        sessionTeamChat.value.connectWS({
            id: sessionDetail.value.id,
            agent_name: sessionDetail.value.agentName,
            token:getToken(),
            user_name:userStore.id,
            sessionId:uuidv4(),
            model_id: "",
            keywords:sessionPamrams.value,
            temperature: "",
            top_p: "",
            presence_penalty:"",
            frequency_penalty:"",
            max_tokens:"",
            prompt:"",
            knowledgeList:[],
            max_recall: "",
            min_match:"",
            message: ""
        })
    })
}

const restrictedInput = ref(''); // 受限的输入值 
//智能体英文标识限制
const validateAgentCode = () => {
    // 正则表达式，匹配以英文字母或下划线开头，后面跟任意个英文字母、数字或下划线的字符串  
    const regex = /^[a-zA-Z_][a-zA-Z0-9_]*$/;  
        
    // 获取原始输入值  
    const value = addForm.value.agentCode;  
        
    // 如果输入为空，则直接返回  
    if (!addForm.value.agentCode) {  
        // addForm.value.agentCode = '';  
        return;  
    }  
    // 如果输入以数字开头或者不符合正则表达式，则进行修正  
    if (!regex.test(addForm.value.agentCode) || isNaN(addForm.value.agentCode.charAt(0) * 1)) {  
        // 移除不符合规范的字符（如果以数字开头，则移除第一个字符）  
        const cleanedValue = addForm.value.agentCode.replace(/^[0-9]|[^a-zA-Z0-9_]/g, '');  
        if (cleanedValue.length > 0 && isNaN(cleanedValue.charAt(0) * 1)) {  
            addForm.value.agentCode = cleanedValue;  
        } else {  
        // 如果修正后仍然以数字开头或为空，则清空输入  
        addForm.value.agentCode = '';   
        }  
    }
}

// 监听原始输入值的变化  
watch(() => addForm.value.agentCode, validateAgentCode, { immediate: true });  

//初始化获取列表数据
getContentList()

// 初始化获取部门列表
getDeptList()
</script>
<style scoped lang="scss">
.common-container {
    display: flex;
    flex-direction: column;
    background-color: #F7F7FA;
    height: calc(100vh - 52px);
    width: 100%;
    margin: 0;
    padding: 0;
    background-size: cover;
}

.common-header {
    height: 80px;
    padding: 28px 30px;
    line-height: 24px;
    display: flex;
    justify-content: space-between;

    .title {
        color: #032220;
        font-size: 18px;
        font-weight: 500;
    }

    .header-right {
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: row;
    }
}

.common-content {
    margin: 0 20px;
    height: calc(100% - 68px);
}

.agent-team-item-button {
    display: grid;
    place-items: center;
    margin-top: 4px;
    width: 50%;
}

::v-deep .el-radio-button__inner{
    width: 100%;
}
::v-deep .el-dialog {
    display: flex;
    flex-direction: column;
    margin: 0 !important;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    max-height: calc(100% - 30px);
    max-width: calc(100% - 30px);
}

::v-deep(.el-dialog__body) {
  padding: 0;
}
</style>
<style lang="scss">

.start-input{
    max-width: 500px;
    background-color: white;
    border-radius: 5px;
    border: 0.1px solid #DCDFE6;
}

.agent-dialog-content {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    .header-img {
        width: 80px;
        height: 80px;
        margin-bottom: 30px;
    }

    .custom-radio-button {
        height: 80px;
        width: 248px;
        line-height: 80px;
        display: flex;
        align-items: center;
        justify-content: center;
    }
}

.agent-dialog-footer {
    display: flex;
    justify-content: end;
    align-items: center;
    height: 60px;
}
</style>
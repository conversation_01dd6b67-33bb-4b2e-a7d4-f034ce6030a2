import request from "@/utils/request"

// 保存一个节点
export function saveKnowledgeNode (data) {
  return request({
    url: "/knowledge/save",
    method: "post",
    data,
  })

}

// 删除一个节点
export function delKnowledgeNode (data) {
  return request({
    url: "/knowledge/delete",
    method: "post",
    data,
  })
}

// 文件上传接口 
export function uploadFile (data) {
  return request({
    url: "/FileResource/uniteupload",
    method: "post",
    data,
  })
}

// 知识点列表 
export function getKnowledgeList (lessonId, version) {
  return request({
    url: `/knowledge/list?lessonId=${lessonId}&versionId=${version ? version : ''}`,
    method: "get"
  })
}

// 知识点资源列表服务
export function getKnowledgeResourceList (data) {
  return request({
    url: "/FileResource/list",
    method: "post",
    data,
  })
}

/**
 * 获取课程列表
 */
export function getLessonList (params) {
  return request({
    url: "/FileResource/getClassList",
    method: "get",
    params
  })
}

/**
 * 获取与某个知识点相关联的其他知识点
 */
export function getLinkedKnowledgePointList (params) {
  return request({
    url: "/knowledge/point/link-list",
    method: "get",
    params
  })
}

/**
 * 关联知识点与另一个知识点
 */
export function linkKnowledgePoint (data) {
  return request({
    url: "/knowledge/link",
    method: "post",
    data,
  })
}

/**
 * 取消两个知识点之间的关联
 */
export function cancelLinkKnowledgePoint (data) {
  return request({
    url: "/knowledge/cancel-link",
    method: "post",
    data,
  })
}

/**
 * 获取与某个知识点相关联的课程
 */
export function getLinkedResourceList (params) {
  return request({
    url: "/knowledge/course/link-list",
    method: "get",
    params
  })
}

/**
 * 关联知识点与一个课程
 */
export function linkResource (data) {
  return request({
    url: "/knowledge/link/course",
    method: "post",
    data,
  })
}

/**
 * 取消知识点与课程之间的关联
 */
export function cancelLinkResource (data) {
  return request({
    url: "/knowledge/cancel-link/course",
    method: "post",
    data,
  })
}

/**
 * 修改知识点概述
 */
export function modifyKnowledgePointOverview (data) {
  return request({
    url: "/KnowledgeRelationEdit/updateDescriptKnowledge",
    method: "post",
    data,
  })
}

// 知识点课程列表 FileResource/getClassList
export function getClassList () {
  return request({
    url: "/FileResource/getClassList",
    method: "get"
  })
}

// 获取网状知识图谱数据
export function getKnowledgeNet (params) {
  return request({
    url: "/knowledge/graph",
    method: "get",
    params
  })
}

// 获取编辑版本号
export function getEditVersion (data) {
  return request({
    url: "/knowledge/copy",
    method: "post",
    data
  })
}
// 知识点关联列表 
export function getKnowledgeRelationList (data) {
  return request({
    url: `/KnowledgeRelationEdit/listEdit?pageNum=${data.pageNum || 1}&pageSize=${data.pageSize || 10}`,
    method: "post",
    data
  })
}

// 文件列表知识点关联 
export function getResourceRelation (data) {
  return request({
    url: `/FileResource/getResourceRelation?pageNum=${data.pageNum || 1}&pageSize=${data.pageSize || 10}`,
    method: "post",
    data
  })
}

// 知识点关联接口  
export function upAssociationKnowledge (data) {
  return request({
    url: "/KnowledgeRelationEdit/upAssociationKnowledge",
    method: "post",
    data
  })
}
//  取消知识点关联接口 /KnowledgeRelationEdit/cancleAssociationKnowledge
export function cancleAssociationKnowledge (data) {
  return request({
    url: "/KnowledgeRelationEdit/cancleAssociationKnowledge",
    method: "delete",
    data
  })
}
// 文件资源关联 
export function upResourceRelation (data) {
  return request({
    url: "/FileResource/upResourceRelation",
    method: "post",
    data
  })
}
// 文件资源取消关联  /FileResource/cancleResourceRelation
export function cancleResourceRelation (data) {
  return request({
    url: "/FileResource/cancleResourceRelation",
    method: "delete",
    data
  })
}

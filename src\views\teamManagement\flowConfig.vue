<template>
    <div class="common-container">
        <div class="single-page-container">
            <div class="single-page-header">
                <div class="left-box">
                    <!-- <el-icon style="margin-right: 10px;"><Back /></el-icon> -->
                    <el-button :icon="Back" @click="closeDrawer" text style="margin-right: 10px;"></el-button>
                    <img :src="baseUrl+teamDetail.teamImgUrl" />
                    <div class="text-box">
                        <div class="title">
                            {{ teamDetail.teamName }}
                        </div>
                        <!-- <div class="detail">
                            {{editForm.agentCode}}
                        </div> -->
                    </div>
                </div>
                <div  class="right-box">
                    <el-button
                        type="primary"
                        style="color: white;font-size: 14px;font-weight: 500;"
                        @click="showChat = !showChat"
                    >调试</el-button>
                    <el-button style="font-size: 14px;font-weight: 500;" v-if="teamDetail.teamType == 3"
                    @click="runTest()">开始协作</el-button>
                    <el-button type="primary" style="color: white;font-size: 14px;font-weight: 500;"
                    @click="publicTeam()">发布</el-button>
                </div>
            </div>
        </div>
        <!--content---->
        <div class="common-content">
            <div class="whitePage">
                <div style="width: 100%;height: 100%;" @keydown="handleKeydown" @keyup="handleKeyup">
                    <div class="dndflow" @drop="onDrop" >
                        <Sidebar style="border-right: 1px solid #eee;" 
                            ref="flowConfigSidebar"  :alterKeys="teamDetail.aiTeamKeys" :alterText="teamDetail.prompt"
                            :agents="agents" :tools="tools" :prologue="teamDetail.prologue" :openQuestion="teamDetail.openQuestion"
                            @update:alterKeys="updateAlterKeys" 
                            @update:alterText="updateAlterText" 
                            @update:Opening="updateOpening"
                            />
                        <VueFlow @dragover="onDragOver" style="position: relative;" :pan-on-drag="panCanDrag">
                            <el-button style="position: absolute;top: 25px;left: 18px;z-index: 9;width: 20px;height: 20px;padding: 0;"  
                                size="small" :icon="ArrowRight" v-if="flowConfigSidebar && flowConfigSidebar.isCollapseSideBar"
                                @click="flowConfigSidebar.isCollapseSideBar = false"></el-button>
                            <vue-flow-custom-tools :current-flow="tempFlow" :can-drag="panCanDrag" @updateCanDrag="updatePanCanDrag"/>
                            <template #node-custom="{ id }">
                                <CustomNode :id="id" :nodeInfo="findNode(id).data"
                                    @openNodeDrawer="openNodeDrawer(id)" />
                            </template>
                            <template #edge-close="props">
                                <closeEdge v-bind="props" />
                            </template>
                            <Background />
                        </VueFlow>
                        <chat v-if="showChat" />
                        <!-- <el-button @click="getFlowObject">获取当前flow数据</el-button> -->
                    </div>
                </div>
                <!-- <el-drawer
                    v-model="editNodeDrawer"
                    title="修改节点"
                    >
                    <span>{{ currentEditingNode.data.name }}</span>
                    <el-input v-model="currentEditingNode.data.name" />
                </el-drawer> -->
            </div>
        </div>
        <!--点击修改时弹出抽屉，覆盖整个页面-->
        <agent-drawer ref="runTestDrawer">
            <template #content>
                <run-test-demo :edit-drawer="runTestDrawer" :edit-model="teamDetail"></run-test-demo>
            </template>
        </agent-drawer>
    </div>
</template>
  
<script setup>
import { VueFlow, useVueFlow,MarkerType } from '@vue-flow/core' 
import '@vue-flow/controls/dist/style.css'
import { Background } from '@vue-flow/background'
import { nextTick, watch } from 'vue'
import Sidebar from '@/components/YimaiFlow/workFlowSideBar.vue'
import vueFlowCustomTools from '@/components/YimaiFlow/vueFlowCustomTools.vue'
import CustomNode from '@/components/YimaiFlow/vueFlowCustomNode.vue'
import closeEdge from '@/components/YimaiFlow/vueFlowCustomEdges.vue'
import {getToolList,getAgentList,getTeamDetail,publishTeam} from '@/api/teamManagement/configFlow'
import {ArrowRight,Back} from '@element-plus/icons-vue'
import { v4 as uuidv4 } from 'uuid';
import {ElMessage,ElMessageBox} from 'element-plus'
import AgentDrawer from '@/components/AgentDrawer/index'
import RunTestDemo from './components/runTestDemo'
import router from "@/router";
import Chat from '@/components/YimaiFlow/chat.vue';

const route = useRoute();

const baseUrl = import.meta.env.VITE_APP_BASE_API;

const runTestDrawer = ref(null)

//team params
const teamId = ref(route.query.id)
// ref(props.editModel.teamId)
const teamDetail = ref({})

//sidebar params
const agents = ref([])
const tools = ref([])
const flowConfigSidebar = ref(null)

//flow params
let currentFlowData = ref(null) //当前流程数据
let editNodeDrawer = ref(false) //修改节点抽屉
let currentEditingNode = ref(null) //当前正在修改的节点

// 调试
const showChat = ref(false)

function openNodeDrawer(id){
    editNodeDrawer.value = true
    currentEditingNode.value = findNode(id)
}

const tempFlow = useVueFlow({
    nodes: []
})

const panCanDrag = ref(false)

const handleKeydown = (event) => {
    if (event.key === ' ') {
        panCanDrag.value = true
    }
}
const handleKeyup = (event) => {
    if (event.key === ' ') {
        panCanDrag.value = false
    }
}

const { findNode, onConnect, addEdges, addNodes, project, vueFlowRef,toObject,fromObject,onMove } = tempFlow



function onDragOver(event) {
    event.preventDefault()
    // 如果浏览器支持dataTransfer，设置dropEffect为move
    if (event.dataTransfer) {
        event.dataTransfer.dropEffect = 'move'
    }
}

const updatePanCanDrag = (val) => {
    panCanDrag.value = val
}

onConnect((params) => {
    params.type = 'close'
    params.markerEnd = MarkerType.ArrowClosed
    params.style = { stroke: '#5050E6'}
    params.labelBgStyle = { fill: '#5050E6' }
    addEdges(params)
})

function onDrop(event) {
    let info = event.dataTransfer?.getData('application/vueflow')
    if(info){
        info = JSON.parse(info)
    }
    if(info.type == 'template'){
        if(getFlowObject().nodes.filter(node => node.data.type == 'agent' || node.data.type == 'tool_code' || node.data.type == 'tool_api').length > 0){
            ElMessageBox({
                title:'注意',
                type:'warning',
                center:true,
                message:'当前存在正在编辑的智能体或工具，确认替换模板吗？',
            }).then(() => {
                fromObject(JSON.parse(info.data.templateJson))
            })
        }else{
            fromObject(JSON.parse(info.data.templateJson))
        }
        return;
    }

    const { left, top } = vueFlowRef.value.getBoundingClientRect()

    const position = project({
        x: event.clientX - left,
        y: event.clientY - top,
    })

    let newId = uuidv4()
    const newNode = {
        id: newId,
        type:'custom',
        position,
        label: `${newId} node`,
        data:{}
    }

    if(info.type == 'agent'){
        let data = {
            type: 'agent',
            data: {
                agentId: info.data.id,
                agentName: info.data.agentName,
                agentCode: info.data.agentCode,
                agentDescribe: info.data.agentDescribe,
                goal:info.data.goal,
                agentIconUrl: info.data.agentIconUrl,
                agentType: info.data.agentType,
                modelId: info.data.aiAgentArrange.modelId,
                temperature: info.data.aiAgentArrange.temperature,
                input:info.data.aiAgentKeyList.map(item => {
                    return {
                        key:item.keyCode,
                        name:item.keyName
                    }
                }),
                prompt:info.data.aiAgentArrange.prompt,
                output:[{name:'output',type:'String'}],
            }
        }
        newNode.data = data
    }else if(info.type == 'tool_code'){
        let data = {
            type: 'tool_code',
            data: {
                toolId: info.data.id,
                toolName: info.data.toolName,
                toolCode: info.data.toolCode,
                toolDesc: info.data.toolDesc,
                toolType: info.data.toolType,
                toolImageUrl: info.data.toolImageUrl,
                toolProcedure: info.data.toolProcedure,
                input:[],
                output:[{name:'output',type:'String'}],
            }
        }
        newNode.data = data
    }else if(info.type == 'tool_api'){
        let data = {
            type: 'tool_api',
            data: {
                toolId: info.data.id,
                toolName: info.data.toolName,
                toolCode: info.data.toolCode,
                toolDesc: info.data.toolDesc,
                toolType: info.data.toolType,
                toolImageUrl: info.data.toolImageUrl,
                toolUrl: info.data.toolUrl,
                requestMethod: info.data.requestMethod,
                headerList:JSON.parse(info.data.headerList),
                inKey:JSON.parse(info.data.inKey),
                outKey:JSON.parse(info.data.outKey),
            }
        }
        newNode.data = data
    }

    addNodes([newNode])

    // align node position after drop, so it's centered to the mouse
    nextTick(() => {
        const node = findNode(newNode.id)
        const stop = watch(
            // 监听node的dimensions属性
            () => node.dimensions,
            // 当dimensions的宽度大于0且高度大于0时，执行下面的代码
            (dimensions) => {
                if (dimensions.width > 0 && dimensions.height > 0) {
                    // 将node的位置设置为x为node的x坐标减去node的宽度的一半，y为node的y坐标减去node的高度的一半
                    node.position = { x: node.position.x - node.dimensions.width / 2, y: node.position.y - node.dimensions.height / 2 }
                    // 停止监听
                    stop()
                }
            },
            // 设置监听的深度为深层，并刷新post
            { deep: true, flush: 'post' },
        )
    })
}

function initStartAndEnd(){
    const newNode = [
    {
        id: 'start',
        type: 'custom',
        data: {
            type: 'start',
            data: {
                label: '开始',
                desc:'工作流的起始节点，用于设置启动工作流所需的信息。'
            }
        },
        position: { x: 300, y: 300 },
    },
    {
        id: 'end',
        type: 'custom',
        data: {
            type: 'end',
            data: {
                label: '结束',
                desc:'工作流的最后一个节点，用于在工作流运行后返回结果信息。'
            }
        },
        position: { x: 1000, y: 300 },
    }]

    addNodes(newNode)
}

function getFlowObject(){
    currentFlowData.value = toObject()
    return toObject()
}

const closeDrawer = () => {
    // props.editDrawer.close()
    router.back();
}

const init = () => {
    getToolList().then(res => {
        tools.value = res.data
        getAgentList().then(res1 => {
            agents.value = res1.data
        })
    })
    getTeamDetail(teamId.value).then(res => {
        teamDetail.value = res.data
        if(!teamDetail.value.arrangeJson){
            initStartAndEnd()
        }else{
            fromObject(JSON.parse(teamDetail.value.arrangeJson))
        }
    })
}

const publicTeam = () => {
    const {id,prompt,aiTeamKeys,teamType,prologue,openQuestion,} = teamDetail.value
    let params = {
        id,
        prompt,
        teamType,
        aiTeamKeys,
        prologue,
        openQuestion,
        arrangeJson:JSON.stringify(getFlowObject())
    }
    console.log(params)
    publishTeam(params).then(res => {
        if(res.code === 200){
            ElMessage.success('发布成功')
        }
    })
}

const updateAlterKeys = (data) => {
    teamDetail.value.aiTeamKeys = data
    // console.log(data)
}

const updateAlterText = (data) => {
    teamDetail.value.prompt = data
    // console.log(data)
}
const updateOpening = (data) => {
    teamDetail.value.prologue = data.prologue
    teamDetail.value.openQuestion = data.openQuestion
    // console.log(data)
}

const runTest = () => {
    teamDetail.value.arrangeJson = JSON.stringify(getFlowObject())
    // console.log(teamDetail.value)
    runTestDrawer.value.open()
}

init()
</script>
<style lang="scss" scoped>
.common-container {
    display: flex;
    flex-direction: column;
    background-color: #FFFFFF !important;
    height: calc(100vh - 52px);
    width: 100%;
    margin: 0;
    padding: 0;
    background-size: cover;
}

.common-header {
    height: 60px;
    padding: 20px 30px;
    line-height: 24px;
    display: flex;
    justify-content: space-between;
    border-bottom: 1px solid #E6E6E6;
    margin-bottom: 10px;
    .title {
        color: #032220;
        font-size: 18px;
        font-weight: bold;
    }

    .header-right {
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: row;
    }
}

.single-page-container{
    // background-color: white;
    // height: 100vh;
    .single-page-header{
        height: 80px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-direction: row;
        border-bottom: 1px solid #ccc;
        .left-box{
            margin-left:24px;
            display: flex;
            align-items: center;
            .text-box{
                display: flex;
                flex-direction: column;
                margin-left: 10px;
                .title{
            display: flex;
            align-items: center;
                    height: 25px;
                    font-size: 18px;
                    font-weight: 500;
                    color: #032220;
                }
                .detail{
                    height: 17px;
                    font-size: 12px;
                    color: #999999;
                    font-weight: 400;
                    margin-top: 4px;
                }
            }
            img {
                height: 40px;
                width: 40px;
                border-radius: 4px;
                border: 1px solid #E6E6E6;
            }
        }
        .right-box{
            margin-right: 20px;
        }
    }
    .single-page-content{
        display: flex;
        flex-direction: row;
        height: calc(100vh - 80px);
        overflow-y: auto;
        .content-left-box{
            width: calc(60% - 48px);
            height: calc(100% - 60px);
            border-right: 1px solid #ccc;
            background-color: white;
            margin-top: 30px;
            .content-left-box-title{
                padding-left: 24px;
                font-size: 14px;
                font-weight: bold;
                color: #032220;
                margin-bottom: 16px;
            }
            .left-box-half-top{
                border-bottom: 1px solid #ccc;
            }
            .custom-form-item-slider{
                display: flex;
                flex-direction: row;
                width: 100%;
                align-items: center;
                justify-content: space-between;
                margin-bottom: 5px;
                .custom-slider-title{
                    margin-right: 20px;
                    width: 120px;
                    text-align: left;
                    font-size: 12px;
                    color: #999999;
                }
                .custom-slider-value{
                    width: calc(100% - 120px);
                    display: flex;
                    flex-direction: row;
                }
            }
        }
        .content-right-box-title{
            color: #032220;
            font-size: 14px;
            font-weight: 500;
            margin-bottom: 10px;
        }
        
        
    }
}

.common-content {
    margin: 0 20px;
    height: calc(100vh - 100px);
}

.common-footer{
    max-height: calc(100vh - 800px);
    // display: flex;
    // justify-content: center;
    // align-items: center;
    text-align: center;
}
.whitePage {
    display: flex;
    justify-content: center;
    align-items: center;
    height: calc(100vh - 80px);
    background-color: white;
}

.vue-flow__minimap {
    transform: scale(75%);
    transform-origin: bottom right;
}

.dndflow {
    flex-direction: row;
    display: flex;
    height: 100%
}

.dndflow .vue-flow-wrapper {
    flex-grow: 1;
    height: 100%
}
</style>
  
<style>
@import '@vue-flow/core/dist/style.css';
@import '@vue-flow/core/dist/theme-default.css';
</style>

<style>
#arrowclosed polyline{
    stroke:rgb(80, 80, 230) !important;
    fill:rgb(80, 80, 230) !important;
    z-index: 999999;
}
</style>
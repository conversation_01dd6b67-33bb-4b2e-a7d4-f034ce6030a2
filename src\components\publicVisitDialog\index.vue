<template>
    <el-dialog
      title="嵌入到网站中"
      v-model="isVisible"
      width="700px"
    >
      <template #header>
        <div class="title-wrapper">
          <span class='title'>嵌入到网站中</span>
        </div>
      </template>
      <div style="padding: 10px;">
        <div style="padding: 5px 0;display: flex;justify-content: space-between;align-items: center;">
            <span>公开访问URL</span>
            <div>
                <el-tag type="success" v-if="isOpen">运行中</el-tag>
                <el-tag type="info" v-if="!isOpen">已停用</el-tag>
                <el-switch v-model="isOpen" style="margin-left: 5px;" />
            </div>
        </div>
        <div style="display: flex;justify-content: space-between;align-items: center;">
            <div class="url-box">
                <div class="font">
                    {{ url }}
                </div>
                <div class="button-box">
                    <el-tooltip content="复制" 
                    effect="light" placement="top" popper-class="tooltip-pop">
                        <el-button :icon="CopyDocument" style="font-size:15px" link @click="copyClick(url)"/>
                    </el-tooltip>
                    <el-tooltip content="重新生成" 
                    effect="light" placement="top" popper-class="tooltip-pop">
                        <el-button :icon="Refresh" style="font-size:15px" link/>
                    </el-tooltip>
                </div>
            </div>
            <!-- <div style="margin-left: 10px;">
                <el-button :icon="Link" @click="jump" :disabled="!isOpen">预览</el-button>
            </div> -->
        </div>
        <div class="iframe-box" v-if="isOpen">
            <div class="top">
                <div class="left">
                    将以下 iframe 嵌入到你的网站中的目标位置
                </div>
                <el-tooltip content="复制" 
                effect="light" placement="top" popper-class="tooltip-pop">
                    <el-button :icon="CopyDocument" style="font-size:15px" link @click="copyClick(iframeCode)"/>
                </el-tooltip>
            </div>
            <div class="iframe-code">
                {{ iframeCode }}
            </div>
        </div>
      </div>
    </el-dialog>
  </template>
  
  <script setup>
    import { CopyDocument,Refresh,Link } from '@element-plus/icons-vue';
    import { onMounted, watch } from 'vue';
    import copy from 'copy-to-clipboard';
    import { ElMessage, ElMessageBox } from 'element-plus';
    import { getApiKeyList, updateApiKey, addApiKey, delApiKey, getApiKeyDetail } from '@/api/agent/apikey';
    import { formatDate } from '@/utils/index.js';
    import useClipboard from 'vue-clipboard3';
    import { useRouter } from 'vue-router'

    const { proxy } = getCurrentInstance();

    const router = useRouter()
    
    const props = defineProps({
        /**
         * 是否打开弹窗
         */
        modelValue: {
            type: Boolean,
            required: true,
            default: false
        },
        /**
         * 应用ID
         */
        id: {
            type: String,
            required: true,
        }
    })
    
    const emit = defineEmits(['update:modelValue']);

    const baseUrl = import.meta.env.VITE_APP_BASE_API;
    const domain = import.meta.env.VITE_APP_BASE
    
    // 是否显示对话框
    const isVisible = ref(props.modelValue);
    watch(isVisible, () => {
        if (isVisible.value !== props.modelValue) {
        emit('update:modelValue', isVisible.value);
        }
        if (isVisible.value) {
            
        }
    })
    watch(() => props.modelValue, () => {
        if (isVisible.value !== props.modelValue) {
        isVisible.value = props.modelValue;
        }
    })

    //是否开启
    const isOpen = ref(true)
    const url = ref(domain+'/web/chat/'+props.id)
    const { toClipboard } = useClipboard();
    const copyClick = async item => {
        try {
            await toClipboard(item);
            proxy.$modal.msgSuccess("复制成功");
        } catch (e) {
            console.error(e);
        }
    };

    const jump = () => {
        let result = url.value
        window.open(result, '_blank');
    }
    
    const iframeCode = ref('<iframe \n'
    +' src="'+url.value+'" \n'
    +' style="width: 100%; height: 100%; " \n'
    +' frameborder="0" \n'
    +' allow="microphone"> \n'
    +'</iframe>')

  </script>
  
  <style scoped>
    .title-wrapper {
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        align-items: flex-start;
    }
    .url-box{
        display: flex;
        justify-content: space-between;
        width:100%;
        height: 35px;
        line-height: 35px;
        border-radius: 5px;
        border: 1px solid #5050e621;
        background-color: #5050e614;
        .font{
            margin-left: 5px;
        }
        .button-box{
            margin-right: 5px;
        }
    }
    .iframe-box{
        margin-top: 10px;
        .top{
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 10px;
            border: 1px solid #5050e621;
            border-top-left-radius: 5px;
            border-top-right-radius: 5px;
            background-color: #5050e614;
            .left{

            }
        }
        .iframe-code{
            white-space: pre-wrap;
            height: 130px;
            background-color: #5050e621;
            margin: 0;
            border-bottom-left-radius: 5px;
            border-bottom-right-radius: 5px;
            padding: 15px;
        }
    }
  </style>
  
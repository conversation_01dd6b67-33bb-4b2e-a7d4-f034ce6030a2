{"name": "ruoyi", "version": "3.8.7", "description": "若依管理系统", "author": "若依", "license": "MIT", "type": "module", "scripts": {"dev": "vite", "build:prod": "vite build", "build:stage": "vite build --mode staging", "preview": "vite preview"}, "repository": {"type": "git", "url": "https://gitee.com/y_project/RuoYi-Vue.git"}, "dependencies": {"@codemirror/lang-javascript": "^6.2.1", "@codemirror/theme-one-dark": "^6.1.2", "@codemirror/view": "^6.23.0", "@element-plus/icons-vue": "2.3.1", "@emoji-mart/data": "^1.1.2", "@kangc/v-md-editor": "^2.3.18", "@mdi/js": "^7.4.47", "@microsoft/fetch-event-source": "^2.0.1", "@tiptap/extension-code-block-lowlight": "^2.23.0", "@tiptap/extension-color": "^2.12.0", "@tiptap/extension-highlight": "^2.12.0", "@tiptap/extension-image": "^2.12.0", "@tiptap/extension-link": "^2.12.0", "@tiptap/extension-subscript": "^2.12.0", "@tiptap/extension-superscript": "^2.12.0", "@tiptap/extension-table": "^2.12.0", "@tiptap/extension-table-cell": "^2.12.0", "@tiptap/extension-table-header": "^2.12.0", "@tiptap/extension-table-row": "^2.12.0", "@tiptap/extension-task-item": "^2.12.0", "@tiptap/extension-task-list": "^2.12.0", "@tiptap/extension-text-style": "^2.12.0", "@tiptap/extension-underline": "^2.12.0", "@tiptap/starter-kit": "^2.12.0", "@tiptap/vue-3": "^2.12.0", "@vue-flow/background": "^1.2.0", "@vue-flow/controls": "^1.1.0", "@vue-flow/core": "^1.27.1", "@vue-flow/minimap": "^1.2.0", "@vue-flow/node-resizer": "^1.3.6", "@vue/reactivity": "^3.4.27", "@vue/runtime-core": "^3.4.15", "@vue/shared": "^3.4.27", "@vueup/vue-quill": "1.2.0", "@vueuse/core": "10.6.1", "axios": "0.27.2", "copy-to-clipboard": "^3.3.3", "dagre": "^0.8.5", "echarts": "5.4.3", "echarts-wordcloud": "^2.1.0", "element-plus": "^2.7.4", "emoji-mart": "^5.5.2", "emoji-mart-vue-fast": "^15.0.2", "file-saver": "2.0.5", "fuse.js": "6.6.2", "highlight.js": "^11.9.0", "html2canvas": "^1.4.1", "js-cookie": "3.0.5", "js-yaml": "^4.1.0", "jsencrypt": "3.3.2", "katex": "^0.16.10", "lottie-web": "^5.13.0", "lowlight": "^3.3.0", "markdown-it": "^14.0.0", "markdown-it-link-attributes": "^4.0.1", "markdown-it-math": "^4.1.1", "mermaid": "^11.7.0", "nprogress": "0.2.0", "papaparse": "^5.4.1", "pinia": "2.1.7", "remixicon": "^4.6.0", "temml": "^0.11.2", "tiptap-markdown": "^0.8.10", "uuid": "^9.0.1", "vue": "3.3.9", "vue-clipboard3": "^2.0.0", "vue-codemirror": "^6.1.1", "vue-cropper": "1.1.1", "vue-inline-svg": "^3.1.2", "vue-pdf-embed": "^2.1.2", "vue-router": "4.2.5", "vue3-tree-org": "^4.2.2", "xlsx": "^0.18.5"}, "devDependencies": {"@vitejs/plugin-vue": "4.5.0", "@vue/compiler-sfc": "3.3.9", "sass": "1.69.5", "unplugin-auto-import": "0.17.1", "unplugin-vue-setup-extend-plus": "1.0.0", "vite": "5.0.4", "vite-plugin-compression": "0.5.1", "vite-plugin-svg-icons": "2.0.1"}}
<template>
  <div style="border-left: 1px solid #c0c4cc; height: 100%">
    <div class="topLine" v-if="cleanData && cleanData.segments.length > 0">
      <span class="text">分段详情：</span>
      <el-button type="primary" @click="download()">下载</el-button>
    </div>
    <div class="segments" v-if="cleanData && cleanData.segments.length > 0">
      <segment
        v-for="(item, index) in cleanData.segments"
        :key="index"
        :segmentNum="index"
        :segment-data="item"
        @delete-segment="deleteSegment"
        @save-content="saveContent"
      >
      </segment>
    </div>
  </div>
</template>

<script>
import yaml from "js-yaml";
import Segment from "./Segment.vue";
// import { parse, stringify } from 'yaml/browser/index.js'

export default {
  props: {
    cleanData: Object,
  },
  components: {
    Segment,
  },
  methods: {
    download() {
      const yamlContent = yaml.dump(this.cleanData);
      let blob = new Blob([yamlContent], { type: "text/plain" });

      let link = document.createElement("a");
      link.href = window.URL.createObjectURL(blob);
      link.download = "clean.dish";
      link.click();
    },

    deleteSegment(modified_at) {
      this.cleanData.segments = this.cleanData.segments.filter(
        (segment) => segment.meta.modified_at !== modified_at
      );
    },
    saveContent(data) {
      console.log("saveContent", data);
      this.cleanData.segments = this.cleanData.segments.map((segment) => {
        if (segment.meta.modified_at === data.modified_at) {
          segment.content = data.content;
        }
        return segment;
      });
    },
  },
};
</script>

<style scoped>
.segments {
  display: flex;
  width: 100%;
  flex-wrap: wrap;
  overflow: auto;
  height: calc(100vh - 130px);
}
.width-center {
  display: flex;
  margin: auto;
}
.text {
  color: #606266;
  margin-left: 10px;
}
.topLine {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding: 10px;
  height: 80px;
}
</style>

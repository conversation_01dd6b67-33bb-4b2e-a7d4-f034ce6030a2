<template>
    <div class="container">
        <div class="content">
            <div class="ai-chat-content" :style="{'height':props.isDebugging?'calc( 100vh - 170px )':'calc( 100vh - 100px )'}" ref="recordsBox">
                <div class="robot-record-box"
                v-for="(item,index) in props.messageItemManager.nowMessageHtml.value">
                    <div class="robot-content" v-if="(isHistory && item.content) || !isHistory">
                        <div class="robot-record-item">
                            <template v-if="item.waitting">
                                <div style="text-align:center">
                                    wait...
                                </div>
                            </template>
                            <template v-else>
                                <!-- <message-renderer :cnt="item.content" /> -->
                                <md :cnt="item.content" :enableHtml="true"/>
                            </template>
                            <!--操作-->
                            <div class="operation" v-if="!item.waitting">
                                <el-button :icon="CopyDocument" size="small" @click="copyClick(item.content)">复制</el-button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
<script setup>
    import { defineProps, nextTick,onBeforeUnmount,ref, watch } from 'vue';
    import {v4 as uuidv4} from 'uuid'
    import useUserStore from '@/store/modules/user'
    import { CircleCheck ,ArrowDownBold ,Fold ,ArrowUpBold,Promotion,Refresh,VideoPause,CopyDocument } from '@element-plus/icons-vue'
    import { getToken } from "@/utils/auth";
    import { TaskQueue } from './utils/taskQueue'
    import sessionmodel from "@/assets/icons/svg/session-model.svg"
    import MessageRenderer from '@/components/Chat/messageRenderer.vue'
    import Md from "@/components/Markdown/index.vue";
    import newSession from "@/assets/icons/svg/newSession.svg" 
    import CitationList from '@/components/Chat/citationList.vue'
    import { conversationGenerate } from "@/api/YiMaiChat/sessionRecords"
    import { ElMessage } from 'element-plus'
    import { parse } from '@vue/compiler-sfc';
    import { getDetail } from '@/api/agent/body'
    import useClipboard from 'vue-clipboard3';
    import { fetchEventSource } from '@microsoft/fetch-event-source';
    import { closeSourceSessionTask,conversationHistory } from '@/api/YiMaiChat/sessionRecordsDify'
    
    const { proxy } = getCurrentInstance();
    const baseUrl = import.meta.env.VITE_APP_BASE_API;
    const emit = defineEmits();

    const props = defineProps({
        messageItemManager:{
            type:Object,
            required: false
        },
        //服务路径
        chatUrl:{
            type:String,
            required:true
        },
        currentInstruct:{
            type:Object,
            required:true
        },
        //是否展示前言
        prefaceState:{
            type:Boolean,
            required:false,
            default:false
        },
        //是否是调试
        isDebugging:{
            type:Boolean,
            required:false,
            default:false
        },
        //是否可以运行
        runDisabled:{
            type:Boolean,
            required:false,
            default:false
        }
    });

    let source = null

    //消息列表ref
    const recordsBox = ref(null)

    const userBoxClassName = "user-record-item";

    const robotBoxClassName = "robot-record-item";

    const conversationId = ref(null)
    //是否是历史会话记录
    const isHistorySession = ref(false)
    const keyList = ref({})
    //是否是历史记录
    const isHistory = ref(false)

    //是否展示前言
    const prefaceState = ref(false)
    watch(() => props.prefaceState, val => {
        if (val) {
            prefaceState.value = true
            return true
        } else {
            prefaceState.value = false
            return false
        }
    },{ deep: true, immediate: true });

    //是否可运行
    const runDisabled = ref(false)
    watch(() => props.runDisabled, val => {
        if (val) {
            runDisabled.value = true
            return true
        } else {
            runDisabled.value = false
            return false
        }
    },{ deep: true, immediate: true });

    //历史记录
    const historyHandle = () => {
        // 是否展示前言
        prefaceState.value = false
        emit("updatePrefaceState", prefaceState.value);
        conversationId.value = currentInstruct.value.conversation.id
        conversationHistory(currentInstruct.value.appId,conversationId.value).then(response => {
            props.messageItemManager.loadHistoryMessage(response.data)
        })
    }

    //当前点击选中的指令
    const currentInstruct = ref({})
    const reload = ref(false)
    watch(() => props.currentInstruct, val => {
        if(val){
            currentInstruct.value = val
            isHistory.value = val.isHistory?true:false
            if(isHistory.value){
                historyHandle()
            }
        }
        else{
            currentInstruct.value = {}
        }
    },{ deep: true, immediate: true });

    
    //连接状态
    const connectState = ref(false)
    const task_id = ref(null)
    const printBotMessage = (message,role,task_id,is_done = false) => {
        return new Promise((resolve) => {
            let tempTxt = ''
            if(!is_done && message.data.text){
                if(props.messageItemManager.nowMessageHtml.value.filter(x=>x.task_id == task_id)[0]){
                    tempTxt = props.messageItemManager.nowMessageHtml.value.filter(x=>x.task_id == task_id)[0].content+message.data.text
                    props.messageItemManager.appendNowMessageContent(tempTxt,role,task_id);
                }
            }
            if(is_done && message.data.outputs.text 
            && props.messageItemManager.nowMessageHtml.value.filter(x=>x.task_id == task_id)[0] 
            &&props.messageItemManager.nowMessageHtml.value.filter(x=>x.task_id == task_id)[0].content == ''){
                tempTxt = message.data.outputs.text
                props.messageItemManager.appendNowMessageContent(tempTxt,role,task_id);
            }
        })
    }

    //记录上一条回复状态
    const event = ref('')
    const role = ref({})
    const ctrl = new AbortController();
    const initConnect = (keyList = null,isClearHistory = false ) => {
        if(isClearHistory){
            props.messageItemManager.cleanHistory();
        }
        return new Promise((resolve,reject) => {
            let query = {
                "response_mode": "streaming",
                "query": '',
                "inputs":keyList || keyList.value
            }
            if(conversationId.value){
                query.conversation_id = conversationId.value
            }
            // 创建EventSource对象来监听SSE事件
            source = fetchEventSource(currentInstruct.value.chatUrl,{
                method: 'POST',
                headers:{
                    'Content-Type': 'application/json',
                    'Authorization':'Bearer ' + getToken(),
                },
                body:JSON.stringify(query),
                openWhenHidden: true,
                signal: ctrl.signal,
                async onopen(response) {
                    console.log('sse连接已打开'); 
                    connectState.value = true
                    resolve();
                },
                onmessage(message) {
                    connectState.value = true
                    if(message.data){
                        const obj = JSON.parse(message.data)
                        task_id.value = obj.task_id
                        if(obj.event == 'workflow_started'){
                            props.messageItemManager.appendInitialContent(obj.task_id)
                        }
                        if(obj.event=='node_started' || obj.event=='node_finished'){
                            event.value = obj.event
                            role.value.name = obj.data.title
                        }
                        if(obj.event == 'text_chunk' && event.value == 'node_started'){
                            printBotMessage(obj,JSON.parse(JSON.stringify(role.value)),obj.task_id,false)
                        }
                        if(obj.event == 'node_finished' && event.value == 'node_started' && props.messageItemManager.nowMessageHtml.value!=''){
                            
                        } 
                        if(obj.event == 'workflow_finished'){
                            emit('conversationRefresh')
                            printBotMessage(obj,JSON.parse(JSON.stringify(role.value)),obj.task_id,true)
                        }
                    }
                },
                onclose() {
                    connectState.value = false
                    runDisabled.value = false
                    emit("updateRunDisabled", runDisabled.value);
                    console.log('sse连接已断开');
                },
                onerror(err) {
                    console.error('发生错误：', err);
                    ctrl.abort();
                    connectState.value = false
                    runDisabled.value = false
                    emit("updateRunDisabled", runDisabled.value);
                    throw err;
                }
            })
        })
    }

    //发消息 任务型
    const sendMessageTask = (keyList,isClearHistory) => {
        if(connectState.value){
            ElMessage.warning('请等待上条信息响应完成')
            return
        }
        initConnect(keyList,isClearHistory)
    }

    const { toClipboard } = useClipboard();
    //复制
    const copyClick = async item => {
      try {
        await toClipboard(item);
        proxy.$modal.msgSuccess("复制成功");
      } catch (e) {
        console.error(e);
      }
    };

    //关闭会话型连接
    async function closeSourceSessionMethod(){
       await closeSourceSessionTask(currentInstruct.value.appId,task_id.value).then(response => {
            // console.log('sse连接已断开');
        })
    }

    //当前应用新会话
    async function openNewSession(){
        Object.keys(keyList.value).forEach(element => {
            keyList.value[element]=''
        });
        //是否是历史会话记录
        isHistory.value = false
        //历史记录取消选中
        // emit('conversationUnselect')
        props.messageItemManager.new()
    }

    //新会话清除选中应用
    async function openNewSessionClear(){
        if(task_id.value){
            await closeSourceSessionMethod()
        }
        keyList.value = []
        //选中的团队或智能体清空
        currentInstruct.value = null  
        // 是否展示前言 （是）
        prefaceState.value = true
        emit("updatePrefaceState", prefaceState.value);
        //是否是历史会话记录
        isHistory.value = false
        //历史记录取消选中
        emit('conversationUnselect')
        props.messageItemManager.new()
    }

    async function openHistorySessionClear(){
        keyList.value = []
        //选中的团队或智能体清空
        currentInstruct.value = null  
        //是否是历史会话记录
        isHistory.value = true
        props.messageItemManager.new()
    }

    const closeSession = () => {
        openNewSession()
    }

    defineExpose({
        closeSession,
        openNewSessionClear,
        sendMessageTask,
        openHistorySessionClear
    })

    onBeforeUnmount(()=>{
        closeSession()
    })

    //定位到最新内容
    const moveScroll = () => {
        nextTick(()=> {
            recordsBox.value.scrollTop = recordsBox.value.scrollHeight;
        })
    }

    watch(props.messageItemManager.nowMessageHtml, () => {
        moveScroll();
    })

    watch(props.messageItemManager.nowMessageLine, () => {
        moveScroll();
    });

    watch(props.messageItemManager.refMessageItemList, (newValue, oldValue) => {
        moveScroll()
    });

    watch(props.messageItemManager.waitting, () => {
        moveScroll();
    });

</script>
<style lang="scss" scoped>
.container{
    position: relative;
    width: 100%;
    .content{
        padding: 10px 20px;
        text-align: center;
        width: 100%;
        .top{
            height: 80px;
            line-height: 80px;
            font-size: 30px;
            color: #5050E6;
            font-weight: 550;
        }
        .ai-chat-content{
            overflow-y: auto;
            overflow-x: hidden;
            .user-input{
                background-color: white;
                padding:20px 20px 1px 20px;
                border-radius: 10px;
                .key-input{
                    font-size: 15px;
                    display: flex;
                    justify-content: space-between;
                    margin-bottom: 20px;
                    // align-items: center;
                    text-align: left;
                }
            }
            .chat-message{
                display: flex;
                flex-direction: column;
                .user-chat-area{
                    align-self: flex-end;
                    display: flex;
                    flex-direction: column;
                    margin-bottom: 10px;
                    padding-left: 0;
                    .user-box{
                        display: flex;
                        flex-direction: row;
                        justify-content: end;
                        align-items: center;
                        margin-bottom: 3px;
                        .user-name{
                            line-height: 34px;
                            height: 34px;
                            color: #B2B2B2;
                            font-size: 13px;
                        }
                        .user-icon{
                            img{
                                width:30px;
                                height:30px;
                                background-color: #6977F1;
                                border-radius: 5px;
                                border: 1px solid #E6E6E6;
                            }
                            margin-left:4px;
                        }
                    }
                    .user-chat-message{
                        background-color: #5050E6;
                        border-radius: 12px;
                        border-top-right-radius: 0;
                        box-sizing: border-box;
                        color: #fff;
                        // cursor: pointer;
                        display: inline-block;
                        font-size: 14px;
                        line-height: 22px;
                        min-height: 26px;
                        outline: none;
                        padding: 9px 14px;
                        white-space: normal;
                        word-break: break-word;
                    }
                }
                .robot-chat-area{
                    align-items: flex-start;
                    display: flex;
                    flex-direction: column;
                    justify-content: flex-start;
                    margin-bottom: 10px;
                    position: relative;
                    .robot-box{
                        display: flex;
                        flex-direction: row;
                        align-items: center;
                        margin-bottom: 3px;
                        .robot-name{
                            line-height: 34px;
                            height: 34px;
                            color: #B2B2B2;
                            font-size: 13px;
                        }
                        .robot-icon{
                            img{
                                width:30px;
                                height:30px;
                                background-color: #6977F1;
                                border-radius: 5px;
                                border: 1px solid #E6E6E6;
                            }
                                margin-right:4px;
                            // background-color: #dfdfff
                        }
                    }
                    .robot-chat-message{
                        background-color: #F9FBFC;
                        border-radius: 0 12px 12px;
                        max-width: 100%;
                        padding: 12px 14px;
                        // width: 100%;
                        box-sizing: border-box;
                        overflow: hidden;
                        ::v-deep(.github-markdown-body) {
                            padding: 0;
                            font-size: 13px !important;
                            p{
                                margin-bottom: 0;
                            }
                        }
                    }
                }
            }
            .open-question-box{
                margin-top: 20px;
                display: flex;
                flex-direction: column;
                .prologue{
                    border:1px solid #E9EAEE;
                    border-radius: 10px;
                    background-color: white;
                    color: black;
                    padding: 10px 20px;
                    font-size: 15px;
                    text-align:left;
                    margin-bottom: 5px;
                }
                .open-question{
                    border:1px solid #E9EAEE;
                    border-radius: 10px;
                    background-color: white;
                    color: #7A8698;
                    padding: 10px 20px;
                    display: inline-block;
                    font-size: 15px;
                    max-width: 100%;
                    text-align: left;
                    cursor: pointer;
                }
            }
            .record-box-user {
                width: calc(100%);
                display: inline-flex;
                .head-img{
                    margin-left: 10px;
                    img{
                        height: 40px;
                        width: 40px;
                        // border: 2px solid #E6E6E6;
                        border-radius: 8px;
                        background-color: #dfdfff;
                        margin:0;
                    }
                }
            
            }
            .record-box-robot {
                width: calc(100%);
                // margin-left: 10px;
                display: inline-flex;
                .head-img{
                    height: 40px;
                    width: 44px;
                    border-radius: 8px;
                    margin-right: 10px;
                    img{
                        height: 40px;
                        width: 40px;
                        border: 2px solid #E6E6E6;
                        border-radius: 8px;
                        background-color: #dfdfff;
                        margin:0;
                    }
                }
                ::v-deep .el-loading-mask {
                    position: absolute;
                    z-index: 2000;
                    background-color: #f5f0f08f;
                    margin: 0;
                    top: 0;
                    right: 0;
                    bottom: 0;
                    left: 0;
                    transition: opacity var(--el-transition-duration);
                    height: 40px;
                    width: 41px;
                    border-radius: 10px;
                    padding: 7px;
                }
                ::v-deep .el-loading-spinner {
                    top: 50%;
                    margin-top: calc((0px - var(--el-loading-spinner-size)) / 2);
                    width: 100%;
                    text-align: center;
                    position: absolute;
                    width: 41px;
                    height: 40px;
                    /* align-items: center; */
                    display: flex;
                    align-items: center;
                }
                ::v-deep .el-loading-spinner .circular {
                    display: inline;
                    -webkit-animation: loading-rotate 2s linear infinite;
                    animation: loading-rotate 2s linear infinite;
                    height: 23px;
                    width: 23px;
                }
            
            }
            .record-item {
                display: inline-block;
                border-radius: 10px;
                background-color: #fff;
                padding: 8px 12px;
                width: 100%;
                margin-bottom: 10px;
                font-size: 15px;
            }
            .user-record-box {
                @extend .record-box-user;
                justify-content: flex-end;
                text-align: right;
                float: right;
            }
            .robot-record-box {
                @extend .record-box-robot;
                text-align: left;
            }
            .user-name{
                line-height: 20px;
                height: 20px;
                color: #B2B2B2;
                font-size: 12px;
            }
            .robot-record-item {
                @extend .record-item;
                color: #032220;
                background-color: white;
                border: 1px solid #EAECF0;
                font-size: 14px;
                font-weight: 500;
                position:relative;
            }
            .stop-responding{
                border: 1px solid #E9EAEE;
                border-radius: 10px;
                background-color: white;
                color: #5050E6;;
                padding: 10px 20px;
                display: flex;
                font-size: 15px;
                align-items: center;
                justify-content: center;
                cursor: pointer;
            }
            .user-record-item {
                @extend .record-item;
                color: white;
                background-color: #5050e6;
                text-align: left;
                font-size: 14px;
                font-weight: 500;
            }
            .user-content{
                display: flex;
                flex-direction: column;
                align-items: end;
                width: 100%;
            }
            .robot-content {
                display: flex;
                flex-direction: column;
                align-items: start;
                width: 100%;
            }
            .process-status{
                .status{
                    background-color: #F9FBFC;
                    padding: 10px;
                    border-radius: 10px;
                    margin-bottom: 5px;
                    color: #19CD56;
                    display: flex;
                    align-items: center;
                    cursor: pointer;
                }
                .process{
                    color: #032220;
                    background-color: #F9FBFC;
                    margin-bottom: 5px;
                    border-radius: 10px;
                    width: calc( 100% - 60px );
                    .process-top{
                        display: flex;
                        justify-content: space-between;
                        align-items:center;
                        font-size:18px;
                        padding: 8px 12px;
                        background-color: white;
                        border-top-left-radius: 10px;
                        border-top-right-radius: 10px;
                        cursor: pointer;
                        .process-left{
                            display: flex;
                            align-items: center;
                        }
                    }
                    .process-content{
                        padding: 5px;
                        .process-title{
                            display: flex;
                            align-items: center;
                            // margin-left: 5px;
                            padding: 10px;
                            background-color: white;
                            border-radius: 10px;
                            cursor: pointer;
                            img{
                                height: 20px;
                                width: 20px;
                                margin-right: 10px;
                            }
                            &:hover{
                                background-color: #F0F0F5;
                            }
                        }
                        .process-child-status{
                            background-color: #F7F7FA;
                            padding: 5px 15px;
                            font-size: 13px;
                            line-height: 20px;
                            border-bottom-right-radius: 10px;
                            border-bottom-left-radius: 10px;
                        }
                    }
                    .process-bottom{
                        background-color: #F9FBFC;
                        padding: 10px;
                        border-bottom-left-radius: 10px;
                        border-bottom-right-radius: 10px;
                        .bottom-content{
                            width: 100px;
                            padding: 5px;
                            font-size: 12px;
                            background-color: #ECF7EC;
                            color: #32A252;
                            display: flex;
                            justify-content: center;
                            border-radius: 5px;
                        }
                    }
                }
            }
            .operation{
                display: flex;
                // justify-content: end;
                width: calc( 100% - 60px );
                margin-bottom: 10px;
                margin-top: -5px;
            }
            .knowledge-list{
                color: #032220;
                background-color: #F9FBFC;
                width: calc( 100% - 60px );
                padding: 8px 12px;
                border-radius: 10px;
            }
            .choices{
                background-color: #F5F6F7;
                // min-width: 150px;
                padding: 10px;
                display: flex;
                align-items: center;
                justify-content: left;
                border-radius: 15px;
                border: 1px solid #E8E9ED;
                margin-bottom: 5px;
                color: #032220;
                font-size: 14px;
                font-weight: 500;
                cursor: pointer;

                &:hover{
                    background-color: #ececec;
                }
            }
        } 
        .bottom-area{
            background-color: transparent;
            padding-top: 10px;
            bottom: 0;
            // position: absolute;
            text-align: center;
            width: 100%;
            z-index: 11;
            .bar{
                border-top: 1px solid transparent;
                background-color: transparent;
                box-sizing: border-box;
                padding: 0 16px;
                width: 100%;
                align-items: center;
                display: flex;
                justify-content: flex-start;
                .tools-list{
                    background: transparent;
                    display: flex;
                    height: 48px;
                    padding-top: 10px;
                    .tools-item{
                        background: #fff;
                        box-shadow: 1px 1px 5px 0 #d9d9ff;
                        align-items: center;
                        border-radius: 8px;
                        cursor: pointer;
                        display: flex;
                        height: 30px;
                        margin-right: 8px;
                        outline: none;
                        padding: 6px 8px;
                        .tool-icon{
                            background-repeat: no-repeat;
                            background-size: cover;
                            display: inline-block;
                            height: 20px;
                            width: 20px;
                        }
                        .tool-name{
                            color: #1c1f1e;
                            font-family: PingFangSC-Regular;
                            font-size: 12px;
                            line-height: 12px;
                            margin-left: 6px;
                        }
                    }
                }
            }
            .submit-area{
                // padding: 0 16px 12px;
                box-sizing: border-box;
                width: 100%;
                .content-area{
                    background-image: linear-gradient(90deg, #5050E6, #b6b6ff);
                    border-radius: 12px;
                    box-shadow: 0 0 12px 0 rgba(0,155,109,.15);
                    box-sizing: border-box;
                    height: 100%;
                    padding: 2px;
                    width: 100%;
                    .user-input-area{
                        border: 0 !important;
                        border-radius: 10px;
                        padding: 8px 10px;
                        position: relative;
                        transition: opacity .5s;
                        width: 100%;
                        background-color: #fff;
                        .top-area{
                            display: flex;
                            position: relative;
                            .text-area{
                                line-height: 1.4;
                                flex: 1;
                                font-size: 14px;
                                overflow: hidden;
                                position: relative;
                                .tool-tip{
                                    align-content: center;
                                    align-items: center;
                                    background-color: #f0f2f2;
                                    border-radius: 4px;
                                    display: flex;
                                    height: 26px;
                                    line-height: 26px;
                                    padding-left: 10px;
                                    padding-right: 0;
                                    cursor: text;
                                    left: 0;
                                    position: absolute;
                                    top: 0;
                                    font-size: 14px;
                                }
                                .user-input{
                                    font-size: 14px;
                                    height: 62px;
                                    border: none;
                                    box-sizing: border-box;
                                    color: #222;
                                    line-height: 20px;
                                    outline: 0;
                                    overflow-y: auto;
                                    width: 100%;
                                }
                                .show-tool-tip{
                                    line-height: 26px;
                                }
                            }
                            textarea{
                                font: 100% arial, helvetica, clean;
                                overflow: auto;
                                vertical-align: top;
                                resize: none;
                            }
                        }
                        .bottom-info-area{
                            display: flex;
                            justify-content: flex-end;
                            margin-top: 5px;
                            .send-btn{
                                background: linear-gradient(316deg, #5050E6 16.71%, #5050E6 116.53%);
                                border-radius: 10px;
                                height: 26px;
                                position: relative;
                                width: 36px;
                                &:after{
                                    background: url(https://edu-wenku.bdimg.com/v1/pc/aigc/presentation-sample/send-1702458556573.svg) no-repeat;
                                    background-size: cover;
                                    content: "";
                                    height: 16px;
                                    position: absolute;
                                    top: 50%;
                                    transform: translate(-50%, -50%);
                                    width: 16px;
                                }
                            }
                            .disabled{
                                cursor: not-allowed;
                            }
                        }
                    }
                }
            }
            .tip{
                line-height: 25px;
                font-size: 12px;
                text-align: left;
                color: #9E9EBB;
            }
        } 
    }
    .coverage-area{
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: calc(100vh - 190px);
        background-color: #fffffff5;
        padding: 20px;
        .title{
            display: flex;
            align-items: center;
            font-size: 19px;
            font-weight: 600;
        }
        .describe{
            margin-top: 20px;
            font-size: 15px;
            font-weight: 500;
        }
        .buttons{
            margin-top: 20px;
            display: flex;
            justify-content: end;
        }
    }
}

</style>
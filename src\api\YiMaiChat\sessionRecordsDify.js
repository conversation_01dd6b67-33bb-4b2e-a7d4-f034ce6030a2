import request from '@/utils/difyRequest'

//获取团队编排相关信息
export function getCurrentInstructTeam(app_id) {
    return request({
        url: 'console/api/apps/'+app_id+'/parameters',
        method: 'get',
    })
}

//关闭会话型团队
export function closeSourceSession(app_id,task_id) {
    return request({
        url: 'console/api/apps/'+app_id+'/team_chat_messages/'+task_id+'/stop',
        method: 'post',
    })
} 
//关闭任务型团队
export function closeSourceSessionTask(app_id,task_id) {
    return request({
        url: 'console/api/apps/'+app_id+'/workflows/tasks/'+task_id+'/stop',
        method: 'post',
    })
}

//获取推荐问题
export function getSuggestedQuestions(app_id,message_id) {
    return request({
        url: 'console/api/apps/'+app_id+'/messages/'+message_id+'/suggested-questions',
        method: 'get',
    })
}

//重命名会话
export function conversationRename(app_id,conversation_id,data) {
    return request({
        url: 'console/api/apps/'+app_id+'/conversations/'+conversation_id+'/name',
        method: 'post',
        data
    })
}

//获取历史记录
export function conversationHistory(app_id,conversation_id) {
    return request({
        url: 'console/api/apps/'+app_id+'/messages?conversation_id='+conversation_id,
        method: 'get'
    })
}

//团队会话型会话停止响应
export function stopResponding(app_id,task_id) {
    return request({
        url: 'console/api/apps/'+app_id+'/team_chat_messages/'+task_id+'/stop',
        method: 'post'
    })
}

//保存过程输入
export function saveUserMessage(data) {
    return request({
        url: 'console/api/apps/save-user-message',
        method: 'post',
        data
    })
}
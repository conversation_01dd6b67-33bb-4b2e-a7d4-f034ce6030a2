<template>
    <div class="node-label" :class="{'hide-border': !isEditLabel}">
        <el-input ref="labelInput" v-model="_modelValue" size="small" 
            @click="isEditLabel = true" @blur="isEditLabel = false" v-bind="$attrs"/>
    </div>
</template>
<script setup>
import { onMounted } from 'vue';


const props = defineProps({
    modelValue:{
        type:String
    }
})

const emit = defineEmits(['update:modelValue']);

const isEditLabel = ref(false)

const _modelValue = ref('');

watch(_modelValue, (newValue) => {
  emit('update:modelValue', newValue);
});

onMounted(() => {
    _modelValue.value = props.modelValue;
})


</script>
<style lang="scss" scoped>

.node-label{
    margin: 0 10px 0 5px;
    ::v-deep(.el-input__inner){
        color: black;
        font-weight: 600;
    }
    ::v-deep(.el-input__wrapper){
        padding-left: 0;
    }
}
.hide-border{
    ::v-deep(.el-input__wrapper){
        box-shadow: none !important;
    }
}

</style>
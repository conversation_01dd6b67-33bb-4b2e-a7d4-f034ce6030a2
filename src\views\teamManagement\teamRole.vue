
<template>
    <div class="app-container">
       <el-form :model="queryParams" ref="queryRef" v-show="false && showSearch" :inline="true">
          <el-form-item label="角色名称" prop="roleName">
             <el-input
                v-model="queryParams.roleName"
                placeholder="请输入角色名称"
                clearable
                style="width: 240px"
                @keyup.enter="handleQuery"
             />
          </el-form-item>
          <el-form-item>
             <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
             <el-button icon="Refresh" @click="resetQuery">重置</el-button>
          </el-form-item>
       </el-form>
 
       <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
             <el-button
                type="primary"
                plain
                icon="Plus"
                @click="openSelectRole"
             >添加授权角色</el-button>
          </el-col>
          <el-col :span="1.5">
             <el-button 
                type="warning" 
                plain 
                icon="Close"
                @click="handleClose"
             >关闭</el-button>
          </el-col>
       </el-row>
 
       <el-table v-loading="loading" :data="roleList" >
          <el-table-column label="角色名称" prop="roleName" :show-overflow-tooltip="true" />
           <el-table-column label="角色权限" prop="roleKey" :show-overflow-tooltip="true" />
           <el-table-column label="备注" prop="remark" :show-overflow-tooltip="true" />
          <el-table-column label="操作" align="center" 
            class-name="small-padding fixed-width" width="120px">
             <template #default="scope">
               <el-button link type="primary" icon="CircleClose" @click="cancelAuthUser(scope.row)" >
                  取消授权
               </el-button>
             </template>
          </el-table-column>
       </el-table>
 
       <pagination
          v-show="total > 0"
          :total="total"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
       />
       <select-role ref="selectRef" :teamId="route.params.teamId" @ok="handleQuery" />
    </div>
 </template>
 
 <script setup name="RoleApp">
 import selectRole from "./components/selectRole";
 import { getRoleList,authTeamCancelRole } from "@/api/teamManagement/teamRole";
 
import {useRoute,useRouter} from 'vue-router'
 const route = useRoute();
 const router = useRouter();
 const { proxy } = getCurrentInstance();
 
 const roleList = ref([]);
 const loading = ref(true);
 const showSearch = ref(true);
 const total = ref(0);
 const selectRef = ref(null)
 
 const queryParams = reactive({
   pageNum: 1,
   pageSize: 10,
 });
 
 /** 查询授权用户列表 */
 function getList() {
   loading.value = true;
   const params = {
      ...queryParams,
      applicationId:route.params.teamId,
      applicationType:'team'
   }
   getRoleList(params).then(response => {
     roleList.value = response.rows;
     total.value = response.total;
     loading.value = false;
   });
 }
 // 返回按钮
 function handleClose() {
   router.back()
 }
 /** 搜索按钮操作 */
 function handleQuery() {
   queryParams.pageNum = 1;
   getList();
 }
 /** 重置按钮操作 */
 function resetQuery() {
   proxy.resetForm("queryRef");
   handleQuery();
 }
 /** 打开授权用户表弹窗 */
 function openSelectRole() {
   selectRef.value.show();
 }
 /** 取消授权按钮操作 */
 function cancelAuthUser(row) {
   const params = {
      roleId:row.roleId,
      applicationId:route.params.teamId,
      applicationType:'team'
   }
   proxy.$modal.confirm('确认要取消授权该角色本应用吗？').then(function () {
     return authTeamCancelRole(params);
   }).then(() => {
     getList();
     proxy.$modal.msgSuccess("取消授权成功");
   }).catch(() => {});
 }
 
 getList();
 </script>
 <style lang="scss">
 .is-dark{
     max-width:500px!important;
     color: black;
 }
 </style>
 
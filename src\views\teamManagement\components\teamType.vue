<template>
    <el-radio-group v-model="_modelValue" class="team-type">
        <template v-for="option in options">
            <el-radio :label="option.value" border :disabled="disabled && option.value != props.modelValue">
                <img height="30" width="30" :src="option.value == 'advanced-chat'?chatBotImg:workflowImg"/>
                <div style="margin-left: 10px;">
                    {{option.label}}
                </div>
            </el-radio>
        </template>
    </el-radio-group>
</template>
<script setup>
import { onMounted } from 'vue';
import chatBotImg from '@/assets/images/chat_bot.png'
import workflowImg from '@/assets/images/workflow.png'

const props = defineProps({
    modelValue:{
        type:String
    },
    disabled:{ 
        type:Boolean,
        default:false
    }
})

const options = [
    {
        label:'聊天助手',
        value:'advanced-chat'
    },
    {
        label:'工作流',
        value:'workflow'
    }
]

const emit = defineEmits(['update:modelValue']);

const _modelValue = ref('');

watch(_modelValue, (newValue) => {
  emit('update:modelValue', newValue);
});

onMounted(() => {
    _modelValue.value = props.modelValue?props.modelValue:'advanced-chat';
})

</script>
<style lang="scss" scoped>

.team-type{
    width: 100%;
    ::v-deep(.el-radio){
        height: 60px;
        width: calc(50% - 16px);
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
    }
    ::v-deep(.el-radio__input){
        display: none;
    }
    ::v-deep(.el-radio__label){
        display: flex;
        flex-direction: row;
        justify-content: start;
        align-items: center;
        color: rgb(96, 98, 102) !important;
        width: 100px;
    }
    .item{

    }
}

</style>
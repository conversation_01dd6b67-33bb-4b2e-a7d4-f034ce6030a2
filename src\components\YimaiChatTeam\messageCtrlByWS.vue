<template>
    <div class="message-send" v-show="chatUrl != ''">
        <!-- <el-button :icon="Clock" text></el-button> -->
        <div class="input-content" v-show="true">
            <el-input maxlength="1000" 
                :disabled="isDisabledSend"
                :placeholder="'您可以向我提任何问题'"
                @keyup.enter="sendMsgHandle" 
                resize="none" 
                :autosize="{ minRows: 1, maxRows: 5 }"
                v-model="inputContent">
                <template #suffix>
                    <el-button link :disabled="isDisabledSend" :icon="Promotion" @click="sendMsgHandle" />
                </template>
            </el-input>
        </div>
        <!-- <el-button :icon="CirclePlus" text></el-button> -->
    </div>
</template>
<script setup>
import "highlight.js/styles/atom-one-light.css";
import 'element-plus/es/components/message/style/css';
import { ElMessage } from 'element-plus';
import { ref, defineProps } from 'vue';
import { Clock,Promotion,CirclePlus} from '@element-plus/icons-vue';
import {TaskQueue} from '@/types/taskQueue'

let ws = null;

const props = defineProps({
    messageItemManager:{
        type:Object,
        required: false
    },
    chatUrl:{
        type:String,
        required: false,
        default:""
    },
    initMessage:{
        type:Object,
        required: false,
        default:{}
    },
    isDisabledSendSet:{
        type:Boolean,
        required: false,
        default:true
    },
});

const inputContent = ref('');
const isDisabledSend = ref(false)
const isMessageEnding = ref(false)
const taskQueue = new TaskQueue()
let timeres = []

taskQueue.setOnComplete(() => {
    if(!taskQueue.running && !isMessageEnding.value && ws.readyState == 1 && taskQueue.index == taskQueue.tasks.length ){
        props.messageItemManager.clearNowMessageRole();
        props.messageItemManager.do();
        props.messageItemManager.waittingStart();
        taskQueue.clearTasks()
    }else{
        inputContent.value = ''
        if(ws.readyState == 3){
            props.messageItemManager.clearNowMessageRole();
        }
        // isDisabledSend.value = false
        // if(props.isDisabledSendSet){
        //     isDisabledSend.value = false
        // }
    }
    if(ws.readyState == 2 || ws.readyState == 3 || ws.readyState == 0){
        inputContent.value = ''
        isDisabledSend.value = true
    }
})

function sendMsgHandle() {
    if (inputContent.value.trim() === '') {
        ElMessage.warning("你要发送什么呢？")
        return;
    }
    let obj = {
        message:inputContent.value
    }
    customSend(obj);
}

//弃用
const wsSend = (message,needPushUserMessage = true) => {
    props.messageItemManager.do();
    props.messageItemManager.waittingStart();
    clearTasks()
    if(needPushUserMessage){
        props.messageItemManager.pushUserMesage(inputContent.value);
    }
    ws = new WebSocket(props.chatUrl);
    isDisabledSend.value = true
    ws.addEventListener('open', () => {
        ws.send(JSON.stringify(message))
    });
    ws.addEventListener('message', (event) => {
        const message = event.data;
        let obj = JSON.parse(message)
        // console.log(obj)
        taskQueue.addTask(printBotMessage,obj)
    });
    ws.addEventListener('close', () => {
        props.messageItemManager.waittingEnd();
        inputContent.value = ''
            isDisabledSend.value = true
        console.log('WebSocket连接已断开');
    });
    ws.addEventListener('error', (error) => {
            isDisabledSend.value = false
        // if(props.isDisabledSendSet){
        //     isDisabledSend.value = false
        // }
        console.error('发生错误：', error);
    });
}

const printBotMessage = (message) => {
    return new Promise((resolve) => {
        props.messageItemManager.do();
        props.messageItemManager.waittingEnd();
        let role = {
            name:message.role,
            key:message.agentId
        };
        if(message.key=='text' || message.key=='tips'){
            let txt = message.messages.split('')
            let tempTxt = ''
            txt.forEach((item,index) => {
                let timer = setTimeout(() => {
                    props.messageItemManager.appendNowMessageContent(tempTxt += item,role,false);
                    if(index == txt.length-1){
                        props.messageItemManager.deleteLastMsgRecord();
                        props.messageItemManager.messageReceiveDone();
                        resolve(message)
                    }
                }, 30 * index );
                timeres.push(timer) 
            })
        }else if(message.key=='image'){
            let imgs = message.messages
            let tempImgs = ''
            imgs.forEach(element => {
                if(element){
                    tempImgs+='<div style="display: inline-block;width: 18%;margin-right:5px"><img src="'+element+'" /></div>'
                }
            });
            props.messageItemManager.appendNowMessageContent(tempImgs,role,false);
            props.messageItemManager.deleteLastMsgRecord();
            props.messageItemManager.messageReceiveDone();
            resolve(message)
        }
    })
}

const clearTasks = () => {
    taskQueue.clearTasks()
}

const clearTimeres = () => {
    timeres.forEach(element => {
        clearTimeout(element)
    });
    timeres = []
}

const customSend = (message,needPushUserMessage = true) => {
    props.messageItemManager.do();
    props.messageItemManager.waittingStart();
    clearTasks()
    isDisabledSend.value = true;
    if(needPushUserMessage){
        props.messageItemManager.pushUserMesage(inputContent.value);
    }
    ws.send(JSON.stringify(message))
}

const initConnect = () => {
    return new Promise((resolve,reject) => {
        isDisabledSend.value = true;
        ws = new WebSocket(props.chatUrl);
        isDisabledSend.value = true;
        ws.addEventListener('open', () => {
            console.log('WebSocket连接已打开');
            isDisabledSend.value = false;
            resolve();
        });
        ws.addEventListener('message', (event) => {
            props.messageItemManager.waittingEnd();
            const message = event.data;
            let obj = JSON.parse(message)
            if(obj.status == 0){
                isMessageEnding.value = false;
            }else{
                inputContent.value = ''
                    isDisabledSend.value = false
                // if(props.isDisabledSendSet){
                //     isDisabledSend.value = false
                // }
                isMessageEnding.value = true;
            }
            // console.log(obj)
            taskQueue.addTask(printBotMessage,obj)
        });
        ws.addEventListener('close', () => {
                isDisabledSend.value = true
            // if(taskQueue.tasks.length == 0){
            //     printBotMessage({
            //         // agentId:'系统消息',
            //         // role:'系统消息',
            //         messages:'抱歉，我无法给出回答',
            //         key:'text'
            //     })
            //     inputContent.value = ''
            //     if(props.isDisabledSendSet){
            //         isDisabledSend.value = false
            //     }
            // }
            console.log('WebSocket连接已断开');
        });
        ws.addEventListener('error', (error) => {
                isDisabledSend.value = true
            // if(props.isDisabledSendSet){
            //     isDisabledSend.value = false
            // }
            console.error('发生错误：', error);
        });
    })
    
}

// initConnect()

defineExpose({
    wsSend,
    clearTasks,
    clearTimeres,
    customSend,
    initConnect
})

</script>

<style lang="scss" scoped >

$btn-box-right-width: 100px;
$btn-box-left-width: 45px;

.message-send {
  display: flex;
  align-items: center;
  justify-content: center;
//   padding: 10px;
  height: calc(100vh - 830px);

  .input-content {
    margin: 0 10px;
    width: calc(100% - $btn-box-right-width - $btn-box-left-width);
  }

  .loading-box {
    height: 100%;
    margin-left: 10px;
  }

  .btn-box {
    position: relative;
    height: 31px;
    .icon-svg {
      height: 30px;
      margin: 0 8px;
      cursor: pointer
    }
  }

  .btn-box-right {
    width: $btn-box-right-width;
    text-align: right;
  }

  .btn-box-left {
    width: $btn-box-left-width;
    text-align: left;
  }

}
</style>
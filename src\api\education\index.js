import request from '@/utils/request'
import pptRequest from '@/utils/pptRequest'
// 查询历史教案
export function getPlane (params) {
    return request({
        url: '/teaching/plane/list',
        method: 'get',
        params
    })
}
// 查询历史教案数量
export function getPlaneLength (params) {
    return request({
        url: '/teaching/plane/count',
        method: 'get',
        params
    })
}
//删除历史教案
export function deletePlane (data) {
    return request({
        url: '/teaching/plane/delete/',
        method: 'post',
        data
    })
}

// 查询知识点树状图
export function getKnowledgePoints (params) {
    return request({
        url: '/teaching/plane/knowledgePoints',
        method: 'get',
        params
    })
}

// AI扩写查询知识点
export function getKnowledgePointsAI (params) {
    return request({
        url: '/knowledge/knowLedgelistAll',
        method: 'get',
        params
    })
}

// 权限编辑获取某角色知识点列表  
export function getRoleKnowledgeTreeselect (roleId) {
    return request({
        url: '/system/knowledge/roleKnowledgeTreeselect/' + roleId,
        method: 'get'
    })
}

// 保存教案
export function savePlane (data) {
    return request({
        url: '/teaching/plane/save/',
        method: 'post',
        data
    })
}

//资源上传
export function addResource (data) {
    return request({
        url: '/resource/resource/uniteupload',
        method: 'post',
        data
    })
}

// 获取ppt签名
export function getpptResource (params) {
    return request({
        url: '/ppt/code',
        method: 'get',
        params
    })
}

// 任务创建
export function taskCreation (data) {
    return pptRequest({
        url: '/api/ai/chat/v2/task',
        method: 'post',
        data
    })
}

// 教案详情
export function planeDetail (params) {
    return request({
        url: '/teaching/plane/one',
        method: 'get',
        params
    })
}

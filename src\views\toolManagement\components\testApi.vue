<template>
    <div>
        <el-button size="small" @click="openTestApiDialog()">测试</el-button>
        <el-dialog v-model="dialogVisible" :title="`测试${toolInfo.tool_name}`" width="600px" append-to-body>
            <div style="height: 700px">
                <config-credentials :credentials="toolInfo.credentials" @change:credentials="updateCredentials">
                </config-credentials>
                <div>
                    <div class="label">参数和值</div>
                    <el-table :data="toolInfo.parameters" border>
                        <el-table-column prop="name" label="参数" width="165px"></el-table-column>
                        <el-table-column label="值">
                            <template #default="scope">
                                <no-border-input v-model="params[scope.row.name]" />
                            </template>
                        </el-table-column>
                    </el-table>
                </div>
                <el-button type="primary" size="large" style="margin-top: 1rem; width: 100%" @click="startTest">测试</el-button>
                <div style="margin-top: 1.5rem">
                    <div style="display: flex;align-items: center;">
                        <div style="
                            font-size: 0.75rem;
                            font-weight: 600;
                            line-height: 18px;
                            color: rgb(102, 112, 133);
                            ">
                            测试结果
                        </div>
                        <div style="height: 1;width: 0;flex-grow: 1;margin-left: .75rem;"></div>
                    </div>
                    <div style="
                        margin-top: 0.5rem;
                        height: 200px;
                        overflow-y: auto;
                        overflow-x: hidden;
                        border-radius: 0.45rem;
                        background-color: rgb(242, 244, 247);
                        padding: 0.5rem 0.75rem;
                        font-size: 0.75rem;
                        font-weight: 400;
                        line-height: 1rem;
                        color: rgb(71, 84, 103);
                        ">
                        {{ testResult || '测试结果将显示在这里' }}
                    </div>
                </div>
            </div>
        </el-dialog>
    </div>
</template>
<script setup>
import configCredentials from "./configCredentials";
import NoBorderInput from "@/components/YimaiFlow/components/common/noBorderInput";
import {testTool} from "@/api/newTool/index"

const dialogVisible = ref(false);

const props = defineProps({
    toolInfo: {
        type: Object,
        default: () => ({}),
    },
});

const toolInfo = ref({});

const params = ref({});

const testResult = ref("");

const updateCredentials = (val) => {
    toolInfo.value.credentials = val;
};

const openTestApiDialog = () => {
    toolInfo.value = { ...props.toolInfo };
    props.toolInfo.parameters.forEach((item) => {
        params.value[item.name] = item.default;
    });
    dialogVisible.value = true;
};

const startTest = () => {
    const data = {
        ...toolInfo.value,
        parameters: params.value,
    }
    testTool(data).then(res => {
        testResult.value = res.error || res.result
    })
}

</script>
<style lang="scss">
.label {
    padding: 0.5rem 0;
    font-size: 0.875rem;
    font-weight: 500;
    color: rgb(16, 24, 40);
    line-height: 1.25rem;
}

.test-result {
    display: flex;
    align-items: center;
    margin-top: 10px;

    .result-label {
        font-size: 0.75rem;
        font-weight: 600;
        line-height: 18px;
        color: rgb(102, 112, 133);
    }

    .result-content {
        margin-top: 0.5rem;
        height: 200px;
        overflow-y: auto;
        overflow-x: hidden;
        border-radius: 0.45rem;
        background-color: rgb(242, 244, 247);
        padding: 0.5rem 0.75rem;
        font-size: 0.75rem;
        font-weight: 400;
        line-height: 1rem;
        color: rgb(71, 84, 103);
    }
}
</style>

<template>
    <div class="title-area">
        <div class="icon">
            <template v-if="tool.type === 'builtin'">
                <div class="img" :style="{backgroundImage:`url(${tool.icon})`}"></div>
            </template>
            <template v-else>
                <span :style="{backgroundColor:tool.icon.background}" class="emoji">
                    <single-emoji :content="tool.icon.content"></single-emoji>
                </span>
            </template>
        </div>
        <div class="text">
            <div class="name">
                <div style="overflow: hidden;white-space: nowrap;text-overflow: ellipsis;">{{ tool.tool_name }}</div>
            </div>
            <div class="author">
                <div style="overflow: hidden;text-overflow: ellipsis;display: -webkit-box;
                    -webkit-box-orient: vertical;-webkit-line-clamp: 2;">
                    {{ tool.desc }}
                </div>
            </div>
        </div>
    </div>
</template>
<script setup>
import singleEmoji from '@/views/toolManagement/components/singleEmoji'

const props = defineProps({
    tool: {
        type: Object,
        default: () => ({})
    }
})

</script>
<style lang="scss" scoped>
.title-area{
    display: flex;
    // align-items: center;
    padding-top: .65rem;
    margin-bottom: .65rem;
    height: 60px;
    max-height: calc(140px - 42px);
    width: 200px;
    flex-shrink: 0;
    flex-grow: 0;
    gap: .75rem;
    .icon{
        position: relative;
        flex-shrink: 0;
        .img{
            height: 2rem;
            width: 2rem;
            background-size: cover;
            background-position: 50%;
            background-repeat: no-repeat;
            border-radius: .375rem;
        }
        .emoji{
            height: 2rem;
            width: 2rem;
            border-radius: .5rem;
            display: flex;
            position: relative;
            align-items: center;
            justify-content: center;
            line-height: 1.75rem;
        }
    }
    .text{
        width: 0;
        flex-grow: 1;
        padding: 1px 0;
        .name{
            font-weight: 600;
            color: rgb(29, 41, 57);
            line-height: 1.25rem;
            font-size: 1rem;
            display: flex;
            align-items: center;
        }
        .author{
            color: rgb(102, 112, 133);
            line-height: 16px;
            font-weight: 500;
            font-size: 12px;
            display: flex;
            align-items: center;
        }
    }
}
</style>
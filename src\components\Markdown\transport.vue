<template>
  <div
    class="transport"
    ref="fromElRef"
    :style="{
      top: `${fromElTop}px`, 
      visibility: fromElTop === -1 ? 'hidden' : 'visible'
    }">
    <slot></slot>
  </div>
</template>

<script setup>
import { onBeforeUnmount, onMounted } from 'vue';

const props = defineProps({
  to: {
    type: String,
    required: true
  }
})

const fromElRef = ref(null)

// -------------调整目标元素高度--------------
const fromElHeight = ref(0)
const styleEl = document.createElement('style')
styleEl.setAttribute('type', 'text/css')
let heightTimer = 0

watch(fromElHeight, () => {
  styleEl.textContent = `#${props.to} {height: ${fromElHeight.value}px;}`
}, {immediate: true})

onMounted(() => {
  document.head.appendChild(styleEl)

  heightTimer = setInterval(() => {
    if (fromElRef.value) {
      fromElHeight.value = fromElRef.value.getBoundingClientRect().height
    }
  }, 500)
})

onBeforeUnmount(() => {
  clearInterval(heightTimer)

  styleEl.remove(styleEl)
})

// -------------调整源元素的位置--------------
const fromElTop = ref(-1)

function setFromElTop() {
  const fromEl = fromElRef.value
  const toEl = document.getElementById(props.to)

  if (!fromEl || !toEl) {
    return
  }

  const fromRect = fromEl.getBoundingClientRect()
  const toRect = toEl.getBoundingClientRect()

  fromElTop.value += toRect.top - fromRect.top
}

let topTimer = 0
onMounted(() => {
  topTimer = setInterval(() => {
    setFromElTop()
  }, 300)
})
onBeforeUnmount(() => {
  clearInterval(topTimer)
})
</script>

<style scoped>
.transport {
  width: 100%;
  position: absolute;
  left: 0;
}
</style>
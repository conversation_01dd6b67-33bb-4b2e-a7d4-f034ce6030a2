<template>
  <div
    :class="classObj"
    :style="{
      background: startGenerating ? 'rgba(0, 0, 0, 0.08)' : 'transparent',
    }"
  >
    <div class="title">
      {{ isOpen ? "研究目录" : "" }}
      <div class="headerBtn">
        <div
          class="refresh"
          @click="refreshSideBar"
          title="刷新目录"
          :style="{ cursor: startGenerating ? 'not-allowed' : 'pointer' }"
          v-if="pageType !== 'view'&&isOpen"
        >
          <i class="ri-refresh-line"></i>
        </div>
        <div class="toggle" @click="toggleSideBar">
          <el-icon><Fold /></el-icon>
        </div>
      </div>
    </div>

    <el-scrollbar view-class="scrollBox">
      <el-menu
        :collapse="!isOpen"
        :text-color="startGenerating ? '#999999' : '#467787'"
        active-text-color="#01DBF5"
        :unique-opened="false"
        :collapse-transition="false"
        mode="vertical"
        popper-class="popperBox"
        class="menu"
        v-show="isOpen"
        :default-active="activeMenu"
        :props="{
          label: 'name',
          value: 'id',
          children: 'children',
        }"
      >
        <sidebar-item
          v-for="(route, index) in menuList"
          :key="index"
          :item="route"
          :collapse="!isOpen"
        />
      </el-menu>
    </el-scrollbar>
  </div>
</template>

<script setup>
import SidebarItem from "./SidebarItem";
import router, { constantRoutes } from "@/router";
import collapseIcon from "@/assets/logo/collapseIcon.png";
import {
  menuList,
  activeMenu,
  pageType,
  startGenerating,
  paragraphs,
} from "@/views/briefGeneration/add/create/js/sharedState.js";
import { refreshSideBar } from "@/views/briefGeneration/add/create/js/paragraphUtils.js";
import { onMounted } from "vue";
const route = useRoute();
const isOpen = ref(true);
const classObj = computed(() => ({
  hideSidebar: !isOpen.value,
  openSidebar: isOpen.value,
  menuContainer: true,
}));
function toggleSideBar() {
  console.log("object :>> ", isOpen.value);
  isOpen.value = !isOpen.value;
}
onMounted(() => {
  activeMenu.value = null;
});
// function getAllSectionIds(menuList) {
//   const ids = [];
//   menuList.forEach((item) => {
//     ids.push(item.value.replace(/^\//, ""));
//     if (item.children) {
//       item.children.forEach((child) =>
//         ids.push(child.value.replace(/^\//, ""))
//       );
//     }
//   });
//   return ids;
// }

// function onScroll() {
//   const ids = getAllSectionIds(menuList.value);
//   let found = "";
//   for (let i = 0; i < ids.length; i++) {
//     const el = document.getElementById(ids[i]);
//     if (el) {
//       const rect = el.getBoundingClientRect();
//       // 80 可根据实际内容区顶部偏移调整
//       if (rect.top <= 80) {
//         found = ids[i];
//       }
//     }
//   }
//   if (found) {
//     activeMenu.value = "/" + found;
//   }
// }
// onMounted(() => {
//   if (scrollRef?.value) {
//     scrollRef.value.addEventListener("scroll", onScroll, true);
//   } else {
//     window.addEventListener("scroll", onScroll, true);
//   }
// });
// onUnmounted(() => {
//   if (scrollRef?.value) {
//     scrollRef.value.removeEventListener("scroll", onScroll, true);
//   } else {
//     window.removeEventListener("scroll", onScroll, true);
//   }
// });
</script>
<style lang='scss' scoped>
.menuContainer {
  height: 100%;
  width: 232px !important;
  transition: width 0.28s;
  padding: 0 5px;
  .el-scrollbar {
    height: 100% !important;
    flex: 1;
    min-height: 0;
    --el-menu-base-level-padding:10px;
    --el-menu-level-padding:10px;
  }
  display: flex;
  flex-direction: column;
  position: relative;
  .title {
    line-height: 20px;
    font-size: 14px;
    color: #6af6ff;
    font-family: PingFangSC-regular;
    padding-top: 12px;
    padding-bottom: 8px;
    padding-left: 10px;
    border-bottom: 1px solid #6af6ff;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .headerBtn {
      display: flex;
      justify-content: space-between;
      align-items: center;
      .toggle {
        cursor: pointer;
        font-size: 24px;
      }
      .refresh {
        cursor: pointer;
        font-size: 22px;
        margin-right: 12px;
      }
    }
  }
  :deep(.nest-menu .el-sub-menu > .el-sub-menu__title:hover),
  :deep(.el-menu-item:hover),
  :deep(.el-sub-menu__title:hover) {
    background-color: #1e4462 !important;
    color: #01dbf5 !important;
  }
}
.openSidebar {
}
.hideSidebar {
  width: 65px !important;
}
.el-menu {
  --el-menu-item-height: 46px;
  --el-menu-sub-item-height: 40px;
  --el-menu-bg-color: transparent;
  border: none;
}
:deep(.scrollBox > .el-menu > div > li > .el-sub-menu__title) {
  // color: #999999; //在智能体框架下面，顶级标题颜色
  font-weight: 400;
}
</style>
<style>
.popperBox {
  --el-menu-item-height: 46px;
  --el-menu-sub-item-height: 40px;
}
</style>

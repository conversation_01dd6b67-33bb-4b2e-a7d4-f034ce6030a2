<template>
    <div class="side-menu-wrapper">
        <div class="logo-wrapper">
            <div class="logo" v-if="!props.tenant || (props.tenant && !props.tenant.tenantLogo)">
                AI
            </div>
            <div class="logo" v-else>
                <img :src="baseUrl+props.tenant.tenantLogo" style="width: 36px;" />
            </div>
        </div>
        <div class="divider">
        </div>
        <div class="menu-item" @click="newChat">
            <inline-svg :src="chatSvg" />
            对话
        </div>
        <div class="menu-item" @click="openAppMarket">
            <inline-svg :src="appSvg" /> 
            应用商店
        </div>

        <div class="personal-center">
            <el-popover
                placement="right-end"
                trigger="hover"
                popper-class="personal-center-popper"
            >
                <template #reference>
                    <img class="avatar" :src="userStore.avatar">
                </template>
                <div class="pmenu">
                    <div class="pmenu-item" @click="showUserProfile = true">
                        个人中心
                    </div>
                    <div class="pmenu-item" @click="logout">
                        退出登录
                    </div>
                </div>
            </el-popover>
        </div>
    </div>
    <el-dialog v-model="showUserProfile" width="1200px">
        <user-profile />
    </el-dialog>
</template>

<script setup>
import appSvg from "@/assets/icons/svg/app.svg";
import chatSvg from "@/assets/icons/svg/chat.svg";
import useUserStore from "@/store/modules/user.js";
import { ElMessageBox } from "element-plus";
import UserProfile from "./profile";
import InlineSvg from 'vue-inline-svg';

const props = defineProps({
    tenant:{
        type:Object,
        required: false
    }
});

const baseUrl = import.meta.env.VITE_APP_BASE_API;

const userStore = useUserStore();
const showUserProfile = ref(false);
const emit = defineEmits(['new-chat', 'open-app-market'])

/**
 * 退出登录
 */
function logout() {
  ElMessageBox.confirm('确定注销并退出系统吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    userStore.logOut().then(() => {
      const env = import.meta.env.VITE_APP_ENV
      location.href = (env === 'production' ? '/yimai-agent/' : '/') + 'chat';
    })
  }).catch(() => { });
}

/**
 * 点击"对话"
 */
function newChat() {
    emit('new-chat');
}

/**
 * 点击“应用市场”
 */
function openAppMarket() {
    emit('open-app-market');
}

</script>

<style scoped lang="scss">
.side-menu-wrapper {
    width: 70px;
    height: 100%;
    overflow: hidden;
    padding: 5px;
    flex-grow: 0;
    flex-shrink: 0;
    border-right: 1px solid lightgray;
    position: relative;
    background-color: #eeecf6;

    .divider {
        width: 100%;
        height: 2px;
        display: flex;
        flex-direction: row;
        justify-content: center;
        align-items: center;
        margin: 15px 0 25px 0;

        &::before {
            content: '';
            display: block;
            border-bottom: 1px solid lightgray;
            width: 55px;
        }
    }

    .logo-wrapper {
        width: 100%;
        height: 80px;
        display: flex;
        flex-direction: row;
        justify-content: center;
        align-items: center;

        .logo {
            width: 36px;
            height: 36px;
            line-height: 36px;
            border-radius: 4px;
            color: white;
            font-size: 20px;
            background-color: #5050E6;
            display: flex;
            flex-direction: row;
            justify-content: center;
            align-items: center;
        }
    }

    .personal-center {
        position: absolute;
        left: 50%;
        transform: translateX(-50%);
        bottom: 20px;
        width: 40px;
        height: 40px;

        .avatar {
            width: 40px;
            height: 40px;
            border: 1px solid lightgray;
            border-radius: 50%;
        }
    }

    .menu-item {
        width: 100%;
        height: 65px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        font-size: 12px;
        text-overflow: nowrap;
        border-radius: 3px;
        margin-bottom: 20px;
        color: #847ea8;

        &:hover {
            background-color: white;
            cursor: pointer;
            font-weight: bold;
        }

        img, svg {
            width: 25px;
            height: 25px;
            margin-bottom: 5px;
        }

        svg {
            fill: #b09ff4;
        }
    }
}
</style>

<style lang="scss">
.personal-center-popper {
    padding: 2px;

    .pmenu-item {
        line-height: 2em;
        text-indent: 1em;

        &:hover {
            background-color: #ecf5ff;
            color: #40b3ff;
            cursor: pointer;
        }
    }
}
</style>
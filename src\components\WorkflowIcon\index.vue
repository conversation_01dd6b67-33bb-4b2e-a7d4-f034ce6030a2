<template>
    <div class="workflow-icon" 
        :style="{width:`${size + 10}px`,height:`${size + 10}px`,backgroundColor:backgroundColor}">
        <svg-icon :icon-class="`workflow-${icon}`" :size="size"></svg-icon>
    </div>
</template>
<script setup>

const props = defineProps({
    icon:{
        type:String,
        default:''
    },
    size:{
        type:String,
        default:''
    }
})

const backgroundColor = computed(()=>{
    const colors = {
        'start':'rgb(249, 115, 22)',
        'llm':'rgb(249, 115, 22)',
        'end':'rgb(249, 115, 22)',
        'answer':'rgb(249, 115, 22)',
        'knowledge-retrieval':'rgb(120, 113, 108)',
        'question-classifier':'rgb(120, 113, 108)',
        'code':'rgb(34, 197, 94)',
        'if-else':'rgb(34, 197, 94)',
        'iteration':'rgb(34, 197, 94)',
        'template-transform':'rgb(34, 197, 94)',
        'variable-assigner':'rgb(34, 197, 94)',
        'variable-aggregator':'rgb(34, 197, 94)',
        'parameter-extractor':'rgb(34, 197, 94)',
        'http-request':'rgb(6, 182, 212)',
        'human-input-text':'rgb(234, 179, 8)',
        'human-input-choice':'rgb(234, 179, 8)'
    }
    return colors[props.icon]
})

</script>
<style lang="scss" scoped>
.workflow-icon{
    background-color: orange;
    border-radius: .5rem;
    padding: 5px;
    color: white;
    .svg-icon{
        vertical-align: 0;
    }
}
</style>
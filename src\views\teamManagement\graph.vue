<template>
    <div class="common-container">
        <yi-mai-flow :id="id"></yi-mai-flow>
    </div>
</template>

<script setup>
import YiMaiFlow from '@/components/YimaiFlow/index'

const route = useRoute();

const id = route.query.id
</script>
<style lang="scss" scoped>
.common-container {
    display: flex;
    flex-direction: column;
    background-color: #FFFFFF !important;
    height: calc(100vh - 52px);
    width: 100%;
    margin: 0;
    padding: 0;
    background-size: cover;
}
</style>
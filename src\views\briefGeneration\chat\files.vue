<template>
  <div class="file-wrapper">
    <div class="file" style="cursor: pointer">
      <img src="/icons/pdf-icon.png" class="file-icon" />
      <span class="file-name" :title="fileObj?.name">{{ fileObj?.name }}</span>
      <span class="file-size">{{ formatFileSize(fileObj?.size) }}</span>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    fileObj: {
      type: Object,
      required: true,
      default: () => ({}),
    },
  },
  methods: {
    // itemClick (item) {
    //   if (item) {
    //     linkTxt.value = item
    //     linkRequest.value = true
    //   }
    // }
    // 格式化文件大小
    formatFileSize(bytes) {
      if (bytes < 1024) {
        return `${bytes} B`;
      } else if (bytes < 1024 * 1024) {
        return `${(bytes / 1024).toFixed(1)} KB`;
      } else {
        return `${(bytes / (1024 * 1024)).toFixed(1)} MB`;
      }
    },
  },
};
</script>


<style lang="scss" scoped>
.file-wrapper {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  background: #173567;
  border-radius: 8px 8px 0px 8px;
  max-width: calc(100% - 32px);
  min-width: 80px;
  width: auto;
  height: auto;
  padding: 4px 16px;
  line-height: 2em;
  position: relative;
  font-size: 14px;
  color: #1e1f24;

  .file {
    display: flex;
    align-items: center;
    padding: 2px 0;
    .file-icon {
      flex: none;
      width: 16px;
      margin-right: 4px;
    }
    .file-name {
      font-size: 12px;
      line-height: 12px;
      color: #fff;
      margin-right: 7px;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 1;
      -webkit-box-orient: vertical;
      max-width: 240px;
    }

    .file-size {
      flex: none;
      font-size: 12px;
      line-height: 12px;
      color: #D4D4D4;
    }
  }
}
</style>
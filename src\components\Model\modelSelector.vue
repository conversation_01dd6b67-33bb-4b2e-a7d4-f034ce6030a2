<template>
    <el-select 
        v-model="curModelName"
        :teleported="teleported"
        :class="[{'model-invalid': !isModelValid}]"
        :disabled="props.isDisable"
    >
        <el-option-group
            v-for="group of modelGroups"
            :key="group.provider"
            :label="group.label.zh_Hans"
        >
            <el-option
                v-for="item of group.models"
                :key="item.model"
                :label="item.label.zh_Hans"
                :value="item.model"
            >
                <img :src="group.icon_small?.zh_Hans + '?_token=' + getToken()" class="model-icon">
                {{ item.label.zh_<PERSON> }}
            </el-option>
        </el-option-group>
        <template #empty>
            <span class="empty">无可选模型</span>
        </template>
        <template #label>
            <img
                v-if="isModelValid && providerIcon"
                :src="providerIcon"
                class="model-icon"
            >
            <el-tooltip
                v-else
                effect="dark"
                content="已废弃，请重新选择"
                placement="top"
            >
                <warning-filled class="model-icon icon-warning"/>
            </el-tooltip>
            {{ props.modelName }}
        </template>
    </el-select>
</template>

<script setup>
import { getAvailableModels, getDefaultModel } from '@/api/model/index.js';
import { onMounted, watch } from 'vue';
import { WarningFilled } from '@element-plus/icons-vue';
import { getToken } from "@/utils/auth";

const props = defineProps({
    /**
     * 模型列表
     */
     modelGroups: {
        type: Array,
        required: false,
    },
    /**
     * 模型的类型，其值为llm, text-embedding, speech2text, tts, rerank等
     */
    type: {
        type: String,
        required: true
    },
    /**
     * 当前的模型名称
     */
    modelName: {
        type: String,
        required: true
    },

    /**
     * 模型的供应商
     */
    modelProvider: {
        type: String,
        required: true
    },

    /**
     * 传给el-select的teleported属性，默认为true
     * 有些地方需要使用该属性，比如popover中，使用了该组件且teleported=true，则在该组件选择后会导至popover隐藏
     */
    teleported: {
        type: Boolean,
        required: false,
        default: true
    },

    /**
     * 如果参数中modelName, modelProvider是空的，则加载默认模型
     */
    enableDefaultModel: {
        type: Boolean,
        required: false,
        default: false
    },
    /**
     * 是否不可操作
     */
    isDisable: {
        type: Boolean,
        required: false,
        default: false
    }
})

// 模型列表
const modelGroups = ref([]);
watch(() => props.modelGroups, val => {
    if (val) {
        modelGroups.value = val
        
    } else {
        modelGroups.value = []
    }
},{ deep: true, immediate: true });

/**
 * 获取可选的模型数据
 */
 function getModelData() {
    getAvailableModels(props.type).then((res) => {
        const groups = [];

        res.data.forEach((group) => {
            if (group.status === 'active') {
                const models = [];
                group.models.forEach((item) => {
                    if (item.status === 'active') {
                        models.push(item);
                    }
                });
                groups.push(Object.assign({}, group, {models}));
            }
        });

        modelGroups.value = groups;
    });
}

if(!props.modelGroups){
    getModelData()
}

// 当前选择的模型
const curModelName = ref(props.modelName);

// 默认的模型名称
let defaultModelName = '';

// 默认的模型供应商
let defaultModelProvider = '';

// 当前供应商的图标
const providerIcon = computed(() => {
    for (const group of modelGroups.value) {
        for (const item of group.models) {
            if (item.model === curModelName.value) {
                return group.icon_small.zh_Hans + '?_token=' + getToken();
            }
        }
    }

    return '';
});

// 模型是否存在
const isModelValid = computed(() => {
    if (!props.modelProvider || !props.modelName || modelGroups.value.length === 0) {
        return true;
    }

    for (const group of modelGroups.value) {
        for (const item of group.models) {
            if (item.model === curModelName.value) {
                return true;
            }
        }
    }

    return false;
});

// model值变化时，emit事件
const emit = defineEmits(['update:modelName', 'update:modelProvider', 'update:providerIcon', 'update:isModelValid']);
watch(curModelName, () => {
    for (const group of modelGroups.value) {
        for (const item of group.models) {
            if (item.model === curModelName.value) {
                emit('update:modelProvider', group.provider);
                emit('update:modelName', curModelName.value);
                return;
            }
        }
    }
});

// props变化时，同步到modelName
watch(props, () => {
    if (curModelName.value !== props.modelName) {
        curModelName.value = props.modelName;
    }
    if (!curModelName.value && props.enableDefaultModel && defaultModelName) {
        emit('update:modelProvider', defaultModelProvider);
        emit('update:modelName', defaultModelName);
    }
}, {immediate: true});

// 同步providerIcon
watch(providerIcon, () => {
    emit('update:providerIcon', providerIcon.value);
}, {immediate: true});

// 同步isModelValid
watch(isModelValid, () => {
    emit('update:isModelValid', isModelValid.value);
}, {immediate: true});

onMounted(() => {
    // getModelData();
    if (props.enableDefaultModel) {
        useDefaultModel();
    }
});

/**
 * 获取可选的模型数据
 */
// function getModelData() {
//     getAvailableModels(props.type).then((res) => {
//         const groups = [];

//         res.data.forEach((group) => {
//             if (group.status === 'active') {
//                 const models = [];
//                 group.models.forEach((item) => {
//                     if (item.status === 'active') {
//                         models.push(item);
//                     }
//                 });
//                 groups.push(Object.assign({}, group, {models}));
//             }
//         });

//         modelGroups.value = groups;
//     });
// }

/**
 * 使用默认模型
 */
async function useDefaultModel() {
    const rs = await getDefaultModel(props.type);
    if (rs.data && !props.modelName && !props.modelProvider) {
        defaultModelName = rs.data.model;
        defaultModelProvider = rs.data.provider.provider;
        emit('update:modelProvider', defaultModelProvider);
        emit('update:modelName', defaultModelName);
    }
}
</script>

<style scoped>
.model-icon {
    width: 1em;
    height: 1em;
    margin-right: 5px;
    display: inline-block;
    vertical-align: middle;
    border: 0;
    outline: 0;
}
.empty {
    display: block;
    color: lightgray;
    width: 100%;
    height: 30px;
    text-align: center;
    line-height: 30px;
}

.icon-warning {
    color: rgb(238, 190, 119);
}
</style>
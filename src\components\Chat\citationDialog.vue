<template>
    <el-dialog
      v-model="showDialog"
      :title="data.doc_name"
      :show-close="true"
      class="citation-dialog"
    >
      <el-scrollbar>
        <div class="content-list">
            <div v-for="(segment, idx) of data.segments" :key="idx" class="content">
              <seg-render :seg="segment.content" />
              <!-- <message-renderer :cnt="segment.content"/> -->
            </div>
        </div>
      </el-scrollbar>
    </el-dialog>
</template>

<script>
// import MessageRenderer from './messageRenderer.vue'
import segRender from './segRender'

export default {
  components:{
    segRender
  },
  data() {
    return {
      /**
       * 是否展示引用内容弹窗
       */
      showDialog: false,

      /**
       * 引用数据
       */
      data: {
        doc_name: '',
        segments: []
      }
    };
  },
  methods: {
    open(data) {
        this.data = data;
        this.showDialog = true;
    }
  }
};
</script>

<style scoped>
.content-list > div:last-child {
  border-bottom: 0 !important;
}
.content-list {
  padding: 10px;
}
.content {
  border-bottom: 1px solid lightgray;
  margin-bottom: 1em;
  padding: 1em;
  white-space: pre;
  text-wrap: wrap;
  text-align: left;
  line-height: 2;
}
</style>

<style>
.citation-dialog {
  border-radius: 2px;
}
.citation-dialog .el-dialog__body {
  max-height: 80vh;
  height: 80vh;
  border-top: 1px solid #ebebeb;
}
</style>
<template>
    <!-- 授权角色 -->
    <el-dialog title="选择角色" v-model="visible" width="800px" top="5vh" append-to-body>
       <el-row>
          <el-table @row-click="clickRow" ref="refTable" :data="roleList" 
            @selection-change="handleSelectionChange" height="560px">
             <el-table-column type="selection" width="55"></el-table-column>
             <el-table-column label="角色名称" prop="roleName" :show-overflow-tooltip="true" />
            <el-table-column label="角色权限" prop="roleKey" :show-overflow-tooltip="true" />
            <el-table-column label="备注" prop="remark" :show-overflow-tooltip="true" />
          </el-table>
       </el-row>
       <template #footer>
          <div class="dialog-footer">
             <el-button type="primary" @click="handleSelectUser">确 定</el-button>
             <el-button @click="visible = false">取 消</el-button>
          </div>
       </template>
    </el-dialog>
 </template>
 
 <script setup name="SelectRole">
 import { getUnAuthRoleList,authTeamAllRole } from "@/api/teamManagement/teamRole";
 
 const props = defineProps({
    agentId: {
     type: [Number, String]
   }
 });
 
 const { proxy } = getCurrentInstance();
 
 const roleList = ref([]);
 const visible = ref(false);
 const roleIds = ref([]);
 
 const queryParams = reactive({
  agentId: undefined,
   roleName: undefined,
   applicationType: undefined
 });
 
 // 显示弹框
 const show = () =>{
   queryParams.agentId = props.agentId;
   getList();
   visible.value = true;
 }
 /**选择行 */
 function clickRow(row) {
   proxy.$refs["refTable"].toggleRowSelection(row);
 }
 // 多选框选中数据
 function handleSelectionChange(selection) {
   roleIds.value = selection.map(item => {return {roleId:item.roleId}});
 }
 // 查询表数据
 function getList() {
   getUnAuthRoleList({
      applicationId:queryParams.agentId,
      applicationType:'agent'
   }).then(res => {
     roleList.value = res.data;
   });
 }
 const emit = defineEmits(["ok"]);
 /** 选择授权角色操作 */
 function handleSelectUser() {
   if (!roleIds.value.length) {
     proxy.$modal.msgError("请选择要分配的角色");
     return;
   }
   authTeamAllRole({
      applicationId:queryParams.agentId,
      applicationType:'agent',
      sysRoleList:roleIds.value
   }).then(res => {
      emit("ok");
      proxy.$modal.msgSuccess("授权成功");
      visible.value = false;
   })
 }
 
 defineExpose({
   show,
 });
 </script>
 
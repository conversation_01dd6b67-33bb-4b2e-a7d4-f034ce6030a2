import { reactive, ref } from "vue";
import md from "./markdown";

export class MessageItem {
  isUser;
  content;
}

class MsgRecord {
  role;
  content;
}

export class MessageItemManagerTeamTask {
  refMessageItemList = reactive([]);
  nowMessageText = "";
  nowMessageHtml = ref([]);
  nowMessageRole = ref({});
  nowMessageLine = ref("");
  nowMessageChatMessage = ref({})
  
  loading = ref(false);
  waitting = ref(false);
  msgRecords = ref([]);

  pushUserMessage(content) {
    this.refMessageItemList.push({ isUser: true, content: content });
    this.msgRecords.value.push({ role: "user", content: content });
    // this.autosuggestionHandle()
  }

  
  /**
   * 清空消息历史，包含界面显示的和记录的
   */
  cleanHistory() {
    if (this.nowMessageHtml.value.length > 0) {
      this.nowMessageHtml.value.splice(0, this.nowMessageHtml.value.length);
    }
  }

  loadHistoryMessage(messages) {
    this.cleanHistory()
    messages.forEach((item) => {
      this.nowMessageHtml.value.push({task_id:'',content:item.answer?JSON.parse(item.answer).text:'',role:{},waitting:false})
    })
  }

  /**
   * 消息接收结束
   *
   * @param record 是否记录本次接收的消息
   */
  messageReceiveDone(record = true) {
   
  }

  clearNowMessageRole(){
    this.nowMessageRole.value = {}
  }

  do() {
    this.loading.value = true;
  }

  stop(){
    this.loading.value = false;
  }

  new() {
    //会话是否等待状态（否）
    this.waitting.value = false
    this.loading.value = false
    //消息内容列表清空
    this.refMessageItemList = []
    this.nowMessageHtml.value = [];
    this.nowMessageText = "";
  }

  waittingStart() {
    this.waitting.value = true;
  }

  waittingEnd() {
    this.waitting.value = false;
  }


  /**
   * 清空消息记录
   */
  cleanRecords() {
    if (this.msgRecords.length > 0) {
      this.msgRecords.value = [];
    }
  }

  deleteLastMsgRecord() {
    return this.msgRecords.value.pop();
  }

  /**
   * 向现在的消息中添加新内容
   * TODO 优化md的转换，减少转化次数
   * @param newContent 新的内容
   */
  appendNowMessageContent(newContent,role,task_id) {
    try {
      this.nowMessageHtml.value.filter(x=>x.task_id == task_id)[0].waitting = false
      this.nowMessageHtml.value.filter(x=>x.task_id == task_id)[0].role = role
      this.nowMessageHtml.value.filter(x=>x.task_id == task_id)[0].content = newContent
    } catch (e) {
      console.log(e);
      return
    }
  }

  //加初始内容
  appendInitialContent(task_id){
    try {
      this.nowMessageHtml.value.push({task_id:task_id,content:'',role:{},waitting:true})
    }catch (e) {
      console.log(e);
    }

  }

}



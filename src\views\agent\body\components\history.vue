
<template>
    <div class="history">
        <div class="list">
            <template v-for="history in historyList">
                <div class="item" @click="showHistory(history)">
                    <span>{{history.aiAgentMessages[0].userMessage}}</span>
                    <!-- <el-button class="hide_button" :icon="Delete" small text></el-button> -->
                </div>
            </template>
        </div>
        <div class="content">
            <records style="margin: 20px 0;" :message-item-manager="msgManager"></records>
        </div>
    </div>
</template>
<script setup>
import {getHistoryList} from '@/api/agent/history'
import useUserStore from '@/store/modules/user'
import {Delete} from '@element-plus/icons-vue'
import Records from '@/components/YimaiChatBot/records'
import { MessageItemManager } from '@/types/chat-types';

const msgManager = new MessageItemManager();

const userStore = useUserStore()

const props = defineProps({
    agentInfo:{
        type:Object,
        requeired:true
    }
})

const historyList = ref([])

const showHistory = (history) => {
    msgManager.cleanHistory()
    msgManager.loadHistoryMessage(history.aiAgentMessages)
}

const getHistory = () => {
    msgManager.nowMessageRole.value = props.agentInfo
    getHistoryList({agentId:props.agentInfo.id}).then(res => {
        historyList.value = res.rows
        msgManager.cleanHistory()
        msgManager.loadHistoryMessage(res.rows[0].aiAgentMessages)
    })
}

watch(() => props.agentInfo,() => {
    nextTick(() => {
        getHistory()
    })
},{deep:true,immediate:true})

</script>
<style lang="scss" scoped>
.history {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: row;
    border-top: 1px solid #ccc;
    .list{
        width: 30%;
        max-width: 250px;
        height: 100%;
        // border-right: 1px solid #ccc;
        background-color: rgb(233, 233, 244);
        .item{
            height: 40px;
            line-height: 40px;
            margin: 10px 10px;
            border-radius: 15px;
            padding-left: 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            cursor: pointer;
            .hide_button{
                display: none;
            }
            &:hover{
                background-color: rgb(253, 253, 254);
                .hide_button{
                    display: block;
                }
            }
        }
    }
    .content{
        width: calc(100% - 250px);
        height: calc(100% - 54px);
    }
}
</style>
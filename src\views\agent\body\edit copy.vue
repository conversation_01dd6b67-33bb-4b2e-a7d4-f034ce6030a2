<template>
    <div class="single-page-container">
        <div class="single-page-header">
            <div class="left-box">
                <!-- <el-icon style="margin-right: 10px;"><Back /></el-icon> -->
                <el-button :icon="Back" @click="closeDrawer" text style="margin-right: 10px;"></el-button>
                <!-- <el-icon size="32px" style="color: #5050E6;margin-right: 10px;" ><ChatDotRound /></el-icon> -->
                <div class="text-box">
                    <div class="title">
                        {{editForm.agentName}}
                        <!-- <el-icon size="20px" style="margin-left: 4px;color: #5050E6;"><Edit /></el-icon> -->
                    </div>
                    <div class="detail">
                        {{editForm.agentCode}}
                    </div>
                </div>
            </div>
            <div  class="right-box">
                <el-button type="primary" @click="debuggingOpen">调试</el-button>
                <el-button type="primary" @click="saveEditForm">保存</el-button>
            </div>
        </div>
        <div class="whole">
            <div class="left" :style="{'width':debuggingState?'65%':'100%'}">
                <div class="basic-settings">
                    <el-row>
                        <el-col :span="24" class="content-left-box-title">
                            智能体基础设置
                        </el-col>
                        <el-col :span="24">
                            <el-form  
                                :model="editForm"
                                :label-position="'top'"
                                style="width: calc(100% - 24px);padding-left: 24px;">
                                <el-row>
                                    <el-col :span="24">
                                        <el-form-item prop="agentCode">
                                            <template #label>
                                                <span class="title-font">
                                                    智能体标识：
                                                </span>
                                            </template>
                                            <el-input v-model="editForm.agentCode" placeholder="请输入内容" />
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="24">
                                        <el-form-item>
                                            <template #label>
                                                <span class="title-font">
                                                    智能体所属部门：
                                                </span>
                                            </template>
                                            <el-tree-select
                                                v-model="editForm.deptId"
                                                :data="deptList"
                                                :props="{ value: 'id', label: 'label', children: 'children' }"
                                                value-key="id"
                                                placeholder="选择所属部门"
                                                style="width:100%"
                                            />
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="24">
                                        <el-form-item>
                                            <template #label>
                                                <span class="title-font">
                                                    智能体分类：
                                                </span>
                                            </template>
                                            <el-select v-model="editForm.agentType" placeholder="请选择" style="width:100%" :fit-input-width="true">
                                                <el-option v-for="(item,index) in agentTypeList" :key="index" :label="item.agentType" :value="item.id"/>
                                            </el-select>
                                        </el-form-item>
                                    </el-col>
                                    <el-col>
                                        <el-form-item>
                                            <template #label>
                                                <span class="title-font">
                                                    智能体描述：
                                                </span>
                                            </template>
                                            <el-input v-model="editForm.agentDescribe" placeholder="请输入内容" type="textarea" :rows="6" />
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="24">
                                        <el-form-item>
                                            <template #label>
                                                <span class="title-font">
                                                    大模型选择：
                                                </span>
                                            </template>
                                            <model-selector
                                                type="llm"
                                                v-model:model-name="editForm.model"
                                                v-model:model-provider="editForm.provider"
                                                style="width:100%;"
                                            />
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="24">
                                        <el-form-item>
                                            <template #label>
                                                <span class="title-font">
                                                    大模型设置：
                                                </span>
                                            </template>
                                            <!--
                                            <model-parameters
                                                v-model="editForm.modelSettings"
                                                :model-name="editForm.model"
                                                :model-provider="editForm.provider"
                                                style="width: 100%;"
                                            />
                                            -->
                                       </el-form-item>
                                    </el-col>
                                </el-row>
                            </el-form>
                        </el-col>
                    </el-row>
                </div>
                <div class="function-settings">
                    <div class="content-left-box-title">
                        功能设置
                    </div>
                    <el-form
                        :model="editForm"
                        :label-position="'top'"
                        style="width: calc(100% - 24px);padding-left: 24px;">
                        <div style="margin-bottom:20px">
                            <el-form-item prop="prompt" >
                                <template #label>
                                    <span class="title-font">
                                        对话提示词：
                                    </span>
                                </template>
                                <el-input v-model="editForm.prompt" @blur="promptBlur" placeholder="请输入内容" style="width: 100%;" type="textarea" :rows="6" />
                            </el-form-item>
                        </div>
                        <div class="variable">
                            <div class="head">
                                <div :span="10">变量</div>
                                <div :span="14">
                                    <el-button :icon="Plus" size="small" text type="primary" @click="addKey">添加</el-button>
                                </div>
                            </div>
                            <div :span="24">
                                <variable-setting v-model="editForm.keyList" ref="variableSettingRef" />
                            </div> 
                        </div>
                        <div class="knowledege-base">
                            <div class="head">
                                <div :span="10">知识库</div>
                                <div :span="14">
                                    <el-button type="primary" size="small" @click="knowledgeBaseSettingsOpen">知识库设置</el-button>
                                    <el-button :icon="Plus" size="small" link type="primary" @click="addknowledgeBase">添加</el-button>
                                </div>
                            </div>
                            <div>
                                <el-table :data="editForm.knowledgeBaseList">
                                    <el-table-column label="知识库名称" align="center" prop="name" :show-overflow-tooltip="true"/>
                                    <el-table-column label="知识库描述" align="center" prop="description" :show-overflow-tooltip="true" />
                                    <el-table-column label="操作" align="center" width="50" class-name="small-padding fixed-width">
                                        <template #default="scope">
                                            <el-button :icon="Remove" link @click="delKnowledgeBase(scope.row)"></el-button>
                                        </template>
                                    </el-table-column>
                                </el-table>
                            </div>           
                        </div>
                        <div class="tool">
                            <div class="head">
                                <div>工具</div>
                                <div>
                                    <el-button :icon="Plus" size="small" link type="primary" @click="addTool">添加</el-button>
                                </div>
                            </div>
                            <div>
                                <el-table :data="editForm.temToolList">
                                    <el-table-column label="工具名称" align="center" prop="toolName" :show-overflow-tooltip="true" />
                                    <el-table-column label="工具描述" align="center" prop="toolDesc" :show-overflow-tooltip="true" />
                                    <el-table-column label="操作" align="center" width="50" class-name="small-padding fixed-width">
                                        <template #default="scope">
                                            <el-button :icon="Remove" link @click="delTool(scope.row)"></el-button>
                                        </template>
                                    </el-table-column>
                                </el-table>
                            </div>
                        </div>
                    </el-form>
                    <div class="content-left-box-title">
                        其他设置
                    </div>
                    <div style="padding-left: 24px;">
                        <div class="opening-settings">
                            <div style="margin-bottom: 10px;">开始设置</div>
                            <div style="padding: 10px;">
                                <div class="title-font" style="margin-bottom:10px">
                                    开场白：
                                </div>
                                <el-input v-model="editForm.prologue" placeholder="请输入开场白" style="width: 100%;" type="textarea" :rows="4" />
                            </div>
                            <div style="padding: 10px;">
                                <div class="head">
                                    <div class="title-font">开场预置问题：</div>
                                    <div>
                                        <el-button :icon="Plus" size="small" link type="primary" @click="addOpeningQuestion">添加</el-button>
                                    </div>
                                </div>
                                <div v-for="(item,index) in editForm.openingQuestionList" style="width: 100%;margin-top: 10px;">
                                    <el-input v-model="item.openingQuestion" placeholder="请输入问题" style="width: 95%;" type="text" />
                                    <el-button :icon="Remove" link @click="delOpeningQuestion(index)" style="width: 5%;"></el-button>
                                </div>
                            </div>
                        </div>
                        <div class="automatic-suggestion">
                            <div class="head">
                                <div>自动建议</div>
                                <div>
                                    <el-switch
                                        v-model="editForm.automaticSuggestion"
                                        class="ml-2"
                                        style="--el-switch-on-color: #5050E6;"
                                    />
                                </div>
                            </div>
                            <div>
                                <span style="font-size:13px;margin-left:20px">每次机器人响应后，根据上下文自动提供三个建议的用户问题</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="right" :style="{'width':debuggingState?'35%':'0'}" v-if="debuggingState">
                <div class="right-head">
                    <div>预览与调试</div>
                    <div>
                        <el-button :icon="Close" link @click="debuggingClose()"></el-button>
                    </div>
                </div>
                <div>
                    <yi-mai-chat-agent :chat-url="'ws://180.103.126.11:7881/conversation/ws'" :current-instruct="editForm" ref="messageCtrl"></yi-mai-chat-agent>
                </div>
            </div>
        </div>

         <!-- 知识库设置 -->
        <el-dialog v-model="knowledgeBaseSettingsDialogShow" title="知识库设置" center width="550px" height="800px">
            <el-form ref="knowledgeBaseSettingsForm" :model="editForm" label-width="120px">
                <!-- <el-form-item label="搜索策略:">
                    <el-radio-group v-model="editForm.searchStrateg">
                        <el-radio :label="1">混合搜索</el-radio>
                        <el-radio :label="2">语义搜索</el-radio>
                        <el-radio :label="3">全文搜索</el-radio>
                    </el-radio-group>
                </el-form-item> -->
                <el-form-item label="最大召回次数:">
                    <custom-slider :value="editForm.maxRecall" :max="10" :min="1" :step="1"
                    @update:value="val => {knowledgeBaseSettingsChangeBindSlider(val,'maxRecall')}" style="margin-left: -130px;"></custom-slider>
                </el-form-item>
                <el-form-item label="最小匹配度:">
                    <custom-slider :value="editForm.minMatch" :max="0.99" :min="0.01" :step="0.01"
                    @update:value="val => {knowledgeBaseSettingsChangeBindSlider(val,'minMatch')}" style="margin-left: -130px;"></custom-slider>
                </el-form-item>
                <!-- <el-form-item label="调用方式:">
                    <el-radio-group v-model="editForm.callMethod">
                        <el-radio :label="1">自动调用</el-radio>
                        <el-radio :label="2">按需调用</el-radio>
                    </el-radio-group>
                </el-form-item> -->

                <!-- <recall-settings v-model="editForm.recallModel"></recall-settings> -->
            </el-form>
            <template #footer>
                <div>
                    <el-button type="primary" @click="knowledgeBaseSettingsConfirm">确认</el-button>
                </div>
            </template>
        </el-dialog>

        <!-- 选择知识库 -->
        <el-dialog v-model="knowledgeBaseSelDialogShow" width="900px" height="800px">
            <template #header>
                <div class="my-header">
                    <span style="line-height: 24px;font-size:18px;color: #303133;">选择知识库</span>
                    <!-- <el-input v-model="knowledgeBaseQueryParams.name" :prefix-icon="Search" placeholder="搜索" style="width: 25%;margin-left:10px;border-radius:4px;" type="text"
                    @keyup.enter="knowledgeBaseSel"/> -->
                    <el-button type="primary" style="color: white;font-size: 14px;font-weight: 500;float:right;margin-right: 20px;" @click="createKnowledgeBase()">创建知识库</el-button>
                </div>
            </template>
            <div v-loading="knowledgeBaseLoading" class="common-content">
                <el-row>
                    <el-col :span="12" v-for="(item,index) in knowledgeBaseList" :key="index" style="margin-top: 20px;" @click="knowledgeBaseSelect(item)">
                        <custom-card :img="documentation" :title="item.name" :detail="item.description" :is-need-footer="false"></custom-card>
                    </el-col>
                </el-row>
            </div>
            <div style="padding:0 30px">
                <pagination
                    v-show="knowledgeBaseTotal > 0"
                    :total="knowledgeBaseTotal"
                    v-model:page="knowledgeBaseQueryParams.page"
                    v-model:limit="knowledgeBaseQueryParams.limit"
                    layout="prev, pager, next"
                    @pagination="getknowledgeBaseListMethod"
                    style="margin-bottom: 30px;"
                    class="mt-4"
                />
            </div>
        </el-dialog>

        <!-- 选择工具 -->
        <el-dialog v-model="toolShow" width="900px" height="800px">
            <template #header>
                <div class="my-header">
                    <span style="line-height: 24px;font-size:18px;color: #303133;">选择工具</span>
                    <el-input v-model="toolQueryParams.toolName" :prefix-icon="Search" placeholder="搜索" style="width: 25%;margin-left:10px;border-radius:4px;" type="text"
                    @keyup.enter="toolSel"/>
                    <el-button type="primary" style="color: white;font-size: 14px;font-weight: 500;float:right;margin-right: 20px;" @click="createTool()">创建工具</el-button>
                </div>
            </template>
            <div v-loading="toolLoading" class="common-content">
                <el-row>
                    <el-col :span="12" v-for="(item,index) in toolList" :key="index" style="margin-top: 20px;" @click="toolSelect(item)">
                        <custom-card :img="baseUrl+item.toolImageUrl" :title="item.toolName" :detail="item.toolDesc" :is-need-footer="false"></custom-card>
                    </el-col>
                </el-row>
            </div>
            <div style="padding:0 30px">
                <pagination
                    v-show="toolTotal > 0"
                    :total="toolTotal"
                    v-model:page="toolQueryParams.pageNum"
                    v-model:limit="toolQueryParams.pageSize"
                    layout="prev, pager, next"
                    @pagination="getToolsListMethod"
                    style="margin-bottom: 30px;margin-left:20px"
                    class="mt-4"
                />
            </div>
        </el-dialog>

    </div>
</template>
<script setup>
import {updateAgent,getDeptListTree,getModelList} from '@/api/agent/body'
import { ElMessage,ElMessageBox } from 'element-plus'
import {Back,Edit,Plus,Remove,Search,Close} from '@element-plus/icons-vue'
import {ref,onMounted, onUnmounted, nextTick} from 'vue'
import CustomSlider from '@/components/CustomSlider/index'
import CustomCard from '@/components/CustomCard/index'
import { getToolList } from "@/api/toolManagement/tool";
import { getKnowledgeBaseList } from "@/api/knowledgeBase/knowledgeBase";
import { getDetail } from '@/api/agent/body'
import documentation from "@/assets/icons/svg/documentation.svg";
import router from "@/router";
import modelSelector from '@/components/Model/modelSelector.vue';
import VariableSetting from './components/variableSetting.vue';
import YiMaiChatAgent from '@/components/YiMaiChatAgent/index.vue';
import recallSettings from '@/components/recallSettings/index.vue';

const baseUrl = import.meta.env.VITE_APP_BASE_API;
const route = useRoute();

const id =route.query.id

const editForm = ref({
    knowledgeBaseList:[],
    temToolList:[],
    openingQuestionList:[],
    maxRecall:1,
    minMatch:0.01,

    // 模型名称
    model: '',

    // 模型供应商
    provider: '',

    // 模型参数
    modelSettings: '',

    // 变量
    keyList: []
})
// const props = defineProps({
//     editModel:{
//         required:true,
//         type:Object
//     },
//     editDrawer:{
//         required:false,
//         type:Object
//     }
// })

// 变量设置组件的引用
const variableSettingRef = ref(null)

//智能体分类
const agentTypeList = ref([
    {id:1,agentType:'聊天型'},
    {id:2,agentType:'文本型'},
    {id:3,agentType:'编程型'},
    {id:4,agentType:'角色型'}
])

//知识库设置
const knowledgeBaseSettingsDialogShow = ref(false)
const knowledgeBaseSettings = ref({
    searchStrateg:1,
    maxRecall:1,
    minMatch:0.01,
    callMethod:1
})

//调试页面
const debuggingState = ref(false)

const knowledgeBaseSettingsOpen = () => {
    knowledgeBaseSettingsDialogShow.value=true
}

const messageCtrl = ref(null)
//打开
const debuggingOpen = () => {
    debuggingState.value=true
}
//关闭
const debuggingClose = () => {
    messageCtrl.value.closeSession()
    debuggingState.value=false
}

const knowledgeBaseSettingsConfirm = () => {
    knowledgeBaseSettings.value
    knowledgeBaseSettingsDialogShow.value=false
}

//选择知识库
const knowledgeBaseSelDialogShow = ref(false)
const knowledgeBaseLoading = ref(false)
const knowledgeBaseQueryParams = ref({
    name:'',
    page:1,
    limit:6
})
const knowledgeBaseTotal = ref(0)
const knowledgeBaseList = ref([])

const addknowledgeBase = () => {
    knowledgeBaseQueryParams.value = {
        page:1,
        limit:6
    }
    getknowledgeBaseListMethod()
    knowledgeBaseSelDialogShow.value = true
}

//知识库列表
const getknowledgeBaseListMethod = () => {
    knowledgeBaseLoading.value=true;
    getKnowledgeBaseList(knowledgeBaseQueryParams.value).then(response => {
        knowledgeBaseList.value=response.data;
        knowledgeBaseTotal.value=response.total;
        knowledgeBaseLoading.value=false;
    })
}

const createKnowledgeBase = () => {
    window.open("/knowledgeBase?type=add");
}

//选择知识库
const knowledgeBaseSelect = (item) => {
    if(editForm.value.knowledgeBaseList){
        if(!(editForm.value.knowledgeBaseList.find(x=> x.id == item.id))){
            editForm.value.knowledgeBaseList.push(item)
        }
    }else{
        editForm.value.knowledgeBaseList=[item]
    }
    knowledgeBaseSelDialogShow.value=false;
}
const delKnowledgeBase = (row) => {
    editForm.value.knowledgeBaseList = editForm.value.knowledgeBaseList.filter(x=>{ return x.id!=row.id })
}

//选择工具
const toolShow = ref(false)
const toolLoading = ref(false)
const toolQueryParams = ref({
    toolName:'',
    pageNum:1,
    pageSize:6
})
const toolTotal = ref(0)
const toolList = ref([])

const addTool = () => {
    toolQueryParams.value = {
        toolName:'',
        pageNum:1,
        pageSize:6
    }
    getToolsListMethod()
    toolShow.value = true
}
//工具列表
const getToolsListMethod = () => {
    toolLoading.value=true;
    getToolList(toolQueryParams.value).then(response => {
        toolList.value=response.rows;
        toolTotal.value=response.total;
        toolLoading.value=false;
    })
}
const toolSel = () => {
    toolQueryParams.value.pageNum=1
    getToolsListMethod()
}

//选择工具
const toolSelect = (item) => {
    if(editForm.value.temToolList){
        if(!(editForm.value.temToolList.find(x=> x.id == item.id))){
            editForm.value.temToolList.push(item)
        }
    }else{
        editForm.value.temToolList=[item]
    }
    toolShow.value = false
}
const delTool = (row) => {
    editForm.value.temToolList = editForm.value.temToolList.filter(x=>{ return x.id!=row.id })
}
//创建工具
const createTool = () => {
    window.open("/toolManagement?type=add");
}

//智能体所属部门
const deptList = ref([])
const getDeptList = () => {
    getDeptListTree().then((resp)=>{
        deptList.value=resp.data
    })
}

const getDetailMethod = () => {
    getDetail(id).then((resp) => {
        const data = resp.data
        data.model = data.model ? data.model : ''
        data.provider = data.provider ? data.provider : ''
        data.modelSettings = data.modelSettings ? data.modelSettings : '{}'

        editForm.value = data
        editForm.value.openingQuestionList=editForm.value.openQuestion?JSON.parse(editForm.value.openQuestion):[]
        editForm.value.knowledgeBaseList=editForm.value.knowledgeList?JSON.parse(editForm.value.knowledgeList):[]
        editForm.value.temToolList=editForm.value.toolList?JSON.parse(editForm.value.toolList):[]
        editForm.value.automaticSuggestion=editForm.value.autoSuggestion==1?true:false
    })
}
getDetailMethod()


const closeDrawer = () => {
    // props.editDrawer.close()
    router.back();
}
const changeBindSlider = (val,key) => {
    editForm.value[key] = val
}

const knowledgeBaseSettingsChangeBindSlider = (val,key) => {
    knowledgeBaseSettings.value[key] = val
}

const emit = defineEmits(['searchContentChild']);

const saveEditForm = () => {
    let openingQuestionList = editForm.value.openingQuestionList?editForm.value.openingQuestionList.filter(item=>{return item.openingQuestion!=''}):[]
    editForm.value.openQuestion=openingQuestionList.length>0?JSON.stringify(openingQuestionList):''
    editForm.value.knowledgeList=editForm.value.knowledgeBaseList.length>0?JSON.stringify(editForm.value.knowledgeBaseList):''
    editForm.value.toolList=editForm.value.temToolList.length>0?JSON.stringify(editForm.value.temToolList):''
    editForm.value.autoSuggestion=editForm.value.automaticSuggestion?1:0
    updateAgent(editForm.value).then((resp) => {
        if(resp.code == 200){
            ElMessage.success('修改成功')
            closeDrawer()
            emit('searchContentChild')
        }else{
            ElMessage.success('修改失败')
        }
    })
}

//添加变量
const addKey = () => {
    /*
    editForm.value.keyList.push({
        agentId:editForm.value.agentId,
        isRequired:0,
        keyCode:'',
        keyName:'',
        keyType:null,
        remark:''
    })
    */
   variableSettingRef.value.appendField()
}
//删除变量
const delKey = (index) => {
    editForm.value.keyList.splice(index,1)
}

//添加开场问题
const addOpeningQuestion = () => {
    if(!editForm.value.openingQuestionList){
        editForm.value.openingQuestionList=[]
    }
    editForm.value.openingQuestionList.push({openingQuestion:''})
}
//删除
const delOpeningQuestion = (index) => {
    editForm.value.openingQuestionList.splice(index,1)
}

//大模型列表
const modelList = ref([])
const getModelListMethod= () =>{
    getModelList().then(response=>{
        modelList.value=response.rows
    })
}

//对话提示词失去焦点
const promptBlur = () => {
    if(editForm.value.prompt){
        var variablesModel = extractVariable(editForm.value.prompt)
        if(variablesModel){
            var variablesState = false
            if(editForm.value.keyList && editForm.value.keyList.length>0){
                variablesModel.result = variablesModel.result.filter(x=>editForm.value.keyList.filter(k => k.keyCode == x.replace(/{|}/g, '')).length<=0)
                variablesModel.variables = variablesModel.variables.filter(x=>editForm.value.keyList.filter(k => k.keyCode == x).length<=0)
                if(variablesModel.result.length>0 && variablesModel.variables.length>0){
                    variablesState = true
                }
            }else{
                variablesState = true
            }
            if(variablesState){
                ElMessageBox.confirm(variablesModel.result.toString(), "提示词中引用了未定义的变量，是否自动添加到用户输入表单中？", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning",
                    showClose: false,
                    closeOnClickModal: false
                }).then(() => {
                    variablesModel.variables.forEach(element => {
                        editForm.value.keyList.push({
                            agentId:editForm.value.agentId,
                            keyCode:element,
                            keyName:element,
                            keyType:1
                        })
                    });
                })
                .catch((e) => {});
            }
            
            
        }
    }
}

//提取字符串里的变量
const extractVariable = (content) => {
    var variables = [];///{([^{}]*)}/g
    const regex = /{{([a-zA-Z]+)}}/g;
    var result = content.match(regex);
    //去重
    result = [...new Set(result)]
    if (result && result.length>0) {
        variables = result.map(m => m.replace(/{{|}}/g, ''));
    }
    var variablesModel = null
    if(variables.length>0){
        variablesModel = {
            result:result,
            variables:variables
        }
    }
    return variablesModel
}

getDeptList()
getModelListMethod()

//切换
function changePage(){
    if(document.visibilityState=='visible'){
        if(toolShow.value){
            getToolsListMethod()
        }
        if(knowledgeBaseSelDialogShow.value){
            knowledgeBaseQueryParams.value.page=1
            getknowledgeBaseListMethod()
        }
    }
}

onMounted(() => {
    document.addEventListener('visibilitychange', changePage);
})
onUnmounted(() => {
    document.removeEventListener('visibilitychange', changePage);
})

</script>
<style scoped lang="scss">
.single-page-container{
    // background-color: white;
    // height: 100vh;
    .single-page-header{
        height: 80px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-direction: row;
        border-bottom: 1px solid #E1E1E1;
        .left-box{
            margin-left:24px;
            display: flex;
            align-items: center;
            .text-box{
                display: flex;
                flex-direction: column;
                margin-left: 10px;
                .title{
            display: flex;
            align-items: center;
                    height: 25px;
                    font-size: 18px;
                    font-weight: 500;
                    color: #032220;
                }
                .detail{
                    height: 17px;
                    font-size: 12px;
                    color: #999999;
                    font-weight: 400;
                    margin-top: 4px;
                }
            }
        }
        .right-box{
            margin-right: 20px;
        }
    }
    .single-page-content{
        display: flex;
        flex-direction: row;
        height: calc(100vh - 130px);
        overflow-y: auto;
        .content-left-box{
            width: calc(60% - 48px);
            height: calc(100% - 60px);
            border-right: 1px solid #E1E1E1;
            background-color: white;
            margin-top: 30px;
            .content-left-box-title{
                padding-left: 24px;
                font-size: 14px;
                font-weight: bold;
                color: #032220;
                margin-bottom: 16px;
            }
            .left-box-half-top{
                border-bottom: 1px solid #E1E1E1;
            }
            .custom-form-item-slider{
                display: flex;
                flex-direction: row;
                width: 100%;
                align-items: center;
                justify-content: space-between;
                margin-bottom: 5px;
                .custom-slider-title{
                    margin-right: 20px;
                    width: 250px;
                    text-align: left;
                    font-size: 12px;
                    color: #999999;
                }
                .custom-slider-value{
                    width: calc(100% - 120px);
                    display: flex;
                    flex-direction: row;
                }
            }
        }
        .content-right-box-title{
            color: #032220;
            font-size: 14px;
            font-weight: 500;
            margin-bottom: 10px;
        }
        
        
    }

    .whole{
        display: flex;
        justify-content: space-between;
        .left{
            background-color: #F9F9F9;
            height: calc(100vh - 130px);
            display: flex;
            justify-content: space-between;
            .basic-settings{
                width:50%;
                padding: 10px 20px;
                border-right:1px solid #d1d5db;
                overflow-y: auto;
            }
            .function-settings{
                width:50%;
                padding: 10px 20px;
                height:calc(100vh - 130px);
                overflow-y: auto;
                .head{
                    display: flex;
                    justify-content: space-between;
                    margin-bottom: 20px;
                }
                .variable{
                    background-color:white;
                    padding: 20px;
                    border-radius: 8px;
                    margin-bottom:20px;
                }
                .knowledege-base{
                    margin-bottom:20px;
                    background-color: white;
                    // border:1px solid #ccc;
                    padding: 20px;
                    border-radius: 8px;
                }
                .tool{
                    margin-bottom:20px;
                    background-color: white;
                    // border:1px solid #ccc;
                    padding: 20px;
                    border-radius: 8px;
                }
                .opening-settings{
                    margin-bottom:20px;
                    // border:1px solid #ccc;
                    background-color: white;
                    padding: 20px;
                    border-radius: 8px;
                }
                .automatic-suggestion{
                    margin-bottom:20px;
                    // border:1px solid #ccc;
                    background-color: white;
                    padding: 20px;
                    border-radius: 8px;
                }
            }
        }

        .right{
            background-color: white;
            height:calc( 100vh - 130px );
            .right-head{
                padding: 20px;
                font-size: 16px;
                font-weight: bold;
                color: #032220;
                box-shadow: 0 2px 4px 0 rgba(0, 0, 0, .04), 0 0 1px 0 rgba(0, 0, 0, .08);
                border-radius: 8px;
                display: flex;
                justify-content: space-between;
            }
        }

        .title-font{
            font-size: 14px;
            font-weight: 400;
            color: #032220;
        }
    }
    
}
.content-left-box-title{
    // padding-left: 24px;
    font-size: 15px;
    font-weight: bold;
    color: #032220;
    margin-bottom: 16px;
}
.left-box-half-top{
    border-bottom: 1px solid #E1E1E1;
}
.content-right-box-alter-text{
    background-color: #F7F7FAFF;
    width: 100%;
    border-radius: 8px;
    margin-top:10px;
    padding:10px 20px
}
.common-content {
    padding: 0 20px;
    height: calc(100vh - 500px);
    background-color: #f4f4f5;
}

::v-deep .pagination-container .el-pagination {
	right: 30px;
	position: absolute;
}

::v-deep .el-dialog {
    display: flex;
    flex-direction: column;
    margin: 0 !important;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    max-height: calc(100% - 30px);
    max-width: calc(100% - 30px);
}
.retrieve-settings{
    margin-top: 20px;
    .title{
        line-height: 35px;
    }
    .radio{
        line-height: 20px;
        // width:45%;
        border:1px solid #dcdfe6;
        border-radius:10px;
        .title-image-box{
            text-align:center;
            padding-top:10px;
            padding-left:30px;
            img{
                padding:5px;
                height: 35px;
                width: 35px;
                border-radius: 4px;
            }
        }
        .title{
            margin-top:5px;
            display: flex;
            justify-content: space-between;
            align-items: center;

        }
        .content{
            font-size: 13px;
            color:#999999;
            padding-right: 5px;
            min-height:30px;
            padding-bottom: 10px;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        .launch-content{
            min-height: 100px;
            border-top:1px solid #dcdfe6;
            padding: 20px 40px;
            .icon{
                color: #999999;
            }
            .icon-group{
                height:40px;
                background-color:#ffd65236;
                border-radius: 10px; 
                padding: 5px;
                margin-top:5px;
                img{
                    padding:5px;
                    height: 30px;
                    width: 30px;
                    border-radius: 4px;
                }
            }
            .slider-demo-block {
                max-width: 600px;
                display: flex;
                align-items: center;
                .el-slider {
                    margin-top: 0;
                    margin-left: 12px;
                }
                .demonstration {
                    width:80px;
                    font-size: 16px;
                    line-height: 44px;
                    color:#706f6f;
                    font-weight: 540;
                }
            }
            .image-box{
                text-align:center;
                padding-top:10px;
                img{
                    height: 25px;
                    width: 25px;
                    border-radius: 4px;
                }
            }
            .option-title{
                cursor: pointer; 
                line-height: 32px;
                // width:125px;
                background-color:#f3f3f3;
                border-radius: 8px;
                padding:0 5px;
            }
        }
    }
    .font{
        font-size: 1vw; /* 这里的5vw表示字体大小为视口宽度的5% */
        white-space: nowrap; /* 防止文字换行 */
        overflow: hidden; /* 超出容器部分隐藏 */
        text-overflow: ellipsis; /* 超过部分显示省略号 */
    }
    .radio-sel{
        border: 1px solid #5050E6;
    }
}

</style>
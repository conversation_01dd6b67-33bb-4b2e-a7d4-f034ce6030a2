<template>
    <div class="variables">
        <div class="variable-title">
            <div>输入字段</div>
            <el-button icon="Plus" text size="small" @click="$refs.variablesDialogRef.show('add')"></el-button>
        </div>
        <div class="user-variables">
            <template v-for="variable,index in _variables">
                <div class="variable-area">
                    <div class="label">{{ variable.variable + '.' + variable.label }}</div>
                    <div class="end-label">
                        <div class="required" v-show="variable.required">必填</div>
                        <div class="data-type">{{ variable.type }}</div>
                        <el-button class="hidden-button" text size="small" icon="Edit" @click="$refs.variablesDialogRef.show('edit',variable,index)"></el-button>
                        <el-button class="hidden-button" text size="small" icon="Delete" @click="deleteVariable(index)"></el-button>
                    </div>
                </div>
            </template>
        </div>
        <div style="height: .5px;background-color: rgba(0, 0, 0, 0.05);margin: 10px 0;"></div>
        <div class="system-variables">
            <template v-for="sysField in sysFields">
                <div class="variable-area">
                    <div class="label">{{ sysField.label }}</div>
                    <div class="data-type">{{ sysField.dataType }}</div>
                </div>
            </template>
        </div>
        <variables-dialog ref="variablesDialogRef" @addVariable="addVariable" @editVariable="editVariable"></variables-dialog>
    </div>
</template>
<script setup>
 
import VariablesDialog from './variablesDialog'

const sysFields = ref([
    {label: "sys.query",dataType: 'String'},
    {label: "sys.files",dataType: 'Array[File]'},
    {label: "sys.conversation_id",dataType: 'String'},
    {label: "sys.user_id",dataType: 'String'},
])

const props = defineProps({
    modelValue:{
        type: Array,
        required:true
    }
})

const _variables = ref(props.modelValue)

const emits = defineEmits(['update:modelValue'])

const addVariable = (val) => {
    _variables.value.push(val)
}

const editVariable = (val) => {
    _variables.value[val.index] = val.data
}

const deleteVariable = (index) => {
    _variables.value.splice(index,1)
}

onMounted(() => {
    addVariable({
        variable: "aaa",
        label: "aaaaaa",
        type: "text-input",
        max_length: 48,
        required: true,
        options: []
    })
})

watch(_variables,(newValue) => {
    emits('update:modelValue', newValue);
})

</script>
<style lang="scss" scoped>
.variables {
    width: 300px;
    height: 100%;
    display: flex;
    flex-direction: column;
    .variable-title{
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        height: 24px;
        font-size: 13px;
        color: rgb(71, 84, 103);
        ::v-deep(.el-button--small ){
            padding: 0 5px;
        }
    }
    .user-variables{
        .variable-area{
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            align-items: center;
            height: 30px;
            margin-top: 4px;
            padding: 0 10px;
            font-size: 13px;
            border: 1px solid rgb(234, 236, 240);
            border-radius: 8px;
            ::v-deep(.el-button--small){
                padding: 0 6px;
                margin-left: 0 !important;
            }
            &:hover{
                .end-label{
                    .hidden-button{
                        display: block;
                    }
                    .required{
                        display: none;
                    }
                    .data-type{
                        display: none;
                    }
                }
            }
            .label{
                color: #475679;
            }
            .end-label{
                color: #667085;
                display: flex;
                flex-direction: row;
                align-items: center;
                .required{
                    margin-right: 8px;
                    display: block;
                }
                .data-type{
                    display: block;
                }
                .hidden-button{
                    display: none;
                }
            }
        }
    }
    .system-variables{
        .variable-area{
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            align-items: center;
            height: 30px;
            margin-top: 4px;
            padding: 0 10px;
            font-size: 13px;
            border: 1px solid rgb(234, 236, 240);
            border-radius: 8px;
            .label{
                color: #475679;
            }
            .data-type{
                color: #667085;
            }
        }
    }
}
</style>
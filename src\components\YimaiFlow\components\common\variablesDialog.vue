<template>
    <el-dialog v-model="dialogShow" :show-close="false" append-to-body width="448px">
        <template #header>
            <span style="font-size: 20px;font-weight: 600;">{{ _handleType == 'add'?'添加变量':'编辑变量' }}</span>
        </template>

        <el-form :model="_data" :label-position="'top'">
            <el-form-item label="字段类型">
                <field-type-select v-model="_data.type"></field-type-select>
            </el-form-item>
            <el-form-item label="变量名称">
                <el-input v-model="_data.variable"></el-input>
            </el-form-item>
            <el-form-item label="显示名称">
                <el-input v-model="_data.label"></el-input>
            </el-form-item>
            <el-form-item label="最大长度" v-show="_data.type == 'text-input' || _data.type == 'paragraph'">
                <el-input-number
                    class="input-number-text-left"
                    style="width: 100%;"
                    v-model="_data.max_length"
                    :min="1"
                    :max="_data.type == 'text-input'?256:33024"
                    controls-position="right"
                />
            </el-form-item>
            <el-form-item label="选项"  v-show="_data.type == 'select'">
                <div class="added-options">
                    <template v-for="(option,index) in _data.options" >
                        <el-input style="width: 100%;" v-model="_data.options[index]" >
                            <template #suffix>
                                <el-button size="small" text icon="Delete" type="danger"></el-button>
                            </template>
                        </el-input>
                    </template>
                </div>
                <div class="add-option-button" @click="_data.options.push('1')">
                    <el-icon><Plus /></el-icon><div>添加选项</div>
                </div>
            </el-form-item>
            <el-form-item label="必填">
                <el-switch v-model="_data.required" inactive-color="rgb(234, 236, 240)" active-color="rgb(37, 99, 235)"></el-switch>
            </el-form-item>
        </el-form>

        <div style="display: flex;justify-content: flex-end;">
            <el-button @click="dialogShow = false">取消</el-button>
            <el-button type="primary" @click="submit">确定</el-button>
        </div>

    </el-dialog>

</template>

<script setup>
import FieldTypeSelect from './fieldTypeSelect'
import {Plus} from '@element-plus/icons-vue'

const dialogShow = ref(false)

const emit = defineEmits(['addVariable','editVariable'])

const _data = ref({})
const _handleType = ref('')
const _index = ref(-1)

const show = (handleType,data,index) => {
    dialogShow.value = true
    _handleType.value = handleType
    if(handleType == 'edit'){
        _data.value = JSON.parse(JSON.stringify(data))
        _index.value = index
    }else{
        _data.value = {
            variable: "",
            label: "",
            type: "text-input",
            max_length: 48,
            required: false,
            options: []
        }
        _index.value = -1
    }
}

const submit = () =>{
    if(_handleType.value == 'edit'){
        emit('editVariable', {data:_data.value,index:_index.value})
    }else{
        emit('addVariable', _data.value)
    }
    dialogShow.value = false
}

watch(_data, () => {
    console.log(_data.value)
})

defineExpose({
    show
})

</script>

<style lang="scss" scoped>

.variables-dialog-header{

}
.add-option-button{
    width: 100%;
    padding: 0 12px;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: start;
    background-color: rgb(242, 244, 247);
    color:rgb(152, 162, 179);
    cursor: pointer;
    border-radius: 8px;
    margin-top: 8px;
    gap: .5rem;
}
.added-options{
    width: 100%;
    display: flex;
    flex-direction: column;
    >div {
        &:nth-child(n+2) {
            margin-top: 8px;
        }
    }
}
.input-number-text-left{
    ::v-deep(.el-input-number .el-input__inner){
        text-align: left;
    }
}

</style>
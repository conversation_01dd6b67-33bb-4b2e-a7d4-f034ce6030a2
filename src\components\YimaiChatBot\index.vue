<template>
    <div class="chat-bot-box">
        <template v-if="chatUrl != ''">
            <div class="chat-records-box" v-if="msgMan.msgRecords.length != 0 || props.keyList.length == 0 ">
                <ChatRecords :message-item-manager="props.messageItemManager"></ChatRecords>
            </div>
            <div style="padding: 16px 24px;" v-if="msgMan.msgRecords.length == 0 && props.keyList.length > 0">
                <div style="color:rgb(68 76 231);background-color: rgb(245 248 255);border: 1px solid rgb(224 234 255);border-radius: 12px;display: flex;justify-content: space-between;flex-direction: column;">
                    <div style="display: flex;justify-content: space-between;padding: 16px 24px;">
                        <span style="line-height: 24px;">{{isEditKeywords?'对话设置':'开始前，您可以修改对话设置'}}</span>
                        <el-button size="small" type="primary" v-if="!isEditKeywords" plain :icon="Edit" @click="isEditKeywords = true">编辑</el-button>
                    </div>
                    <div v-if="isEditKeywords">
                        <el-row style="padding: 0 24px;padding-bottom: 0;">
                            <el-col :span="24">
                                <el-form>
                                    <el-row>
                                        <el-col :span="24" v-for="(key) in props.keyList" :key="key.keyCode">
                                            <el-form-item  :label="key.keyName" >
                                                <el-input v-model="props.keywords[key.keyCode]" ></el-input>
                                            </el-form-item>
                                        </el-col>
                                    </el-row>
                                </el-form>
                            </el-col>
                        </el-row>
                        <div style="display: flex;align-items: center;justify-content: center;padding-bottom: 20px;">
                            <el-button @click="isEditKeywords = false">关闭</el-button>
                        </div>
                    </div>
                </div>
                
            </div>
            <div class="input-send">
                <!-- <MessageCtrl :message-item-manager="messageItemManager"></MessageCtrl> -->
                <!-- <MessageCtrlByWS :message-item-manager="props.messageItemManager" 
                    :chat-url="props.chatUrl"
                    :sessionId="props.sessionInfo.uuid"
                    ref="messageCtrl"
                    ></MessageCtrlByWS> -->
                <MessageCtrlByLongWS :message-item-manager="props.messageItemManager" 
                    :chat-url="props.chatUrl" :keywords="props.keywords"
                    :sessionId="sessionInfo.uuid"
                    ref="messageCtrl"
                ></MessageCtrlByLongWS>
            </div>
        </template>
        <template v-else>
            <div class="empty-url">
                <div style="width: 100vw;">请先配置chatUrl</div>
            </div>
        </template>
    </div>
</template>
<script setup>
import { MessageItemManager } from '@/types/chat-types';
import ChatRecords from './records';
// import MessageCtrl from '@/components/chatBot/messageCtrl.vue';
// import MessageCtrlByWS from './messageCtrlByWS.vue';
import MessageCtrlByLongWS from './messageCtrlByLongWS';
import { defineProps, nextTick } from 'vue';
import {Edit} from '@element-plus/icons-vue'

const messageCtrl = ref(null);
const isEditKeywords = ref(false);  

const props = defineProps({
    messageItemManager: {
        type: Object,
        required: false,
        default: new MessageItemManager()
    },
    chatUrl:{
        type:String,
        required:false,
        default:''
    },
    keywords:{
        type:Object,
        required:false,
        default: () => {
            return {}
        }
    },
    keyList:{
        type:Array,
        required:false,
        default: () => {
            return []
        }
    },
    agentInfo:{
        type:Object,
        requeired:true
    }
});

const sessionInfo = ref({})

const msgMan = ref(props.messageItemManager)

const connectWS = (botInfo)=>{
    props.messageItemManager.cleanHistory()
    messageCtrl.value.initConnectWS(botInfo)
    sessionInfo.value = botInfo
}

msgMan.value.nowMessageRole = JSON.parse(JSON.stringify(props.agentInfo))


defineExpose({
    connectWS
})

</script>
<style lang="scss" scoped>
.chat-bot-box {
    height: 100%;
    position: relative;
    .input-send {
        position: absolute;
        bottom: 30px;
        left: 0;
        width: 100%;
        height: 6%;
        max-height: 6%;
        align-items: center;
        justify-content: space-between;
    }
    .chat-records-box {
        width: 100%;
        height: 94%;
        max-height: 90%;
        // overflow-y: scroll;
        padding: 10px 0 0 0;
    }
    .empty-url{
        height: 100%;
        width: 100%;
        background-color: #FEF0F0;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-self: center;
        text-align: center;
        color: #F67E7E;
    }
    ::-webkit-scrollbar {
        width: 6px;
        height: 6px;
    }

    ::-webkit-scrollbar-thumb {
        border-radius: 3px;
        background-color: rgba(0, 0, 0, 0.2);
    }

    ::-webkit-scrollbar-track {
        border-radius: 3px;
        background-color: rgba(0, 0, 0, 0.1);
    }
}




</style>

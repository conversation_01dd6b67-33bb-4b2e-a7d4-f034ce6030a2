<template>
   <div class="app-container">
      <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch">
         <el-form-item label="任务名称" prop="jobName">
            <el-input
               v-model="queryParams.jobName"
               placeholder="请输入任务名称"
               clearable
               style="width: 200px"
               maxlength="50"
               @keyup.enter="handleQuery"
            />
         </el-form-item>
         <el-form-item label="任务组名" prop="jobGroup">
            <el-select v-model="queryParams.jobGroup" placeholder="请选择任务组名" clearable style="width: 200px">
               <el-option
                  v-for="dict in sys_job_group"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
               />
            </el-select>
         </el-form-item>
         <el-form-item label="任务状态" prop="status">
            <el-select v-model="queryParams.status" placeholder="请选择任务状态" clearable style="width: 200px">
               <el-option
                  v-for="dict in sys_job_status"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
               />
            </el-select>
         </el-form-item>
         <el-form-item label="任务类型" prop="jobType">
            <el-select v-model="queryParams.jobType" placeholder="请选择任务类型" clearable style="width: 200px">
               <el-option
                  v-for="dict in sys_job_type"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
               />
            </el-select>
         </el-form-item>
         <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
         </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
         <el-col :span="1.5">
            <el-button
               type="primary"
               plain
               icon="Plus"
               @click="handleAdd"
               v-hasPermi="['monitor:job:add']"
            >新增</el-button>
         </el-col>
         <el-col :span="1.5">
            <el-button
               type="success"
               plain
               icon="Edit"
               :disabled="single"
               @click="handleUpdate"
               v-hasPermi="['monitor:job:edit']"
            >修改</el-button>
         </el-col>
         <el-col :span="1.5">
            <el-button
               type="danger"
               plain
               icon="Delete"
               :disabled="multiple"
               @click="handleDelete"
               v-hasPermi="['monitor:job:remove']"
            >删除</el-button>
         </el-col>
         <el-col :span="1.5">
            <el-button
               type="warning"
               plain
               icon="Download"
               @click="handleExport"
               v-hasPermi="['monitor:job:export']"
            >导出</el-button>
         </el-col>
         <el-col :span="1.5">
            <el-button
               type="info"
               plain
               icon="Operation"
               @click="handleJobLog"
               v-hasPermi="['monitor:job:query']"
            >日志</el-button>
         </el-col>
         <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>

      <el-table v-loading="loading" :data="jobList" @selection-change="handleSelectionChange"
         :tooltip-options="{'popper-class':'tooltip-pop-table'}">
         <el-table-column type="selection" width="55" align="center" />
         <el-table-column label="任务编号" width="100" align="center" prop="jobId" />
         <el-table-column label="任务名称" align="center" prop="jobName" :show-overflow-tooltip="true" />
         <el-table-column label="任务组名" align="center" prop="jobGroup">
            <template #default="scope">
               <dict-tag :options="sys_job_group" :value="scope.row.jobGroup" />
            </template>
         </el-table-column>
         <el-table-column label="任务类型" align="center" prop="jobType">
            <template #default="scope">
               <dict-tag :options="sys_job_type" :value="scope.row.jobType" />
            </template>
         </el-table-column>
         <el-table-column label="调用目标字符串" align="center" prop="invokeTarget" :show-overflow-tooltip="true" />
         <el-table-column label="cron执行表达式" align="center" prop="cronExpression" :show-overflow-tooltip="true" />
         <el-table-column label="状态" align="center">
            <template #default="scope">
               <el-switch
                  v-model="scope.row.status"
                  active-value="0"
                  inactive-value="1"
                  @change="handleStatusChange(scope.row)"
               ></el-switch>
            </template>
         </el-table-column>
         <el-table-column label="操作" align="center" width="200" class-name="small-padding fixed-width">
            <template #default="scope">
               <el-tooltip content="修改" placement="top">
                  <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['monitor:job:edit']"></el-button>
               </el-tooltip>
               <el-tooltip content="删除" placement="top">
                  <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['monitor:job:remove']"></el-button>
               </el-tooltip>
               <el-tooltip content="执行一次" placement="top">
                  <el-button link type="primary" icon="CaretRight" @click="handleRun(scope.row)" v-hasPermi="['monitor:job:changeStatus']"></el-button>
               </el-tooltip>
               <el-tooltip content="任务详细" placement="top">
                  <el-button link type="primary" icon="View" @click="handleView(scope.row)" v-hasPermi="['monitor:job:query']"></el-button>
               </el-tooltip>
               <el-tooltip content="调度日志" placement="top">
                  <el-button link type="primary" icon="Operation" @click="handleJobLog(scope.row)" v-hasPermi="['monitor:job:query']"></el-button>
               </el-tooltip>
            </template>
         </el-table-column>
      </el-table>

      <pagination
         v-show="total > 0"
         :total="total"
         v-model:page="queryParams.pageNum"
         v-model:limit="queryParams.pageSize"
         @pagination="getList"
      />

      <!-- 添加或修改定时任务对话框 -->
      <el-dialog :title="title" v-model="open" :width="!workflowPick?600:1000" append-to-body class="edit-job">
         <div style="display: flex;justify-content: space-between;padding: 10px 0;">
            <el-form ref="jobRef" :model="form" :rules="rules" label-width="100px" style="width: 600px;padding: 0 10px;min-height: 350px;">
               <el-row>
                  <el-col :span="24">
                     <el-form-item label="任务名称" prop="jobName">
                        <el-input v-model="form.jobName" maxlength="50" placeholder="请输入任务名称" />
                     </el-form-item>
                  </el-col>
                  <el-col :span="24">
                     <el-form-item label="任务分组" prop="jobGroup">
                        <el-select v-model="form.jobGroup" placeholder="请选择">
                           <el-option
                              v-for="dict in sys_job_group"
                              :key="dict.value"
                              :label="dict.label"
                              :value="dict.value"
                           ></el-option>
                        </el-select>
                     </el-form-item>
                  </el-col>
                  <el-col :span="16">
                     <el-form-item label="任务类型" prop="jobType">
                        <el-select v-model="form.jobType" placeholder="请选择" @change="jobTypeChange">
                           <el-option v-for="dict in sys_job_type"
                              :key="dict.value"
                              :label="dict.label"
                              :value="dict.value"
                           ></el-option>
                        </el-select>
                     </el-form-item>
                  </el-col>
                  <el-col :span="8" style="display: flex;justify-content: right;" v-if="form.jobType=='workflow'">
                     <el-button type="primary" plain @click="openSelectWorkflow"  style="margin-left: 50px;">选择工作流</el-button>
                  </el-col>
                  <el-col :span="24" v-if="form.jobType=='invokemethod'">
                     <el-form-item prop="invokeTarget">
                        <template #label>
                           <span>
                              调用方法
                              <el-tooltip placement="top">
                                 <template #content>
                                    <div>
                                       调用示例：agentTask.agentParams('参数')
                                       <br />参数说明：支持字符串，布尔类型，长整型，浮点型，整型
                                    </div>
                                 </template>
                                 <el-icon><question-filled /></el-icon>
                              </el-tooltip>
                           </span>
                        </template>
                        <el-input v-model="form.invokeTarget" maxlength="500" placeholder="请输入调用目标字符串" />
                     </el-form-item>
                  </el-col>
                  <el-col :span="24">
                     <el-form-item label="cron表达式" prop="cronExpression">
                        <el-input v-model="form.cronExpression" placeholder="请输入cron执行表达式">
                           <template #append>
                              <el-button type="primary" @click="handleShowCron">
                                 生成表达式
                                 <i class="el-icon-time el-icon--right"></i>
                              </el-button>
                           </template>
                        </el-input>
                     </el-form-item>
                  </el-col>
                  <el-col :span="24" v-if="form.jobId !== undefined">
                     <el-form-item label="状态">
                        <el-radio-group v-model="form.status">
                           <el-radio
                              v-for="dict in sys_job_status"
                              :key="dict.value"
                              :label="dict.value"
                           >{{ dict.label }}</el-radio>
                        </el-radio-group>
                     </el-form-item>
                  </el-col>
                  <el-col :span="24">
                     <el-form-item label="执行策略" prop="misfirePolicy">
                        <el-radio-group v-model="form.misfirePolicy">
                           <el-radio-button label="1">立即执行</el-radio-button>
                           <el-radio-button label="2">执行一次</el-radio-button>
                           <el-radio-button label="3">放弃执行</el-radio-button>
                        </el-radio-group>
                     </el-form-item>
                  </el-col>
                  <el-col :span="24">
                     <el-form-item label="是否并发" prop="concurrent">
                        <el-radio-group v-model="form.concurrent">
                           <el-radio-button label="0">允许</el-radio-button>
                           <el-radio-button label="1">禁止</el-radio-button>
                        </el-radio-group>
                     </el-form-item>
                  </el-col>
               </el-row>
            </el-form>
            <div style="width: 350px;margin-left: 30px; border-left: 1px solid #DCDFE6;padding: 0 20px;" v-if="workflowPick">
               <div class="title">
                  <img :src="workflowPick.applicationIcon?baseUrl+workflowPick.applicationIcon:TeamDefaultIcon" class="img" />
                  <span>{{ workflowPick.applicationName }}</span>
               </div>
               <div class="key-input-list">
                  <div v-for="(item,index) in workflowPick.user_input_form">
                     <div v-if="Object.keys(item)[0]" class="key-input">
                           <div class="text">{{ item[Object.keys(item)[0]].label }}</div>
                           <div v-if="Object.keys(item)[0] && Object.keys(item)[0] == 'text-input'">
                              <el-input v-model="keyList[item[Object.keys(item)[0]].variable]" :placeholder="item[Object.keys(item)[0]].label" type="text"
                              :maxlength="item[Object.keys(item)[0]].max_length" />
                           </div>
                           <div v-if="Object.keys(item)[0] && Object.keys(item)[0]=='paragraph'">
                              <el-input v-model="keyList[item[Object.keys(item)[0]].variable]" type="textarea" show-word-limit rows="4" :placeholder="item[Object.keys(item)[0]].label"
                              :maxlength="item[Object.keys(item)[0]].max_length" />
                           </div>
                           <div v-if="Object.keys(item)[0] && Object.keys(item)[0]=='select'">
                              <el-select clearable v-model="keyList[item[Object.keys(item)[0]].variable]" :placeholder="item[Object.keys(item)[0]].label" style="width: 100%;">
                                 <el-option v-for="(item,index) in item[Object.keys(item)[0]].options" 
                                 :key="item" :label="item" :value="item" />
                              </el-select>
                           </div>
                           <div v-if="Object.keys(item)[0] && Object.keys(item)[0] == 'number'">
                              <el-input-number v-model="keyList[item[Object.keys(item)[0]].variable]" controls-position="right" style="width: 100%;" />
                           </div>
                     </div>
                  </div>
               </div>
            </div>
         </div>
         <template #footer>
            <div class="dialog-footer" style="text-align: center;">
               <el-button type="primary" @click="submitForm">确 定</el-button>
               <el-button @click="cancel">取 消</el-button>
            </div>
         </template>
      </el-dialog>

     <el-dialog title="Cron表达式生成器" v-model="openCron" append-to-body destroy-on-close>
       <crontab ref="crontabRef" @hide="openCron=false" @fill="crontabFill" :expression="expression"></crontab>
     </el-dialog>

      <!-- 任务日志详细 -->
      <el-dialog title="任务详细" v-model="openView" :width="!keyList || Object.keys(keyList).length==0?700:1100" append-to-body>
         <div class="view-job">
            <el-form :model="form" label-width="120px"  style="width: 700px;padding: 0 10px;min-height: 350px;">
               <el-row>
                  <el-col :span="12">
                     <el-form-item label="任务编号：">{{ form.jobId }}</el-form-item>
                     <el-form-item label="任务名称：">{{ form.jobName }}</el-form-item>
                  </el-col>
                  <el-col :span="12">
                     <el-form-item label="任务分组：">{{ jobGroupFormat(form) }}</el-form-item>
                     <el-form-item label="创建时间：">{{ form.createTime }}</el-form-item>
                  </el-col>
                  <el-col :span="12">
                     <el-form-item label="cron表达式：">{{ form.cronExpression }}</el-form-item>
                  </el-col>
                  <el-col :span="12">
                     <el-form-item label="下次执行时间：">{{ parseTime(form.nextValidTime) }}</el-form-item>
                  </el-col>
                  <el-col :span="24">
                     <el-form-item label="任务类型：">{{ jobTypeFormat(form) }}</el-form-item>
                  </el-col>
                  <el-col :span="24" v-if="form.jobType=='invokemethod'">
                     <el-form-item label="调用目标方法：">{{ form.invokeTarget }}</el-form-item>
                  </el-col>
                  <el-col :span="24" v-if="form.jobType=='workflow' && workflowPick">
                     <el-form-item label="工作流：">
                        <div class="title">
                           <img :src="workflowPick.applicationIcon?baseUrl+workflowPick.applicationIcon:TeamDefaultIcon" class="img" />
                           <span>{{ workflowPick.applicationName }}</span>
                        </div>
                     </el-form-item>
                  </el-col>
                  <el-col :span="12">
                     <el-form-item label="任务状态：">
                        <div v-if="form.status == 0">正常</div>
                        <div v-else-if="form.status == 1">暂停</div>
                     </el-form-item>
                  </el-col>
                  <el-col :span="12">
                     <el-form-item label="是否并发：">
                        <div v-if="form.concurrent == 0">允许</div>
                        <div v-else-if="form.concurrent == 1">禁止</div>
                     </el-form-item>
                  </el-col>
                  <el-col :span="12">
                     <el-form-item label="执行策略：">
                        <div v-if="form.misfirePolicy == 0">默认策略</div>
                        <div v-else-if="form.misfirePolicy == 1">立即执行</div>
                        <div v-else-if="form.misfirePolicy == 2">执行一次</div>
                        <div v-else-if="form.misfirePolicy == 3">放弃执行</div>
                     </el-form-item>
                  </el-col>
               </el-row>
            </el-form>
            <div style="width: 350px;margin-left: 30px; border-left: 1px solid #DCDFE6;padding: 0 20px;" v-if="keyList && Object.keys(keyList).length>0">
               <div style="font-size: 14px;font-weight: 700;margin-bottom: 20px;">
                  工作流参数
               </div>
               <div class="key-input-list">
                  <div v-for="(item,index) in workflowPick.user_input_form">
                     <div v-if="Object.keys(item)[0]" class="key-input">
                           <div class="left">{{ item[Object.keys(item)[0]].label }}：</div>
                           <div class="right">{{ keyList[item[Object.keys(item)[0]].variable] }}</div>
                     </div>
                  </div>
               </div>
            </div>
         </div>
         <template #footer>
            <div class="dialog-footer"  style="text-align: center;">
               <el-button @click="openView = false">关 闭</el-button>
            </div>
         </template>
      </el-dialog>

      <!-- 选择工作流 -->
      <el-dialog v-model="selectWorkflowShow" width="1000px">
            <template #header>
                <div class="my-header">
                    <span style="line-height: 24px;font-size:18px;color: #303133;">选择工作流</span>
                    <el-input v-model="workflowQuery.applicationName" :prefix-icon="Search" placeholder="搜索" style="width: 25%;margin-left:10px;border-radius:4px;" type="text"
                    @keyup.enter="workflowSel"/>
                </div>
            </template>
            <div v-loading="workflowLoading" class="common-content">
                <el-row>
                    <el-col :span="12" v-for="(item,index) in workflowList" :key="index" style="margin-top: 20px;" @click="workflowSelect(item)">
                        <custom-card :img="item.applicationIcon?baseUrl+item.applicationIcon:TeamDefaultIcon" :title="item.applicationName" :detail="item.applicationDesc" :is-need-footer="false"></custom-card>
                    </el-col>
                </el-row>
            </div>
            <div style="padding:0 30px">
                <pagination
                    v-show="workflowTotal > 0"
                    :total="workflowTotal"
                    v-model:pageNum="workflowQuery.pageNum"
                    v-model:pageSize="workflowQuery.pageSize"
                    layout="prev, pager, next"
                    @pagination="getWorkflowListMethod"
                    style="margin-bottom: 30px;"
                    class="mt-4"
                />
            </div>
        </el-dialog>
   </div>
</template>

<script setup name="Job">
import { listJob, getJob, delJob, addJob, updateJob, runJob, changeJobStatus } from "@/api/monitor/job";
import Crontab from '@/components/Crontab'
import { listApp } from "@/api/application/store"
import CustomCard from '@/components/CustomCard/index'
import TeamDefaultIcon from '@/assets/icons/svg/team-default.svg'
import { getCurrentInstructTeam } from '@/api/YiMaiChat/sessionRecordsDify'
import useUserStore from '@/store/modules/user'
import {getTeamInfo} from '@/api/teamManagement/team'
import { isObject } from "@vueuse/core";

const userStore = useUserStore()
const baseUrl = import.meta.env.VITE_APP_BASE_API

const router = useRouter();
const { proxy } = getCurrentInstance();
const { sys_job_group, sys_job_status,sys_job_type } = proxy.useDict("sys_job_group", "sys_job_status","sys_job_type");

const jobList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const openView = ref(false);
const openCron = ref(false);
const expression = ref("");

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    jobName: undefined,
    jobGroup: undefined,
    status: undefined,
    jobType: undefined
  },
  rules: {
    jobName: [{ required: true, message: "任务名称不能为空", trigger: "blur" }],
    invokeTarget: [{ required: true, message: "调用目标字符串不能为空", trigger: "blur" }],
    cronExpression: [{ required: true, message: "cron执行表达式不能为空", trigger: "change" }],
    jobType: [{ required: true, message: "任务类型", trigger: "change" }]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询定时任务列表 */
function getList() {
  loading.value = true;
  listJob(queryParams.value).then(response => {
    jobList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}
/** 任务组名字典翻译 */
function jobGroupFormat(row, column) {
  return proxy.selectDictLabel(sys_job_group.value, row.jobGroup);
}

/** 任务类型字典翻译 */
function jobTypeFormat(row, column) {
  return proxy.selectDictLabel(sys_job_type.value, row.jobType);
}

/** 取消按钮 */
function cancel() {
  open.value = false;
  reset();
}
/** 表单重置 */
function reset() {
  form.value = {
    jobId: undefined,
    jobName: undefined,
    jobGroup: undefined,
    invokeTarget: undefined,
    cronExpression: undefined,
    misfirePolicy: 3,
    concurrent: 1,
    status: "0",
    jobType: 'workflow',
    workFlowInfo:undefined
  };
  proxy.resetForm("jobRef");
   workflowPick.value = null
   keyList.value = {}
}
/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}
/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}
// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.jobId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}
// 更多操作触发
function handleCommand(command, row) {
  switch (command) {
    case "handleRun":
      handleRun(row);
      break;
    case "handleView":
      handleView(row);
      break;
    case "handleJobLog":
      handleJobLog(row);
      break;
    default:
      break;
  }
}
// 任务状态修改
function handleStatusChange(row) {
  let text = row.status === "0" ? "启用" : "停用";
  proxy.$modal.confirm('确认要"' + text + '""' + row.jobName + '"任务吗?').then(function () {
    return changeJobStatus(row.jobId, row.status);
  }).then(() => {
    proxy.$modal.msgSuccess(text + "成功");
  }).catch(function () {
    row.status = row.status === "0" ? "1" : "0";
  });
}
/* 立即执行一次 */
function handleRun(row) {
  proxy.$modal.confirm('确认要立即执行一次"' + row.jobName + '"任务吗?').then(function () {
    return runJob(row.jobId, row.jobGroup);
  }).then(() => {
    proxy.$modal.msgSuccess("执行成功");})
  .catch(() => {});
}
/** 任务详细信息 */
function handleView(row) {
  getJob(row.jobId).then(response => {
    form.value = response.data;
    //工作流内容
    if(response.data.jobType == 'workflow'){
      const workFlowInfo = JSON.parse(response.data.workFlowInfo)
      getTeamInfo(workFlowInfo.applicationId).then(res => {
         let instruct = {
            appId:res.data.appId,
            applicationId:res.data.id,
            applicationIcon:res.data.teamImgUrl,
            applicationName:res.data.teamName,
            applicationType:'team',
            mode:res.data.mode,
         }
         workflowSelect(instruct)
         keyList.value = workFlowInfo.keyList
         openView.value = true;
      })
    }
    else{
      openView.value = true;
    }
  });
}
/** cron表达式按钮操作 */
function handleShowCron() {
  expression.value = form.value.cronExpression;
  openCron.value = true;
}
/** 确定后回传值 */
function crontabFill(value) {
  form.value.cronExpression = value;
}
/** 任务日志列表查询 */
function handleJobLog(row) {
  const jobId = row.jobId || 0;
  router.push('/monitor/job-log/index/' + jobId)
}

//选择工作流弹框
const selectWorkflowShow = ref(false)
const workflowLoading = ref(false)
const workflowTotal = ref(0)
const workflowPick = ref(null)
const keyList = ref({})

const workflowQuery = ref({
   applicationName:'',
   pageNum:1,
   pageSize:6,
   mode:'workflow'
})

const openSelectWorkflow = () => {
   workflowQuery.value = {
      applicationName:'',
      pageNum:1,
      pageSize:6,
      mode:'workflow'
   }
   getWorkflowListMethod()
   selectWorkflowShow.value = true
}

const workflowList = ref([])
const getWorkflowListMethod = () => {
   listApp(workflowQuery.value).then(res => {
      workflowTotal.value = res.total
      workflowList.value = res.rows
   })
}
const workflowSel = () => {
   workflowQuery.value.pageNum = 1
   getWorkflowListMethod()
}
const workflowSelect = (workflow) => {
   workflowPick.value = null
   keyList.value = {}
   workflowPick.value = {
      appId : workflow.appId,
      applicationId : workflow.applicationId,
      applicationName : workflow.applicationName,
      applicationIcon : workflow.applicationIcon,
      applicationType  : workflow.applicationType,
      mode  : workflow.mode
   }
   getCurrentInstructTeam(workflow.appId).then(response => {
      workflowPick.value.user_input_form = response.user_input_form
   })
   selectWorkflowShow.value = false
}
const jobTypeChange = () => {
   workflowPick.value = null
   keyList.value = {}
   form.value.invokeTarget = undefined
}

//用户输入表单验证
const keyListCheck = () => {
   if(workflowPick.value.user_input_form && workflowPick.value.user_input_form.length>0){
      for(let i=0;i<workflowPick.value.user_input_form.length;i++){
         let singleStrip = (workflowPick.value.user_input_form[i]['text-input']?workflowPick.value.user_input_form[i]['text-input'] :null)
            ||(workflowPick.value.user_input_form[i]['paragraph']?workflowPick.value.user_input_form[i]['paragraph'] :null)
            ||(workflowPick.value.user_input_form[i]['number']?workflowPick.value.user_input_form[i]['number'] :null)
            ||(workflowPick.value.user_input_form[i]['select']?workflowPick.value.user_input_form[i]['select'] :null)
         if(singleStrip && !keyList.value[singleStrip.variable] && singleStrip.required){
            proxy.$modal.msgError(singleStrip.label+'必须填写')
            return false
         }
         if((Object.keys(workflowPick.value.user_input_form[i])[0] =='text-input' 
         || Object.keys(workflowPick.value.user_input_form[i])[0] =='paragraph')
         && keyList.value[singleStrip.variable] 
         && keyList.value[singleStrip.variable].length>singleStrip.max_length){
            proxy.$modal.msgError(singleStrip.label+'长度不能大于'+singleStrip.max_length)
            return false
         }
      }
   }
   return true
}


/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加任务";
}
/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const jobId = row.jobId || ids.value;
  getJob(jobId).then(response => {
    form.value = response.data;
    //工作流内容
    if(response.data.jobType == 'workflow'){
      const workFlowInfo = JSON.parse(response.data.workFlowInfo)
      getTeamInfo(workFlowInfo.applicationId).then(res => {
         let instruct = {
            appId:res.data.appId,
            applicationId:res.data.id,
            applicationIcon:res.data.teamImgUrl,
            applicationName:res.data.teamName,
            applicationType:'team',
            mode:res.data.mode,
         }
         workflowSelect(instruct)
         keyList.value = workFlowInfo.keyList
         open.value = true;
         title.value = "修改任务";
      })
    }
    else{
      open.value = true;
      title.value = "修改任务";
    }
  });
}
/** 提交按钮 */
function submitForm() {
  proxy.$refs["jobRef"].validate(valid => {
    if (valid) {
      if(form.value.jobType == 'workflow' && !workflowPick.value){
         proxy.$modal.msgError("请先选择工作流")
         return
      }
      if(form.value.jobType == 'workflow'){
         //用户表单校验
         if(!keyListCheck()){
               return
         }
         //处理工作流方法
         const workflowQuery = {
            response_mode: "streaming",
            query: '',
            user_id:userStore.userDifyId,
            inputs:keyList.value
         }
         form.value.invokeTarget = "workFlowTask.toRunWorkFlow('"+JSON.stringify(workflowQuery)+"','"+workflowPick.value.appId+"')"
         const workFlowInfo = {
            appId:workflowPick.value.appId,
            applicationId:workflowPick.value.applicationId,
            keyList:keyList.value
         }
         form.value.workFlowInfo = JSON.stringify(workFlowInfo)
      }
      if (form.value.jobId != undefined) {
        updateJob(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addJob(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}
/** 删除按钮操作 */
function handleDelete(row) {
  const jobIds = row.jobId || ids.value;
  proxy.$modal.confirm('是否确认删除定时任务编号为"' + jobIds + '"的数据项?').then(function () {
    return delJob(jobIds);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}
/** 导出按钮操作 */
function handleExport() {
  proxy.download("monitor/job/export", {
    ...queryParams.value,
  }, `job_${new Date().getTime()}.xlsx`);
}

getList();
</script>

<style scoped lang="scss">
   .common-content {
      padding: 0 20px;
      background-color: #f4f4f5;
      padding-bottom: 20px;
      min-height: 450px;
   }

   ::v-deep .pagination-container .el-pagination {
      right: 30px;
      position: absolute;
   }

   .edit-job{
      .key-input-list{
         padding: 5px 10px;
         height: 350px;
         overflow-y: auto;
         .key-input{
            font-size: 13px;
            margin-bottom: 20px;
            .text{
                  margin: 10px 0;
            }
         }
      }

      .title{
         font-size: 18px;
         font-weight: 600;
         color: #1D2939;
         margin: 10px;
         display: flex;
         justify-content:left;
         align-items: center;
         .img{
            width: 30px;
            height: 30px;
            border-radius: 5px;
            margin-right: 10px;
         }
      }
   }

   .view-job{
      display: flex;
      justify-content: space-between;
      padding: 10px 0;
      .key-input-list{
         padding: 5px 10px;
         height: 350px;
         overflow-y: auto;
         .key-input{
            font-size: 13px;
            margin-bottom: 20px;
            display: flex;
            justify-content: left;
            .left{
               flex: 0 0 auto; 
            }
            .right{
               flex: 1 0;
               word-wrap: break-word; /* 允许在边界处断开单词 */
               white-space: normal; /* 允许换行 */
            }
         }
      }

      .title{
         font-size: 14px;
         font-weight: 500;
         color: #1D2939;
         display: flex;
         justify-content:left;
         align-items: center;
         .img{
            width: 25px;
            height: 25px;
            border-radius: 5px;
            margin-right: 10px;
         }
      }
   }
   
</style>
<style lang="scss">
.tooltip-pop-table.el-popper{
    max-width: 600px !important;
    word-break:break-all
}
</style>

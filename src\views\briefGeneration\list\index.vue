<template>
  <div class="container" :style="{
    '--current-color': '#3793aa',
    '--el-color-primary': '#3793aa',
    '--el-fill-color': 'transparent',
    '--el-fill-color-blank': 'transparent',
    '--el-fill-color-light': 'transparent',
    '--el-disabled-bg-color': 'transparent',
    '--el-text-color-primary': '#fff',
    '--el-mask-color': 'transparent'
  }">
    <div class="header">
      <div class="box">
        <div @click="handleReturn" class="returnBtn"><i class="ri-arrow-left-s-line" style="margin-right: 5px;"></i>项目管理
        </div>
        <div @click="handleAdd" class="addBtn">
          <el-icon style="font-size: 20px; margin-right: 11px">
            <CirclePlus />
          </el-icon>新建研究
        </div>
      </div>
    </div>
    <div v-loading="loading" class="common-content">
      <div class="row" v-if="teamList && teamList.length > 0">
        <custom-card :status="item.status == '1' ? '已完稿' : '编辑中'" :title="item.title" :is-need-footer="true"
          v-for="(item, index) in teamList" :key="index">
          <template #footer>
            <span class="timeBox"><i class="ri-time-line"></i>{{ formatDate(item.createTime) }}</span>
            <div class="tools">
              <div @click="handleItem(item, 'view')">
                <i class="ri-eye-fill"></i>
              </div>
              <div @click="handleItem(item, 'edit')">
                <i class="ri-pencil-fill"></i>
              </div>
              <div @click="
                () => {
                  handleDelete(item);
                }
              ">
                <i class="ri-delete-bin-line"></i>
              </div>
            </div>
          </template>
        </custom-card>
      </div>
      <div v-else class="empty">暂无内容</div>
      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize" layout="prev, pager, next"
        style="margin-right: 20px; background: transparent" />
    </div>

  </div>
</template>
<script setup>
import { ref, reactive, toRefs, onMounted, watch } from "vue";
import {
  Tools,
  Edit,
  Delete,
  Star,
  StarFilled,
  Search,
  Check,
} from "@element-plus/icons-vue";
import { useRoute, useRouter } from "vue-router";
import CustomCard from "./components/customCard.vue";
import { deleteBriefReport, reportList } from "@/api/briefGeneration/list";
import { ElMessage, ElMessageBox } from "element-plus";
import { formatDate } from "@/utils/index.js";
import { projectId } from "@/views/briefGeneration/list/js/sharedState.js";
const { proxy } = getCurrentInstance();
const route = useRoute()
const router = useRouter();

const loading = ref(false);


const teamList = ref([]);

//搜索
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
});
const total = ref(0);
const handleReturn = () => {
  router.push("/briefGeneration/projectMannage");
};
const handleAdd = () => {
  if (projectId) {
    router.push(`/briefGeneration/home?projectId=${projectId}`);
  } else {
    router.push(`/briefGeneration/home`);
  }
};
// 简报操作
const handleItem = (item, action) => {
  if (action === "edit") {
    router.push(`/briefGeneration/add/create/${action}/${item.id}`);
  } else if (action === "view") {
    router.push(`/briefGeneration/add/create/${action}/${item.id}`);
  }
};
// 删除简报
const handleDelete = async (item) => {
  try {
    await ElMessageBox.confirm("确认要删除该简报吗？", "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      customClass: 'junzhiMessage',
      type: "warning",
    });

    await deleteBriefReport(item.id);
    ElMessage.success("删除成功");
    getList();
  } catch (e) {
    if (e !== "cancel") {
      ElMessage.error("删除失败：" + (e.message || "未知错误"));
    }
  }
};
// 简报列表
const getList = async () => {
  try {
    loading.value = true;
    let res = await reportList(queryParams.value);
    teamList.value = res.rows || [];
    total.value = res.total || 0;
    loading.value = false;
  } catch (e) {
    console.error(e);
  }
};
watch(
  projectId,
  () => {
    queryParams.value = {
      projectId: projectId.value,
      pageNum: 1,
      pageSize: 10,
    }
  },
  { immediate: true, deep: true }
);
watch(
  () => queryParams.value,
  (newVal) => {
    if (newVal.projectId) {
      getList();
    }
  },
  { immediate: true, deep: true }
);
</script>
<style lang='scss' scoped>
.container {
  padding: 20px 24px;
  font-family: HarmonyOS Sans SC;
  height: 100%;
  // background: #0d1e4f;
  display: flex;
  flex-direction: column;

  .header {
    margin-bottom: 26px;
    flex: none;

    .box {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .returnBtn {
        color: #00E5FF;
        font-size: 14px;
        cursor: pointer;
      }

      .addBtn {
        background: #0f5763;
        border-radius: 15px;
        border: 1px solid #148d9a;
        cursor: pointer;
        padding: 5px 9px;
        font-weight: 400;
        font-size: 14px;
        line-height: 20px;
        color: #6af6ff;
        display: flex;
        align-items: center;
      }
    }
  }
}

.common-content {
  flex: 1;
  min-height: 0;
  overflow-y: auto;

  .row {
    box-sizing: border-box;
    display: flex;
    flex-wrap: wrap;
    position: relative;
    column-gap: 100px;
    row-gap: 50px;

    .agent-team-item {
      width: calc(25% - 100px);
      height: 130px;
    }

    .timeBox {
      i {
        color: #2c6f7b;
        margin-right: 5px;
      }

      font-family: Microsoft YaHei;
      font-weight: 400;
      font-size: 12px;
      color: #2c6f7b;
    }

    .tools {
      display: flex;
      align-items: center;
      gap: 20px;

      &>div {
        font-size: 16px;
        cursor: pointer;

        &:nth-child(1) {
          color: #13f1b9;
        }

        &:nth-child(2) {
          color: #00e6ff;
        }

        &:nth-child(3) {
          color: #fa1744;
        }
      }
    }
  }
}

::v-deep .el-card__footer {
  padding: calc(var(--el-card-padding) - 15px) var(--el-card-padding);
  border-top: 1px solid var(--el-card-border-color);
  box-sizing: border-box;
}

.state {
  line-height: 32px;
  width: 125px;
  background-color: #e6e6e6;
  border-radius: 8px;
  padding: 0 5px;
}

.tab {
  line-height: 20px;
  font-size: 12px;
  color: #032220;
  padding: 5px;
  margin: 2px;
  border-radius: var(--radius-sm, 4px);
}

.tab_click {
  line-height: 20px;
  font-size: 12px;
  color: #032220;
  padding: 5px;
  margin: 2px;
  border-radius: var(--radius-sm, 4px);
  background: #f4f4f6;
}

.custom-cursor {
  cursor: pointer;
}

.agent-team-item-button {
  display: grid;
  place-items: center;
  margin-top: 4px;
  width: 50%;
}

.empty {
  color: #86909c;
  text-align: center;
  padding: 40px 0;
}
</style>
<style lang="scss">
.customPopper {
  min-width: 120px !important;
}

.el-popover {
  --el-popover-padding: 0 !important;
}
</style>
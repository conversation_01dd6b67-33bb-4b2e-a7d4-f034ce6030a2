<template>
    <div class="field-type-select">
        <template v-for="option in options">
            <div class="option" :class="{ 'selected': option.value === modelValue }"
                @click="$emit('update:modelValue', option.value)">
                <div>
                    {{ option.label }}
                </div>
            </div>
        </template>
    </div>
</template>
<script setup>
const options = ref([
    { label: '文本', value: 'text-input' },
    { label: '段落', value: 'paragraph' },
    { label: '下拉选项', value: 'select' },
    { label: '数字', value: 'number' }
])

const props = defineProps({
    modelValue: {
        type: String,
        default: 'text-input'
    }
})
</script>
<style lang="scss" scoped>
.field-type-select {
    width: 400px;
    height: 58px;
    display: flex;
    >div {
        &:nth-child(n+2) {
            margin-left: 8px;
        }
    }
    .option {
        border-radius: 8px;
        width: 94px;
        height: 58px;
        border: 1px solid #eaecf0;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        cursor: pointer;
        &:hover {
            background-color: #f5f8ff;
            border-color:#b2ccff
        }
    }

    .selected {
        border-color: #528bff;
        background-color: #f5f8ff;
        color:#155eef;
        &:hover {
            border-color: #528bff;
            background-color: #f5f8ff;
        }
    }
}
</style>
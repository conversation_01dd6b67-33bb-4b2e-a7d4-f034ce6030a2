<template>
  <div ref="chartEl" class="graph"></div>
</template>

<script setup>
import * as echarts from 'echarts'
import { ref, watch, onMounted, onBeforeUnmount, nextTick } from 'vue'

const props = defineProps({
  option: {
    type: Object,
    required: true
  }
})

const chartEl = ref(null)
let chartInstance = null

onMounted(() => {
  chartInstance = echarts.init(chartEl.value)
  chartInstance.setOption(props.option)
})

onBeforeUnmount(() => {
  chartInstance.dispose() 
})

</script>

<style scoped>
.graph {
  width: min(1000px, 90%);
  aspect-ratio: 16 / 9;
  border-radius: 3px;
  overflow: hidden;
}
</style>

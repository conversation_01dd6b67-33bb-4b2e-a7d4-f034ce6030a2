<template>
  <div class="upload-demo">
    <el-upload
      v-model:file-list="nvLocalFileList"
      action=""
      multiple
      :http-request="uploadFile"
      :accept="'.doc,.docx,.xls,.xlsx,.pdf,.txt,.md'"
      :show-file-list="false"
    >
      <div class="upload-btn">
        <img class="upload-img" src="/icons/upload.png" alt="上传" />
        {{ localFileList.length > 0 ? "继续上传" : "点击上传" }}
      </div>
      <template #tip>
        <div
          v-for="(file, index) in localFileList"
          :key="index"
          class="file-list"
        >
          <div class="file-name-text">
            <div class="file-name">{{ file.fileName }}</div>
            <img
              class="file-type"
              v-if="file.status === 'loading'"
              src="/icons/loading.png"
              alt="上传中"
            />
            <img
              class="file-type"
              v-if="file.status === 'success'"
              src="/icons/success.png"
              alt="成功"
            />
            <img
              class="file-type"
              v-if="file.status === 'error'"
              src="/icons/error.png"
              alt="错误"
            />
          </div>
          <img
            class="file-delet-icon"
            src="/icons/delet.png"
            @click="removeFile(index)"
            alt="删除"
          />
        </div>
        <div class="el-upload__tip">仅支持doc/docx/excel/pdf/txt/md格式</div>
      </template>
    </el-upload>
  </div>
</template>

<script setup>
import { ref, watch, computed } from 'vue'
import { addResource } from '@/api/education/index.js'
import { ElMessage } from 'element-plus'

const props = defineProps({
  modelValue: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['modelValue'])
const nvLocalFileList = ref([])
const localFileList = ref([...props.modelValue])



const uploadFile = async (request) => {


  // 检查文件格式
  const allowedExtensions = ['.doc', '.docx', '.xls', '.xlsx', '.pdf', '.txt', '.md']
  const fileExtension = '.' + request.file.name.split('.').pop().toLowerCase()
  if (!allowedExtensions.includes(fileExtension)) {
    ElMessage.error('文件格式不支持')
    return
  }
  if (localFileList.value.length >= 5) {
    ElMessage.error('最多只能上传五个文件')
    return
  }



  const formData = new FormData()
  formData.append('file', request.file)

  const fileItem = {
    title: request.file.name,
    fileName: request.file.name,
    filePath: '',
    status: 'loading',
  }

  //   索引
  const fileIndex = localFileList.value.length
  localFileList.value.push(fileItem)

  try {
    const response = await addResource(formData)
    localFileList.value[fileIndex].filePath = response.url
    localFileList.value[fileIndex].status = 'success'
    request.onSuccess(response)

    emit('modelValue', localFileList.value)

  } catch (error) {
    localFileList.value[fileIndex].status = 'error'
    request.onError(error)
  }
}

const removeFile = (index) => {
  localFileList.value.splice(index, 1)
  emit('modelValue', localFileList.value)
}
</script>

<style scoped>
.upload-demo {
  margin-top: 16px;
}

.upload-btn {
  width: 102px;
  height: 32px;
  background: white;
  border-radius: 4px;
  border: 1px solid #eaeaec;
  display: flex;
  align-items: center;
  font-family: PingFangSC, PingFang SC;
  font-size: 12px;
  color: #1e1f24;
}

.upload-img {
  width: 18px;
  height: 18px;
  margin: 0 8px;
}

.file-list {
  margin-top: 8px;
  display: flex;
  align-items: center;
}

.file-name-text {
  width: 262px;
  height: 24px;
  background: #f0f0f0;
  border-radius: 4px;
  padding-left: 12px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.file-name {
  width: 220px;
  font-family: PingFangSC, PingFang SC;
  font-size: 11px;
  color: #1e1f24;
  line-height: 24px;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}

.file-type {
  width: 16px;
  height: 16px;
  margin-right: 8px;
}

.file-delet-icon {
  width: 16px;
  height: 16px;
  margin-left: 8px;
  cursor: pointer;
}

.el-upload__tip {
  font-family: PingFangSC, PingFang SC;
  font-size: 10px;
  color: #848691;
  margin-top: 12px;
}
</style>

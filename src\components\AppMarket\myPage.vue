<template>
  <div v-loading="loading" class="common-content">
    <el-row>
      <el-col :span="24">
        <span class="category">场景</span>
        <div class="divider" />
      </el-col>
    </el-row>
    <el-row>
      <el-col
        :span="6"
        v-for="(item, index) in filteredTeamList"
        :key="index"
        style="margin-bottom: 20px"
      >
        <app-card
          :app="item"
          :img="item.applicationIcon ? baseUrl + item.applicationIcon : TeamDefaultIcon"
          :title="item.applicationName"
          :detail="item.applicationDesc"
          :has-permission="true"
          :is-pinned="item.isIndex === 1"
        />
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="24">
        <span class="category">职业</span>
        <div class="divider" />
      </el-col>
    </el-row>
    <el-row>
      <el-col
        :span="6"
        v-for="(item, index) in filteredAgentList"
        :key="index"
        style="margin-bottom: 20px"
      >
        <app-card
          :app="item"
          :img="item.applicationIcon ? baseUrl + item.applicationIcon : AgentDefaultIcon"
          :title="item.applicationName"
          :detail="item.applicationDesc"
          :has-permission="true"
          :is-pinned="item.isIndex === 1"
        />
      </el-col>
    </el-row>

  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from "vue";
import { getTeamInfoList } from "@/api/teamManagement/team";
import { Star, StarFilled } from "@element-plus/icons-vue";
import AppCard from "./appCard.vue";
import AgentDefaultIcon from '@/assets/icons/svg/agent-default.svg'
import TeamDefaultIcon from '@/assets/icons/svg/team-default.svg'

const props = defineProps({
  /**
   * 搜索框关键词
   */
  keyword: {
    type: String,
    required: true
  },


  /**
   * 已分配权限的团队列表
   */
  teamList: {
    type: Array,
    required: true
  },

  /**
   * 已分配权限的智能体列表
   */
  agentList: {
    type: Array,
    required: true
  }
});

// 根据关键词过滤后的团队列表
const filteredTeamList = computed(() => {
  return (!props.keyword)
  ? [...props.teamList]
  : props.teamList.filter((item) => item.applicationName.toLowerCase().includes(props.keyword.toLowerCase()));
});

// 根据关键词过滤后的智能体列表
const filteredAgentList = computed(() => {
  return (!props.keyword)
  ? [...props.agentList]
  : props.agentList.filter((item) => item.applicationName.toLowerCase().includes(props.keyword.toLowerCase()));
});

// 加载状态
const loading = ref(false);

// 基础地址
const baseUrl = import.meta.env.VITE_APP_BASE_API;

// 事件定义
const emit = defineEmits(['use', 'apply']);
</script>

<style scoped>
.category {
  font-size: 18px;
  font-weight: bold;
}
.divider {
  border-bottom: 1px solid lightgray;
  width: 100%;
  height: 0px;
  margin: 5px 0 10px 0;
}
</style>
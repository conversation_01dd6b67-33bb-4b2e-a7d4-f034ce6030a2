<template>
    <div class="select-tool-item">
        <div class="icon">
            <template v-if="tool.type === 'builtin'">
                <div class="img" :style="{backgroundImage:`url(${tool.icon})`}"></div>
            </template>
            <template v-else>
                <span :style="{backgroundColor:tool.icon.background}" class="emoji">
                    <single-emoji :content="tool.icon.content"></single-emoji>
                </span>
            </template>
        </div>
        <div class="text">
            <div class="name">{{tool.label || '&nbsp;'}}</div>
            <div class="desc">{{tool.description || '&nbsp;'}}</div>
        </div>
        <el-button v-if="!selected" @click="selectTool" icon="Plus" plain type="primary">添加</el-button>
        <el-button v-else @click="cancelSelectTool" disabled plain type="info">已添加</el-button>
    </div>
</template>
<script setup>
import singleEmoji from '@/views/toolManagement/components/singleEmoji'
const props = defineProps({
    tool:{
        type:Object,
        default:()=>{return {}}
    },
    selected:{
        type:Boolean,
        default:false
    }
})

const emits = defineEmits(['select-tool','cancel-select-tool'])

const selectTool = () => {
    emits('select-tool',props.tool)
}

const cancelSelectTool = () => {
    emits('cancel-select-tool',props.tool)
}

</script>
<style lang="scss" scoped>
.select-tool-item {
    padding: .5rem;
    height: 4rem;
    display: flex;
    align-items: center;
    background-color: white;
    border-radius: .75rem;
    border: 2px solid rgb(234,236,240);
    border-color: transparent;
    box-shadow: 0 0 1px 0 rgba(0,0,0,.1);
    cursor: pointer;
    transition: all .2s;
    box-shadow: 0px 1px 2px 0px rgba(16,24,40,.06),0px 1px 3px 0px rgba(16,24,40,.1);
    &:hover{
        box-shadow: 0px 4px 6px -2px rgba(16,24,40,.03),0px 12px 16px -4px rgba(16,24,40,.08);
    }
    .icon{
        position: relative;
        flex-shrink: 0;
        margin-right: .75rem;
        .img{
            height: 2.5rem;
            width: 2.5rem;
            background-size: cover;
            background-position: 50%;
            background-repeat: no-repeat;
            border-radius: .375rem;
        }
        .emoji{
            height: 40px;
            width: 40px;
            border-radius: .5rem;
            display: flex;
            position: relative;
            align-items: center;
            justify-content: center;
            line-height: 1.75rem;
        }
    }
    .text{
        width: 0;
        padding-right: 10px;
        flex-grow: 1;
        .name{
            font-weight: 600;
            color: rgb(29, 41, 57);
            line-height: 1.5rem;
            font-size: 1rem;
            display: flex;
            align-items: center;
        }
        .desc{
            flex-grow: 1;
            overflow: hidden;
            max-height: 16px;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            text-overflow: ellipsis;
            color: rgb(102, 112, 133);
            line-height: 1rem;
            font-weight: 500;
            font-size: 12px;
            align-items: center;
        }
    }
}
</style>
<template>
    <div class="agent-team-item">
        <div class="agent-team-item-main" @click="jumpUrl?router.push(jumpUrl):''" :class="jumpUrl?'custom-cursor':''">
            <el-row>
                <el-col :span="4">
                    <div>
                        <template v-if="props.img == baseUrl + null || props.img == baseUrl + ''">
                        </template>
                        <template v-else>
                            <img :src="props.img" />
                        </template>
                    </div>
                </el-col>
                <el-col :span="20" class="item-text">
                    <el-row style="line-height: 20px;">
                        <el-col :span="!props.isDelTool?16:24">
                            <el-tooltip
                                    :content="props.title"
                                    placement="top"
                                    effect="customized"
                                    popper-class="card-pop"
                                >
                                <div class="text-title">
                                    {{ props.title }}
                                </div>
                            </el-tooltip>
                        </el-col>
                        <el-col :span="8" v-if="!props.isDelTool">
                            <div class="right-buttons">
                                <slot name="tool"></slot>
                            </div>
                        </el-col>
                    </el-row>
                    <el-row style="margin-top:10px">
                        <el-col>
                            <div class="text-content">
                                <el-tooltip
                                    :content="props.detail"
                                    placement="bottom"
                                    effect="customized"
                                    popper-class="card-pop"
                                >
                                    {{ props.detail&&props.detail.length>80?props.detail.substr(0,80)+'...':props.detail }}
                                </el-tooltip>
                            </div>
                        </el-col>
                    </el-row>
                </el-col>
            </el-row>
        </div>
        <div class="agent-team-item-footer" v-if="props.isNeedFooter">
            <slot name="footer"></slot>
        </div>
    </div>
</template>
<script setup>
const props = defineProps({
    img: {
        type: String,
        required: false
    },
    title: {
        type: String,
        required: false
    },
    detail: {
        type: String,
        required: false
    },
    isNeedFooter:{
        type: Boolean,
        required: false,
        default: false
    },
    isDelTool:{
        type: Boolean,
        required: false,
        default: false
    },
    jumpUrl:{
        type: String,
        required: false,
        default: null
    }
})

import router from "@/router";

const baseUrl = import.meta.env.VITE_APP_BASE_API;

</script>
<style scoped lang="scss">
.agent-team-item {
    margin: 0 10px;
    padding: 24px;
    padding-bottom: 10px;
    border-radius: 8px;
    background: #FFF;
    box-shadow: 0 4px 10px 0 #0000000d;
    display: flex;
    flex-direction: column;
    // height: 156px;
    overflow: hidden;
    .agent-team-item-main {
        // display: flex;
        // flex-direction: row;
        // justify-content: space-between;
        height: 90px;
        img {
            height: 85%;
            width: 85%;
            max-width: 75px;
            max-height: 75px;
            border-radius: 4px;
        }
        .empty-box{
            height: 85%;
            width:  85%;
            max-width: 75px;
            max-height: 75px;
            border-radius: 4px;
        }
        .item-text {
            // margin-left: 16px;
            // margin-right: 16px;
            min-width: 100px;
            padding-left: 5px;
            .text-title {
                max-height: 20px;
                color: #032220;
                overflow: hidden;
                font-size: 18px;
                font-weight: 600;
                white-space: nowrap;
                text-overflow: ellipsis;
                // margin-bottom: 4px;
            }
            .text-content {
                // height: 51px;
                max-height: 51px;
                overflow: hidden;
                text-overflow: ellipsis;
                font-size: 12px;
                color: #999999;
                // line-height:18px
            }
        }
    }
    .agent-team-item-footer{
        margin-top: 10px;
        display: flex;
        flex-direction: row;
        justify-items: center;
        align-items: center;
        border-top: 1px solid #E6E6E6;
    }
}
.right-buttons{
    //min-width: 100px;
    display: flex;
    box-sizing: border-box;
    justify-content: end;
    align-items: center;
    max-height: 24px;
    ::v-deep .el-button{
        padding: 0 3px !important;
        height: 20px !important;
    }
}
.custom-cursor { cursor: pointer; }
</style>
<style lang="scss">
    .card-pop.el-popper{
        max-width: 450px !important;
    }
    .el-popper.is-customized {
    /* Set padding to ensure the height is 32px */
        // padding: 6px 12px;
        background: #E6E6FA
        //#E6E6FA
        //linear-gradient(90deg, rgb(159, 229, 151), rgb(204, 229, 129));
    }
    .el-popper.is-customized .el-popper__arrow::before {
        background: #E6E6FA;
        right: 0;
    }
</style>
import request from '@/utils/conversationClientRequest'

// 应用数据看板
export function getDataBoard(params,app_id) {
    return request({
        url: 'dashboard/app/'+app_id,
        method: 'get',
        params
    })
}

//活跃用户数
export function getActiveUsers(params,app_id) {
    return request({
        url: 'dashboard/app/'+app_id+'/chart/user',
        method: 'get',
        params
    })
}

//使用数
export function getConversations(params,app_id) {
    return request({
        url: 'dashboard/app/'+app_id+'/chart/conversation',
        method: 'get',
        params
    })
}

//消息数
export function getChatMessages(params,app_id) {
    return request({
        url: 'dashboard/app/'+app_id+'/chart/messages',
        method: 'get',
        params
    })
}

//Token消耗数
export function getTokensUsage(params,app_id) {
    return request({
        url: 'dashboard/app/'+app_id+'/chart/tokens',
        method: 'get',
        params
    })
}
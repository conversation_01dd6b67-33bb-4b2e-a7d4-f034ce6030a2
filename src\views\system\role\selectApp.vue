<template>
    <!-- 授权应用 -->
    <el-dialog title="选择应用" v-model="visible" width="800px" top="5vh" append-to-body>
       <el-form :model="queryParams" ref="queryRef" :inline="true">
        <el-form-item label="应用名称" prop="applicationName">
             <el-input
                v-model="queryParams.applicationName"
                placeholder="请输入应用名称"
                clearable
                style="width: 240px"
                @keyup.enter="handleQuery"
             />
          </el-form-item>
          <el-form-item label="应用类型" prop="applicationType">
             <el-select
                v-model="queryParams.applicationType"
                placeholder="请选择应用类型"
                clearable
                style="width: 240px"
             >
                <el-option
                   :label="'智能体'"
                   :value="'agent'"
                />
                <el-option
                   :label="'团队'"
                   :value="'team'"
                />
             </el-select>
          </el-form-item>
          <el-form-item>
             <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
             <el-button icon="Refresh" @click="resetQuery">重置</el-button>
          </el-form-item>
       </el-form>
       <el-row>
          <el-table @row-click="clickRow" ref="refTable" :data="appList" @selection-change="handleSelectionChange" height="560px">
             <el-table-column type="selection" width="55"></el-table-column>
             <el-table-column label="应用名称" prop="applicationName" :show-overflow-tooltip="true" />
             <el-table-column label="应用类型" align="center" prop="applicationType" width="180">
                <template #default="scope">
                   <span>{{ scope.row.applicationType === 'agent' ? '智能体' : '团队'}}</span>
                </template>
             </el-table-column>
             <el-table-column label="应用描述" prop="applicationDesc" :show-overflow-tooltip="true" />
             <!-- <el-table-column label="邮箱" prop="email" :show-overflow-tooltip="true" />
             <el-table-column label="手机" prop="phonenumber" :show-overflow-tooltip="true" />
             <el-table-column label="状态" align="center" prop="status">
                <template #default="scope">
                   <dict-tag :options="sys_normal_disable" :value="scope.row.status" />
                </template>
             </el-table-column>
             <el-table-column label="创建时间" align="center" prop="createTime" width="180">
                <template #default="scope">
                   <span>{{ parseTime(scope.row.createTime) }}</span>
                </template>
             </el-table-column> -->
          </el-table>
          <!-- <pagination
             v-show="total > 0"
             :total="total"
             v-model:page="queryParams.pageNum"
             v-model:limit="queryParams.pageSize"
             @pagination="getList"
          /> -->
       </el-row>
       <template #footer>
          <div class="dialog-footer">
             <el-button type="primary" @click="handleSelectUser">确 定</el-button>
             <el-button @click="visible = false">取 消</el-button>
          </div>
       </template>
    </el-dialog>
 </template>
 
 <script setup name="SelectApp">
 import { authUserSelectAllApp, unallocatedAppList,unallocatedNoPageList } from "@/api/system/role";
 
 const props = defineProps({
   roleId: {
     type: [Number, String]
   }
 });
 
 const { proxy } = getCurrentInstance();
 const { sys_normal_disable } = proxy.useDict("sys_normal_disable");
 
 const appList = ref([]);
 const visible = ref(false);
 const total = ref(0);
 const userIds = ref([]);
 
 const queryParams = reactive({
   roleId: undefined,
   applicationName: undefined,
   applicationType: undefined
 });
 
 // 显示弹框
 function show() {
   queryParams.roleId = props.roleId;
   getList();
   visible.value = true;
 }
 /**选择行 */
 function clickRow(row) {
   proxy.$refs["refTable"].toggleRowSelection(row);
 }
 // 多选框选中数据
 function handleSelectionChange(selection) {
   userIds.value = selection.map(item => {item.roleId = props.roleId ; return item;});
 }
 // 查询表数据
 function getList() {
   unallocatedNoPageList(queryParams).then(res => {
     appList.value = res.data;
   });
 }
 /** 搜索按钮操作 */
 function handleQuery() {
   getList();
 }
 /** 重置按钮操作 */
 function resetQuery() {
   proxy.resetForm("queryRef");
   handleQuery();
 }
 const emit = defineEmits(["ok"]);
 /** 选择授权用户操作 */
 function handleSelectUser() {
   const roleId = queryParams.roleId;
   const uIds = userIds.value.join(",");
   if (uIds == "") {
     proxy.$modal.msgError("请选择要分配的应用");
     return;
   }
   authUserSelectAllApp(userIds.value).then(res => {
     proxy.$modal.msgSuccess(res.msg);
     if (res.code === 200) {
       visible.value = false;
       emit("ok");
     }
   });
 }
 
 defineExpose({
   show,
 });
 </script>
 
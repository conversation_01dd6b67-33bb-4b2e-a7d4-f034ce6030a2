<template>
  <div
    class="container"
    style="
      --el-dialog-bg-color: rgba(11, 83, 116, 0.8);
      --el-dialog-box-shadow: 2px 2px 10px 2px rgba(36, 66, 179, 0.4);
      --el-input-border-color: #05c0dc;
      --el-border-color: #05c0dc;
      --el-border-color-hover: #05c0dc;
      --el-color-primary: #05c0dc;
      --el-fill-color-blank: rgba(0, 229, 255, 0.18);
      --el-text-color-regular: #fff;
    "
  >
    <div class="tools">
      <tools id="tools" :isFinish="isFinish" />
    </div>
    <div class="content">
      <div class="left">
        <toggleMenu />
      </div>
      <div class="middle"><main-vue /></div>
      <div class="right" v-if="popType" id="panel"><chat /></div>
    </div>
  </div>
</template>
<script setup>
import { ref, reactive, toRefs, onMounted } from "vue";
import tools from "./components/tools.vue";
import toggleMenu from "./components/toggleMenu";
import MainVue from "./components/main";
import chat from "./components/chat.vue";
import {
  popType,
  menuList,
  referId,
  // form,
  paragraphs,
  configData,
} from "@/views/briefGeneration/add/create/js/sharedState.js";
import { useRoute, useRouter } from "vue-router";
import { reportDetail } from "@/api/briefGeneration/index.js";
const isFinish = ref(false); //true 已完稿
const route = useRoute();
function showRightPanel(which) {
  popType.value = which;
}
provide("showRightPanel", showRightPanel);
onMounted(() => {
  if (route.params.type == "add") {
    // menuList.value = [];
    // // 获取目录列表
    // getMenuList({
    //   structureVersion: 1,
    //   analysisStructureType: 1,
    // });
    // getMenuList({
    //   structureVersion: 1,
    //   analysisStructureType: 2,
    // });
    // getMenuList({
    //   structureVersion: 1,
    //   analysisStructureType: 3,
    // });
    // getMenuList({
    //   structureVersion: 1,
    //   analysisStructureType: 4,
    // });
    // getMenuList({
    //   structureVersion: 1,
    //   analysisStructureType: 5,
    // });
  } else if (route.params.type == "edit" || route.params.type == "view") {
    // 获取目录详情
    reportDetail({
      id: route.params.id,
    }).then((res) => {
      console.log("res :>> ", res);
      let obj = res.data || {};
      let option = JSON.parse(obj.option);
      if (res.code === 200) {
        // form.value = {
        //   title: obj.title,
        //   subject: obj.subject,
        //   stage: obj.stage, // 研究阶段的默认值
        //   businessVersion: obj.businessVersion, // 行业分析框架的默认值
        //   competeVersion: obj.competeVersion, // 竞争分析框架的默认值
        //   clientVersion: obj.clientVersion, // 顾客认知分析框架的默认值
        //   manageVersion: obj.manageVersion, // 企业经营分析框架的默认值
        //   entrepreneurVersion: obj.entrepreneurVersion, // 企业家分析框架的默认值
        //   knowledge: option.useKonowledgeBase == 1 ? true : false,
        //   internet: option.useInterNet == 1 ? true : false,
        // };
        configData.value = {
          title: obj.title,
          brandName: option.brandName,
          version: option.version,
          dataBank: option.dataBank,
          internet: option.internet,
          labelConfigId: option.labelConfigId,
        };
        menuList.value = JSON.parse(JSON.parse(obj.catalog));
        referId.value = obj.referId;
        paragraphs.value = JSON.parse(obj.context) || [];
        console.log("paragraphs :>> ", paragraphs.value);

        isFinish.value = obj.status == 1;
      }
    });
  }
});
</script>
<style lang='scss' scoped>
.container {
  height: 100%;
  display: flex;
  flex-direction: column;
  // background: #0d1e4f;
  // background: skyblue;
  font-family: HarmonyOS Sans SC;
  color: #fff;
  padding: 0 10px;
  .tools {
    height: 45px;
    flex: none;
    min-height: 0;
  }
  .content {
    flex: 1;
    min-height: 0;
    display: flex;
    padding-bottom: 13px;
    .left {
      border-radius: 4px;
      border: 1px solid #0a7eb6;
      margin-right: 14px;
      background-color: rgba(17, 49, 78, 0.8);
    }
    .middle {
      flex: 1;
      min-width: 0;
      border-radius: 4px;
      border: 1px solid #0a7eb6;
      margin-right: 14px;
      background-color: rgba(17, 49, 78, 0.8);
    }
    .right {
      border-radius: 4px;
      border: 1px solid #0a7eb6;
      background-color: rgba(17, 49, 78, 0.7);
    }
  }
}
</style>
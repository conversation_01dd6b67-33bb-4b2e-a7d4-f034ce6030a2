<template>
    <div>  
        <div class="common-container">
            <div class="common-header">
                <div class="title">店铺列表</div>
            </div> 
            <div v-loading="loading" class="common-content">
                <el-row>
                    <el-col :span="8" v-for="(item,index) in shopList" :key="index"  style="margin-bottom: 20px;">
                        <custom-card :img="baseUrl+item.shopImage" 
                        :title="item.shopName" :detail="item.shopDesc"  :is-need-footer="true">
                            <template #footer>
                                <div class="agent-team-item-button">
                                    <el-button type="primary" size="large" style="width: 100%;" text @click="shopDesign(item)">
                                        <span style="font-weight: 600;">店铺设计</span>
                                    </el-button>
                                </div>
                                <div class="agent-team-item-button">
                                    <el-button type="primary" size="large" style="width: 100%;" text @click="storeOperations(item)">
                                        <span style="font-weight: 600;">店铺运营</span>
                                    </el-button>
                                </div>
                            </template>
                        </custom-card>
                    </el-col>
                </el-row>
            </div>
        </div>
        <!--编辑店铺设计-->
        <el-dialog v-model="shopDesignVisible" title="店铺设计" center>
            <div style="height: calc(100vh - 295px);overflow-y: auto;padding:0 20px">
                <el-form ref="shopDesignInfoForm" :model="shopDesignInfo" :rules="shopDesignRules" label-width="120px" :label-position="'top'">
                    <span style="font-size: 20px;">商品</span>
                    <el-row style="margin-top:10px">
                        <el-col :span="8">
                            <el-form-item label="品牌特性" prop="brandCharacteristics" style="margin-right:10px">
                                <el-input v-model="shopDesignInfo.brandCharacteristics" placeholder="如'创新'、'可靠'、'环保'等。"/>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="目标市场" prop="agentTemplate" style="margin-right:10px">
                                <el-input v-model="shopDesignInfo.agentTemplate" placeholder="如'年轻人'、'专业人士'等。"/>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="产品类型" prop="productType">
                                <el-input v-model="shopDesignInfo.productType" placeholder="如'时尚'、'科技'等。"/>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <span style="font-size: 20px;">人设</span>
                    <el-row style="margin-top:10px">
                        <el-col :span="12">
                            <el-form-item label="个人故事" prop="personalStory" style="margin-right:10px">
                                <el-input v-model="shopDesignInfo.personalStory" placeholder="请输入个人故事"  type="textarea" rows="5"/>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="品牌价值观" prop="brandValues">
                                <el-input v-model="shopDesignInfo.brandValues" placeholder="请输入品牌价值观"   type="textarea" rows="5"/>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="24">
                            <el-form-item label="品牌起源" prop="brandRigin">
                                <el-input v-model="shopDesignInfo.brandRigin" placeholder="请输入品牌起源"  type="textarea" rows="5"/>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <span style="font-size: 20px;">上传文件<span style="font-size: 12px;color:red;">（上传店铺背景信息文档）</span></span>
                    <div>
                        <file-upload-pdf
                            ref="uploadPdfRef"
                            :limit="10"
                            :file-type="['docx']"
                            v-model="shopDesignInfo.shopUrl"
                        >
                            <el-icon class="el-icon--upload"><upload-filled /></el-icon>
                            <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
                        </file-upload-pdf>
                    </div>
                </el-form>
            </div>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="cancel">取消</el-button>
                    <el-button type="primary" @click="submitShopDesign">保存</el-button>
                </span>
            </template>
        </el-dialog>
    </div>
</template>
<script setup>
import { ref, reactive, toRefs } from 'vue'
import { getToken } from "@/utils/auth";
import CustomCard from '@/components/CustomCard/index'
import FileUploadPdf from'@/components/FileUploadPdf/index'
import { getShopList,ecommerceEdit,getEcommerceInfo } from "@/api/ecommerce/shop";
const router = useRouter();
const baseUrl = import.meta.env.VITE_APP_BASE_API;

const { proxy } = getCurrentInstance();

const loading = ref(false)
//店铺列表
const shopList = ref([])

//店铺设计弹框
const shopDesignVisible = ref(false)
//店铺设计弹框表单
const shopDesignInfo = ref({
    id:'',
    //品牌特性
    brandCharacteristics:'',
    //目标市场
    agentTemplate:'',
    //产品类型
    productType:'',
    //个人故事
    personalStory:'',
    //品牌价值观
    brandValues:'',
    //品牌起源
    brandRigin:'',
    shopUrl:''
})
/*** 上传文件参数 */
const shopDesignUpload = reactive({
  // 是否禁用上传
  isUploading: false,
  // 设置上传的请求头部
  headers: { Authorization: "Bearer " + getToken() },
  // 上传的地址
  url: import.meta.env.VITE_APP_BASE_API + "/system/user/importData"
});
//店铺设计弹框表单规则校验
const shopDesignRules = ref({})

//店铺设计
const shopDesign = (row) => {
    if(row.id){
        getEcommerceInfo(row.id).then(response => {
            shopDesignInfo.value=response.data;
            shopDesignVisible.value = true;
        })
    }
}

//保存店铺设计
const submitShopDesign = () => {
    ecommerceEdit(shopDesignInfo.value).then(response => {
        proxy.$modal.msgSuccess("保存成功");
        shopDesignVisible.value = false;
    })
}

//取消
const cancel = () => {
    shopDesignVisible.value=false;
}

//店铺列表
const getEcommerceShopList = () => {
    loading.value=true;
    getShopList().then(response => {
        shopList.value=response.rows;
        loading.value=false;
    })
}

//店铺运营
const storeOperations = (item) => {
    router.push('/ecommerceTools?shopId='+item.id);
}

getEcommerceShopList()
</script>
<style lang='scss' scoped>
.common-container {
    display: flex;
    flex-direction: column;
    background-color: #F7F7FA;
    height: calc(100vh - 52px);
    width: 100%;
    margin: 0;
    padding: 0;
    background-size: cover;
}
.common-header {
    height: 80px;
    padding: 28px 30px;
    line-height: 24px;
    display: flex;
    justify-content: space-between;

    .title {
        color: #032220;
        font-size: 18px;
        font-weight: 500;
    }

    .header-right {
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: row;
    }
}
.common-content {
    margin: 0 20px;
    height: calc(100% - 68px);
}
::v-deep .el-card__footer {
    padding: calc(var(--el-card-padding) - 15px) var(--el-card-padding);
    border-top: 1px solid var(--el-card-border-color);
    box-sizing: border-box;
}
.agent-team-item-button {
    display: grid;
    place-items: center;
    margin-top: 4px;
    width: 50%;
}
</style>
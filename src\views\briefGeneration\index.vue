<template>
  <div
    :class="['app-wrapper', {'bg-dark': !isHome}, {home: isHome}]"
  >
    <side-menu class="menu-container" />
    <div class="primary-container">
      <div class="header-wrapper">
        <common-header :crumbs="curCrumbs" />
      </div>
      <div class="body-wrapper">
        <router-view></router-view>
      </div>
    </div>
  </div>
</template>

<script setup>
import SideMenu from './menu.vue'
import { useRoute } from 'vue-router'
import CommonHeader from './header.vue'

const route = useRoute()

// ----------------------------- 屏幕适配 ---------------------------------

// 是否首页
const isHome = computed(() => {
  return route.path === '/briefGeneration/home'
})

// 设备类型
const deviceType = ref('desktop')

// 页面比例
const pageRatioClass = ref('')

/**
 * 屏幕尺寸适配
 */
function fitScreen() {
  const width = window.innerWidth
  const height = window.innerHeight
  const root = document.getRootNode().children[0]

  pageRatioClass.value = 'page-ratio-' + (height / width * 10).toFixed(0)

  if (width <= 767) {
    deviceType.value = "mobile"
  } else  if (width > 767 && width <= 1024) {
    deviceType.value = "tablet"
  } else {
    deviceType.value = 'desktop'
  }

  if (deviceType.value === 'desktop') {
    if (width > 1440) {
      root.style.fontSize = (width / 1440).toFixed(1) + 'px'
    } else {
      root.style.fontSize = '1px'
    }
  }
}

fitScreen()
window.addEventListener('resize', () => {
  fitScreen()
})


// ------------------------------ 面包屑 ----------------------------------

// 当前路径的面包屑
const curCrumbs = ref([])

const pathCrumbsMap = {
  '/briefGeneration/add/config': [
    {
      name: '项目空间',
    },
    {
      name: '个人研究',
      path: '/briefGeneration/list'
    },
    {
      name: '新建研究',
    }
  ],
  '/briefGeneration/list': [
    {
      name: '项目空间',
    },
    {
      name: '个人研究',
    }
  ],
  '/briefGeneration/add/create/view/\\d+': [
    {
      name: '项目空间',
    },
    {
      name: '个人研究',
      path: '/briefGeneration/list'
    },
    {
      name: '简报查看',
    }
  ],
  '/briefGeneration/add/create/edit/\\d+': [
    {
      name: '项目空间',
    },
    {
      name: '个人研究',
      path: '/briefGeneration/list'
    },
    {
      name: '简报编辑',
    }
  ],
  '/briefGeneration/add/create/add': [
    {
      name: '项目空间',
    },
    {
      name: '个人研究',
      path: '/briefGeneration/list'
    },
    {
      name: '新建简报',
      path: '/briefGeneration/add/config'
    },
    {
      name: '简报生成',
    }
  ],
  '/briefGeneration/projectMannage': [
    {
      name: '项目管理',
    },
  ],
}

watch(() => route.path, () => {
  const curPath = route.path
  let crumbs = []
  for (const pathPattern in pathCrumbsMap) {
    const pathRegexp = new RegExp(pathPattern)
    if (pathRegexp.test(curPath)) {
      crumbs = pathCrumbsMap[pathPattern]
      break
    }
  }
  setCurCrumbs(crumbs)
}, {immediate: true})

// 设置面包屑
function setCurCrumbs(crumbs) {
  curCrumbs.value = crumbs
}
</script>

<style lang="scss" scoped>
@import "@/assets/styles/mixin.scss";
@import "@/assets/styles/variables.module.scss";

.app-wrapper {
  @include clearfix;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: stretch;
  position: relative;
  height: 100vh;
  width: 100vw;
  overflow: hidden;
  background-image: url(/images/bg-star.png);
  background-size: 100% 100%;
  background-position: 0 0;

  &.home {
    :deep(.menu-container) {
      border-right: 0;
    }

    &::before {
      content: ' ';
      display: block;
      width: calc(100% - 24px);
      height: 0;
      border-bottom: 1px solid #00E5FF;
      position: absolute;
      left: 12px;
      top: 79px;
    }
  }

  &::before {
    content: ' ';
    display: block;
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    z-index: 0;
  }

  &.bg-dark::before {
    background-color: #002851;
    opacity: 0.7;
  }

  .menu-container {
    width: 150px;
    flex-grow: 0;
    flex-shrink: 0;
    z-index: 1;
  }

  .primary-container {
    flex-basis: 500px;
    flex-grow: 1;
    flex-shrink: 1;
    z-index: 1;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: stretch;
    min-width: 0;

    .header-wrapper {
      height: 80px;
      flex-grow: 0;
      flex-shrink: 0;      
    }

    .body-wrapper {
      flex-grow: 0;
      flex-shrink: 0;
      height: calc(100% - 80px);
    }
  }
  :deep(.el-dialog) {
    --el-bg-color: rgba(11, 83, 116, 0.8);
    --el-text-color-primary: #4bafff;
    --el-text-color-regular: #4bafff;
    --el-color-primary: #4bafff;
    --el-color-primary-light-3: #4bafff;
  }
  :deep(.el-table) {
    --el-table-tr-bg-color:rgba(13,52,102,0.4);
    --el-table-text-color:#fff;
    --el-table-header-text-color:#00EDFF;
    --el-table-header-bg-color:#0D3466;
    --el-table-border-color:transparent;
    --el-fill-color-lighter:rgba(0,237,255,0.2);
    .el-table__header-wrapper,
    .el-table__fixed-header-wrapper {
      th {
        background-color: #0D3466 !important;
			  height: 43px !important;
			  color: #00EDFF;
      }
    }
  }
}
</style>
<template>
  <div class="sidebar-logo-container" :class="{ collapse: collapse }">
    <transition name="sidebarLogoFade">
      <router-link
        v-if="collapse"
        key="collapse"
        class="sidebar-logo-link"
        to="/briefGeneration"
      >
        <img :src="collapseLogo" class="sidebar-logo" />
      </router-link>
      <router-link v-else key="expand" class="sidebar-logo-link" to="/briefGeneration">
        <img :src="logo" class="sidebar-logo" />
      </router-link>
    </transition>
  </div>
</template>

<script setup>
import variables from "@/assets/styles/variables.module.scss";
import logo from "@/assets/logo/logo.png";
import collapseLogo from "@/assets/logo/collapseLogo.png";
import useSettingsStore from "@/store/modules/settings";

defineProps({
  collapse: {
    type: Boolean,
    required: true,
  },
});

const title = import.meta.env.VITE_APP_TITLE;
const settingsStore = useSettingsStore();
const sideTheme = computed(() => settingsStore.sideTheme);
</script>

<style lang="scss" scoped>
.sidebarLogoFade-enter-active {
  transition: opacity 1.5s;
}

.sidebarLogoFade-enter,
.sidebarLogoFade-leave-to {
  opacity: 0;
}

.sidebar-logo-container {
  margin-top: 45px;
  position: relative;
  width: 100%;
  height: 40px;
  line-height: 40px;
  background: transparent;
  text-align: center;
  overflow: hidden;

  & .sidebar-logo-link {
    display: flex !important;
    padding: 0 15px;
    flex-direction: row;
    align-items: center;
    height: 100%;
    width: 100%;
    .sidebar-logo{
      height: 40px;
    }
  }
}
</style>
<template>
    <div >
      <el-upload
        multiple
        drag
        :action="uploadFileUrl"
        :before-upload="handleBeforeUpload"
        :file-list="fileList"
        :limit="props.limit"
        :on-error="handleUploadError"
        :on-exceed="handleExceed"
        :on-success="handleUploadSuccess"
        :show-file-list="false"
        :headers="headers"
        :data="{source:'datasets'}"
        ref="fileUpload"
        :disabled="props.disabled"
      >
        <!-- 上传按钮 -->
        <el-icon class="el-icon--upload" style="color: #5050E6;" size="25"><upload-filled /></el-icon>
        <div class="el-upload__text">
            点击上传或拖拽文档到这里
        </div>
        <div class="el-upload__tip" style="font-size: 12px;color: #888d92;">
            支持 {{props.fileType.join(', ')}},每个文件不超过 15MB
        </div>
      </el-upload>
      <template v-if="fileList.length > 0">
        <el-table :data="fileList" style="margin-top: 20px;" border>
          <el-table-column label="文档名称" prop="name">
              <!-- <template #default="{ row }">
                  <el-input v-model="row.name"></el-input>
              </template> -->
          </el-table-column>
          <!-- <el-table-column label="状态" prop="status"></el-table-column> -->
          <el-table-column label="文件大小" prop="size">
              <template #default="{ row }">
                  {{ row.size >= 1024*1024*0.1 ? (row.size/1024/1024).toFixed(2) + 'MB' : (row.size/1024).toFixed(2) + 'KB'}}
              </template>
          </el-table-column>
          <el-table-column label="操作" prop="name" width="120">
              <template #default="{ row,$index }">
                  <el-button text type="info" @click="handleDelete($index)" :icon="Delete"></el-button>
              </template>
          </el-table-column>
        </el-table>
      </template>
      
    </div>
  </template>
  
  <script setup>
  import { getToken } from "@/utils/auth";
  import {Delete} from '@element-plus/icons-vue'
  
  const props = defineProps({
    modelValue: [String, Object, Array],
    // 数量限制
    limit: {
      type: Number,
      default: 10,
    },
    // 大小限制(MB)
    fileSize: {
      type: Number,
      default: 15,
    },
    // 文件类型, 例如['png', 'jpg', 'jpeg']
    fileType: {
      type: Array,
      default: () => ["TXT", "MARKDOWN", "PPTX", "PDF", "HTML","XLSX", "XLS", "DOCX", "CSV"],
    },
    // 是否显示提示
    isShowTip: {
      type: Boolean,
      default: true
    },
    disabled:{
      type: Boolean,
      default: false
    }
  });
  
  const { proxy } = getCurrentInstance();
  const emit = defineEmits();
  const number = ref(0);
  const uploadList = ref([]);
  const baseUrl = import.meta.env.VITE_APP_DIFY_API;
  const uploadFileUrl = ref(`${baseUrl}/console/api/files/upload`); // 上传文件服务器地址
  const headers = ref({ Authorization: "Bearer " + getToken() });
  // const headers = ref({});
  const fileList = ref([]);
  const showTip = computed(
    () => props.isShowTip && (props.fileType || props.fileSize)
  );
  
  watch(() => props.modelValue, val => {
    if (val) {
      let temp = 1;
      // 首先将值转为数组
      const list = Array.isArray(val) ? val : props.modelValue.split(',');
      // 然后将数组转为对象数组
      fileList.value = list.map(item => {
        if (typeof item === "string") {
          item = { name: item, url: item };
        }
        item.uid = item.uid || new Date().getTime() + temp++;
        return item;
      });
    } else {
      fileList.value = [];
      return [];
    }
  },{ deep: true, immediate: true });
  
  // 上传前校检格式和大小
  function handleBeforeUpload(file) {
    // 校检文件类型
    if (props.fileType.length) {
      const fileName = file.name.split('.');
      const fileExt = fileName[fileName.length - 1];
      const isTypeOk = ['md',...props.fileType.map(item => item.toLowerCase())].indexOf(fileExt) >= 0;
      if (!isTypeOk) {
        proxy.$modal.msgError(`文件格式不正确, 请上传${props.fileType.join("/")}格式文件!`);
        return false;
      }
    }
    // 校检文件大小
    if (props.fileSize) {
      const isLt = file.size / 1024 / 1024 < props.fileSize;
      if (!isLt) {
        proxy.$modal.msgError(`上传文件大小不能超过 ${props.fileSize} MB!`);
        return false;
      }
    }
    proxy.$modal.loading("正在上传文件，请稍候...");
    number.value++;
    return true;
  }
  
  // 文件个数超出
  function handleExceed() {
    proxy.$modal.msgError(`上传文件数量不能超过 ${props.limit} 个!`);
  }
  
  // 上传失败
  function handleUploadError(err) {
      console.log('error')
    proxy.$modal.msgError("上传文件失败");
  }
  
  // 上传成功回调
  function handleUploadSuccess(res, file) {
    uploadList.value.push({ name: res.name, id: res.id ,size: res.size});
      uploadedSuccessfully();
    // if (res.code === 200 || res.code === 201) {
    //   console.log('success')
      
    // } else {
    //   console.log('error')
    //   number.value--;
    //   proxy.$modal.closeLoading();
    //   proxy.$modal.msgError(res.msg);
    //   proxy.$refs.fileUpload.handleRemove(file);
    //   uploadedSuccessfully();
    // }
  }
  
  // 删除文件
  function handleDelete(index) {
    fileList.value.splice(index, 1);
    emit("update:modelValue", fileList.value);
  }
  
  // 上传结束处理
  function uploadedSuccessfully() {
    if (number.value > 0 && uploadList.value.length === number.value) {
      fileList.value = fileList.value.filter(f => f.id !== undefined).concat(uploadList.value);
      uploadList.value = [];
      number.value = 0;
      emit("update:modelValue", fileList.value);
      proxy.$modal.closeLoading();
    }
  }
  
  // 获取文件名称
  function getFileName(name) {
    // 如果是url那么取最后的名字 如果不是直接返回
    if (name.lastIndexOf("/") > -1) {
      return name.slice(name.lastIndexOf("/") + 1);
    } else {
      return name;
    }
  }
  
  // 对象转成指定字符串分隔
  function listToString(list, separator) {
    let strs = "";
    separator = separator || ",";
    for (let i in list) {
      if (list[i].id) {
        strs += list[i].id + separator;
      }
    }
    return strs != '' ? strs.substr(0, strs.length - 1) : '';
  }
  </script>
  
  <style scoped lang="scss">
  .upload-file-uploader {
    margin-bottom: 5px;
  }
  .upload-file-list .el-upload-list__item {
    border: 1px solid #e4e7ed;
    line-height: 2;
    margin-bottom: 10px;
    position: relative;
  }
  .upload-file-list .ele-upload-list__item-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: inherit;
  }
  .ele-upload-list__item-content-action .el-link {
    margin-right: 10px;
  }
  </style>
  
<template>
    <el-drawer v-model="editBodyDrawerVisible" :with-header="false" size="100%">
        <slot name="content" v-if="editBodyDrawerVisible">

        </slot>
    </el-drawer>
</template>
<script setup>
import { ref } from 'vue'
const editBodyDrawerVisible = ref(false)
const open = () => {
    editBodyDrawerVisible.value = true
}
const close = () => {
    editBodyDrawerVisible.value = false
}
defineExpose({
    open,
    close
})
</script>
<template>
  <div class="tiny-link" @click="openLink">
    <img :src="icon" alt="icon" class="icon" />
    <span class="title">{{ title }}</span>
  </div>
</template>

<script lang="ts" setup>
interface Props {
  icon: string;
  title: string;
  url: string;
}

const props = defineProps<Props>();

const openLink = () => {
  window.open(props.url, '_blank');
};
</script>

<style scoped>
.tiny-link {
  display: inline-flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  gap: 8px;
  padding: 0 12px 0 8px;
  border: 1px solid #eaecec;
  background-color: white;
  cursor: pointer;
  border-radius: 8px;
  height: 32px;
}

.icon {
  width: 24px;
  height: 24px;
}

.title {
  font-size: 12px;
  color: #333;
  font-weight: bold;
}
</style>

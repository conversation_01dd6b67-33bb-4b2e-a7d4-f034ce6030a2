export default {
  toolbar: {
    undo: '恢复',
    redo: '重做',
    bold: '强调',
    italic: '倾斜',
    strikethrough: '删除线',
    underline: '下划线',
    subscript: '下标',
    superscript: '上标',
    highlight: '高光',
    heading1: '一级标题',
    heading2: '二级标题',
    heading3: '三级标题',
    bulletedList: '符号列表',
    numberedList: '编号列表',
    taskList: '任务列表',
    quote: '引用',
    codeBlock: '代码',
    link: '链接',
    unlink: '取消链接',
    insertTable: '插入表格',
    deleteTable: '删除表格',
    addColBefore: '在左侧插入列',
    addColAfter: '在右侧插入列',
    deleteColumn: '删除列',
    addRowBefore: '在上方插入行',
    addRowAfter: '在下方插入行',
    deleteRow: '删除行',
    splitCell: '拆分单元格',
    mergeCells: '合并单元格',
    horizontalRule: '插入水平线',
    insertNetworkImage: '插入网络图片',
    uploadLocalImage: '上传本地图片',
  },
  insertNetworkImageDialog: {
    title: '图片',
    cancel: '取消',
    ok: '确定',
    field: {
      title: {
        label: '标题',
        placeholder: '请输入标题',
      },
      url: {
        label: '地址',
        placeholder: 'https://',
        emptyErr: '请输入图片地址',
        invalidErr: '图片地址必须以http://或https://开头',
      },
    },
  },
  insertLinkDialog: {
    title: '链接',
    cancel: '取消',
    ok: '确定',
    field: {
      title: {
        label: '标题',
        placeholder: '请输入标题',
        emptyErr: '请输入标题'
      },
      url: {
        label: '地址',
        placeholder: 'https://',
        emptyErr: '请输入链接地址',
        invalidErr: '链接地址必须以http://或https://开头',
      },
    },
  },
  insertTableBoard: {
    title: '插入表格',
    info: '{row}行 {col}列',
  },
}

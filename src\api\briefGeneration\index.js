import request from "@/utils/request";

//保存简报/提交简报
export function reportSave(data) {
  return request({
    url: "/report/save",
    method: "post",
    data,
  });
}
// 查看简报
export function reportDetail(params) {
  return request({
    url: "/report/query",
    method: "get",
    params,
  });
}
// 下载简报
export function reportDownload(params) {
  return request({
    url: "/report/download",
    method: "get",
    params,
  });
}
// 添加竞品
export function addCompetitor(data) {
  return request({
    url: "http://180.169.228.166:5003/console/api/apps/save-user-message",
    method: "post",
    data,
  });
}
// 获取调用
export function getAppSecretKey(params = {}) {
  return request({
    url: "/label/list",
    method: "get",
    params,
  });
}
// 通知后端在后台跑工作流任务
export function workflowtaskStart(data) {
  return request({
    url: "/workflowtask/start",
    method: "post",
    data,
  });
}
// 获取后台任务完成状态
export function workflowtaskStatus(params = {}) {
  return request({
    url: "/workflowtask/query",
    method: "get",
    params,
  });
}
// 获取首页输入框下的标签列表
export function getHomeLabels(params = {}) {
  return request({
    url: "/label/listconfig",
    method: "get",
    params,
  });
}

import request from '@/utils/request'

//获取编排团队详情
export function getTeamDetail(id) {
    return request({
        url: '/team/team/getInfo/'+id,
        method: 'get'
    })
}

//发布团队
export function publishTeam(data) {
    return request({
        url: '/team/team',
        method: 'put',
        data
    })
}

//获取工具列表
export function getToolList() {
    return request({
        url: '/system/tool/tlist',
        method: 'get'
    })
}

//获取智能体列表
export function getAgentList() {
    return request({
        url: '/agent/aiagent/tlist',
        method: 'get'
    })
}
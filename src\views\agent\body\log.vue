<template>
    <div class="single-page-container">
        <div class="single-page-header">
            <div class="left-box">
                <!-- <el-icon style="margin-right: 10px;"><Back /></el-icon> -->
                <el-button :icon="Back" @click="router.back();" text style="margin-right: 10px;" link></el-button>
                <!-- <el-icon size="32px" style="color: #5050E6;margin-right: 10px;" ><ChatDotRound /></el-icon> -->
                <div class="text-box" v-if="currentInstruct">
                    <div class="title">
                        {{currentInstruct.agentName}}
                        <!-- <el-icon size="20px" style="margin-left: 4px;color: #5050E6;"><Edit /></el-icon> -->
                    </div>
                    <div class="detail">
                        {{currentInstruct.agentCode}}
                    </div>
                </div>
            </div>
            <div  class="right-box">
            </div>
        </div>
        <div class="whole">
            <div class="Tips">日志记录了应用的运行情况，包括用户的输入和 AI 的回复。</div>
            <div class="search">
                <el-date-picker
                v-model="dateRange"
                value-format="YYYY-MM-DD"
                type="daterange"
                range-separator="-"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :shortcuts="shortcuts"
                ></el-date-picker>
                <el-input v-model="queryParams.title" style="width: 240px;margin-left: 20px;" :prefix-icon="Search" clearable @keyup.enter="searchHandle"/>
            </div>
            <div class="table">
                <el-table v-loading="loading" :data="historyList" @row-click="handleRowClick">
                    <el-table-column label="时间" prop="created_at"  />
                    <el-table-column label="用户" prop="user_name"  >
                        <template #default="{ row,$index }">
                            {{row.from_source.user_name}}
                        </template>
                    </el-table-column>
                    <el-table-column label="标题" prop="name"  />
                    <el-table-column label="消息数" prop="message_count"  />
                    <!-- <el-table-column label="用户反馈" prop="user_feedback">
                        <template #default="{ row,$index }">
                            {{row.user_feedback?row.user_feedback:'-'}}
                        </template>
                    </el-table-column>
                    <el-table-column label="管理员反馈" prop="admin_feedback">
                        <template #default="{ row,$index }">
                            {{row.admin_feedback?row.admin_feedback:'-'}}
                        </template>
                    </el-table-column> -->
                </el-table>

                <pagination
                    v-show="total > 0"
                    :total="total"
                    v-model:page="queryParams.page"
                    v-model:limit="queryParams.size"
                    @pagination="getList"
                />
            </div>

            <!-- 历史会话记录 -->
            <el-drawer v-model="sessionRecord" :with-header="false" size="45%" class="retrieve-settings-drawer" >
                <div class="top">
                    <div class="title">
                        <llm-model-setting v-if="sessionHistory.llm_profile"
                            v-model:model-params="sessionHistory.llm_profile.llm_model_configJson"
                            v-model:model-name="sessionHistory.llm_profile.llm_model_name"
                            v-model:model-provider="sessionHistory.llm_profile.provider"
                            :placement="bottom"
                            :width="500"
                            :is-disable = true
                        />
                        <el-button :icon="Close" text @click="sessionRecord=false" />
                    </div>
                </div> 
                <div>
                    <session-records-history :session-history-list="sessionHistoryList" :current-instruct="currentInstruct" v-if="sessionRecord"></session-records-history>
                </div>
            </el-drawer>
        </div>
    </div>
</template>
<script setup>
    import { nextTick, ref,watch } from 'vue'
    import { dayjs } from 'element-plus';
    import {Search ,Back} from '@element-plus/icons-vue'
    import { getHistoryList,getSessionHistoryList } from "@/api/YiMaiChatAgent/sessionRecords"
    import sessionRecordsHistory from '@/components/YiMaiChatAgent/sessionRecordsHistory.vue'
    import {Close} from '@element-plus/icons-vue'
    import { getDetail } from '@/api/agent/body'
    import router from "@/router";
    import llmModelSetting from '@/components/Model/llmModelSetting.vue';

    const route = useRoute();

    const id =route.query.id

    const loading = ref(false)

    //历史会话记录
    const sessionRecord = ref(false)

    //日期选择器处理
    const shortcuts = [
        {
            text: '今天',
            value: () => {
                return dayjs().startOf('day')
            },
        },
        {
            text: '过去7天',
            value: () => {
                const date_end = dayjs().endOf('day');
                const date_start = date_end.subtract(6, 'day');
                return [date_start.format('YYYY-MM-DD'), date_end.format('YYYY-MM-DD')];
            },
        },
        {
            text: '过去4周',
            value: () => {
                const fourWeeksAgo = dayjs().subtract(28, 'day').startOf('day');
                const today = dayjs().endOf('day');
                return [fourWeeksAgo.format('YYYY-MM-DD'), today.format('YYYY-MM-DD')]
            },
        },
        {
            text: '过去3月',
            value: () => {
                const threeMonthsAgo = dayjs().subtract(3, 'month');
                const date_end = dayjs().endOf('day'); 
                return [threeMonthsAgo.format('YYYY-MM-DD'), date_end.format('YYYY-MM-DD')]
            },
        },
        {
            text: '过去12月',
            value: () => {
                const twelveMonthsAgo = dayjs().subtract(12, 'month');
                const date_end = dayjs().endOf('day'); 
                return [twelveMonthsAgo.format('YYYY-MM-DD'), date_end.format('YYYY-MM-DD')]
            },
        },
        {
            text: '本月至今',
            value: () => {
                const firstDayOfMonth = dayjs().startOf('month');
                const date_end = dayjs().endOf('day'); 
                return [firstDayOfMonth.format('YYYY-MM-DD'), date_end.format('YYYY-MM-DD')]
            },
        },
        {
            text: '本季度至今',
            value: () => {
                const now = dayjs();
                const startOfMonth = now.startOf('month');
                const month = startOfMonth.month();
                const quarterStart = startOfMonth.subtract(month % 3, 'month').startOf('month');
                const date_end = dayjs().endOf('day'); 
                return [quarterStart.format('YYYY-MM-DD'), date_end.format('YYYY-MM-DD')]
            },
        },
        {
            text: '本年至今',
            value: () => {
                const today = dayjs();
                const startOfYear = dayjs(today).startOf('year');
                const date_end = dayjs().endOf('day'); 
                return [startOfYear.format('YYYY-MM-DD'), date_end.format('YYYY-MM-DD')]
            },
        },
        {
            text: '所有时间',
            value: () => {
                return [null,null]
            },
        },
    ];

    const dateRange = ref([])
    const queryParams = ref({
        page:1,
        size:10,
        date_start:null,
        date_end:null,
        annotation_status:'all',
        is_debug:true,
        title:null
    })
    const total = ref(0)
    const historyList = ref([])

    const getList = () => {
        loading.value=true
        getHistoryList(id,queryParams.value).then(response=>{
            historyList.value = response.data
            total.value = response.count
            loading.value=false
        })
    }

    const searchHandle = () => {
        queryParams.page=1
        getList()
    }

    const sessionHistoryList = ref([])
    const sessionHistory = ref(null)
    const handleRowClick = (row) => {
        sessionHistory.value = row
        sessionHistory.value.llm_profile.llm_model_configJson = JSON.stringify(sessionHistory.value.llm_profile.llm_model_config)
        getSessionHistoryList(row.id).then(response=>{
            sessionHistoryList.value=response
            sessionRecord.value=true
        })
    }

    const currentInstruct = ref(null)
    const getDetailMethod = () => {
        getDetail(id).then((resp) => {
            const data = resp.data
            data.model = data.model ? data.model : ''
            data.provider = data.provider ? data.provider : ''
            data.modelSettings = data.modelSettings ? data.modelSettings : '{}'

            currentInstruct.value = data
            currentInstruct.value.openingQuestionList=currentInstruct.value.openQuestion?JSON.parse(currentInstruct.value.openQuestion):[]
            currentInstruct.value.knowledgeBaseList=currentInstruct.value.knowledgeList?JSON.parse(currentInstruct.value.knowledgeList):[]
            currentInstruct.value.temToolList=currentInstruct.value.toolList?JSON.parse(currentInstruct.value.toolList):[]
            currentInstruct.value.automaticSuggestion=currentInstruct.value.autoSuggestion==1?true:false
            currentInstruct.value.quote=currentInstruct.value.isQuote==1?true:false
            currentInstruct.value.knowledgeSettingsParse = currentInstruct.value.knowledgeSettings?JSON.parse(currentInstruct.value.knowledgeSettings):{"search_method": "single"}
        })
    }
    getDetailMethod()

    getList()

    watch(() => dateRange.value, val => {
        if(dateRange.value && dateRange.value.length>0){
            queryParams.value.date_start = dateRange.value[0] + ' 00:00:00'
            queryParams.value.date_end = dateRange.value[1] + ' 23:59:59'
            searchHandle()
        }else{
            queryParams.value.date_start = null
            queryParams.value.date_end = null
            searchHandle()
        }
    });
</script>
<style scoped lang="scss">
    .single-page-container{
        background-color: #F7F7FA;
        height: calc(100vh - 50px);
        .single-page-header{
            height: 80px;
            display: flex;
            background-color: white;
            margin: 0 20px;
            justify-content: space-between;
            align-items: center;
            flex-direction: row;
            border-radius: 8px;
            .left-box{
                margin-left:24px;
                display: flex;
                align-items: center;
                .text-box{
                    display: flex;
                    flex-direction: column;
                    margin-left: 10px;
                    .title{
                display: flex;
                align-items: center;
                        height: 25px;
                        font-size: 18px;
                        font-weight: 500;
                        color: #032220;
                    }
                    .detail{
                        height: 17px;
                        font-size: 12px;
                        color: #999999;
                        font-weight: 400;
                        margin-top: 4px;
                    }
                }
            }
            .right-box{
                margin-right: 20px;
            }
        }
        .whole{
            height: calc(100vh - 150px);
            background-color: white;
            padding: 20px;
            margin: 10px 20px 0 20px;
            border-radius: 8px;
            .Tips{
                font-size: 15px;
                color:#667085;
            }
            .search{
                margin-top: 20px;
            }
            .table{
                margin-top: 20px;
            }

            ::v-deep .el-drawer.rtl {
                height: 98%;
                top: 10px;
                bottom: 10px;
                right: 10px;
                border-radius:10px;
            }
        }
    }

    ::v-deep.el-table .el-table__row:hover {
        cursor: pointer;
    }

    .retrieve-settings-drawer{
        .top{
            border-bottom: 1px solid #ccc;
            .title{
                padding:20px;
                display:flex;
                justify-content:space-between
            }
        }
    }
    
</style>
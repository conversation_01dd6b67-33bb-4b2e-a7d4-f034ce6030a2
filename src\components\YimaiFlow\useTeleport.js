import { getConnectedEdges, useVueFlow } from '@vue-flow/core'
import { nextTick, ref } from 'vue'

/**
 * Utility composable for specifying animations
 *
 * Animations that resize a node need to call the `updateNodeDimensions` function from store to update node handle positions
 * Otherwise edges do not connect properly
 */
export function useTeleport(id) {
  const teleport = ref(null)

  // const { updateNodeInternals, findNode, edges } = useVueFlow()

  return {
    teleport,
  }
}

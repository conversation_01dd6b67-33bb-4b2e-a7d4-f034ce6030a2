import request from '@/utils/request'

//团队详情
export function getTeamDetail(teamId) {
    return request({
        url: '/team/team/'+teamId,
        method: 'get'
    })
}

//获取关联的taskId的所有智能体
export function getTeamAgentList(params) {
    return request({
        url: '/agent/aiagent/get-task',
        method: 'get',
        params
    })
}

//获取关联的taskId的所有表单项
export function getTeamFormList(taskId) {
    return request({
        url: '/team/task/'+taskId,
        method: 'get'
    })
}

//获取智能团队编排表单项
export function teamKeyList(teamId) {
    return request({
        url: '/team/key/list?teamId='+teamId,
        method: 'get'
    })
}

//发布团队
export function publishTeam(data) {
    return request({
        url: '/team/team',
        method: 'put',
        data
    })
}

<template>
    <div v-if="currentInstruct" style="height:calc(100vh)">
        <!--智能体会话-->
        <session-records :message-item-manager="messageItemManager" :chat-url="agentUrl+'/conversation/ws'" :current-instruct="currentInstruct" 
        :preface-state="false" :preface-height="0" :is-title="false" :is-key-list="true" ref="messageCtrl" :is-public-visit="true"
        @conversation-unselect="conversationUnselect" @conversation-refresh="conversationRefresh" ></session-records>
    </div>
</template>
<script setup>
    import {ref,onMounted, onUnmounted, nextTick} from 'vue'
    import { useRoute } from 'vue-router';
    import sessionRecords from '@/components/YiMaiChatIntegration/YiMaiChat/sessionRecords.vue'
    import { getDetail } from '@/api/agent/body'
    import { MessageItemManager } from '@/components/YiMaiChatIntegration/YiMaiChat/utils/msgManager';
    import ConversationList from '@/components/YiMaiChatIntegration/webConversationList.vue'
    import { ArrowLeft , ArrowRight } from '@element-plus/icons-vue'

    const messageItemManager = new MessageItemManager()

    const agentUrl = import.meta.env.VITE_APP_AGENT_CHAT

    const route = useRoute();
    
    // 获取路径参数
    const token = route.params.token; 
    console.log(token)

    const showDiv = ref(true)

    //会话组件
    const messageCtrl = ref(null)

    const currentInstruct = ref(null)

    const getDetailMethod = () => {
        getDetail(token).then((resp) => {
            currentInstruct.value = resp.data
            if(currentInstruct.value){
                currentInstruct.value.applicationType = 'agent'
                currentInstruct.value.model = currentInstruct.value.model ? currentInstruct.value.model : ''
                currentInstruct.value.provider = currentInstruct.value.provider ? currentInstruct.value.provider : ''
                currentInstruct.value.modelSettings = currentInstruct.value.modelSettings ? currentInstruct.value.modelSettings : '{}'
                currentInstruct.value.maxIterations = currentInstruct.value.agentType==2 && !currentInstruct.value.maxIterations?5:currentInstruct.value.maxIterations
                currentInstruct.value.openingQuestionList = currentInstruct.value.openQuestion?JSON.parse(currentInstruct.value.openQuestion):[]
                currentInstruct.value.knowledgeBaseList = currentInstruct.value.knowledgeList?JSON.parse(currentInstruct.value.knowledgeList):[]
                currentInstruct.value.temToolList = currentInstruct.value.toolList?JSON.parse(currentInstruct.value.toolList):[]
                currentInstruct.value.selectedToolList = currentInstruct.value.toolList?JSON.parse(currentInstruct.value.toolList):[]
                currentInstruct.value.automaticSuggestion = currentInstruct.value.autoSuggestion==1?true:false
                currentInstruct.value.isUpload = currentInstruct.value.isUpload==1?true:false
                currentInstruct.value.uploadSettingParse = currentInstruct.value.uploadSettings?JSON.parse(currentInstruct.value.uploadSettings):null
                currentInstruct.value.quote = currentInstruct.value.isQuote==1?true:false
                currentInstruct.value.knowledgeSettingsParse = currentInstruct.value.knowledgeSettings?JSON.parse(currentInstruct.value.knowledgeSettings):{"search_method": "single"}
                
            }
        })
    }
    getDetailMethod()

    //历史记录组件
    const conversation = ref(null)
    //历史记录取消选中
    const conversationUnselect = () => {
        conversation.value.unselect()
    }

    //历史记录刷新
    const conversationRefresh = () => {
        conversation.value.refresh(true)
    }
    
</script>
<style scoped lang="scss">
</style>
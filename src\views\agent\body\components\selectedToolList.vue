<template>
    <div class="selected-tool-list" v-if="toolList?.builtinToolList?.length">
        <template v-for="tool in selectedToolList">
            <div class="tool-item">
                <div class="left-box">
                    <div class="icon">
                        <template v-if="tool.provider_type === 'builtin'">
                            <div class="img" :style="{backgroundImage:`url(${getToolIcon(tool)})`}"></div>
                        </template>
                        <template v-else>
                            <span :style="{backgroundColor:getToolIcon(tool).background}" class="emoji">
                                <single-emoji :content="getToolIcon(tool).content"></single-emoji>
                            </span>
                        </template>
                    </div>
                    <div class="text">
                        <!-- <span class="provider-name">
                            {{ tool.provider_type === 'builtin' ? tool.provider_name : tool.tool_label }}
                        </span> -->
                        <div class="tool-name">
                            <span>
                                {{ tool.tool_label}}
                            </span>
                        </div>
                    </div>
                </div>
                <div class="right-box">
                    <div class="operation">
                        <el-tooltip placement="top" effect="light" trigger="click">
                            <template #content> 
                                <tool-more-info-tip :tool="getToolTipInfo(tool)" />
                            </template>
                            <el-button size="small" text icon='InfoFilled'>
                            </el-button>
                        </el-tooltip>
                        <el-button size="small" text 
                            @click="deleteSelectedTool(tool)" icon='Delete'>
                        </el-button>
                    </div>
                </div>
            </div>
        </template>
    </div>
</template>
<script setup>
import singleEmoji from '@/views/toolManagement/components/singleEmoji'
import toolMoreInfoTip from './toolMoreInfoTip'
import { 
    getAllBuiltinTools,
    getAllApiTypeTools
} from '@/api/newTool'

const props = defineProps({
    selectedToolList: {
        type: Array,
        default: () => []
    }
})

const emits = defineEmits(['delete-selected-tool'])

const toolList = ref({
    apiToolList:[],
    builtinToolList:[]
})

const getToolIcon = (tool) => {
    let toolIcon = null
    if(tool.provider_type === 'builtin') {
        let builtinTool = toolList.value.builtinToolList.find(item => 
            item.id === tool.provider_id
        )
        toolIcon = builtinTool?builtinTool.icon:null
    } else {
        let builtinTool = toolList.value.apiToolList.find(item => 
            item.id === tool.provider_id
        )
        toolIcon = builtinTool?builtinTool.icon:null
    }
    return toolIcon
}

const getToolTipInfo = (tool) => {
    let toolInfo = null
    if(tool.provider_type === 'builtin') {
        const findTools = toolList.value.builtinToolList.find(item => 
            item.id === tool.provider_id
        )
        const findTool = findTools.tools.find(item => 
            item.name === tool.tool_name
        )
        toolInfo = {
            type: 'builtin',
            tool_name: findTool.label.zh_Hans,
            desc: findTool.description.zh_Hans,
            icon: findTools.icon
        }
    } else {
        const findTools = toolList.value.apiToolList.find(item => 
            item.id === tool.provider_id
        )
        const findTool = findTools.tools.find(item => 
            item.name === tool.tool_name
        )
        toolInfo = {
            type: 'builtin',
            tool_name: findTool.label.zh_Hans,
            desc: findTool.description.zh_Hans,
            icon: findTools.icon
        }
    }
    return toolInfo
}


const deleteSelectedTool = (tool) => {
    emits('delete-selected-tool', tool)
}

const getInitData = () => {
    getAllApiTypeTools().then( res => {
        toolList.value.apiToolList = res
        getAllBuiltinTools().then( res => {
            toolList.value.builtinToolList = res
        })
    })
}

getInitData()

</script>
<style scoped lang="scss">
.selected-tool-list {
    display: grid;
    grid-template-columns: repeat(2,minmax(100px, 1fr));
    align-items: center;
    justify-content: space-between;
    gap: .25rem;
    margin-top: 10px;
    .tool-item{
        position: relative;
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
        height: 100%;
        border: .5px solid rgb(234, 236, 240);
        border-radius: .5rem;
        background-color: #fff;
        padding: .5rem .75rem .5rem .625rem;
        box-shadow: rgba(0, 0, 0, 0) 0px 0px 0px 0px, 
            rgba(0, 0, 0, 0) 0px 0px 0px 0px, 
            rgba(16, 24, 40, 0.05) 0px 1px 2px 0px;
        .left-box{
            display: flex;
            width: 0;
            flex-grow: 1;
            align-items: center;
            .icon{
                position: relative;
                flex-shrink: 0;
                margin-right: .75rem;
                .img{
                    height: 1.5rem;
                    width: 1.5rem;
                    background-size: cover;
                    background-position: 50%;
                    background-repeat: no-repeat;
                    border-radius: .375rem;
                }
                .emoji{
                    height: 24px;
                    width: 24px;
                    border-radius: .5rem;
                    display: flex;
                    position: relative;
                    align-items: center;
                    justify-content: center;
                    line-height: 1.75rem;
                }
            }
            .text{
                margin-left: .5rem;
                width: 0;
                flex-grow: 1;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
                font-size: 13px;
                font-weight: 500;
                line-height: 18px;
                color: rgb(29, 41, 57);
                .provider-name{
                    padding-right: .5rem;
                    color: rgb(29, 41, 57);
                }
                .tool-name{
                    color: rgb(102, 112, 133);
                    display: inline-block;
                }
            }
        }
        .right-box{
            margin-left: .25rem;
            display: flex;
            flex-shrink: 0;
            align-items: center;
            .operation{
                display: none;
                align-items: center;
            }
        }
        &:hover{
            .right-box{
                .operation{
                    display: flex;
                }
            }
        }
    }
}
</style>
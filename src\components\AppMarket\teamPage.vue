<template>
  <div v-loading="loading" class="common-content">
    <el-row>
      <el-col
        :span="6"
        v-for="item of filteredTeamList"
        :key="item.id"
        style="margin-bottom: 20px"
      >
        <app-card
          :app="item"
          :img="item.teamImgUrl ? baseUrl + item.teamImgUrl : TeamDefaultIcon"
          :title="item.teamName"
          :detail="item.teamDescribe"
          :has-permission="item.approveStatus"
          :is-pinned="props.allocatedTeamMap[item.id] && props.allocatedTeamMap[item.id].isIndex === 1"
        />
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from "vue";
import { getTeamInfoList } from "@/api/teamManagement/team";
import { Star, StarFilled } from "@element-plus/icons-vue";
import AppCard from "./appCard.vue";
import TeamDefaultIcon from '@/assets/icons/svg/team-default.svg'

const props = defineProps({
  /**
   * 搜索框关键词
   */
  keyword: {
    type: String,
    required: true
  },

  // 已要配权限的团队map, key是团队id, value是团队数据
  allocatedTeamMap: {
    type: Object,
    required: true
  }
});

// 查询条件
const query = {
  pageSize: 100,
  pageNum: 1,
  listType: 1,
};

// 团队列表
const teamList = ref([]);

// 根据关键词过滤后的团队列表
const filteredTeamList = computed(() => {
  return (!props.keyword)
  ? [...teamList.value]
  : teamList.value.filter((item) => item.teamName && item.teamName.toLowerCase().includes(props.keyword.toLocaleLowerCase()));
});

// 加载状态
const loading = ref(false);

// 总数
const total = ref(0);

// 基础地址
const baseUrl = import.meta.env.VITE_APP_BASE_API;

/**
 * 获取团队数据
 **/
async function getTeamList() {
  const pageSize = 100;

  loading.value = true;
  teamList.value = [];
  for (let i=0; i<10; i++) {
    const res = await getTeamInfoList({
      pageSize: pageSize,
      pageNum: i + 1,
      listType: 1
    });
    teamList.value.splice(teamList.value.length - 1, 0, ...res.rows);
    total.value = res.total;
    if (teamList.value.length === res.total || i + 1 >= Math.floor((res.total + pageSize - 1) / pageSize))  {
      break;
    }
  }
  loading.value = false;
}

/**
 * 将团队数据转换成公用格式
 */
async function convertToPublic(team) {
  return {
    applicationId: team.id,
    applicationType: 'team',
    applicationName: team.teamName,
    applicationIcon: teamImgUrl,
    applicationDesc: teamDescribe,
    arrangeJson: team.arrangeJson,
    teamType: team.teamType,
    prompt: team.prompt,
    prologue: team.prologue,
    openQuestion: team.openQuestion,
    recommendQuestion: team.recommendQuestion
  }
}

onMounted(() => {
  getTeamList();
});

defineExpose({
  getTeamList
})
</script>

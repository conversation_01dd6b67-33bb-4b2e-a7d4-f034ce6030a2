<template>
    <div class="common-container">
        <div class="single-page-container">
            <div class="single-page-header">
                <div class="left-box">
                    <!-- <el-icon style="margin-right: 10px;"><Back /></el-icon> -->
                    <el-button :icon="Back" @click="closeDrawer" text style="margin-right: 10px;"></el-button>
                    <img :src="baseUrl+mainImage" />
                    <div class="text-box">
                        <div class="title">
                            {{mainTitle}}
                        </div>
                        <!-- <div class="detail">
                            {{editForm.agentCode}}
                        </div> -->
                    </div>
                </div>
                <div  class="right-box">
                    <el-button type="primary" style="color: white;font-size: 14px;font-weight: 500;"
                    @click="publicTeam()" v-if="props.isEdit">发布</el-button>
                </div>
            </div>
        </div>
        <!--content-->
        <div class="common-content" v-loading="isLoading">
            <!--half top-->
            <div class="team-agents">
                <div class="title">团队成员</div>
                <div class="agents-items">
                    <template v-for="teamAgent in teamAgentList">
                        <div class="agents-item" :class="{'active':props.isEdit?teamDetail.selectedAgent.find(item => item == teamAgent.agentId):false}" 
                        @click="props.isEdit?selectTeamAgent(teamAgent):''" v-if="(!props.isEdit && teamDetail.selectedAgent.find(item => item == teamAgent.agentId)) || props.isEdit">
                            <img :src="baseUrl+teamAgent.agentIconUrl" />
                            <div class="agent-name">{{teamAgent.agentName}}</div>
                            <div class="agent-desc">{{teamAgent.goal}}</div>
                        </div>
                    </template>
                </div>
                <div class="selected-agents" v-if="props.isEdit">
                    已选择{{teamDetail.selectedAgent?.length}}位成员
                    <template v-for="(item,index) in teamDetail.selectedAgent">
                        <el-tag closable style="margin: 0 5px;" @close="teamDetail.selectedAgent.splice(index,1)">{{teamAgentList.find(teamAgent => teamAgent.agentId == item)?.agentName}}</el-tag>
                    </template>
                </div>
            </div>
            <!--half bottom-->
            <div class="team-keys">
                <div class="title">需求</div>
                <div style="margin-top: 20px;height: calc(100vh - 400px);overflow-y:auto;">
                    <el-form :label-width="'100px'" :label-position="'top'">
                        <el-row>
                            <el-col :span="8" v-for="(key) in keyList" :key="key.keyCode">
                                <el-form-item  :label="key.keyName" :style="key.keyType=='image'?{minHeight:'190px'}:''">
                                    <el-input v-model="testPamrams[key.keyCode]" style="width: 80%;" v-if="key.keyType=='text'" type="text"></el-input>
                                    <Image-Upload v-model="testPamrams[key.keyCode]" :limit="1" :isShowTip="false" v-if="key.keyType=='image'"></Image-Upload>
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </el-form>
                </div>
            </div>
        </div>
        <div class="common-footer">
            <el-button style="font-size: 14px;font-weight: 500;" size="large" type="primary"
                    @click="runTest()">开始协作</el-button>
            <el-input
            v-model="speechRecognition"
            size="large"
            style="max-width: 800px;margin-left: 10px;" 
            >
                <template #prepend>
                    <el-button :icon="Microphone" :style="{color:microphoneState?'#5050E6':''}" @click="microphoneClick()"/>
                </template>
            </el-input>
            <el-button style="font-size: 14px;font-weight: 500;;margin-left: 10px" size="large" type="primary"
                    @click="sending()">需求分析</el-button>
        </div>
        <el-dialog v-model="dialogVisible" :title="mainTitle+'协作'" width="70%" v-if="dialogVisible" style="background-color: #F7F7FA;"
        :close-on-click-modal="false" :close-on-press-escape="false" :before-close="handleClose">
            <el-row style="border-top:1px solid #E6E6E6">
                <el-col style="background-color: #ffffff;">
                    <yimai-chat-team style="" :chat-url="'ws://***********:29998'" 
                    :is-disabled-send-set="false" :team-agent-selected-list="teamAgentSelectedList"
                    ref="testTeamChat">
                    </yimai-chat-team>
                </el-col> 
            </el-row>
        </el-dialog>
    </div>
</template>
<script setup>
import { nextTick, ref } from 'vue'
import YimaiChatTeam from '@/components/YimaiChatTeam/index';
import {Back,Edit,Plus,Remove,Promotion,Microphone} from '@element-plus/icons-vue'
import { useRoute } from 'vue-router';
import {getTeamAgentList,getTeamDetail,getTeamFormList,publishTeam} from '@/api/teamManagement/config'
import {ElMessage} from 'element-plus'

const props = defineProps({
    editModel:{
        required:true,
        type:Object
    },
    editDrawer:{
        required:false,
        type:Object
    },
    isEdit:{
        type: Boolean,
        required: false,
        default: true
    }
})

const baseUrl = import.meta.env.VITE_APP_BASE_API;

const mainTitle = ref('')
const mainImage = ref('')
const teamType = ref('')
const route = useRoute()
const isLoading = ref(false)
const keyList = ref([])
const teamAgentList = ref([])
//选中的智能体列表
const teamAgentSelectedList = ref([])
const teamDetail = ref({})
const testPamrams = ref({})
const dialogVisible = ref(false)
const testTeamChat = ref(null)

const microphoneState = ref(false)
const speechRecognition = ref('')

const runTest = () => {
    dialogVisible.value = true
    // console.log(testPamrams.value)
    nextTick(() => {
        testTeamChat.value.initChat({
            teamId: props.editModel.teamId,
            agentIds:teamDetail.value.selectedAgent,
            // agentIds:null,
            keywords:testPamrams.value
        })
    })
}

const publicTeam = () => {
    let data = {
        id: props.editModel.teamId,
        teamJson: teamDetail.value.selectedAgent.join(','),
        teamType:teamType.value
    }
    publishTeam(data).then((res) => {
        ElMessage.success(res.msg)
    })
}

const selectTeamAgent = (teamAgent) => {
    let findIndex = teamDetail.value.selectedAgent.findIndex(item => {
        return item == teamAgent.agentId
    })
    if(findIndex != -1){
        teamDetail.value.selectedAgent.splice(findIndex,1)
    }else{
        teamDetail.value.selectedAgent.push(teamAgent.agentId)
    }
    teamAgentSelectedHandle()
}

const init = () => {
    isLoading.value = true
    let teamId = props.editModel.teamId
    getTeamDetail(teamId).then((response) => {
        teamDetail.value = response.data
        teamDetail.value.selectedAgent = teamDetail.value.teamJson?teamDetail.value.teamJson.split(','):[]
        mainTitle.value = response.data.teamName
        mainImage.value = response.data.teamImgUrl
        teamType.value = response.data.teamType
        let params = {
            taskId:response.data.taskId
        }
        getTeamAgentList(params).then((res) => {
            teamAgentList.value = res.rows
            getTeamFormList(response.data.taskId).then ((res) => {
                let paramsObj = {}
                keyList.value = JSON.parse(res.data.templateJson).keys
                keyList.value.forEach((item) => {
                    paramsObj[item.keyCode] = ''
                })
                testPamrams.value = paramsObj
                teamAgentSelectedHandle()
                isLoading.value = false
            })
        })
    })
}

const closeDrawer = () => {
    props.editDrawer.close()
}

//处理选中的智能体列表
const teamAgentSelectedHandle = () =>{
    teamAgentSelectedList.value=[]
    teamAgentList.value.forEach(item=>{
        if(teamDetail.value.selectedAgent.find(res=>{return res==item.agentId})){
            teamAgentSelectedList.value.push(item)
        }
    })
}

const handleClose = (done) => {
    testTeamChat.value.clearTimeres()
    done()
}

const isMicrophone = ref(true)
//点击麦克风
const microphoneClick = () => {
    if(isMicrophone.value){
        isMicrophone.value = false
        microphoneState.value=!microphoneState.value
        let speechRecognitionTxt = "设计一款面向年轻人办公室场景耳机"
        let txt = speechRecognitionTxt.split('')
        let tempTxt = ''
        let timeres = []
        let timer = setTimeout(() => {
            txt.forEach((item,index) => {
                let timer = setTimeout(() => {
                    speechRecognition.value=tempTxt += item
                    if(index == txt.length-1){
                        microphoneState.value=false
                        isMicrophone.value = true
                    }
                }, 30 * index );
                timeres.push(timer) 
            })
        }, 3000 );
    }
}

//发送
const sending = () => {
    if(speechRecognition.value!='' && !microphoneState.value){
        testPamrams.value.product='耳机'
        testPamrams.value.user_situation='我们的用户主要是一群年轻，充满活力的人群，他们年龄在18到35岁之闹，有稳定的经济收入，每月收入在5000到10000元，这些用户注重生活品质，追求时尚和品牌，同时也非常注重产品的便携性。'
        testPamrams.value.usage_scenario='在办公室工作时'
        testPamrams.value.product_type='头戴式耳机'
        testPamrams.value.function='提供高品质的音乐体验,具有降噪功能，有效去除环境噪音'
        testPamrams.value.use_flow='用户在办公室工作时，通过连接到手机或电脑，享受高品质音乐或清晰会议通话体验。用户可以通过耳机上的控制按钮调节音量、切换歌曲。'
        testPamrams.value.product='耳机'
        testPamrams.value.proposals_number=2
        testPamrams.value.supplement='耳机的舒适设计和便携性使用户能够长时间佩戴'
    }
}

init()
</script>
<style scoped lang="scss">
.common-container {
    display: flex;
    flex-direction: column;
    background-color: #FFFFFF;
    height: 100vh !important; 
    // calc(100vh - 52px);
    width: 100%;
    margin: 0;
    padding: 0;
    background-size: cover;
}

.single-page-container{
    // background-color: white;
    // height: 100vh;
    .single-page-header{
        height: 80px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-direction: row;
        border-bottom: 1px solid #ccc;
        .left-box{
            margin-left:24px;
            display: flex;
            align-items: center;
            .text-box{
                display: flex;
                flex-direction: column;
                margin-left: 10px;
                .title{
            display: flex;
            align-items: center;
                    height: 25px;
                    font-size: 18px;
                    font-weight: 500;
                    color: #032220;
                }
                .detail{
                    height: 17px;
                    font-size: 12px;
                    color: #999999;
                    font-weight: 400;
                    margin-top: 4px;
                }
            }
            img {
                height: 40px;
                width: 40px;
                border-radius: 4px;
                border: 1px solid #E6E6E6;
            }
        }
        .right-box{
            margin-right: 20px;
        }
    }
    .single-page-content{
        display: flex;
        flex-direction: row;
        height: calc(100vh - 80px);
        overflow-y: auto;
        .content-left-box{
            width: calc(60% - 48px);
            height: calc(100% - 60px);
            border-right: 1px solid #ccc;
            background-color: white;
            margin-top: 30px;
            .content-left-box-title{
                padding-left: 24px;
                font-size: 14px;
                font-weight: bold;
                color: #032220;
                margin-bottom: 16px;
            }
            .left-box-half-top{
                border-bottom: 1px solid #ccc;
            }
            .custom-form-item-slider{
                display: flex;
                flex-direction: row;
                width: 100%;
                align-items: center;
                justify-content: space-between;
                margin-bottom: 5px;
                .custom-slider-title{
                    margin-right: 20px;
                    width: 120px;
                    text-align: left;
                    font-size: 12px;
                    color: #999999;
                }
                .custom-slider-value{
                    width: calc(100% - 120px);
                    display: flex;
                    flex-direction: row;
                }
            }
        }
        .content-right-box-title{
            color: #032220;
            font-size: 14px;
            font-weight: 500;
            margin-bottom: 10px;
        }
        
        
    }
}

.common-header {
    height: 60px;
    padding: 20px 30px;
    line-height: 24px;
    display: flex;
    justify-content: space-between;
    border-bottom: 1px solid #E6E6E6;
    margin-bottom: 10px;
    .title {
        color: #032220;
        font-size: 18px;
        font-weight: bold;
    }

    .header-right {
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: row;
    }
}

.common-content {
    margin: 0 20px;
    height: calc(100vh - 150px);
}

.common-footer{
    // max-height: calc(100vh - 900px);
    // display: flex;
    // justify-content: center;
    // align-items: center;
    text-align: center;
}

.team-agents{
    // height: 400px;
    width: 100%;
    display: flex;
    flex-direction: column;
    padding: 10px 10px;
    border-bottom: 1px solid #E6E6E6;
    margin-bottom: 20px;
    .title{
        color: #032220;
        font-size: 18px;
        font-weight: 500;
    }
    .agents-items{
        display: flex;
        flex-direction: row;
        margin-top: 15px;
        margin-bottom: 20px;
        .agents-item{
            width: 160px;
            height: 210px;
            margin-bottom: 20px;
            background-color: #f5f5ff;
            border-radius: 10px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
            padding: 10px 20px;
            margin: 0 10px;
            &:hover{
                cursor: pointer;
            }
            img{
                width: 70px;
                height: 90px;
                // margin-bottom: 10px;
                border-radius: 10px;
                background-color: #dfdfff;
                border: none;
                overflow:hidden; 
            }
            img[src=""],img:not([src]){
                opacity: 0;
                border:none;
                visibility: hidden;
                max-width: none;
            }
            .agent-name{
                height: 50px;
                color: #032220;
                font-size: 16px;
                font-weight: 500;
                line-height: 60px;
                white-space: nowrap;
            }
            .agent-desc{
                height: 80px;
                max-height: 80px;
                overflow: hidden;
                color: #999999;
                font-size: 12px;
                font-weight: 400;
            }
        }
        .active{
            // background-color: #94bff1;
            border: 2px solid #5050E6;
        }
    }
}
.team-keys{
    max-height: calc(100vh - 500px);
    width: 100%;
    display: flex;
    flex-direction: column;
    padding: 0 10px;
    // overflow-y:auto;
    .title{
        color: #032220;
        font-size: 18px;
        font-weight: 500;
    }
}
::v-deep(.el-dialog__body) {
  padding: 0;
}
</style>

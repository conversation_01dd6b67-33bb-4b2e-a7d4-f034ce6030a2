export class Graph {  
    constructor(nodes, edges) {  
        this.nodes = nodes;  
        this.edges = edges;  
        this.adjacencyList = {};  
        this.nodeIdMap = {};  
  
        // 构建邻接表和节点ID映射  
        nodes.forEach((node, index) => {  
            this.nodeIdMap[node.id] = index;  
            this.adjacencyList[node.id] = [];  
        });  
  
        edges.forEach(edge => {  
            this.adjacencyList[edge.source].push(edge.target);  
        });  
    }  
  
    dfs(startNodeId, visited, path) {  
        visited.add(startNodeId);  
        path.push(startNodeId);  
  
        const neighbors = this.adjacencyList[startNodeId];  
        for (const neighbor of neighbors) {  
            if (!visited.has(neighbor)) {  
                this.dfs(neighbor, visited, path);  
            }  
        }  
    }  
  
    sortNodes(startNodeId) {  
        const visited = new Set();  
        const path = [];  
  
        this.dfs(startNodeId, visited, path);  
  
        // 按路径顺序返回节点  
        return path.map(nodeId => this.nodes[this.nodeIdMap[nodeId]]);  
    }  
}
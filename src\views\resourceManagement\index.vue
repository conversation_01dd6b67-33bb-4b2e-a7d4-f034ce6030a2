<template>
    <div class="whole">
        <div class="left">
            <el-divider content-position="left" class="divider">
                分类管理
            </el-divider>
            <!-- <el-divider content-position="left" class="divider">
                分类管理
            </el-divider> -->
            <div class="sel">
                <el-input v-model="classifyParams.classifyName" placeholder="请输入分类名称" clearable class="input" :maxlength="100" 
                 @keyup.enter="getClassifyListMethod" />
                <el-button type="primary" :icon="Plus" class="button" @click="addClassifyMethod" v-hasPermi="['resource:classify:add']">新增</el-button>
            </div>
            <div class="list">
                <div v-for="(item,index) in classifyList" class="single-strip" @click="classifyClick(item)" 
                :style="{'background-color':queryParams.classifyId == item.classifyId?'#F9F9F9':''}">
                    <div class="type">
                        {{ item.classifyName }} 
                        <el-tooltip :content="item.classifyDesc" effect="light" placement="top" popper-class="tooltip-pop" v-if="item.classifyDesc">
                            <el-icon class="icon" style="margin-left:5px"><QuestionFilled /></el-icon>
                        </el-tooltip>
                    </div>
                    <div>
                        <el-button type="primary" :icon="Edit" size="small" link @click.stop="editClassifyMethod(item)" 
                        v-hasPermi="['resource:classify:edit']" />
                        <el-button type="danger" :icon="Delete" size="small" link @click.stop="delClassifyMethod(item)" v-hasPermi="['resource:classify:remove']" />
                    </div>
                </div>
            </div>
        </div>
        <div class="right">
            <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" >
                <el-form-item label="资源名称" prop="resourceName">
                    <el-input
                    v-model="queryParams.resourceName"
                    placeholder="请输入名称"
                    clearable
                    :maxlength="100" 
                    style="width: 200px"
                    @keyup.enter="selClassifyList"
                    />
                </el-form-item>
                <el-form-item label="上传时间" prop="dateRange">
                    <el-date-picker
                    style="width: 250px"
                    v-model="dateRange"
                    value-format="YYYY-MM-DD"
                    type="daterange"
                    range-separator="-"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    ></el-date-picker>
                </el-form-item>
                <el-form-item label="分类" prop="type">
                    <el-select v-model="queryParams.classifyId" placeholder="请选择分类" clearable @change="selClassifyList()"
                    style="width: 200px">
                        <el-option
                            v-for="(item,index) in classifyList"
                            :key="item.classifyId"
                            :label="item.classifyName"
                            :value="item.classifyId"
                        />
                    </el-select>
                </el-form-item>
                <el-form-item label="来源" prop="type">
                    <el-select v-model="queryParams.source" placeholder="请选择分类" clearable
                    style="width: 200px">
                        <el-option
                            v-for="dict in sys_resource_source"
                            :key="dict.value"
                            :label="dict.label"
                            :value="dict.value"
                        />
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" icon="Search" @click="selClassifyList" v-hasPermi="['resource:classify:query']">搜索</el-button>
                    <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                </el-form-item>
            </el-form>
            <el-row :gutter="10" class="mb8">
                <el-col :span="1.5">
                    <el-button
                    type="primary"
                    plain
                    icon="Upload"
                    @click="addResourceMethod"
                    v-hasPermi="['resource:resource:add']"
                    >上传</el-button>
                </el-col>
                <el-col :span="1.5">
                    <el-button
                    type="danger"
                    plain
                    icon="Delete"
                    :disabled="multiple"
                    @click="handleDeleteBatch"
                    v-hasPermi="['resource:resource:remove']"
                    >删除</el-button>
                </el-col>
                <el-col :span="1.5">
                    <el-button
                    type="warning"
                    plain
                    icon="Download"
                    @click="handleExport"
                    v-hasPermi="['resource:resource:export']"
                    >导出全部</el-button>
                </el-col>
                <!-- <el-col :span="1.5">
                    <el-button
                    type="primary"
                    plain
                    @click="oosConfig"
                    v-hasPermi="['tenant:platform:list']"
                    >存储配置</el-button>
                </el-col> -->
                <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
            </el-row>

            <el-table v-loading="loading" :data="resourceList" @selection-change="handleSelectionChange"   max-height="calc( 100vh - 310px )">
                <el-table-column type="selection" width="55" align="center" :selectable="seletableHandle"/>
                <el-table-column label="名称" align="center" prop="resourceName" />
                <el-table-column label="分类" align="center" prop="configName" :show-overflow-tooltip="true" width="120">
                    <template #default="scope">
                        {{ formatAssortName(scope.row.classifyId) }}
                    </template>
                </el-table-column>
                <el-table-column label="描述" align="center" prop="resourceDesc" :show-overflow-tooltip="true" />
                <el-table-column label="上传时间" align="center" prop="createdTime" width="180" />
                <el-table-column label="文件大小" align="center" prop="resourceSize" width="100px">
                    <template #default="scope">
                        {{ formatFileSize(scope.row.resourceSize) }}
                    </template>
                </el-table-column>
                <el-table-column label="文件地址" align="center" prop="resourcePath" >
                    <template #default="scope">
                        <a :href="scope.row.resourcePath" target="_blank">
                            {{ scope.row.resourcePath }}
                        </a>
                    </template>
                </el-table-column>
                <el-table-column label="操作" align="center" width="220" class-name="small-padding fixed-width">
                    <template #default="scope">
                    <el-button link type="primary" icon="Edit" size="small" @click="editResourceMethod(scope.row)" v-hasPermi="['resource:resource:edit']" >修改</el-button>
                    <el-button link type="primary" icon="Delete" size="small" @click="handleDelete(scope.row)" 
                    v-hasPermi="['resource:resource:remove']" :disabled="scope.row.source!='system'">删除</el-button>
                    <el-button link type="primary" icon="DocumentCopy" size="small" @click="handleCopy(scope.row)">内容复制</el-button>
                    </template>
                </el-table-column>
            </el-table>

            <pagination
                v-show="total > 0"
                :total="total"
                v-model:page="queryParams.pageNum"
                v-model:limit="queryParams.pageSize"
                @pagination="getResourceListMethod"
            />
        </div>

        <!--创建分类-->
        <el-dialog v-model="addClassifyDialog" :title="classifyTitle" center width="550px" >
            <div>
                <el-form class="classify-form" :model="classifyForm" ref="classifyBodyForm"  :label-position="'top'" :rules="classifyRules"
                :validate-on-rule-change="false">
                    <el-form-item prop="classifyName" label="资源分类" >
                        <el-input v-model="classifyForm.classifyName" placeholder="请输入资源分类名称" />
                    </el-form-item>
                    <el-form-item prop="sort" label="显示序号">
                        <el-input-number v-model="classifyForm.sort" controls-position="right" :min="0">
                            <template #decrease-icon>
                                <el-icon>
                                    <Minus />
                                </el-icon>
                            </template>
                        </el-input-number>
                    </el-form-item>
                    <el-form-item prop="classifyDesc" label="分类描述">
                        <el-input v-model="classifyForm.classifyDesc" :rows="3" type="textarea" placeholder="请输入描述" />
                    </el-form-item>
                </el-form>
            </div>
            <template #footer>
                <div>
                    <el-button type="primary" @click="addClassifyHandle">确认</el-button>
                    <el-button @click="addClassifyDialog = false">取消</el-button>
                </div>
            </template>
        </el-dialog>

        <!--资源上传-->
        <VideoDialog
        ref="videoDialog"
        @refresh="selClassifyList"
        @isVisibleChange = "isVisibleChange"
        />
    </div>
</template>
<script setup>
    import { nextTick, ref,watch } from 'vue'
    import {Plus , Edit , Delete ,Upload ,Download ,DocumentCopy} from '@element-plus/icons-vue'
    import { start } from 'nprogress';
    import { getClassifyList,addClassify,editClassify,delClassify,getResourceList,delResource,resourceExport } from '@/api/resourceManagement/resource'
    import { ElMessage, ElMessageBox } from 'element-plus'
    import VideoDialog from '@/components/VideoDialog/index.vue'
    import router from "@/router";

    // 基础地址
    const baseUrl = import.meta.env.VITE_APP_BASE_API;
    const { proxy } = getCurrentInstance();
    const { sys_resource_source } = proxy.useDict("sys_resource_source");
    
    const queryParams = ref({
        pageNum:1,
        pageSize:10,
        resourceName:null,
        startDate:null,
        endDate:null,
        classifyId:null,
        source:null
    })
    const dateRange = ref([])
    const queryRef = ref(null)
    const showSearch = ref(true)

    const loading = ref(false)
    const total = ref(0)
    const resourceList = ref([])
    //资源列表
    const getResourceListMethod = () => {
        loading.value = true
        getResourceList(queryParams.value).then(response => {
            resourceList.value = response.rows
            total.value = response.total
            loading.value = false
        })
    }
    getResourceListMethod()

    //搜索资源列表
    const selClassifyList = () => {
        queryParams.value.pageNum = 1
        getResourceListMethod()
    }
    //重置
    const resetQuery = () =>{
        queryParams.value = {
            pageNum:1,
            pageSize:10,
            resourceName:null,
            startDate:null,
            endDate:null,
            classifyId:null,
            source:null
        }
        dateRange.value = []
        getResourceListMethod()
    }
    //文件大小转化 
    const formatFileSize = (bytes) => {
      if (bytes === 0) return '0 MB';
      const k = 1024;
      const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
      const i = parseInt(Math.floor(Math.log(bytes) / Math.log(k)));
      return Math.round(100 * (bytes / Math.pow(k, i))) / 100 + ' ' + sizes[i];
    }

    const videoDialog = ref(null)
    //上传资源
    const addResourceMethod = () => {
        videoDialog.value.openAdd();
        reset();
    }
    //修改资源
    const editResourceMethod = (row) => {
        videoDialog.value.openEdit({
            id: row.resourceId,
            name: row.resourceName,
            desc: row.resourceDesc,
            path: row.resourcePath,
            url: row.resourcePath,
            size: row.resourceSize,
            classifyId: row.classifyId
        });
    }

    //删除资源
    const delResourceMethod = (resourceIds) => {
        delResource(resourceIds).then(response=>{
            ElMessage.success('删除成功')
            selClassifyList()
        })
    }
    //删除资源-单条
    const handleDelete = (row) => {
        ElMessageBox.confirm( '是否确认删除['+row.resourceName+']的数据项？', '系统提示',{
            confirmButtonText: '确认',
            cancelButtonText: '取消',
            type: 'warning',
        }).then(() => {
            delResourceMethod(row.resourceId)
        }).catch(() => {
        })
    }

    const resourceIdsList = ref('')
    const single = ref(true);
    const multiple = ref(true);
    /** 选择条数  */
    function handleSelectionChange(selection) {
        resourceIdsList.value = selection.map(item => item.resourceId);
        single.value = selection.length != 1;
        multiple.value = !selection.length;
    };

    const seletableHandle = (row, index) => {
        if (row.source != 'system') {
            return false;
        } else {
            return true;
        }
    }

    //删除资源-批量
    const handleDeleteBatch = () => {
        let resourceIds = ''
        let resourceNames = ''
        if(resourceIdsList.value && resourceIdsList.value.length>0){
            resourceIdsList.value.forEach(element => {
                let montage = resourceIds && resourceNames?',':''
                resourceIds+=montage+element
                resourceNames+=montage+resourceList.value.filter(x=>x.resourceId == element)[0].resourceName
            });
            ElMessageBox.confirm( '是否确认删除['+resourceNames+']的数据项？', '系统提示',{
                confirmButtonText: '确认',
                cancelButtonText: '取消',
                type: 'warning',
            }).then(() => {
                delResourceMethod(resourceIds)
            }).catch(() => {
            })
        }
    }

    //导出全部
    const handleExport = () => {
        proxy.download("resource/resource/export", {}, `resource_${new Date().getTime()}.xlsx`);
    }

    //复制按钮操作
    const handleCopy = (row) => {
      const ext = row.resourcePath.split('.').pop();
      const isImg = ['jpg', 'jpeg', 'png', 'gif', 'webp'].includes(ext);
      const url = encodeURI(row.resourcePath);
      // const md = isImg
      //   ? `!["${row.resourceName}"](${url})\n${row.resourceDesc}`
      //   : `["${row.resourceName}"](${url})\n${row.resourceDesc}`;

      const md = `${row.resourceName} \n ${url}`;
      const textToCopy = md;
      const tempTextArea = document.createElement('textarea');
      tempTextArea.value = textToCopy;
      document.body.appendChild(tempTextArea);

      tempTextArea.select();
      document.execCommand('copy');

      document.body.removeChild(tempTextArea);

      ElMessage.success('内容已复制到剪贴板');
    }


    const form = ref({})
    // 表单重置
    const reset = () => {
      form.value = {
        classifyId: undefined,
        assortName: undefined,
        resourceName: undefined,
        resourceDesc: undefined,
        postSort: 0,
        status: '0',
        remark: undefined
      };
    }

    const classifyParams = ref({
        classifyName:''
    })
    const classifyList = ref([])
    //资源分类列表
    const getClassifyListMethod = () => {
        getClassifyList(classifyParams.value).then(response => {
            classifyList.value = response.data
        })
    }

    getClassifyListMethod()
    //点击资源分类
    const classifyClick = (classify) => {
        queryParams.value.classifyId = classify.classifyId
        selClassifyList()
    }
    //资源分类
    const addClassifyDialog = ref(false)
    const classifyBodyForm = ref(null)
    const classifyForm  = ref({
        classifyName:'',
        sort:0,
        classifyDesc:''
    })
    const classifyRules = ref({
        classifyName: [{ required: true, message: "资源分类不能为空", trigger: "blur" }],
        sort: [{ required: true, message: "显示序号不能为空", trigger: "blur" }],
    })
    const classifyTitle = ref('')
    //新增资源分类
    const addClassifyMethod = () => {
        classifyTitle.value = '新增分类'
        classifyForm.value = {
            classifyName:'',
            sort:0,
            classifyDesc:''
        }
        addClassifyDialog.value = true
        nextTick(()=>{
            proxy.resetForm("classifyBodyForm");
        })
    }
    //修改资源分类
    const editClassifyMethod = (classify) => {
        classifyTitle.value = '修改分类'
        classifyForm.value = JSON.parse(JSON.stringify(classify))
        addClassifyDialog.value = true
        proxy.resetForm("classifyBodyForm");
    }
    //保存新增/修改分类
    const addClassifyHandle = () => {
        classifyBodyForm.value.validate(valid => {
            if(valid){
                if(!classifyForm.value.classifyId){
                    addClassify(classifyForm.value).then(response => {
                        ElMessage.success('新增成功')
                        addClassifyDialog.value = false
                        getClassifyListMethod()
                    })  
                }else{
                    editClassify(classifyForm.value).then(response => {
                        ElMessage.success('修改成功')
                        addClassifyDialog.value = false
                        getClassifyListMethod()
                    })  
                }
            }
        })
    }
    //删除资源分类
    const delClassifyMethod = (classify) => {
        ElMessageBox.confirm( '确认删除['+classify.classifyName+']分类吗？', '系统提示',{
            confirmButtonText: '确认',
            cancelButtonText: '取消',
            type: 'warning',
        }).then(() => {
            delClassify(classify.classifyId).then(response => {
                ElMessage.success('删除成功')
                getClassifyListMethod()
            })
        }).catch(() => {
        })
    }

    //根据classifyId获取分类名称
    const formatAssortName = (classifyId) => {
        if(classifyList.value){
            for (let i = 0; i < classifyList.value.length; i++) {
                if (classifyList.value[i].classifyId === classifyId) {
                    return classifyList.value[i].classifyName;
                }
            }
        }
    }

    //存储配置
    const oosConfig = () => {
        router.push("/tenant/oos");
    }

    watch(() => dateRange.value, val => {
        if(dateRange.value && dateRange.value.length>0){
            queryParams.value.startDate = dateRange.value[0] + ' 00:00:00'
            queryParams.value.endDate = dateRange.value[1] + ' 23:59:59'
        }else{
            queryParams.value.startDate = null
            queryParams.value.endDate = null
        }
    });

</script>
<style lang="scss" scoped>
    .whole{
        height: calc( 100vh - 50px );
        display: flex;
        justify-content: space-between;
        background-color: #F7F7FA;
        padding: 10px 20px;
        .left{
            width: 20%;
            padding:5px 10px;
            height: calc( 100vh - 70px );
            background-color:#FDFDFD ;
            border-top-left-radius: 8px;
            border-bottom-left-radius: 8px;
            .divider {
                width: calc(100% - 20px);
                position: relative;
                left: 10px;

                :deep(.el-divider__text) {
                    background-color: #FDFDFD;
                    left: 0;
                    padding-left: 5px;
                    color: #8587aa;
                }
            }
            .sel{
                display: flex;
                justify-content: space-between;
                .input{
                    width: 70%;
                }
                .button{
                    margin-left: 10px;
                }
            }
            .list{
                margin-top: 10px;
                height: calc(100vh - 180px);
                overflow-y: auto;
                .single-strip{
                    padding:10px;
                    border-radius: 10px;
                    font-size: 14px;
                    display: flex;
                    justify-content: space-between;
                    cursor: pointer;
                    .type{
                        width: 75%;
                        display: flex;
                        align-items:center ;
                        .icon{
                            color: #999999;
                        }
                    }
                    &:hover{
                        background-color: #F9F9F9;
                    }
                }
                
            }
        }
        .right{
            width: 80%;
            height: calc( 100vh - 70px );
            padding:20px;
            background-color:white ;
            border-top-right-radius: 8px;
            border-bottom-right-radius: 8px;
        }
    }
    ::v-deep .el-dialog:not(.is-fullscreen) {
        margin-top: 0vh !important;
    }
</style>
<style lang="scss">
.tooltip-pop.el-popper{
    max-width: 250px !important;
}
</style>
<template>
  <div v-if="!item.hidden" style="padding: 8px 0;" class="menu-item-wrapper">
    <template v-if="hasOneShowingChild(item.children, item) && 
      (!onlyOneChild.children || onlyOneChild.noShowingChildren) && 
      !item.alwaysShow">
      <app-link v-if="onlyOneChild.meta" :to="resolvePath(onlyOneChild.path, onlyOneChild.query)">
        <el-menu-item :index="resolvePath(onlyOneChild.path)" 
          :class="{ 'submenu-title-noDropdown': !isNest, 'menu-item': true }" 
          style="width: 100%;" :style="{'padding':isTop?'0':''}">
          <svg-icon v-if="!isTop" :icon-class="onlyOneChild.meta.icon || (item.meta && item.meta.icon)"/>
          <div v-else style="display: flex;flex-direction: row; gap: .25rem;
            align-items: center;padding: 10px 0;line-height: 18px;font-size: 14px;width: 100%;">
            <svg-icon style="margin: 0;" :icon-class="onlyOneChild.meta.icon || (item.meta && item.meta.icon)"/>
            <span class="menu-title" :title="hasTitle(onlyOneChild.meta.title)">
              {{ onlyOneChild.meta.title }}
            </span>
          </div>
          <template #title v-if="!isTop">
            <span class="menu-title" :title="hasTitle(onlyOneChild.meta.title)">
              {{ onlyOneChild.meta.title }}
            </span>
          </template>
        </el-menu-item>
      </app-link>
    </template>

    <el-sub-menu v-else ref="subMenu" :index="resolvePath(item.path)" teleported 
          style="border-radius: 8px;">
      <template v-if="item.meta" #title>
        <div style="border-radius: 8px;width: 100%;height: 60px;" :style="{'padding':isTop?'0':''}">
          <svg-icon v-if="!isTop" :icon-class="item.meta && item.meta.icon" />
          <div v-else style="display: flex;flex-direction: column; gap: .25rem;
            align-items: center;padding: 10px 0;line-height: 18px;font-size: 14px;width: 100%;">
            <svg-icon style="margin: 0;" :icon-class="item.meta.icon || (item.meta && item.meta.icon)"/>
            <span style="display: block;" class="menu-title" :title="item.meta.title">
              {{ item.meta.title }}
            </span>
          </div>
          <span class="menu-title" :title="hasTitle(item.meta.title)">{{ item.meta.title }}</span>
        </div>
      </template>

      <sidebar-item
        v-for="(child, index) in item.children"
        :key="child.path + index"
        :is-nest="true"
        :is-top="false"
        :item="child"
        :base-path="resolvePath(child.path)"
        class="nest-menu"
      />
    </el-sub-menu>
  </div>
</template>

<script setup>
import { isExternal } from '@/utils/validate'
import AppLink from './Link'
import { getNormalPath } from '@/utils/ruoyi'

const props = defineProps({
  // route object
  item: {
    type: Object,
    required: true
  },
  isTop: {
    type: Boolean,
    default: true
  },
  isNest: {
    type: Boolean,
    default: false
  },
  basePath: {
    type: String,
    default: ''
  }
})

const onlyOneChild = ref({});

function hasOneShowingChild(children = [], parent) {
  if (!children) {
    children = [];
  }
  const showingChildren = children.filter(item => {
    if (item.hidden) {
      return false
    } else {
      // Temp set(will be used if only has one showing child)
      onlyOneChild.value = item
      return true
    }
  })

  // When there is only one child router, the child router is displayed by default
  if (showingChildren.length === 1) {
    return true
  }

  // Show parent if there are no child router to display
  if (showingChildren.length === 0) {
    onlyOneChild.value = { ...parent, path: '', noShowingChildren: true }
    return true
  }

  return false
};

function isTopOneMenu(item) {
  return item.children && item.children.length === 1 && !item.alwaysShow;
}

function resolvePath(routePath, routeQuery) {
  if (isExternal(routePath)) {
    return routePath
  }
  if (isExternal(props.basePath)) {
    return props.basePath
  }
  if (routeQuery) {
    let query = JSON.parse(routeQuery);
    return { path: getNormalPath(props.basePath + '/' + routePath), query: query }
  }
  return getNormalPath(props.basePath + '/' + routePath)
}

function hasTitle(title){
  if (title.length > 5) {
    return title;
  } else {
    return "";
  }
}
</script>
<style scoped>
/*隐藏文字*/
.el-menu--collapse  .el-sub-menu__title span{
    display: none;
}
/*隐藏 > */
.el-menu--collapse  .el-sub-menu__title .el-submenu__icon-arrow{
    display: none;
}
.menu-item-wrapper {
  margin-top: 50px;
}
.menu-item {
  height: 32px;
  padding-left: 31px !important;
  background-color: transparent !important;
}
.menu-item:hover {
  background-image: url(/images/home-menu-bg.png);
  background-size: 100% 100%;
  background-position: 0 0;
  background-repeat: no-repeat;
  background-color: transparent !important;
  color: white !important;
}
.menu-item.is-active {
  background-image: url(/images/home-menu-bg.png);
  background-size: 100% 100%;
  background-position: 0 0;
  background-repeat: no-repeat;
  background-color: transparent;
  color: white !important;
}

</style>

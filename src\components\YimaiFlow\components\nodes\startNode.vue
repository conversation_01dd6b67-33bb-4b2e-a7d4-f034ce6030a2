<template>
    <div class="start-node-area">
        <Handle type="source" :position="Position.Right" style="background-color: #5050E6;height: 8px;width: 8px;"/>
        <node-variables v-model="nodeInfo.variables"></node-variables>
    </div>
</template>
<script setup>
import { Handle, Position } from '@vue-flow/core'
import NodeVariables from '../common/nodeVariables'

const props = defineProps({
    id: {
        type: String,
        required: true,
    },
    nodeInfo: {
        type: Object,
        required: false,
    },
})

</script>
<style lang="scss" scoped>
.start-node-area{
    height: 100%;
    display: flex;
    flex-direction: column;
    padding: 0 16px 16px 16px;
    .header{
        display: flex;
        align-items: center;
        .mock-icon{
            height: 24px;
            width: 24px;
            background-color: #5050E6;
            border-radius: 5px;
        }
    }
}
</style>
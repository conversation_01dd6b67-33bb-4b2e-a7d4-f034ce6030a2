<template>
    <div class="single-page-container">
        <div class="single-page-header">
            <div class="left-box">
                <!-- <el-icon style="margin-right: 10px;"><Back /></el-icon> -->
                <el-button :icon="Back" @click="closeDrawer" link style="margin:0 10px;"></el-button>
                <!-- <el-icon size="32px" style="color: #5050E6;margin-right: 10px;" ><ChatDotRound /></el-icon> -->
                <div class="text-box">
                    <div class="title">
                        {{editForm.agentName}}
                        <!-- <el-icon size="20px" style="margin-left: 4px;color: #5050E6;"><Edit /></el-icon> -->
                    </div>
                    <div class="detail">
                        {{editForm.agentCode}}
                    </div>
                </div>
            </div>
            <div  class="right-box">
                <el-button type="primary" @click="statistics">数据看板</el-button>
                <el-button type="primary" @click="logList">对话日志</el-button>
                <!-- <el-button type="primary" @click="showApiKeyDialog = true">API授权</el-button> -->
                <el-popover placement="bottom" :width="234" trigger="click"
                    :popper-style="{'padding':'0'}">
                    <template #reference>
                        <el-button type="primary">
                            发布<el-icon style="margin-left: 5px;"><ArrowDown /></el-icon>
                        </el-button>
                    </template>
                    <template #default>
                        <div style="width: 234px;">
                            <div style="padding: 12px 16px;">
                                <el-button style="width: 204px;" type="primary" 
                                    @click="saveEditForm">{{editForm.publishStatus?'更新':'发布'}}
                                </el-button>
                            </div>
                            <el-divider style="margin: 0;" />
                            <div style="padding: 12px 16px;">
                                <el-button style="width: 204px;"
                                    :disabled="!editForm.publishStatus" 
                                    @click="toAppAuth" text bg>
                                    <div style="display: flex;width:  184px;
                                        flex-direction: row;justify-content: space-between;
                                        align-items: center;">
                                        <div>
                                            <svg-icon style="margin-right: 5px;" 
                                                :icon-class="'app-auth'" 
                                                :size="'1em'" />
                                            应用授权
                                        </div>
                                        <el-icon><TopRight /></el-icon>
                                    </div>
                                </el-button>
                                <el-button style="width: 204px;margin-left: 0;margin-top: 4px;" 
                                    :disabled="!editForm.publishStatus"
                                    @click="toApiAuth" text bg>
                                    <div style="display: flex;width:  184px;
                                        flex-direction: row;justify-content: space-between;
                                        align-items: center;">
                                        <div>
                                            <svg-icon style="margin-right: 5px;" 
                                                :icon-class="'api-key-auth'" 
                                                :size="'1em'" />
                                            API授权 
                                        </div>
                                        <el-icon><TopRight /></el-icon>
                                    </div>
                                </el-button>
                                <!-- <el-button style="width: 204px;margin-left: 0;margin-top: 4px;" 
                                    @click="toPublicVisit" text bg>
                                    <div style="display: flex;width:  184px;
                                        flex-direction: row;justify-content: space-between;
                                        align-items: center;">
                                        <div style="display: flex;justify-content: center;">
                                            <el-icon style="margin-right: 5px;" :size="'1em'"><Monitor /></el-icon>
                                            嵌入网站 
                                        </div>
                                        <el-icon><TopRight /></el-icon>
                                    </div>
                                </el-button> -->
                            </div>
                        </div>
                    </template>
                </el-popover>
            </div>
        </div>
        <div class="whole">
            <div class="left">
                <div class="settings">
                    <div class="ai-setting">
                        <div class="head">
                            <div class="head-title"><img :src="robot" draggable="false"/>AI 配置</div>
                        </div>
                        <div class="model">
                            <div class="title-font">AI模型</div>
                            <div style="margin-left: 20px;">
                                <llm-model-setting
                                    v-model:model-params="editForm.modelSettings"
                                    v-model:model-name="editForm.model"
                                    v-model:model-provider="editForm.provider"
                                    :width="500" :is-chat-assistant="editForm.agentType==2" v-model:max-iterations="editForm.maxIterations"
                                    v-model:agent-mode="editForm.agentMode"
                                />
                            </div>
                        </div>
                        <div class="prompt">
                            <div class="title-font">提示词</div>
                            <div>
                                <el-input v-model="editForm.prompt" @blur="promptBlur" placeholder="请输入内容" style="width: 100%;" type="textarea" :rows="10" />
                            </div>
                        </div>
                    </div>
                    <div class="knowledege-base">
                        <div class="head">
                            <div :span="10" class="head-title"><img :src="knowledegeBase" draggable="false"/>关联知识库</div>
                            <div :span="14">
                                <el-button type="primary" size="small" :icon="Tools" @click="knowledgeBaseSettingsOpen" link
                                v-if="editForm.agentType==1">知识库设置</el-button>
                                <el-button :icon="Plus" size="small" link type="primary" @click="addknowledgeBase">添加</el-button>
                            </div>
                        </div>
                        <div  v-if="editForm.knowledgeBaseList && editForm.knowledgeBaseList.length>0" style="margin-top: 10px;">
                            <el-table :data="editForm.knowledgeBaseList" tooltip-effect="light" 
                                :tooltip-options="{'popper-class':'tooltip-pop-table'}">
                                <el-table-column label="知识库名称" align="center" prop="name" :show-overflow-tooltip="true"/>
                                <el-table-column label="知识库描述" align="center" prop="description" :show-overflow-tooltip="true" />
                                <el-table-column label="操作" align="center" width="50" class-name="small-padding fixed-width">
                                    <template #default="scope">
                                        <el-button :icon="Remove" link @click="delKnowledgeBase(scope.row)"></el-button>
                                    </template>
                                </el-table-column>
                            </el-table>
                        </div>           
                    </div>
                    <div class="upload-file" v-if="editForm.agentType==2">
                        <div class="head">
                            <div class="head-title"><img :src="agentUploadFile" draggable="false"/>文件上传
                                <el-tooltip content="开启后，可以在对话中上传文档/图片。" 
                                effect="light" placement="top" popper-class="tooltip-pop">
                                    <el-icon class="info-icon" style="margin-left:5px"><QuestionFilled /></el-icon>
                                </el-tooltip>
                            </div>
                            <div>
                                <el-button :icon="Tools" size="small" link type="primary" @click="uploadSettingMethod()" v-if="editForm.isUpload">上传设置</el-button>
                                <el-switch
                                    v-model="editForm.isUpload"
                                    class="ml-2"
                                    style="--el-switch-on-color: #5050E6;margin-left: 5px;"
                                    @change="isUploadChange"
                                />
                            </div>
                        </div>
                    </div>
                    <div class="tool" v-if="editForm.agentType==2">
                        <div class="head">
                            <div class="head-title"><img :src="toolBody" draggable="false"/>工具调用</div>
                            <div>
                                <el-button :icon="Plus" size="small" link type="primary" @click="addTool">添加</el-button>
                            </div>
                        </div>
                        <selected-tool v-if="selectedToolList.length"
                            :selected-tool-list="selectedToolList"
                            @delete-selected-tool="deleteSelectedTool" />
                        <!-- <div v-if="editForm.temToolList && editForm.temToolList.length>0" style="margin-top: 10px;">
                            <el-table :data="editForm.temToolList" v-if="editForm.temToolList && editForm.temToolList.length>0" tooltip-effect="light">
                                <el-table-column label="工具名称" align="center" prop="toolName" :show-overflow-tooltip="true" />
                                <el-table-column label="工具描述" align="center" prop="toolDesc" :show-overflow-tooltip="true" />
                                <el-table-column label="操作" align="center" width="50" class-name="small-padding fixed-width">
                                    <template #default="scope">
                                        <el-button :icon="Remove" link @click="delTool(scope.row)"></el-button>
                                    </template>
                                </el-table-column>
                            </el-table>
                        </div> -->
                    </div>
                    <div class="variable">
                        <div class="head">
                            <div class="head-title"><img :src="variable" draggable="false"/>变量
                                <el-tooltip content="变量将以表单形式让用户在对话前填写，用户填写的表单内容将自动替换提示词中的变量。" 
                                effect="light" placement="top" popper-class="tooltip-pop">
                                    <el-icon class="info-icon" style="margin-left:5px"><QuestionFilled /></el-icon>
                                </el-tooltip>
                            </div>
                            <div>
                                <el-button :icon="Plus" size="small" link type="primary" @click="addKey">添加</el-button>
                            </div>
                        </div>
                        <div :style="{marginTop: editForm.keyList && editForm.keyList.length > 0 ? '10px' : 0}">
                            <variable-setting v-model="editForm.keyList" ref="variableSettingRef"/>
                        </div> 
                    </div>
                    <div class="opening-settings">
                        <div class="head">
                            <div class="head-title"><img :src="pinkSession" draggable="false"/>对话开场白</div>
                        </div>
                        <div style="padding: 10px;">
                            <el-input v-model="editForm.prologue" placeholder="请输入开场白" style="width: 100%;" type="textarea" :rows="4" />
                        </div>
                        <div style="padding: 10px;">
                            <div class="head">
                                <div class="title-font">开场预置问题<el-tooltip content="填写超过3条时，将显示前3条开场预置问题" 
                                    effect="light" placement="top" popper-class="tooltip-pop">
                                        <el-icon class="info-icon" style="margin-left:5px"><QuestionFilled /></el-icon>
                                    </el-tooltip>
                                </div>
                                <div>
                                    <el-button :icon="Plus" size="small" link type="primary" @click="addOpeningQuestion">添加</el-button>
                                </div>
                            </div>
                            <div v-for="(item,index) in editForm.openingQuestionList" style="width: 100%;margin-top: 10px;">
                                <el-input v-model="item.openingQuestion" placeholder="请输入问题" style="width: 95%;" type="text" :maxlength="400" />
                                <el-button :icon="Remove" link @click="delOpeningQuestion(index)" style="width: 5%;"></el-button>
                            </div>
                        </div>
                    </div>
                    <div class="automatic-suggestion">
                        <div class="head">
                            <div class="head-title"><img :src="automaticSuggestion" draggable="false"/>猜你想问
                                <el-tooltip content="对话结束后，根据上下文自动提供3个建议的用户问题" 
                                effect="light" placement="top" popper-class="tooltip-pop">
                                    <el-icon class="info-icon" style="margin-left:5px"><QuestionFilled /></el-icon>
                                </el-tooltip>
                            </div>
                            <div>
                                <el-switch
                                    v-model="editForm.automaticSuggestion"
                                    class="ml-2"
                                    style="--el-switch-on-color: #5050E6;"
                                />
                            </div>
                        </div>
                    </div>
                    <div class="automatic-suggestion">
                        <div class="head">
                            <div class="head-title"><img :src="quote" draggable="false"/>引用和归属
                                <el-tooltip content="启用后，显示源文档和生成内容的归属部分。" 
                                effect="light" placement="top" popper-class="tooltip-pop">
                                    <el-icon class="info-icon" style="margin-left:5px"><QuestionFilled /></el-icon>
                                </el-tooltip>
                            </div>
                            <div>
                                <el-switch
                                    v-model="editForm.quote"
                                    class="ml-2"
                                    style="--el-switch-on-color: #5050E6;"
                                />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="right">
                <div>
                    <yi-mai-chat-agent :chat-url="agentUrl+'/conversation/ws'" :current-instruct="editForm" ref="messageCtrl" :is-debugging="true">
                    </yi-mai-chat-agent>
                </div>
            </div>
        </div>

         <!-- 知识库设置 -->
        <el-dialog v-model="knowledgeBaseSettingsDialogShow" title="知识库设置" center width="550px" height="800px">
            <div>
                <recall-settings v-model="editForm.knowledgeSettingsParse"></recall-settings>
            </div>
            <template #footer>
                <div>
                    <el-button type="primary" @click="knowledgeBaseSettingsConfirm">确认</el-button>
                </div>
            </template>
        </el-dialog>

        <!-- 选择知识库 -->
        <el-dialog v-model="knowledgeBaseSelDialogShow" width="900px" height="800px">
            <template #header>
                <div class="my-header">
                    <span style="line-height: 24px;font-size:18px;color: #303133;">选择知识库</span>
                    <el-input v-model="knowledgeBaseQueryParams.keyword" :prefix-icon="Search" placeholder="搜索" style="width: 25%;margin-left:10px;border-radius:4px;" type="text"
                    @keyup.enter="knowledgeBaseSel"/>
                    <el-button type="primary" style="color: white;font-size: 14px;font-weight: 500;float:right;margin-right: 20px;" @click="createKnowledgeBase()">创建知识库</el-button>
                </div>
            </template>
            <div v-loading="knowledgeBaseLoading" class="common-content">
                <el-row>
                    <el-col :span="12" v-for="(item,index) in knowledgeBaseList" :key="index" style="margin-top: 20px;" @click="knowledgeBaseSelect(item)">
                        <custom-card :img="knowledgeBaseIcon" :title="item.name" :detail="item.description" :is-need-footer="false"></custom-card>
                    </el-col>
                </el-row>
            </div>
            <div style="padding:0 30px">
                <pagination
                    v-show="knowledgeBaseTotal > 0"
                    :total="knowledgeBaseTotal"
                    v-model:page="knowledgeBaseQueryParams.page"
                    v-model:limit="knowledgeBaseQueryParams.limit"
                    layout="prev, pager, next"
                    @pagination="getknowledgeBaseListMethod"
                    style="margin-bottom: 30px;"
                    class="mt-4"
                />
            </div>
        </el-dialog>

        <!-- 文件上传设置 -->
        <el-dialog v-model="uploadFileDialogShow" width="400px" height="800px">
            <template #header>
                <div class="my-header">
                    <span style="line-height: 24px;font-size:18px;color: #303133;">上传设置</span>
                </div>
            </template>
            <div class="upload-file-dialog">
                <div style="margin-bottom: 10px;">支持类型</div>
                <div style="margin-bottom: 20px;" >
                    <el-checkbox v-model="editForm.uploadSettingParse.isSupportFile" label="文件（docx/pdf/txt/xls/xlsx/csv）" size="large" style="font-size: 15px;" />
                    <el-checkbox v-model="editForm.uploadSettingParse.isSupportImage" label="图片（jpeg/jpg/png/bmp）" size="large" style="font-size: 15px;" />
                </div>
                <div v-if="editForm.uploadSettingParse.isSupportFile">
                    <div class="title">
                        最大文件数量
                        <el-tooltip content="对话中单次上传文件的最大数量。" 
                        effect="light" placement="top" popper-class="tooltip-pop">
                            <el-icon class="info-icon" style="margin-left:5px"><QuestionFilled /></el-icon>
                        </el-tooltip>
                    </div>
                    <el-slider size="small" v-model="editForm.uploadSettingParse.maxFileNum" :max="15" :min="1" :step="1"
                    :marks="{1:'1',15:'15'}" class="slider" />
                    <div class="title">
                        单文件大小限制
                        <el-tooltip content="对话中单次上传单个文件的大小限制。" 
                        effect="light" placement="top" popper-class="tooltip-pop">
                            <el-icon class="info-icon" style="margin-left:5px"><QuestionFilled /></el-icon>
                        </el-tooltip>
                    </div>
                    <el-slider size="small" v-model="editForm.uploadSettingParse.maxFile" :max="15" :min="1" :step="1"
                    :marks="{1:'1',15:'15MB'}" class="slider"  />
                </div>
                <div v-if="editForm.uploadSettingParse.isSupportImage">
                    <div class="title">
                        最大图片数量
                        <el-tooltip content="对话中单次上传图片的最大数量。" 
                        effect="light" placement="top" popper-class="tooltip-pop">
                            <el-icon class="info-icon" style="margin-left:5px"><QuestionFilled /></el-icon>
                        </el-tooltip>
                    </div>
                    <el-slider size="small" v-model="editForm.uploadSettingParse.maxImageNum" :max="15" :min="1" :step="1"
                    :marks="{1:'1',15:'15'}" class="slider"  />
                    <div class="title">
                        单图片大小限制
                        <el-tooltip content="对话中单次上传单个图片的大小限制。" 
                        effect="light" placement="top" popper-class="tooltip-pop">
                            <el-icon class="info-icon" style="margin-left:5px"><QuestionFilled /></el-icon>
                        </el-tooltip>
                    </div>
                    <el-slider size="small" v-model="editForm.uploadSettingParse.maxImage" :max="10" :min="1" :step="1"
                    :marks="{1:'1',10:'10MB'}" class="slider"  />
                </div>
            </div>
            <template #footer>
                <div  style="text-align: center;">
                    <el-button type="primary" @click="uploadSettingSave">确定</el-button>
                </div>
            </template>
        </el-dialog>

        <!-- 选择工具 -->
        <el-dialog v-model="toolShow" width="700px"  title="选择工具">
            <select-tool @change-select-tool="changeSelectTool" ref="selectToolRef" />
        </el-dialog>

        <api-key-dialog :agentId="id" v-model="showApiKeyDialog"/>
        
        <!--公开访问-->
        <public-visit-dialog v-model="publicVisitDisabled" :id="id"></public-visit-dialog>
    </div>
</template>
<script setup>
import {updateAgent,getDeptListTree,getModelList} from '@/api/agent/body'
import { ElMessage,ElMessageBox } from 'element-plus'
import {Back,Edit,Plus,Remove,Search,Close,QuestionFilled,Tools,Monitor} from '@element-plus/icons-vue'
import {ref,onMounted, onUnmounted, nextTick} from 'vue'
import CustomSlider from '@/components/CustomSlider/index'
import CustomCard from '@/components/CustomCard/index'
import { getToolList } from "@/api/toolManagement/tool";
import { getKnowledgeBaseList } from "@/api/knowledgeBase/knowledgeBase";
import { getDetail } from '@/api/agent/body'
import knowledgeBaseIcon from "@/assets/icons/svg/knowledgeBaseIcon.svg";
import router from "@/router";
import llmModelSetting from '@/components/Model/llmModelSetting.vue';
import VariableSetting from './components/variableSetting.vue';
import YiMaiChatAgent from '@/components/YiMaiChatAgent/index.vue';
import recallSettings from '@/components/recallSettings/index.vue';
import robot from "@/assets/icons/svg/robot.svg";
import knowledegeBase from "@/assets/icons/svg/knowledege-base.svg";
import toolBody from "@/assets/icons/svg/tool-body.svg";
import variable from "@/assets/icons/svg/variable.svg";
import pinkSession from "@/assets/icons/svg/pink-session.svg";
import automaticSuggestion from "@/assets/icons/svg/automatic-suggestion.svg";
import quote from "@/assets/icons/svg/quote.svg";
import apiKeyDialog from './components/apiKeyDialog.vue'
import selectTool from './components/selectTool'
import selectedTool from './components/selectedToolList'
import agentUploadFile from "@/assets/icons/svg/agent-upload-file.svg";
import publicVisitDialog from '@/components/publicVisitDialog/index.vue';

const env = import.meta.env.VITE_APP_ENV
const baseUrl = import.meta.env.VITE_APP_BASE_API;

const agentUrl = import.meta.env.VITE_APP_AGENT_CHAT;

const route = useRoute();

const id =route.query.id

const selectedToolList = ref([])

const selectToolRef = ref(null)

const deleteSelectedTool = (tool) => {
    const delIndex = selectedToolList.value.findIndex((item) => {
        return tool.provider_id == item.provider_id && 
            tool.tool_name == item.tool_name
    })
    selectedToolList.value.splice(delIndex,1)
    editForm.value.selectedToolList = selectedToolList.value
}

const editForm = ref({
    knowledgeBaseList:[],
    selectedToolList:[],
    openingQuestionList:[],
    //知识库设置
    knowledgeSettings:{
        search_method:'single'
    },

    // 模型名称
    model: '',

    // 模型供应商
    provider: '',

    // 模型参数
    modelSettings: '',

    // 变量
    keyList: [],
    //最大迭代次数
    maxIterations:null,
    //agentMode
    agentMode:null
})
// const props = defineProps({
//     editModel:{
//         required:true,
//         type:Object
//     },
//     editDrawer:{
//         required:false,
//         type:Object
//     }
// })

// 变量设置组件的引用
const variableSettingRef = ref(null)

// 是否显示api key弹窗
const showApiKeyDialog = ref(false)

//智能体分类
const agentTypeList = ref([
    {id:1,agentType:'聊天型'},
    {id:2,agentType:'文本型'},
    {id:3,agentType:'编程型'},
    {id:4,agentType:'角色型'}
])

//知识库设置
const knowledgeBaseSettingsDialogShow = ref(false)

const knowledgeBaseSettingsOpen = () => {
    knowledgeBaseSettingsDialogShow.value=true
}

const messageCtrl = ref(null)

const knowledgeBaseSettingsConfirm = () => {
    knowledgeBaseSettingsDialogShow.value=false
}

//选择知识库
const knowledgeBaseSelDialogShow = ref(false)
const knowledgeBaseLoading = ref(false)
const knowledgeBaseQueryParams = ref({
    name:'',
    page:1,
    limit:6
})
const knowledgeBaseTotal = ref(0)
const knowledgeBaseList = ref([])

const addknowledgeBase = () => {
    knowledgeBaseQueryParams.value = {
        page:1,
        limit:6
    }
    getknowledgeBaseListMethod()
    knowledgeBaseSelDialogShow.value = true
}

//知识库列表
const getknowledgeBaseListMethod = () => {
    knowledgeBaseLoading.value=true;
    getKnowledgeBaseList(knowledgeBaseQueryParams.value).then(response => {
        knowledgeBaseList.value=response.data;
        knowledgeBaseTotal.value=response.total;
        knowledgeBaseLoading.value=false;
    })
}

const knowledgeBaseSel = () => {
    knowledgeBaseQueryParams.value.page=1
    getknowledgeBaseListMethod()
}

const createKnowledgeBase = () => {
    window.open((env === 'production' ? '/yimai-agent/' : '/')+"knowledgeBase?type=add");
}

//选择知识库
const knowledgeBaseSelect = (item) => {
    if(editForm.value.knowledgeBaseList){
        if(!(editForm.value.knowledgeBaseList.find(x=> x.id == item.id))){
            editForm.value.knowledgeBaseList.push(item)
        }
    }else{
        editForm.value.knowledgeBaseList=[item]
    }
    knowledgeBaseSelDialogShow.value=false;
}
const delKnowledgeBase = (row) => {
    editForm.value.knowledgeBaseList = editForm.value.knowledgeBaseList.filter(x=>{ return x.id!=row.id })
}

//文件上传设置
const uploadFileDialogShow = ref(false)
const isUploadChange = (val) => {
    if(val){    
        ElMessage.warning('请选择合适工具处理用户上传的文件')
        if(!editForm.value.uploadSettingParse && editForm.value.uploadSettings){
            editForm.value.uploadSettingParse = JSON.parse(editForm.value.uploadSettings)
        }
        if(!editForm.value.uploadSettingParse && !editForm.value.uploadSettings){
            editForm.value.uploadSettingParse = {
                isSupportFile:true,
                isSupportImage:true,
                maxFileNum:1,
                maxFile:25,
                maxImageNum:1,
                maxImage:5
            }
        }
    }
}
const uploadSettingMethod = () => {
    if(!editForm.value.uploadSettingParse && editForm.value.uploadSettings){
        editForm.value.uploadSettingParse = JSON.parse(editForm.value.uploadSettings)
    }
    if(!editForm.value.uploadSettingParse && !editForm.value.uploadSettings){
        editForm.value.uploadSettingParse = {
            isSupportFile:true,
            isSupportImage:true,
            maxFileNum:1,
            maxFile:25,
            maxImageNum:1,
            maxImage:5
        }
    }
    uploadFileDialogShow.value = true
}

const uploadSettingSave =() => {
    if(!editForm.value.uploadSettingParse.isSupportFile){
        editForm.value.uploadSettingParse.maxFileNum = 1
        editForm.value.uploadSettingParse.maxFile = 25
    }
    if(!editForm.value.uploadSettingParse.isSupportImage){
        editForm.value.uploadSettingParse.maxImageNum = 1
        editForm.value.uploadSettingParse.maxImage = 5
    }
    editForm.value.uploadSettings = JSON.stringify(editForm.value.uploadSettingParse)
    uploadFileDialogShow.value = false
}

//选择工具
const toolShow = ref(false)
const toolLoading = ref(false)
const toolQueryParams = ref({
    toolName:'',
    pageNum:1,
    pageSize:6
})
const toolTotal = ref(0)
const toolList = ref([])

const addTool = () => {
    toolQueryParams.value = {
        toolName:'',
        pageNum:1,
        pageSize:6
    }
    getToolsListMethod()
    toolShow.value = true
    nextTick(() => {
        selectToolRef.value.setSelectTool(selectedToolList.value)
    })
}
//工具列表
const getToolsListMethod = () => {
    toolLoading.value=true;
    getToolList(toolQueryParams.value).then(response => {
        toolList.value=response.rows;
        toolTotal.value=response.total;
        toolLoading.value=false;
    })
}
const toolSel = () => {
    toolQueryParams.value.pageNum=1
    getToolsListMethod()
}

//选择工具
const toolSelect = (item) => {
    if(editForm.value.temToolList){
        if(!(editForm.value.temToolList.find(x=> x.id == item.id))){
            editForm.value.temToolList.push(item)
        }
    }else{
        editForm.value.temToolList=[item]
    }
    toolShow.value = false
}

const changeSelectTool = (val) =>{
    selectedToolList.value = val
    editForm.value.selectedToolList = val
}
const delTool = (row) => {
    editForm.value.temToolList = editForm.value.temToolList.filter(x=>{ return x.id!=row.id })
}
//创建工具
const createTool = () => {
    window.open((env === 'production' ? '/yimai-agent/' : '/')+"toolManagement?type=add");
}

//智能体所属部门
const deptList = ref([])
const getDeptList = () => {
    getDeptListTree().then((resp)=>{
        deptList.value=resp.data
    })
}

const getDetailMethod = () => {
    getDetail(id).then((resp) => {
        const data = resp.data
        data.model = data.model ? data.model : ''
        data.provider = data.provider ? data.provider : ''
        data.modelSettings = data.modelSettings ? data.modelSettings : '{}'
        data.maxIterations = data.agentType==2 && !data.maxIterations?5:data.maxIterations

        editForm.value = data
        editForm.value.openingQuestionList=editForm.value.openQuestion?JSON.parse(editForm.value.openQuestion):[]
        editForm.value.knowledgeBaseList=editForm.value.knowledgeList?JSON.parse(editForm.value.knowledgeList):[]
        editForm.value.temToolList=editForm.value.toolList?JSON.parse(editForm.value.toolList):[]
        selectedToolList.value=editForm.value.toolList?JSON.parse(editForm.value.toolList):[]
        editForm.value.selectedToolList =editForm.value.toolList?JSON.parse(editForm.value.toolList):[]
        editForm.value.automaticSuggestion=editForm.value.autoSuggestion==1?true:false
        editForm.value.isUpload=editForm.value.isUpload==1?true:false
        editForm.value.uploadSettingParse = editForm.value.uploadSettings?JSON.parse(editForm.value.uploadSettings):null
        editForm.value.quote=editForm.value.isQuote==1?true:false
        editForm.value.knowledgeSettingsParse = editForm.value.knowledgeSettings?JSON.parse(editForm.value.knowledgeSettings):{"search_method": "single"}
    })
}
getDetailMethod()


const closeDrawer = () => {
    // props.editDrawer.close()
    router.back();
}
const changeBindSlider = (val,key) => {
    editForm.value[key] = val
}

const emit = defineEmits(['searchContentChild']);

const saveEditForm = () => {
    let openingQuestionList = editForm.value.openingQuestionList?editForm.value.openingQuestionList.filter(item=>{return item.openingQuestion!=''}):[]
    editForm.value.openQuestion=openingQuestionList.length>0?JSON.stringify(openingQuestionList):''
    editForm.value.knowledgeList=editForm.value.knowledgeBaseList.length>0?JSON.stringify(editForm.value.knowledgeBaseList):''
    // editForm.value.toolList=editForm.value.temToolList.length>0?JSON.stringify(editForm.value.temToolList):''
    editForm.value.toolList=selectedToolList.value.length>0?JSON.stringify(selectedToolList.value):''
    editForm.value.autoSuggestion=editForm.value.automaticSuggestion?1:0
    editForm.value.isUpload=editForm.value.isUpload?1:0
    editForm.value.uploadSettings = editForm.value.uploadSettingParse && editForm.value.isUpload?JSON.stringify(editForm.value.uploadSettingParse):null
    editForm.value.isQuote=editForm.value.quote?1:0
    editForm.value.knowledgeSettings=JSON.stringify(editForm.value.knowledgeSettingsParse)
    editForm.value.publishStatus = 1
    updateAgent(editForm.value).then((resp) => {
        if(resp.code == 200){
            ElMessage.success('修改成功')
            closeDrawer()
            emit('searchContentChild')
        }else{
            ElMessage.success('修改失败')
        }
    })
}

const toApiAuth = () => {
    showApiKeyDialog.value = true
}

const toAppAuth = () => {
    window.open('http://172.16.1.19/yimai-agent/agent/agent-role/agent/'+id)
}

//添加变量
const addKey = () => {
    /*
    editForm.value.keyList.push({
        agentId:editForm.value.agentId,
        isRequired:0,
        keyCode:'',
        keyName:'',
        keyType:null,
        remark:''
    })
    */
   variableSettingRef.value.appendField()
}
//删除变量
const delKey = (index) => {
    editForm.value.keyList.splice(index,1)
}

//添加开场问题
const addOpeningQuestion = () => {
    if(!editForm.value.openingQuestionList){
        editForm.value.openingQuestionList=[]
    }
    editForm.value.openingQuestionList.push({openingQuestion:''})
}
//删除
const delOpeningQuestion = (index) => {
    editForm.value.openingQuestionList.splice(index,1)
}

//大模型列表
// const modelList = ref([])
// const getModelListMethod= () =>{
//     getModelList().then(response=>{
//         modelList.value=response.rows
//     })
// }

//对话提示词失去焦点
const promptBlur = () => {
    if(editForm.value.prompt){
        var variablesModel = extractVariable(editForm.value.prompt)
        if(variablesModel){
            var variablesState = false
            if(editForm.value.keyList && editForm.value.keyList.length>0){
                variablesModel.result = variablesModel.result.filter(x=>editForm.value.keyList.filter(k => k.keyCode == x.replace(/{|}/g, '')).length<=0)
                variablesModel.variables = variablesModel.variables.filter(x=>editForm.value.keyList.filter(k => k.keyCode == x).length<=0)
                if(variablesModel.result.length>0 && variablesModel.variables.length>0){
                    variablesState = true
                }
            }else{
                variablesState = true
            }
            if(variablesState){
                ElMessageBox.confirm(
                '<div><div>提示词中引用了未定义的变量，是否自动添加到用户输入表单中？</div>'+
                '<div>'+variablesModel.result.toString()+'</div></div>'    
                , "系统提示", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning",
                    dangerouslyUseHTMLString: true,
                    showClose: false,
                    closeOnClickModal: false
                }).then(() => {
                    variablesModel.variables.forEach(element => {
                        editForm.value.keyList.push({
                            agentId:editForm.value.agentId,
                            keyCode:element,
                            keyName:element,
                            keyType:1
                        })
                    });
                })
                .catch((e) => {});
            }
            
            
        }
    }
}

//提取字符串里的变量
const extractVariable = (content) => {
    var variables = [];///{([^{}]*)}/g
    const regex = /{{([a-zA-Z]+)}}/g;
    var result = content.match(regex);
    //去重
    result = [...new Set(result)]
    if (result && result.length>0) {
        variables = result.map(m => m.replace(/{{|}}/g, ''));
    }
    var variablesModel = null
    if(variables.length>0){
        variablesModel = {
            result:result,
            variables:variables
        }
    }
    return variablesModel
}

getDeptList()
// getModelListMethod()

//切换
function changePage(){
    if(document.visibilityState=='visible'){
        if(toolShow.value){
            getToolsListMethod()
        }
        if(knowledgeBaseSelDialogShow.value){
            knowledgeBaseQueryParams.value.page=1
            getknowledgeBaseListMethod()
        }
    }
}

//日志
const logList = () => {
    router.push('/agent/bodyLog?id='+id);
}
//数据看板
const statistics = () => {
    router.push('/agent/bodyStatistics?id='+id);
}

//公开访问
const publicVisitDisabled = ref(false)
const toPublicVisit = () => {
    publicVisitDisabled.value = true
}

onMounted(() => {
    document.addEventListener('visibilitychange', changePage);
})
onUnmounted(() => {
    document.removeEventListener('visibilitychange', changePage);
})

</script>
<style scoped lang="scss">
.single-page-container{
    background-color: #F7F7FA;
    // height: 100vh;
    .single-page-header{
        height: 80px;
        display: flex;
        background-color: white;
        margin: 0 20px;
        justify-content: space-between;
        align-items: center;
        flex-direction: row;
        border-radius: 8px;
        .left-box{
            display: flex;
            align-items: center;
            .text-box{
                display: flex;
                flex-direction: column;
                margin-left: 10px;
                .title{
            display: flex;
            align-items: center;
                    height: 25px;
                    font-size: 18px;
                    font-weight: 500;
                    color: #032220;
                }
                .detail{
                    height: 17px;
                    font-size: 12px;
                    color: #999999;
                    font-weight: 400;
                    margin-top: 4px;
                }
            }
        }
        .right-box{
            margin-right: 20px;
        }
    }
    .single-page-content{
        display: flex;
        flex-direction: row;
        height: calc(100vh - 130px);
        overflow-y: auto;
        .content-left-box{
            width: calc(60% - 48px);
            height: calc(100% - 60px);
            border-right: 1px solid #E1E1E1;
            background-color: white;
            margin-top: 30px;
            .content-left-box-title{
                padding-left: 24px;
                font-size: 14px;
                font-weight: bold;
                color: #032220;
                margin-bottom: 16px;
            }
            .left-box-half-top{
                border-bottom: 1px solid #E1E1E1;
            }
            .custom-form-item-slider{
                display: flex;
                flex-direction: row;
                width: 100%;
                align-items: center;
                justify-content: space-between;
                margin-bottom: 5px;
                .custom-slider-title{
                    margin-right: 20px;
                    width: 250px;
                    text-align: left;
                    font-size: 12px;
                    color: #999999;
                }
                .custom-slider-value{
                    width: calc(100% - 120px);
                    display: flex;
                    flex-direction: row;
                }
            }
        }
        .content-right-box-title{
            color: #032220;
            font-size: 14px;
            font-weight: 500;
            margin-bottom: 10px;
        }
        
        
    }

    .whole{
        display: flex;
        justify-content: space-between;
        .left{
            background-color: #F9F9F9;
            height: calc(100vh - 130px);
            display: flex;
            justify-content: space-between;
            width:50%;
            .settings{
                width:100%;
                padding: 0 20px;
                border-right:1px solid #d1d5db;
                overflow-y: auto;
                margin-top: 10px;
                margin-bottom: 10px;
                .head{
                    font-size: 15px;
                    display: flex;
                    justify-content: space-between;
                    // margin-bottom: 10px;
                    .head-title{
                        display: flex;
                        align-items: center;
                        img{
                            margin-right: 5px;
                            width: 25px;
                        }
                    }
                }
                .ai-setting{
                    margin-bottom:10px;
                    background-color: white;
                    // border:1px solid #ccc;
                    padding: 20px;
                    border-radius: 8px;
                    .prompt{
                        
                    }
                    .model {
                        display: flex;
                        flex-direction: row;
                        // justify-content: space-between;
                        align-items: center;
                    }
                }
                
                .knowledege-base{
                    margin-bottom:10px;
                    background-color: white;
                    // border:1px solid #ccc;
                    padding: 10px 20px;
                    border-radius: 8px;
                }
                .upload-file{
                    margin-bottom:10px;
                    background-color: white;
                    // border:1px solid #ccc;
                    padding: 10px 20px;
                    border-radius: 8px;
                }
                .tool{
                    margin-bottom:10px;
                    background-color: white;
                    // border:1px solid #ccc;
                    padding: 10px 20px;
                    border-radius: 8px;
                }
                .opening-settings{
                    margin-bottom:10px;
                    // border:1px solid #ccc;
                    background-color: white;
                    padding: 10px 20px;
                    border-radius: 8px;
                }
                .automatic-suggestion{
                    margin-bottom:10px;
                    // border:1px solid #ccc;
                    background-color: white;
                    padding: 10px 20px;
                    border-radius: 8px;
                }
                .variable{
                    background-color:white;
                    padding: 10px 20px;
                    border-radius: 8px;
                    margin-bottom:10px;
                }
            }
        }

        .right{
            background-color: #F7F7FA;
            height:calc( 100vh - 130px );
            width:50%;
                padding: 10px 20px;
        }

        .title-font{
            font-size: 14px;
            font-weight: 400;
            color: #032220;
            margin: 10px 0;
            display: flex;
            align-items: center;
        }

        .info-icon{
            color: #999999;
        }
    }
    
}
.content-left-box-title{
    // padding-left: 24px;
    font-size: 15px;
    font-weight: bold;
    color: #032220;
    margin-bottom: 16px;
}
.left-box-half-top{
    border-bottom: 1px solid #E1E1E1;
}
.content-right-box-alter-text{
    background-color: #F7F7FAFF;
    width: 100%;
    border-radius: 8px;
    margin-top:10px;
    padding:10px 20px
}
.common-content {
    padding: 0 20px;
    background-color: #f4f4f5;
    padding-bottom: 20px;
}

::v-deep .pagination-container .el-pagination {
	right: 30px;
	position: absolute;
}

::v-deep .el-dialog {
    display: flex;
    flex-direction: column;
    margin: 0 !important;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    max-height: calc(100% - 30px);
    max-width: calc(100% - 30px);
}
.retrieve-settings{
    margin-top: 20px;
    .title{
        line-height: 35px;
    }
    .radio{
        line-height: 20px;
        // width:45%;
        border:1px solid #dcdfe6;
        border-radius:10px;
        .title-image-box{
            text-align:center;
            padding-top:10px;
            padding-left:30px;
            img{
                padding:5px;
                height: 35px;
                width: 35px;
                border-radius: 4px;
            }
        }
        .title{
            margin-top:5px;
            display: flex;
            justify-content: space-between;
            align-items: center;

        }
        .content{
            font-size: 13px;
            color:#999999;
            padding-right: 5px;
            min-height:30px;
            padding-bottom: 10px;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        .launch-content{
            min-height: 100px;
            border-top:1px solid #dcdfe6;
            padding: 20px 40px;
            .icon{
                color: #999999;
            }
            .icon-group{
                height:40px;
                background-color:#ffd65236;
                border-radius: 10px; 
                padding: 5px;
                margin-top:5px;
                img{
                    padding:5px;
                    height: 30px;
                    width: 30px;
                    border-radius: 4px;
                }
            }
            .slider-demo-block {
                max-width: 600px;
                display: flex;
                align-items: center;
                .el-slider {
                    margin-top: 0;
                    margin-left: 12px;
                }
                .demonstration {
                    width:80px;
                    font-size: 16px;
                    line-height: 44px;
                    color:#706f6f;
                    font-weight: 540;
                }
            }
            .image-box{
                text-align:center;
                padding-top:10px;
                img{
                    height: 25px;
                    width: 25px;
                    border-radius: 4px;
                }
            }
            .option-title{
                cursor: pointer; 
                line-height: 32px;
                // width:125px;
                background-color:#f3f3f3;
                border-radius: 8px;
                padding:0 5px;
            }
        }
    }
    .font{
        font-size: 1vw; /* 这里的5vw表示字体大小为视口宽度的5% */
        white-space: nowrap; /* 防止文字换行 */
        overflow: hidden; /* 超出容器部分隐藏 */
        text-overflow: ellipsis; /* 超过部分显示省略号 */
    }
    .radio-sel{
        border: 1px solid #5050E6;
    }
}
.upload-file-dialog{
    padding: 0 10px;
    .title{
        display: flex;
        align-items: center;
        .info-icon{
            color: #999999;
        }
    }
    .slider{
        margin-bottom: 30px;
        padding:0 5px;
    }
    
}
</style>
<style lang="scss">
.tooltip-pop.el-popper{
    max-width: 200px !important;
    word-break:break-all
}
.tooltip-pop-table.el-popper{
    max-width: 500px !important;
    word-break:break-all
}
</style>
<template>
    <div>
        <el-input v-model="searchText" 
            prefix-icon="Search" 
            placeholder="search emoji ..."
            @input="searchTextInput" 
        />
        <div class="divider"></div>
        <div class="categories-emojis">
            <template v-if="isSearching">
                <div class="categorie">
                    <p class="categorie-title">SEARCH</p>
                    <div class="categorie-emojis">
                        <template v-for="emoji in searchedEmojis">
                            <div class="categorie-emoji">
                                <div class="emoji-area" 
                                    @click="setCurrentEmoji(emoji)" >
                                    <Emoji :data="emojiIndex" :emoji="emoji" :size="24" />
                                </div>
                            </div>
                        </template>
                    </div>
                </div>
            </template>
            <template v-for="categorie in categories">
                <div class="categorie">
                    <p class="categorie-title">{{ categorie.id }}</p>
                    <div class="categorie-emojis">
                        <template v-for="emoji in categorie.emojis">
                            <div class="categorie-emoji">
                                <div class="emoji-area" 
                                    @click="setCurrentEmoji(emoji)" >
                                    <Emoji :data="emojiIndex" :emoji="emoji" :size="24" />
                                </div>
                            </div>
                        </template>
                    </div>
                </div>
            </template>
        </div>
        <div class="background-colors">
            <p class="title">选择背景颜色</p>
            <div class="background-colors-list">
                <template v-for="backgroundColor in backgroundColors">
                    <div class="background-colors-item"
                        :class="{'actived':currentBackgroundColor == backgroundColor}">
                        <div  class="background-colors-area" 
                            @click="setCurrentBackgroundColor(backgroundColor)" 
                            :style="{'background-color': backgroundColor}">
                            <template v-if="currentEmoji">
                                <Emoji :data="emojiIndex" :emoji="currentEmoji" :size="24" />
                            </template>
                        </div>
                    </div>
                </template>
            </div>
        </div>
        <div class="divider"></div>
    </div>
</template>
<script setup>
import singleEmoji from './singleEmoji'

import "emoji-mart-vue-fast/css/emoji-mart.css";

import { EmojiIndex, Emoji } from "emoji-mart-vue-fast/src";

import data from "emoji-mart-vue-fast/data/all.json";

let emojiIndex = new EmojiIndex(data);

const categories = emojiIndex._data.categories

const props = defineProps({
    content: String,
})

const searchText = ref('')

const currentEmoji = ref(null)

const currentBackgroundColor = ref('#FFEAD5')

const isSearching = ref(false)

const searchedEmojis = ref([])

const backgroundColors = [
  '#FFEAD5',
  '#E4FBCC',
  '#D3F8DF',
  '#E0F2FE',

  '#E0EAFF',
  '#EFF1F5',
  '#FBE8FF',
  '#FCE7F6',

  '#FEF7C3',
  '#E6F4D7',
  '#D5F5F6',
  '#D1E9FF',

  '#D1E0FF',
  '#D5D9EB',
  '#ECE9FE',
  '#FFE4E8',
]

const setCurrentEmoji = (emoji) => {
    currentEmoji.value = emoji
}

const setCurrentBackgroundColor = (backgroundColor) => {
    currentBackgroundColor.value = backgroundColor
}

const findEmojiByContent = () => {
    return  emojiIndex.findEmoji(props.content)
}

const searchTextInput = (val) => {
    isSearching.value = (val != '')
    searchedEmojis.value = emojiIndex.search(val)
}

defineExpose({
    currentEmoji,
    currentBackgroundColor
})

</script>
<style lang="scss" scoped>
.categories-emojis{
    max-height: 200px;
    width: 100%;
    overflow-y: auto;
    overflow-x: hidden;
    padding-left: .75rem;
    padding-right: .75rem;
    .categorie{
        display: flex;
        flex-direction: column;
        .categorie-title{
            margin-bottom: .25rem;
            font-size: .75rem;
            line-height: 1rem;
            font-weight: 500;
            text-transform: uppercase;
            color: rbg(16,24,40);
        }
        .categorie-emojis{
            display: grid;
            height: 100%;
            width: 100%;
            gap: .25rem;
            grid-template-columns: repeat(8, minmax(0, 1fr));
            .categorie-emoji{
                display: inline-flex;
                width: 2.5rem;
                height: 2.5rem;
                align-items: center;
                justify-content: center;
                border-radius: .5rem;
                cursor: pointer;
                .emoji-area{
                    display: flex;
                    height: 2rem;
                    width: 2rem;
                    align-items: center;
                    justify-content: center;
                    border-radius: .5rem;
                    padding: .25rem;
                }
                &:hover{
                    box-shadow: rgb(255, 255, 255) 0px 0px 0px 1px, 
                        rgb(208, 213, 221) 0px 0px 0px 2px, 
                        rgba(0, 0, 0, 0) 0px 0px 0px 0px;
                }
            }
        }
    }
}
.background-colors{
    padding: .75rem;
    .title{
        margin-bottom: .5rem;
        font-size: .75rem;
        line-height: 1rem;
        font-weight: 500;
        text-transform: uppercase;
        color: rbg(16,24,40);
    }
    .background-colors-list{
        display: grid;
        height: 100%;
        width: 100%;
        grid-template-columns: repeat(8, minmax(0, 1fr));
        gap: .25rem;
        .background-colors-item{
            display: inline-flex;
            height: 2.5rem;
            width: 2.5rem;
            cursor: pointer;
            align-items: center;
            justify-content: center;
            border-radius: .5rem;
            .background-colors-area{
                display: flex;
                height: 2rem;
                width: 2rem;
                align-items: center;
                justify-content: center;
                border-radius: .5rem;
                padding: .25rem;
            }
            &:hover{
                box-shadow: rgb(255, 255, 255) 0px 0px 0px 1px, 
                    rgb(208, 213, 221) 0px 0px 0px 2px, 
                    rgba(0, 0, 0, 0) 0px 0px 0px 0px;
            }
        }
    }
}
.divider{
    margin-top: .5rem;
    margin-bottom: .5rem;
    height: .5px;
    width: 100%;
    background-color: rgb(234,236,240);
}
.actived{
    box-shadow: rgb(255, 255, 255) 0px 0px 0px 1px, 
        rgb(208, 213, 221) 0px 0px 0px 2px, 
        rgba(0, 0, 0, 0) 0px 0px 0px 0px;
}
.emoji-mart-emoji{
    height: 24px;
    padding: 0;
}
</style>
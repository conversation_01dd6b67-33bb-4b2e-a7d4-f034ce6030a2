<template>
    <div style="display: flex;">
        <div class="common-container">
            <div class="common-header">
                <div class="title">工具列表</div>
                <div class="header-right">
                    <div style="width: 140px;margin-right: 8px;">
                        <el-select v-model="queryParams.labels"
                            multiple collapse-tags clearable placeholder="全部标签">
                            <template v-for="item in tagOptions">
                                <el-option :label="item.label.zh_Hans" :value="item.name" />
                            </template>
                        </el-select>
                    </div>
                    <el-input
                        v-model="queryParams.toolName"
                        placeholder="搜索"
                        clearable
                        :prefix-icon="Search"
                        style="width: 180px;"
                    />
                    <el-button @click="addTool" type="primary" style="margin-left: 8px;">新建自定义工具</el-button>
                </div>
            </div>
            <div v-loading="loading" class="common-content">
                <el-tabs v-model="activeName" @tab-click="handleClick"  type="card">
                    <el-tab-pane label="用户工具" name="custom"></el-tab-pane>
                    <el-tab-pane label="系统工具" name="built-in"></el-tab-pane>
                </el-tabs>
                <div class="tool-list" v-if="toolList && toolList.filter(tool => {
                        return (tool.label.zh_Hans.indexOf(queryParams.toolName.toLowerCase()) != -1 || 
                                tool.description.zh_Hans.indexOf(queryParams.toolName.toLowerCase()) != -1) 
                            && (queryParams.labels.length == 0 || 
                                queryParams.labels.find(label => tool.labels.indexOf(label) != -1))
                        }).length>0">
                    <template v-for="tool in toolList.filter(tool => {
                        return (tool.label.zh_Hans.indexOf(queryParams.toolName.toLowerCase()) != -1 || 
                                tool.description.zh_Hans.indexOf(queryParams.toolName.toLowerCase()) != -1) 
                            && (queryParams.labels.length == 0 || 
                                queryParams.labels.find(label => tool.labels.indexOf(label) != -1))
                        })">
                        <tool-item :tool="tool" 
                            v-show="activeName==='built-in' ? 
                                tool.type === 'builtin' : 
                                tool.type === 'api'" 
                            @set-actived-tool="setActivedTool"
                            @delete-tool="deleteToolMethod"
                            @to-auth="toAuth"
                            @to-delete-auth="toDeleteAuth"
                            :selected="activedTool?.id == tool.id"
                            :tag-options="tagOptions"
                            ></tool-item>
                    </template>
                </div>
                <div class="common-content" style="display: flex;flex-direction: column;align-items: center;justify-content: center;" 
                v-if="!toolList || toolList.filter(tool => {
                        return (tool.label.zh_Hans.indexOf(queryParams.toolName.toLowerCase()) != -1 || 
                                tool.description.zh_Hans.indexOf(queryParams.toolName.toLowerCase()) != -1) 
                            && (queryParams.labels.length == 0 || 
                                queryParams.labels.find(label => tool.labels.indexOf(label) != -1))
                        }).length==0">
                    <img :src="noData" style="height: 100px;width: 100px;"/>
                    <div style="color:#dbdbdb;font-weight:550">
                        暂无数据
                    </div>
                </div>
            </div>
        </div>
        <!-- <div class="tool-info" v-if="activedTool != null">
            <tool-panel :tool="activedTool" 
                :tool-tools="toolTools" 
                @update-tool="updateTool"
                @delete-tool="deleteTool"
                ></tool-panel>
        </div> -->
        <el-dialog v-model="dialogVisible" :title="editToolTitle" width="630px" style="height: 80vh;">
            <div style="height: calc(-120px + 80vh);display: flex;flex-direction: column;">
                <edit-custom-tool ref="editCustomToolRef"/>
            </div>
            <div style="display: flex;flex-direction: row;justify-content: flex-end;margin-top: 20px;">
                <el-button @click="dialogVisible = false">取 消</el-button>
                <el-button type="primary" @click="toSave">保 存</el-button>
            </div>
        </el-dialog>

        <el-dialog v-model="toolDialogVisible" title="工具" width="400px" class="tool-dialog">
            <div class="tools">
                <div class="title">包含 {{ toolTools?.length }} 个工具</div>
                <div class="tool-list">
                    <template v-for="toolTool in toolTools">
                        <div class="tool-item" :class="{'tool-item-disabled':activedTool && !activedTool.is_team_authorization && activedTool.type == 'builtin'}">
                            <div class="tools-label">{{ toolTool.label.zh_Hans }}</div>
                            <el-tooltip :content="toolTool.description.zh_Hans" placement="top" effect="light" popper-class="tool-pop">
                                <div class="tools-desc">{{ toolTool.description.zh_Hans }}</div>
                            </el-tooltip>
                        </div>
                    </template>
                </div>
            </div>
            <template #footer >
                <span class="dialog-footer">
                    <el-button @click="toolDialogVisible = false">关 闭</el-button>
                </span>
            </template>
        </el-dialog>

        <el-dialog v-model="authDialogVisible" title="设置授权" width="400px" style="height: 80vh;">
            <div style="height: calc(-120px + 80vh);display: flex;flex-direction: column;overflow-y: auto;overflow-x: hidden;">
                <auth-config :auth-fields="authConfigFields" ref="authConfigRef"
                    :auth-fields-values="authConfigFieldsValues"></auth-config>
            </div>
            <div style="display: flex;flex-direction: row;align-items: center;justify-content: center;margin-top: 20px;">
                <el-button @click="authDialogVisible = false">取 消</el-button>
                <el-button type="primary" @click="updateAuth">保 存</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script setup>
import {Plus,Search} from '@element-plus/icons-vue'
import toolItem from './components/toolItem'
import toolPanel from './components/toolPanel'
import editCustomTool from './components/editCustomTool'
import { 
    getAllTool,
    getToolDetailByProvider,
    getBuiltinToolsByProvider,
    getCustomToolsByProvider ,
    addCustomTool,
    getAllTags,
    updateTool,
    deleteTool,
    getBuiltinToolAuthFields,
    getBuiltinToolAuthFieldValues,
    authBuiltinTool,
    deleteAuthBuiltinTool
} from '@/api/newTool'
import { nextTick } from 'vue'
import noData from "@/assets/icons/svg/no-data.svg";
import { ElMessage } from 'element-plus';
import authConfig from './components/authConfig'

const activeName = ref('built-in')
const queryParams = ref({
    labels:[],
    toolName:''
})
const loading = ref(false)
const toolList = ref([])
const tagOptions = ref([])

const activedTool = ref(null)
const toolTools = ref([])
const dialogVisible = ref(false)
const editToolTitle = ref(null)

const toolDialogVisible = ref(false)

const authDialogVisible = ref(false)

const editCustomToolRef = ref(null)

const handleClick = (tab, event) => {
    if(activedTool.value){
        activedTool.value = null
    }
}

const getList = () => {
    getAllTool().then( res => {
        toolList.value = res
    })
}

const setActivedTool = (tool,action) => {
    // if(activedTool.value?.id == tool.id){
    //     activedTool.value = null
    //     toolTools.value = []
    // }else{
        if(tool.type == 'builtin'){
            getBuiltinToolsByProvider(tool.id).then(res => {
                toolTools.value = res
                activedTool.value = tool
                if (action=='show'){
                    toolDialogVisible.value = true
                }
            })
        }else if(tool.type == 'api'){
            getToolDetailByProvider({provider:tool.name}).then((response) => {
                activedTool.value = {
                    id:tool.id,
                    provider:tool.name,
                    type:tool.type,
                    ...response}
                getCustomToolsByProvider({provider:tool.name}).then(res => {
                    toolTools.value = res
                    if(action=='edit'){
                        editToolTitle.value = '编辑自定义工具'
                        dialogVisible.value = true
                        nextTick(() => {
                            editCustomToolRef.value.formData = JSON.parse(JSON.stringify({
                                    paramsSchemas:[...activedTool.value.tools],
                                    ...activedTool.value
                                }
                            ))
                        })
                    }
                    else if (action=='show'){
                        toolDialogVisible.value = true
                    }
                })
            })
        }
    // }
}

const addTool = () => {
    activedTool.value = null
    editToolTitle.value = '新建自定义工具'
    dialogVisible.value = true
    nextTick(() => {
        editCustomToolRef.value.resetFromValue()
    })
}

const updateToolMethod = (data) => {
    getAllTool().then( res => {
        toolList.value = res
        nextTick(() => {
            if(data.type == 'api'){
                getToolDetailByProvider({provider:data.provider}).then((response) => {
                    activedTool.value = {
                        id:data.id,
                        provider:data.provider,
                        type:data.type,
                        ...response}
                    getCustomToolsByProvider({provider:data.provider}).then(res1 => {
                        toolTools.value = res1
                    })
                })
            }
            if(data.type == 'builtin'){
                getBuiltinToolsByProvider(data.id).then(res1 => {
                    toolTools.value = res1
                    activedTool.value = toolList.value.find(item => item.id == data.id)
                })
            }
        })
    })
    
}

// const updateTool = () => {
//     activedTool.value = null
//     toolTools.value = []
//     getList()
// }

const deleteToolMethod = (tool) => {
    deleteTool({provider:tool.name}).then(() => {
        ElMessage.success('操作成功')
        activedTool.value = null
        toolTools.value = []
        getList() 
    })
}

const toSave = () => {
    //新增
    if(!editCustomToolRef.value.formData.id){
        editCustomToolRef.value.validateValue().then(() => {
            addCustomTool(editCustomToolRef.value.formData).then((res) => {
                dialogVisible.value = false
                editToolTitle.value = null
                getList()
            })
        })
    }
    //编辑
    else{
        editCustomToolRef.value.validateValue().then(() => {
            const {credentials,custom_disclaimer,
                description,icon,labels,privacy_policy,
                provider,schema,schema_type} = editCustomToolRef.value.formData
            const postData = {
                credentials,custom_disclaimer,
                description,icon,labels,privacy_policy,
                provider,schema,schema_type,original_provider:activedTool.value.provider
            }
            updateTool(postData).then(() => {
                ElMessage.success('操作成功')
                dialogVisible.value = false
                editToolTitle.value = null
                const returnData = {
                    id:activedTool.value.id,
                    type:activedTool.value.type,
                    provider:postData.provider,
                }
                updateToolMethod(returnData)
            })
        })
    }
}

getAllTags().then((res) => {
    tagOptions.value = res
})


const authConfigRef  = ref(null)
const authConfigFields = ref([])
const authConfigFieldsValues = ref({})

const toAuth = (tool) => {
    activedTool.value = {
        id:tool.id,
        name:tool.name
    }
    getBuiltinToolAuthFields(tool.name).then((res) => {
        getBuiltinToolAuthFieldValues(tool.name).then((values) => {
            authConfigFields.value = res
            authConfigFieldsValues.value = values
            nextTick(() => {
                authDialogVisible.value = true
            })
        })
    })
}

const updateAuth = () => {
    let params = {
        credentials:authConfigRef.value.authFieldsValue
    }
    authBuiltinTool(activedTool.value.name,params).then(() => {
        ElMessage.success('操作成功')
        authDialogVisible.value = false
        activedTool.value = null
        toolTools.value = []
        getList()
    })
}

const toDeleteAuth = (tool) => {
    deleteAuthBuiltinTool(tool.name).then(() => {
        ElMessage.success('操作成功')
        activedTool.value = null
        toolTools.value = []
        getList()
    })
}

getList()

</script>

<style lang='scss' scoped>
.tool-info{
    border-left: 1px solid rgba(0,0,0,.08);
    height: calc(100vh - 52px);
    width: 420px;
    flex-shrink: 0;
    background-color: #F7F7FA;
    overflow-y: auto;
}
.common-container {
    display: flex;
    flex-direction: column;
    background-color: #F7F7FA;
    height: calc(100vh - 52px);
    width: 100%;
    margin: 0;
    padding: 0;
    background-size: cover;
}
.common-header {
    height: 80px;
    padding: 28px 30px;
    line-height: 24px;
    display: flex;
    justify-content: space-between;

    .title {
        color: #032220;
        font-size: 18px;
        font-weight: 500;
    }

    .header-right {
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: row;
    }
}

.common-content {
    margin: 0 30px;
    height: calc(100vh - 180px);
    display: flex;
    flex-direction: column;
    overflow: hidden;
    padding-bottom: 10px;
}

.tool-list{
    display: grid;
    grid-template-columns: repeat(3, minmax(0, 1fr));
    gap: 1rem;
    flex-shrink: 0;
    flex-grow: 1;
    align-content: flex-start;
    max-height: calc(100vh - 250px);
    overflow-y: auto;
    padding-bottom: 10px;
    .add-tool{
        min-height: 160px;
        display: flex;
        flex-direction: column;
        grid-column: span 1 / span 1;
        background-color: rgb(234, 236, 240);
        border-radius: .75rem;
        border: 1px solid rgb(234,236,240);
        // box-shadow: 0 0 1px 0 rgba(0,0,0,.1);
        cursor: pointer;
        transition: all .2s;
        // box-shadow: 0px 1px 2px 0px rgba(16,24,40,.06),0px 1px 3px 0px rgba(16,24,40,.1);
        &:hover{
            box-shadow: 0px 4px 6px -2px rgba(16,24,40,.03),0px 12px 16px -4px rgba(16,24,40,.08);
            background-color: white;
        }
        .add-tool-btn{
            flex-grow: 1;
            border-radius: .75rem;
                display: flex;
                align-items: center;
                justify-content: center;
            .btn-area{
                display: flex;
                // padding: 1rem;
                // padding-bottom: .75rem;
                align-items: center;
                justify-content: center;
                flex-direction: column;
                flex-shrink: 0;
                .icon{
                    display: flex;
                    height: 3rem;
                    width: 3rem;
                    justify-content: center;
                    align-items: center;
                    border-radius: .5rem;
                    border: 1px solid rgb(234, 236, 240);
                    background-color: rgb(242, 244, 247);
                    margin-bottom: 1rem;
                }
                .text{
                    color: rgb(29, 41, 57);
                    line-height: 1.25rem;
                    font-weight: 600;
                    font-size: .875rem;
                }
            }
            &:hover{
                .text{
                    color: #5050E6;
                }
                .icon{
                    color: #5050E6;
                    border-color: rgb(221 214 254);
                    background-color: rgb(237 233 254);
                }
            }
        }
    }
}
::v-deep(.el-dialog) {
    display: flex;
    flex-direction: column;
    margin: 0 !important;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    max-height: calc(100% - 30px);
    max-width: calc(100% - 30px);
}

::v-deep(.el-dialog__body) {
  padding: 0;
}

.tools{
    margin-top: .75rem;
    .title{
        font-size: .75rem;
        font-weight: 500;
        line-height: 1.5rem;
        color: rgb(102, 112, 133);
        margin-left: 10px;
    }
    .tool-list{
        margin-top: .25rem;
        height: 580px;
        overflow: auto;
        display: flow-root;
        padding: 10px;
        .tool-item{
            margin-bottom: .5rem;
            cursor: pointer;
            border-radius: .75rem;
            border-width: .5px ;
            border-color: rgb(234, 236, 240);
            border-style: solid;
            background-color: rgb(252, 252, 253);
            padding: 1rem .75rem;
            box-shadow: rgba(0, 0, 0, 0) 0px 0px 0px 0px, rgba(0, 0, 0, 0) 0px 0px 0px 0px, rgba(16, 24, 40, 0.05) 0px 1px 2px 0px;
            .tools-label{
                font-size: .875rem;
                font-weight: 600;
                line-height: 1.25rem;
                color: rgb(29, 41, 57)
            }
            .tools-desc{
                margin-top: .125rem;
                -webkit-line-clamp: 2;
                overflow: hidden;
                display: -webkit-box;
                -webkit-box-orient: vertical;
                font-size: .75rem;
                line-height: 18px;
                color: rgb(102, 112, 133);
            }
        }
        .tool-item-disabled{
            cursor: not-allowed;
            background-color: rgb(247, 248, 250);
        }
    }
}
</style>
<style lang="scss">
    .tool-pop.el-popper{
        max-width: 450px !important;
    }
</style>
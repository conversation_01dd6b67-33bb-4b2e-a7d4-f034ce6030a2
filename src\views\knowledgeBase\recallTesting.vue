<template>
    <div class="common-container">
        <div class="single-page-header">
            <div class="left-box">
                <!-- <el-icon style="margin-right: 10px;"><Back /></el-icon> -->
                <el-button :icon="Back" @click="back()" text style="margin-right: 10px;" link></el-button>
                <img :src="knowledgeBaseIcon" />
                <div class="text-box">
                    <div class="title">
                        召回测试
                        <!-- <el-icon size="20px" style="margin-left: 4px;color: #5050E6;"><Edit /></el-icon> -->
                    </div>
                    <div class="detail">
                        基于给定的查询文本测试知识库的召回效果
                    </div>
                </div>
            </div>
        </div>
        <div class="whole">
            <el-row  style="height:calc(100vh - 150px)">
                <el-col :span="12" style="height:calc(100vh - 150px)">
                    <!-- <div class="min-title">召回测试</div>
                    <div style="font-size:15px;color:#606060;line-height:30px">基于给定的查询文本测试知识库的召回效果</div> -->
                    <div style="margin-top:10px">
                        <el-row style="padding: 10px;border-radius: 8px;background-color: white;" ref="childContext">
                            <el-col :span="10" style="margin-bottom: 10px;font-size:15px;line-height:25px;font-weight:600">测试文本</el-col>
                            <el-col :span="14" style="margin-bottom: 10px;display: flex;justify-content: end;">
                                <el-button plain @click="retrieveSettingsDrawerShow=true" style="color:#7d50e6">
                                    <img :src="vectorretrievalSet" style="padding:5px;height: 25px;width: 25px;"/>
                                    检索策略
                                </el-button>
                            </el-col>
                            <el-col :span="24">
                                <el-input maxlength="200" show-word-limit v-model="retrievalInfo.query" placeholder="请输入内容" style="width: 100%;" type="textarea" :rows="10" />
                            </el-col>    
                            <el-col :span="24" style="text-align: right;margin-top: 10px;">
                                <el-button type="primary" @click="testingMethod()" :disabled="retrievalInfo.query?false:true">测试</el-button>
                            </el-col>       
                        </el-row>
                    </div>
                    <div style="background-color: white;margin-top:20px;border-radius: 8px;padding: 20px;height: calc(100vh - 510px);">
                        <div class="min-title">历史查询</div>
                        <div v-if="total<=0" class="no-recent-queries">
                            <div style="padding-top:10px;">
                                <img :src="recentqueries" style="padding:10px;height: 50px;width: 50px;border:1px solid #e7e6e6;border-radius:5px"/>
                            </div>
                            <div style="color:#706f6f">
                                暂无历史查询结果
                            </div>
                        </div>
                        <div>
                            <el-table v-loading="loading" :data="historicalQueryList" max-height="280" style="margin-top:10px" :tooltip-options="{'popper-class': 'knowledge-recall-table-column-tooltip-popper'}" v-if="total>0">
                                <el-table-column label="数据源" prop="source" :show-overflow-tooltip="true" />
                                <el-table-column label="文本" prop="content" :show-overflow-tooltip="true" />
                                <el-table-column label="时间" prop="created_at" width="250">
                                    <template #default="scope">
                                        <span>{{ scope.row.created_at?formatDate(scope.row.created_at):'' }}</span>
                                    </template>
                                </el-table-column>
                            </el-table>

                            <pagination
                                v-show="total > 0"
                                :total="total"
                                v-model:page="queryParams.page"
                                v-model:limit="queryParams.limit"
                                layout="prev, pager, next"
                                @pagination="historicalQueryMethod"
                                style="margin-right:20px"
                            />
                        </div>
                    </div>
                </el-col>
                <el-col :span="12" style="padding:10px 20px">
                    <div v-if="recallParagraphList.length>0" v-loading="recallParagraphLoading">
                        <div class="min-title">召回段落</div>
                        <el-row style="margin-top: 20px;max-height: calc(100vh - 215px);overflow-y: auto;">
                            <el-col :span="12" style="padding:5px 10px;" v-for="(item,index) in recallParagraphList">
                                <el-card class="slider-demo">
                                    <div class="slider-demo-block">
                                        <img :src="recalltestscore" style="height: 15px;width: 15px;margin-right:10px"/>
                                        <el-slider v-model="item.score" :max="0.99" disabled />
                                        <span style="margin-left: 20px;">{{ item.score }}</span>
                                    </div>
                                        <!-- <el-progress :percentage="item.score" aria-valuemax="0.99" color="#909399">
                                            {{ item.score.toFixed(2) }}
                                        </el-progress> -->
                                    <div class="content">
                                        {{ item.segment.content }}
                                    </div>
                                    <div class="hidden-area">
                                        <el-button :key="plain" link 
                                        style="color:#5050E6;font-weight:550;" @click="viewVectorCharts(item)">
                                        查看向量图表 <el-icon><TopRight /></el-icon></el-button>
                                    </div>
                                    <template #footer>
                                        <div class="footer">
                                            {{item.segment.document.name}}
                                        </div>
                                    </template>
                                </el-card>
                            </el-col>
                        </el-row>
                    </div>
                    <div v-if="recallParagraphList.length<=0" style="display: flex;justify-content: center;align-items: center;height: calc(100vh - 168px);" 
                    v-loading="recallParagraphLoading" >
                        <div style="text-align:center">
                            <img :src="noData" style="height: 100px;width: 100px;"/>
                            <div style="color:#dbdbdb;font-weight:550">
                                暂无数据
                            </div>
                        </div>
                    </div>
                </el-col>
            </el-row>
        </div>

        <!-- 检索设置 -->
        <!-- <el-drawer v-model="retrieveSettingsDrawerShow" :with-header="false" size="45%" class="retrieve-settings-drawer" >
            <div style="border-bottom: 1px solid #ccc;">
                <div style="padding:20px;display:flex;justify-content:space-between"><span style="align-items:center;line-height:32px" >检索设置</span> 
                    <el-button :icon="Close" text @click="cancel" />
                </div>
            </div> 
            <retrieve-settings v-model="retrievalInfo.retrieval_model"></retrieve-settings>
            <div class="footer">
                <el-button plain @click="cancel">取消</el-button>
                <el-button type="primary" @click="cancel">保存</el-button>
            </div>
        </el-drawer> -->

        <el-dialog v-model="retrieveSettingsDrawerShow" title="检索设置">
            <div style="padding: 20px;">
                <retrieve-settings v-model="retrievalInfo.retrieval_model"></retrieve-settings>
            </div>
            <template #footer>
                <span class="dialog-footer">
                    <el-button plain @click="cancel">取消</el-button>
                <el-button type="primary" @click="cancel">保存</el-button>
                </span>
            </template>
        </el-dialog>

        <!--查看向量图表-->
        <el-dialog v-model="viewVectorChartsVisible" :show-close="false">
            <el-row class="view-vector-charts" >
                <el-col :span="12" class="view-vector-charts-left">
                    <div class="header">
                        <div class="title">#<span style="margin-left:5px">{{ viewVectorChartsInfo.segment.position }}</span></div>
                        <div class="prompt">
                            <div style="display:flex;align-items:center">
                                <el-icon><Reading /></el-icon> 
                                <span style="margin-left:5px">{{ viewVectorChartsInfo.segment.word_count }}</span>
                            </div>
                            <div style="display:flex;align-items:center;margin-left:10px">
                                <el-icon><Aim /></el-icon> 
                                <span style="margin-left:5px">{{ viewVectorChartsInfo.segment.hit_count }} </span>
                            </div>
                        </div>
                    </div>
                    <div class="content" v-html="viewVectorChartsInfo.segment.content" ></div>
                    <div class="footer">
                        <div>关键词</div>
                        <div class="tag-list">
                            <el-tag v-for="(item,index) in viewVectorChartsInfo.segment.keywords" :key="index" type="info" 
                            :disable-transitions="false" style="margin-right: 5px;margin-top:5px" >
                                {{ item }}
                            </el-tag>
                        </div>
                    </div>
                </el-col>
                <el-col :span="12" class="view-vector-charts-right">
                    <div class="header">
                        <div style="display: flex;align-items: center;">
                            <img :src="vector" style="height: 15px;width: 15px;margin-right:5px" />
                            向量哈希:
                        </div>
                        <div class="content">{{ viewVectorChartsInfo.segment.index_node_hash }}</div>
                        <el-button text class="close" :icon="Close" link @click="viewVectorChartsVisible=false"></el-button>
                    </div>
                    <div v-if="viewVectorChartsVisible">
                        <div ref="vectorchart" style="height: calc( 100vh - 430px);" />
                    </div>
                </el-col>
            </el-row>
        </el-dialog>
    </div>
</template>

<script setup>
import { nextTick, ref } from 'vue'
import {Back,Close,TopRight} from '@element-plus/icons-vue'
import { getKnowledgeBaseInfo,historicalQuery,testing } from "@/api/knowledgeBase/knowledgeBase";
import CustomSlider from '@/components/CustomSlider/index'
import router from "@/router";
import documentation from "@/assets/images/knowledgeBase.png";
import vectorretrieval from "@/assets/icons/svg/vectorretrieval.svg";
import vectorretrievalSet from "@/assets/icons/svg/vectorretrieval-set.svg";
import fullTextSearch from "@/assets/icons/svg/fullTextSearch.svg";
import mixedRetrieval from "@/assets/icons/svg/mixedRetrieval.svg";
import model from "@/assets/icons/svg/model.svg";
import recentqueries from "@/assets/icons/svg/recentqueries.svg";
import recalltest from "@/assets/icons/svg/recalltest.svg";
import recalltestscore from "@/assets/icons/svg/recalltestscore.svg";
import vector from "@/assets/icons/svg/vector.svg";
import * as echarts from 'echarts';
import retrieveSettings from '@/components/retrieveSettings/index.vue'
import { ElMessage } from 'element-plus'
import knowledgeBaseIcon from "@/assets/icons/svg/knowledgeBaseIcon.svg";
import noData from "@/assets/icons/svg/no-data.svg";

const route = useRoute();
const knowledgeId = route.query.knowledgeId
const historicalQueryList = ref([])
const recallParagraphList = ref([])
const loading = ref(false)
const recallParagraphLoading = ref(false)
const retrieveSettingsDrawerShow = ref(false)

//向量图表
const viewVectorChartsVisible = ref(false)
const viewVectorChartsInfo = ref({})
const vectorchart = ref(null);

const queryParams = ref({
    page:1,
    limit:10
})
const total = ref(0)

//检索设置列表
const retrievalList = ref([
    {search_method:'semantic_search',title:'向量检索',img:vectorretrieval,content:'通过生成查询嵌入并查询与其向量表示最相似的文本分段'},
    {search_method:'full_text_search',title:'全文检索',img:fullTextSearch,content:'索引文档中的所有词汇，从而允许用户查询任意词汇，并返回包含这些词汇的文本片段'},
    {search_method:'keyword_search',title:'混合检索',img:mixedRetrieval,content:'同时执行全文检索和向量检索，并应用重排序步，从两类查询结果中选择匹配用户问题的最佳结果，需配置 Rerank 模型 API'},
])

const retrievalInfo = ref({
    query:'',
    retrieval_model:{
        search_method:'semantic_search',
        reranking_enable:false,
        reranking_model:{
            reranking_provider_name:'',
            reranking_model_name:''
        },
        top_k:3,
        score_threshold_enabled:false,
        score_threshold:null
    }
})

//取消
const cancel = () => {
    retrieveSettingsDrawerShow.value=false
}

const historicalQueryMethod = () => {
    loading.value=true
    historicalQuery(queryParams.value,knowledgeId).then(response=>{
        historicalQueryList.value=response.data
        total.value=response.total
        loading.value=false
    })
}

//时间戳转时间
const formatDate = (time) => {
    const date = new Date(time*1000);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0'); // 月份从0开始，所以加1，并用0填充
    const day = String(date.getDate()).padStart(2, '0'); // 用0填充
    const hours = String(date.getHours()).padStart(2, '0'); // 用0填充
    const minutes = String(date.getMinutes()).padStart(2, '0'); // 用0填充
    return `${year}-${month}-${day} ${hours}:${minutes}`;
}

//测试
const testingMethod = () => {
    if(retrievalInfo.value.retrieval_model.search_method == 'hybrid_search'
    && (retrievalInfo.value.retrieval_model.reranking_model.reranking_model_name == null
    || retrievalInfo.value.retrieval_model.reranking_model.reranking_model_name == "")){
        ElMessage.warning('请选择Rerank 模型！')
        return
    }
    if(retrievalInfo.value.retrieval_model.reranking_enable && retrievalInfo.value.retrieval_model.search_method!='hybrid_search'
    && (retrievalInfo.value.retrieval_model.reranking_model.reranking_model_name == null
    || retrievalInfo.value.retrieval_model.reranking_model.reranking_model_name == "")){
        ElMessage.warning('请选择Rerank 模型或关闭Rerank 模型！')
        return
    }
    recallParagraphLoading.value=true
    testing(retrievalInfo.value,knowledgeId).then(response=>{
        response.records.forEach(element => {
            element.score=element.score ? Number(element.score.toFixed(2)) : 0
        });
        // setTimeout(() => {
            recallParagraphList.value=response.records
            recallParagraphLoading.value=false
        // }, 3000 );
        historicalQueryMethod()
    })

}

//查看向量图标
const viewVectorCharts = (info) => {
    viewVectorChartsInfo.value=info
    viewVectorChartsVisible.value=true
    nextTick(() => {
        getVectorChart(viewVectorChartsInfo.value.tsne_position)
    });
}

//向量图表
const getVectorChart = (tsne_position) => {
    const vectorChartInstance = echarts.init(vectorchart.value);
    vectorChartInstance.setOption({
        tooltip: {
            trigger: 'axis',
            axisPointer: {
            type: 'cross'
            }
        },
        xAxis: {
            splitLine: {
                lineStyle: {
                    type: 'dashed'
                }
            },
        },
        yAxis: {
            splitLine: {
                lineStyle: {
                    type: 'dashed'
                }
            }
        },
        series: [{
            symbolSize: 20,
            data: [
                [tsne_position.x, tsne_position.y],
            ],
            type: 'scatter'
        }]
    })

    window.addEventListener("resize", () => {
        vectorChartInstance.resize();
    });
    
}

//
const getKnowledgeBaseInfoMethod = () => {
    if(!knowledgeId){
        router.push("/knowledgeBase");
    }else{
        getKnowledgeBaseInfo(knowledgeId).then(response=>{
            retrievalInfo.value.retrieval_model=response.retrieval_model_dict
        })
    }
}

getKnowledgeBaseInfoMethod()

const back = () =>{
    if(knowledgeId){
        router.push("/knowledgeBase/documentManagement?id=" + knowledgeId);
    }else{
        router.push("/knowledgeBase");
    }
}

historicalQueryMethod()
</script>
<style scoped lang="scss">
    .common-container {
        display: flex;
        flex-direction: column;
        background-color: #FFFFFF;
        width: 100%;
        padding: 0;
        background-size: cover;
        background-color: #F7F7FA;
        height: calc(100vh - 50px);
    }

    .single-page-header{
        height: 80px;
        display: flex;
        background-color: white;
        margin: 0 20px;
        justify-content: space-between;
        align-items: center;
        flex-direction: row;
        border-radius: 8px;
        .left-box{
            margin-left:24px;
            display: flex;
            align-items: center;
            .text-box{
                display: flex;
                flex-direction: column;
                margin-left: 10px;
                .title{
                    display: flex;
                    align-items: center;
                    height: 25px;
                    font-size: 18px;
                    font-weight: 500;
                    color: #032220;
                }
                .detail{
                    height: 17px;
                    font-size: 12px;
                    color: #999999;
                    font-weight: 400;
                    margin-top: 4px;
                }
            }
            img {
                height: 40px;
                width: 40px;
                border-radius: 4px;
            }
        }
        .right-box{
            margin-right: 20px;
        }
    }

    .whole{
        height: calc(100vh - 150px);
        //background-color: white;
        // padding: 20px;
        margin: 10px 20px 0 20px;
        border-radius: 8px;
    }
    .min-title{
        font-size:18px;
        font-weight: 550;
    }

    ::v-deep .el-dialog {
        display: flex;
        flex-direction: column;
        margin: 0 !important;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        max-height: calc(100% - 30px);
        max-width: calc(100% - 30px);
        border-radius: 5px;
    }

    ::v-deep(.el-dialog__body) {
        padding: 0;
    }

    ::v-deep(.el-card__body){
        padding:5px !important
    }
    ::v-deep(.el-progress__text){
        min-width:0 !important
    }
    ::v-deep(.el-card__footer){
        padding:5px 0px !important
    }
    ::v-deep .el-dialog__header{
        padding-bottom: 0 !important;
        padding:0 !important
    }

    .slider-demo{
        background-color:white;
        position:relative;
        padding:10px;
        margin-bottom:25px;
        cursor: pointer;
        &:hover{
            background-color:#F7F7FA;
            .hidden-area{
                display:block;
            }
        }
        .hidden-area{
            display:none;
            bottom:10px;
            right:10px;
            position:absolute;
            background-color:white
        }
        .content{
            margin-top:10px;
            margin-bottom:10px;
            font-size: 14px;
            height:100px;
            text-overflow: ellipsis;
            overflow: hidden;
            width: 100%;
        }
        .footer{
            line-height: 20px;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
            font-size: 14px;
            width: 100%;
            margin-bottom: -5px;
        }
    }

    .slider-demo-block {
        max-width: 600px;
        display: flex;
        align-items: center;
    }

    ::v-deep .el-drawer.rtl {
        height: 98%;
        top: 10px;
        bottom: 10px;
        right: 10px;
        border-radius:10px;
    }

    .retrieve-settings-drawer{
        .retrieve-settings{
            padding:20px;
            max-height: calc(100vh - 150px);
            overflow:auto;
            .radio{
                line-height: 20px;
                // width:45%;
                border:1px solid #dcdfe6;
                border-radius:10px;
                .title-image-box{
                    text-align:center;
                    padding-top:10px;
                    padding-left:30px;
                    img{
                        padding:5px;
                        height: 35px;
                        width: 35px;
                        border-radius: 4px;
                    }
                }
                .title{
                    margin-top:5px;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                }
                .content{
                    font-size: 13px;
                    color:#999999;
                    padding-right: 5px;
                    min-height:30px;
                    padding-bottom: 10px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                }
                .launch-content{
                    min-height: 100px;
                    border-top:1px solid #dcdfe6;
                    padding: 20px 40px;
                    .icon{
                        color: #999999;
                    }
                    .icon-group{
                        height:40px;
                        background-color:#ffd65236;
                        border-radius: 10px; 
                        padding: 5px;
                        margin-top:5px;
                        img{
                            padding:5px;
                            height: 30px;
                            width: 30px;
                            border-radius: 4px;
                        }
                    }
                    .slider-demo-block {
                        max-width: 600px;
                        display: flex;
                        align-items: center;
                        .el-slider {
                            margin-top: 0;
                            margin-left: 12px;
                        }
                        .demonstration {
                            width:80px;
                            font-size: 16px;
                            line-height: 44px;
                            color:#706f6f;
                            font-weight: 540;
                        }
                    }
                    .image-box{
                        text-align:center;
                        padding-top:10px;
                        img{
                            height: 25px;
                            width: 25px;
                            border-radius: 4px;
                        }
                    }
                    .option-title{
                        cursor: pointer; 
                        line-height: 32px;
                        // width:125px;
                        background-color:#f3f3f3;
                        border-radius: 8px;
                        padding:0 5px;
                    }
                }
            }
            .font{
                font-size: 1vw; /* 这里的5vw表示字体大小为视口宽度的5% */
                white-space: nowrap; /* 防止文字换行 */
                overflow: hidden; /* 超出容器部分隐藏 */
                text-overflow: ellipsis; /* 超过部分显示省略号 */
            }
            .radio-sel{
                border: 1px solid #5050E6;
            }
        }

        .footer{
            display:flex;
            justify-content:right;
            padding:20px;
        }
    }
    

    .no-recent-queries{
        margin-top:20px;
        height:140px;
        background-color:#F7F7FA;
        border-radius:10px;
        padding:20px
    }

    .view-vector-charts{
        .view-vector-charts-left{
            padding: 20px;
            background-color: #dad6d612;
            .header {
                display: flex;
                flex-direction: row;
                gap: 16px;
                .title{
                    font-weight: 500;
                    padding: 2px 0 2px 7px;
                    border: 1px solid #ece7e7;
                    width: 60px;
                    border-radius: 8px;
                    color:#9e9e9e;
                    font-size: 14px;
                    font-style: italic;
                }
                .prompt{
                    font-size: 12px;
                    color: #999999;
                    font-weight: 400;
                    display:flex;
                    align-items: center;
                }
            }
            .content{
                margin-top: 20px;
                font-size: 15px;
                color: #000000c9;
                white-space: pre-wrap;
                height: calc( 100vh - 450px);
                overflow-y:scroll;
                // background-color: white;
            }
            .footer{
                margin-top: 20px;
                .tag-list{
                    max-height: 100px;
                    overflow:auto;
                }
            }
        }
        .view-vector-charts-right{
            padding: 20px;
            .header {
                color: #999999;
                position: relative;
                .content{
                    margin-top: 5px;
                    overflow: hidden; 
                    white-space: nowrap;
                    text-overflow:ellipsis;
                    width: 95%;
                }
            }
            .close{
                display: block;
                position:absolute;
                bottom:15px;
                right:-5px;
                font-size: 16px;
                color: #999999;
            }
        }
    }
    
    ::v-deep .pagination-container{
        background:#FDFDFD
    }
</style>
<style lang="scss">
    .popover-my{
        .popover-option{
            cursor: pointer; 
            line-height:20px;
            font-size:12px;
            color: #032220;
            border-radius: var(--radius-sm, 4px);
        }
        .tab_click{
            background: #f3f3f3; 
        }
        .image-box{
            text-align:center;
            padding-top:6px;
            img{
                height: 25px;
                width: 25px;
                border-radius: 4px;
            }
        }
        .option-height{
            height: 330px;
            overflow: auto;
        }
    }

    .tooltip-pop.el-popper{
        max-width: 200px !important;
    }
</style>
<style>
.knowledge-recall-table-column-tooltip-popper {
    max-width: 300px;
}
</style>
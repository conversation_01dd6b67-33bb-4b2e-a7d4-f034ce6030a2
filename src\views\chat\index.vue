<template>
    <div class="whole">
        <app-market
            ref="appMarketRef"
            @apply="(app) => { console.log(app);}"
            @use="appMarketUse"
        />
        <side-menu
            @new-chat="openNewSessionClear"
            @open-app-market="openAppMarket"
        />
        <div class="main-body" v-show="!showShop">
            <div class="history" :style="{'width':showDiv?'20%':'2%'}">
                <div v-if="showDiv" class="show-history">
                    <conversation-list ref="conversation"
                        @after-delete="(list) => {console.log('after-delete'); console.log(list)}"
                        @select="conversationSelect" @new-chat="openNewSessionClear"
                    />
                    <div class="suspended-button">
                        <div class="box">
                            <el-button link text :icon="ArrowLeft" @click="showDiv=!showDiv"></el-button>
                        </div>
                    </div>
                </div>
                <div class="hide-history" v-if="!showDiv">
                    <div class="box">
                        <el-button link text :icon="ArrowRight" @click="showDiv=!showDiv"></el-button>
                    </div>
                </div>
            </div>
            <div class="chat" :style="{'width':!showDiv ?'98%':'100%'}">
                <!--智能体和会话型团队-->
                <div v-if="!isTaskOrientedTeam">
                    <div class="top">
                        AgentFactory
                    </div>              
                    <div style="position: relative;">
                        <div>
                            <yi-mai-chat :chat-url="currentInstruct && currentInstruct.applicationType == 'agent'?'ws://180.103.126.11:5082/conversation/ws':''"
                            ref="messageCtrl" :current-instruct="currentInstruct"
                            :preface-state="prefaceState" @update-preface-state="updatePrefaceState" :is-title="true" :is-key-list="true" 
                            @conversation-unselect="conversationUnselect" @conversation-refresh="conversationRefresh" 
                            :preface-height="prefaceHeight">
                            </yi-mai-chat>
                        </div>
                        <div class="preface" ref="prefaceRef" v-if="prefaceState">
                            <div class="preface-div">
                                <div class="preface-left">
                                    <img :src="sessionmodel"/>
                                </div>
                                <div class="preface-right">
                                    <div class="preface-font">
                                        <div>
                                            作为AgentFactory，我们不仅仅是一个工具，更是您企业数字化转型的得力助手。
                                        </div>
                                        <div>
                                            我们专注于为您打造专业、高效的数字员工——Agent，它们能够全面助力您的产品设计、售后服务以及BOM（Bill of Materials，物料清单）生成等关键业务环节。
                                        </div>
                                        <div style="margin-top: 20px;display: flex;justify-content: space-between;">
                                            <span>你可以试着问我：</span>
                                            <el-button :icon="Plus" size="small" link type="primary" @click="openAppMarket">更多<el-icon><DArrowRight /></el-icon></el-button>
                                        </div>
                                    </div>
                                    <el-row v-if="ownList">
                                        <el-col v-for="(item,index) in ownList.slice(0,4)" style="padding: 5px 5px;" :span="12">
                                            <el-tooltip placement="top-start" effect="light" v-if="item.teamAgentList && item.teamAgentList.length>0">
                                                <template #content> 
                                                    <div class="team-agent-list-box">
                                                        <div class="title">团队成员</div>
                                                        <div class="agents-items">
                                                            <template v-for="teamAgent in item.teamAgentList">
                                                                <div class="agents-item" :class="{'active':false}">
                                                                    <img :src="baseUrl+teamAgent.agentIconUrl" />
                                                                    <div class="agent-name">{{teamAgent.agentName}}</div>
                                                                    <div class="agent-desc">{{teamAgent.goal}}</div>
                                                                </div>
                                                            </template>
                                                        </div>
                                                    </div>
                                                </template>
                                                <div class="preface-tool" @click="clickInstructItem(item)">
                                                    <div style="display: flex;justify-content: space-between;align-items: center;">
                                                        <div class="preface-title">
                                                            <img :src="item.applicationIcon?baseUrl+item.applicationIcon:
                                                            item.applicationType=='agent'?AgentDefaultIcon:TeamDefaultIcon"/>
                                                            <span style="margin-left: 10px;font-weight: 600;color: #291E59;">{{ item.applicationName }}</span>
                                                        </div>
                                                        <div>
                                                            <el-tooltip content="工作流" effect="light" placement="top" popper-class="tooltip-pop" v-if="item.mode=='workflow'">
                                                                <img :src="chatTask" style="width:20px;height:20px;" />
                                                            </el-tooltip>
                                                            <el-tooltip content="聊天助手" effect="light" placement="top" popper-class="tooltip-pop" v-else-if="item.mode=='advanced-chat'">
                                                                <img :src="advancedChat" style="width:20px;height:20px;" />
                                                            </el-tooltip>
                                                        </div>
                                                    </div>
                                                    <div class="preface-content">
                                                        {{ item.applicationDesc }}
                                                    </div>
                                                </div>
                                            </el-tooltip>
                                            <div class="preface-tool" @click="clickInstructItem(item)" v-if="!item.teamAgentList || (item.teamAgentList && item.teamAgentList.length<=0)">
                                                <div style="display: flex;justify-content: space-between;align-items: center;">
                                                    <div class="preface-title">
                                                        <img :src="item.applicationIcon?baseUrl+item.applicationIcon:
                                                            item.applicationType=='agent'?AgentDefaultIcon:TeamDefaultIcon"/>
                                                        <span style="margin-left: 10px;font-weight: 600;color: #291E59;">{{ item.applicationName }}</span>
                                                    </div>
                                                    <div>
                                                        <el-tooltip content="工作流" effect="light" placement="top" popper-class="tooltip-pop" v-if="item.mode=='workflow'">
                                                            <img :src="chatTask" style="width:20px;height:20px;" />
                                                        </el-tooltip>
                                                        <el-tooltip content="聊天助手" effect="light" placement="top" popper-class="tooltip-pop" v-else-if="item.mode=='advanced-chat'">
                                                            <img :src="advancedChat" style="width:20px;height:20px;" />
                                                        </el-tooltip>
                                                        <el-tooltip content="聊天助手" effect="light" placement="top" popper-class="tooltip-pop" v-else-if="item.applicationType=='agent' && item.interType==1">
                                                            <img :src="advancedChat" style="width:20px;height:20px;" />
                                                        </el-tooltip>
                                                        <el-tooltip content="Agent" effect="light" placement="top" popper-class="tooltip-pop"  v-else>
                                                            <img :src="agent" style="width:20px;height:20px;"/>
                                                        </el-tooltip>
                                                    </div>
                                                </div>
                                                <div class="preface-content">
                                                    {{ item.applicationDesc }}
                                                </div>
                                            </div>
                                        </el-col>
                                    </el-row>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!--任务型团队会话-->
                <div v-if="isTaskOrientedTeam">
                    <task-oriented-team ref="messageCtrl" :current-instruct="currentInstruct" @conversation-refresh="conversationRefresh" ></task-oriented-team>
                </div>
            </div>
        </div>
        <div class="main-body" v-show="showShop">
            <app-shop />
        </div>
    </div>
</template>
<script setup> 
    import { nextTick, ref } from 'vue'
    import { ArrowLeft , ArrowRight , Delete ,Search ,CircleCheck ,ArrowDownBold ,Fold ,ArrowUpBold,Promotion,DArrowRight } from '@element-plus/icons-vue'
    import AppMarket from '@/components/AppMarket'
    import SideMenu from './sideMenu.vue';
    import YiMaiChat from './components/YiMaiChat/index.vue'
    import ConversationList from './components/conversationList.vue'
    import sessionmodel from "@/assets/icons/svg/session-model.svg"
    import { getOwnList,getConversationDetail,checkusestatus } from '@/api/session/session'
    import { getCurrentInstructTeam } from '@/api/YiMaiChat/sessionRecordsDify'
    import TaskOrientedTeam from './components/TaskOrientedTeam.vue'
    import AppShop from './components/appShop'
    import { ElMessage } from 'element-plus'
    import chatTask from "@/assets/icons/svg/chat-task.svg"
    import advancedChat from '@/assets/images/chat_bot.png'
    import agent from "@/assets/icons/svg/agent.svg"
    import AgentDefaultIcon from '@/assets/icons/svg/agent-default.svg'
    import TeamDefaultIcon from '@/assets/icons/svg/team-default.svg'

    //baseUrl
    const baseUrl = import.meta.env.VITE_APP_BASE_API;

    const prefaceState = ref(true)
    const showDiv = ref(true)

    const isTaskOrientedTeam = ref(false)

    //应用市场
    const appMarketRef = ref(null)
    const showShop = ref(false)    
    //打开应用市场
    const openAppMarket = () => {
        // appMarketRef.value.open();
        showShop.value = true;
    }

    //选中的智能体列表
    const teamAgentSelectedList = ref([])
    //获取智能体列表  智能团队编排
    const handleTeamAgentList = (arrangeJson) =>{
        if(arrangeJson){
            let arrangeJsonNodes = JSON.parse(arrangeJson).nodes
            //处理智能体
            teamAgentSelectedList.value=[]
            arrangeJsonNodes.forEach(element => {
                if(element.data.type=='agent'){
                    let teamAgentSelected=element.data.data
                    teamAgentSelectedList.value.push(teamAgentSelected)
                }
            });
        }
    }

    const ownList = ref([])
    const prefaceRef = ref(null)
    const prefaceHeight = ref(0)
    const getOwnListMethod = () => {
        let query = {
            isIndex:1
        }
        getOwnList(query).then(response=>{
            ownList.value=response.rows
            ownList.value.forEach(item=>{
                if(item.applicationType=='team'){
                    handleTeamAgentList(item.arrangeJson)
                    item.teamAgentList=teamAgentSelectedList.value
                }
            })
            nextTick(() => {
                prefaceHeight.value=prefaceRef.value.offsetHeight;
                console.log(prefaceHeight.value)
            })
        })
    }
    getOwnListMethod()

    //修改prefaceState
    const updatePrefaceState = (newValue) => {
        prefaceState.value = newValue
    }

    const currentInstruct = ref(null)
    //点击智能体或团队指令触发事件
    async function clickInstructItem(instruct){
        const query = {applicationId:instruct.applicationId,applicationType:instruct.applicationType}
        await checkusestatus(query).then(response=>{
            openNewSessionClear(false)
            nextTick(() => {
                if(instruct.applicationType == 'agent'){   
                    currentInstruct.value = instruct 
                }else{
                    if(instruct.mode){
                        getCurrentInstructTeamMethod(instruct.appId,instruct.mode,instruct,null)
                    }
                }
            })
        }).catch(err=>{
            return
        })
    }

    //获取团队详情
    async function getCurrentInstructTeamMethod(appId,mode,instruct,conversation){
        await getCurrentInstructTeam(appId).then(response => {
            let currentInstructStaging = {}
            currentInstructStaging = {...instruct,...response}
            currentInstructStaging.appId = appId
            //会话型
            if(mode == 'advanced-chat'){
                currentInstructStaging.chatUrl = 'http://180.103.126.11:5001/console/api/apps/'+appId+'/team_chat_messages'
                currentInstructStaging.isTaskOrientedTeam = false
                isTaskOrientedTeam.value = false
            }//任务型
            else if(mode == 'workflow'){
                currentInstructStaging.chatUrl = 'http://180.103.126.11:5001/console/api/apps/'+appId+'/workflows/run'
                currentInstructStaging.isTaskOrientedTeam = true
                isTaskOrientedTeam.value = true
            }
            if(conversation){
                currentInstructStaging.conversation = conversation
            }
            currentInstruct.value = currentInstructStaging
        })
    }

    //历史记录组件
    const conversation = ref(null)
    //历史记录取消选中
    const conversationUnselect = () => {
        conversation.value.unselect()
    }

    //历史记录刷新
    const conversationRefresh = () => {
        conversation.value.refresh(true)
    }

    //会话组件
    const messageCtrl = ref(null)
    //应用使用
    const appMarketUse = (app) => {
        clickInstructItem(app)
    }

    const openNewSessionClear  = (isUpdate = true) => {
        isTaskOrientedTeam.value = false
        showShop.value = false
        //选中的团队或智能体清空
        currentInstruct.value = null 
        if(isUpdate){
            getOwnListMethod()
        }
        nextTick(() => {
            messageCtrl.value.openNewSessionClear()
        })
        
    }

    //选中历史会话
    const conversationSelect = (conversation) => {
        let query = {
            applicationId:conversation.app_id,
            applicationType:conversation.type=='agent'?conversation.type:'team'
        }
        getOwnList(query).then(response=>{
            if(!response.rows || response.rows.length==0){
                ElMessage.warning("您暂无使用应用的权限，请在【应用商店】进行申请使用")
                return
            }
            messageCtrl.value.openHistorySessionClear()
            // currentInstruct.value = null
            nextTick(() => {
                response.rows[0].isHistory = true
                if(conversation.type == 'advanced-chat' || conversation.type == 'workflow'){
                    getCurrentInstructTeamMethod(response.rows[0].appId,conversation.type,response.rows[0],conversation)
                }else{
                    isTaskOrientedTeam.value = false
                    nextTick(()=>{
                        let currentInstructStaging = response.rows[0]
                        if(conversation){
                            currentInstructStaging.conversation = conversation
                        }
                        currentInstruct.value = currentInstructStaging
                    })
                }
            })
        })
    }

    provide('useApp', appMarketUse);

</script>
<style scoped lang="scss">
.whole{
    display: flex;
    justify-content: space-between;
    height: calc( 100vh );
    background-color: #F7F7FA;
    .main-body{
        width: 100%;
        display: flex;
        justify-content: space-between;
        background-color: #F7F7FA;
        .history{
            margin-right: 10px;
            .show-history{
                display: flex;
                justify-content: space-between;
                flex-shrink: 0;
                flex-grow: 0;
                .suspended-button{
                    width: 10%;
                    display: flex;
                    align-items: center;
                    padding: 0 5px;
                    .box{
                        display: flex;
                        align-items: center;
                        height: 30px;
                        background-color: #EEECF6;
                        border-radius: 5px;
                        ::v-deep .el-button.is-link {
                            padding: 0;
                            color: #909090;
                        }
                    }
                }
            }
            .hide-history{
                width: 2%;
                display: flex;
                align-items: center;
                padding: 0 5px;
                height: calc( 100vh );
                .box{
                    display: flex;
                    align-items: center;
                    height: 30px;
                    background-color: #EEECF6;
                    border-radius: 5px;
                    ::v-deep .el-button.is-link {
                        padding: 0;
                        color: #909090;
                    }
                }
            }
        }
        .chat{
            width: calc(100% - 70px);
            height: calc( 100vh );
            .top{
                height: 80px;
                line-height: 80px;
                font-size: 30px;
                color: #5050E6;
                font-weight: 550;
                text-align: center
            }
            .preface{
                position: absolute;
                top: 0;
                width: 100%;
                padding: 0 20% 0 16.1%;
                .preface-div{
                    display: flex;
                    justify-content: space-between;
                    .preface-left{
                        width: 6%;
                        img{
                            width:25px;
                            height:25px;
                            background-color: #6977F1;
                            border-radius: 5px;
                        }
                    }
                    .preface-right{
                        width: 94%;
                        background-color: white;
                        border-radius: 5px;
                        padding: 20px;
                        .preface-font{
                            line-height: 30px;
                            font-size: 14px;
                            text-align: left;
                            color: #747796;
                        }
                        .preface-tool{
                            background-color: #F7F8FD;
                            padding: 20px 10px;
                            border-radius: 5px;
                            cursor: pointer; 
                            .preface-title{
                                display: flex;
                                align-items: center;
                                img{
                                    width:20px;
                                    height:20px;
                                    border-radius: 5px;
                                }
                            }
                            .preface-content{
                                overflow: hidden; 
                                white-space: nowrap;
                                text-overflow:ellipsis;
                                width: 100%;
                                line-height: 30px;
                                font-size: 14px;
                                text-align: left;
                                color: #878AA5;
                            }
                        }
                    }
                }
            }
        }
    }
}
.team-agent-list-box{
    width: 100%;
    display: flex;
    flex-direction: column;
    padding: 10px 10px;
    .title{
        color: #032220;
        font-size: 18px;
        font-weight: 500;
    }
    .agents-items{
        display: flex;
        flex-direction: row;
        margin-top: 15px;
        margin-bottom: 10px;
        .agents-item{
            width: 120px;
            height: 180px;
            margin-bottom: 20px;
            background-color: #f5f5ff;
            border-radius: 10px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
            padding: 10px 20px;
            margin: 0 10px;
            img{
                width: 50px;
                height: 70px;
                // margin-bottom: 10px;
                border-radius: 10px;
                background-color: #dfdfff;
                border: none;
                overflow:hidden; 
            }
            img[src=""],img:not([src]){
                opacity: 0;
                border:none;
                visibility: hidden;
                max-width: none;
            }
            .agent-name{
                height: 50px;
                color: #032220;
                font-size: 16px;
                font-weight: 500;
                line-height: 60px;
                white-space: nowrap;
            }
            .agent-desc{
                height: 80px;
                max-height: 80px;
                overflow: hidden;
                color: #999999;
                font-size: 12px;
                font-weight: 400;
            }
        }
        .active{
            // background-color: #94bff1;
            border: 2px solid #5050E6;
        }
    }
}
::v-deep .el-dialog:not(.is-fullscreen) {
    margin-top: 0vh !important;
}
</style>
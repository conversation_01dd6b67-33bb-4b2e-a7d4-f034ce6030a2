<template>
    <codemirror 
        class="custom-font"
        v-model="code" 
        placeholder="Code gose here..." 
        :style="{ height: props.height,fontFamily: 'Arial, monospace' }" 
        :autofocus="true"
        :tabSize="2" 
        :extensions="extensions" />
</template>
<script setup>
import { Codemirror } from "vue-codemirror";
import { javascript } from "@codemirror/lang-javascript";
import { oneDark } from "@codemirror/theme-one-dark";
import { ref,watch } from "vue";
// 接受的参数
const props = defineProps({
    value:{
        type:String,
        required:true
    },
    height:{
        type: String,
        required: false,
        default:'400px'
    }
})
const code = ref(``);
code.value = JSON.parse(JSON.stringify(props.value))
const extensions = [javascript()];
const emits = defineEmits(['update:bindVal'])
watch(code,(newValue)=>{
    emits('update:bindVal',newValue);
})
</script>
<style scoped>
.custom-font :deep(.cm-scroller){
    font-family: 'Courier New', Courier, monospace !important;
}
</style>
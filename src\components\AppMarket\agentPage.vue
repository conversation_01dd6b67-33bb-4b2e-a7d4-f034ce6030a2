<template>
  <div v-loading="loading" class="tree-wrapper">
    <vue3-tree-org
      center
      :data="treeList.data"
      :toolBar="treeList.toolBar"
      :horizontal="treeList.horizontal"
      :collapsable="treeList.collapsable"
      :label-style="treeList.style"
      :node-draggable="false"
      :scalable="false"
      :only-one-node="treeList.onlyOneNode"
      :default-expand-level="1"
      :clone-node-drag="treeList.cloneNodeDrag"
    >
      <template v-slot="{ node }">
        <div
          class="tree-org-node__text node-label app-card"
          v-if="node.$$data.isAgent && node.$$data.isAgent == 0"
        >
          <div>{{ node.label }}{{ node.$$data.isAgent }}</div>
        </div>
        <agent-card
          v-if="node.$$data.isAgent && node.$$data.isAgent === 1"
          :app="node.$$data"
          :img="node.$$data.url ? baseUrl + node.$$data.url : AgentDefaultIcon"
          :title="node.label"
          detail=""
          :has-permission="node.approveStatus"
          :is-pinned="props.allocatedAgentMap[node.$$data.id] && props.allocatedAgentMap[node.$$data.id].isIndex === 1"
          :class="{match: node.$$data.match}"
        />
      </template>
      <template v-slot:expand="{ node }">
        <div>{{ node.children.length }}</div>
      </template>
    </vue3-tree-org>
  </div>
</template>

<script setup>
import { ref, inject, onMounted, watch } from "vue";
import { getAiagentTreeList } from "@/api/agent/body";
import { Star, StarFilled } from "@element-plus/icons-vue";
import AgentCard from "./agentCard.vue";
import AgentDefaultIcon from '@/assets/icons/svg/agent-default.svg'

const props = defineProps({
  /**
   * 搜索框关键词
   */
  keyword: {
    type: String,
    required: true
  },

  // 已要配权限的智能体map, key是智能体id, value是智能体数据
  allocatedAgentMap: {
    type: Object,
    required: true
  }
});

// 查询条件
const query = {
  pageSize: 100,
  pageNum: 1,
  listType: 1,
};

// 智能体树形数据
const treeList = ref({
  data: {},
  props: {
    id: "id",
    pid: "pid",
    label: "label",
    expand: "expand",
    children: "children",
    isAgent: "isAgent",
  },
  toolBar: {
    scale: false,
    restore: false,
    expand: false,
    zoom: false,
    fullscreen: false,
  },
  horizontal: false,
  collapsable: false,
  onlyOneNode: false,
  cloneNodeDrag: true,
  expandAll: true,
  noDragging: false,
  style: {
    background: "#fff",
    color: "#5e6d82",
  },
});

// 智能体列表
const agentList = ref([]);

// 加载状态
const loading = ref(false);

// 总数
const total = ref(0);

// 基础地址
const baseUrl = import.meta.env.VITE_APP_BASE_API;

const use = inject('use');
const apply = inject('apply');

/**
 * 遍历树
 */
function traverseTree(tree, funcHandleNode) {
  const stack = [tree];
  while(true) {
    const node = stack.pop();
    if (!node || node.isAgent === undefined) {
      break;
    }

    funcHandleNode(node);
    if (node.children) {
      node.children.forEach((child) => {
        stack.push(child);
      });
    }
  }
}

/**
 * 根据关键词在树中查找, 将命中的结点中的match属性设为true, 未命中的设为false
 */
function search() {
  traverseTree(treeList.value.data, (node) => {
    const match = (props.keyword && node.isAgent === 1 && node.label && node.label.toLocaleLowerCase().includes(props.keyword.toLocaleLowerCase()));
    node.match = match;
  });
}

/**
 * 初始休树，加上match属性
 */
function init(tree) {
  traverseTree(tree, (node) => {
    node.match = false;
  })
}

watch(() => {
  return props.keyword
}, () => {
  search();
}, {immediate: true});


/**
 * 获取树形数据
 */
function getTreeList() {
  getAiagentTreeList().then((resp) => {
    const tree = resp.data[0];
    init(tree);
    treeList.value.data = tree;
  });
}

onMounted(() => {
  getTreeList();
});

defineExpose({
  getTreeList
})
</script>

<style scoped>
.tree-wrapper {
    width: 100%;
    height: 600px;
    overflow: auto;
}
.match {
  outline: 1px solid red;
}
</style>

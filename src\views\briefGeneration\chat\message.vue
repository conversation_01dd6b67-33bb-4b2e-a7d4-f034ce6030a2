<template>
  <div :class="['message-container', type]">
    <component
      v-if="type === 'answer' && deepThinkComp"
      :is="deepThinkComp"
      :observation="observation"
    />
    <div ref="message" :class="['message', 'long-link', widthClass]">
      <md :cnt="content" :show-loading="type === 'answering'" :enableHtml="true"/>
      <slot name="tail"></slot>
    </div>
    <message-btn-list
      @regenerate="$emit('regenerate')"
      @insert="$emit('insert')"
      @like="$emit('like')"
      @dislike="$emit('dislike')"
      @cancel-like="$emit('cancel-like')"
      @cancel-dislike="$emit('cancel-dislike')"
      :msg="{ id, type, content }"
      :fnBtnList="fnBtnList"
      v-if="enableButtons && !isTyping"
    />
    <links :items="links" v-if="links" />
    <files :fileObj="fileObj" v-if="fileObj.name" />
    <div ref="scrollMark" class="scroll-mark" />
  </div>
</template>

<script>
import MessageBtnList from './messageBtnList.vue'
import Links from './links.vue'
import Files from './files.vue'
import Md from '@/components/Markdown'

/**
 * 显示一行消息，有以下几种类型:
 * 1. 提问。
 * 2. 回答。
 * 3. 回答中。
 * 4. 回答请求错误。
 */
export default {
  components: { MessageBtnList, Links, Files, Md },
  props: {
    /**
     * 消息id
     */
    id: {
      type: [String, Number],
      required: true
    },

    /**
     * 消息内容, markdown语法
     */
    content: {
      type: String,
      required: true
    },

    // 附加卡片 
    observation: {
      type: Object,
      required: false,
    },

    /**
     * 类型
     * 枚举值: prologue, question, answer, answering, answer-error
     *  prologue:      显示在左侧的开场白
     *  question:      显示在右侧的提问
     *  answer:        显示在左侧的回案
     *  answering:     显示在左侧的回复复，且是处理正在回复中的状态
     *  answer-error:  显示在左侧，错误提示
     */
    type: {
      type: String,
      required: true
    },

    /**
     * 链接
     */
    links: {
      type: Array,
      required: false,
      default: function () {
        return []
      }
    },

    /**
     * 文件
     */
    fileObj: {
      type: Object,
      required: false,
      default: function () {
        return {}
      }
    },

    /**
     * 变量替换回调
     */
    fnVariableCallback: {
      type: Function,
      required: false,
      default: function () {
        return function (term) {
          return term
        }
      }
    },

    /**
     * 是否开启打字效果
     */
    enableTypingEffect: {
      type: Boolean,
      required: false,
      default: true
    },

    /**
     * 是否展示按钮
     */
    enableButtons: {
      type: Boolean,
      required: false,
      default: true
    },

    // 获取按钮列表的函数
    fnBtnList: {
      type: Function,
      required: false
    },

    /**
     * 深度思考组件
     */
    deepThinkComp: {
      type: Object,
      required: false
    },

    /**
     * 是否自动滚动
     */
    enableAutoScroll: {
      type: Boolean,
      required: false,
      default: false
    }
  },
  data () {
    return {
      /**
       * 是否展示打字效果
       */
      showTypingEffect: this.enableTypingEffect && this.type === 'answering',

      /**
       * 当前打字位置索引
       */
      typingIdx: 0,

      /**
       * 是否正在打字中
       */
      isTyping: false,

      /**
       * 消息宽度相关的css的class，仅在type不是answering的时候有效
       */
      widthClass: 'unknown',
    }
  },
  computed: {
    /**
     * 将消息内容渲染成html
     */
    content() {
      let content = this.showTypingEffect
        ? this.content.slice(0, this.typingIdx + 1)
        : this.content
      content = content.replace(/「※.*?※」/g, (match) => {
        const term = match.slice(2, -2).trim()
        return `「※${btoa(term)}※」`
      })
      content = this.handleUncompletedImg(content)
      return content
    }
  },
  watch: {
    type(newVal, oldVal) {
      if (newVal === 'answering' && oldVal === 'answer') {
        this.typingIdx = 0
        if (this.enableTypingEffect) {
          this.simulateTyping()
        }
      }
    }
  },
  mounted () {
    if (this.type === 'answering' && this.enableTypingEffect) {
      this.simulateTyping()
    }
    if (this.type !== 'answering') {
      const messageWidth = this.$refs.message.clientWidth
      this.widthClass = 'w' + Math.floor(messageWidth / 100)
    }
  },
  methods: {
    /**
     * 自动滚动
     */
    autoScroll() {
      if (!this.enableAutoScroll) {
        return
      }

      setTimeout(() => {
        if (this.$refs.scrollMark) {
          this.$refs.scrollMark.scrollIntoView(false)
        }
      }, 0)
    },
    /**
     * 模拟打印效果
     */
    simulateTyping () {
      // const left = this.content.length - 1 - this.typingIdx;
      // const delay = left > 20 ? 3000 / left : 50;
      const delay = 50
      this.isTyping = true

      setTimeout(() => {
        if (this.type !== 'answering') {
          this.typingIdx = this.content.length - 1
          this.isTyping = false
          this.$emit('finish-typing')
          this.autoScroll()
          return
        }

        if (this.typingIdx <= this.content.length - 1) {
          this.typingIdx += 1
        }

        // 遇到链接,代码块,英文单词时一次性显示出来
        const flyPattern = /^(https?:\/\/[^ ]+)|(\!?\[[^\]]*\]\(https?:\/\/[^\)]+\))|([a-zA-Z0-9_]+)|(```[\s\S]*?```)/
        const match = this.content.slice(this.typingIdx).match(flyPattern)
        if (match) {
          this.typingIdx += (match[0].length + 1)
        }

        this.$emit('typing')
        this.autoScroll()

        this.simulateTyping()
      }, delay)
    },

    /**
     * 处理末尾图片
     * 由于是流式响应，有时content的末尾可能不完整，如"![图片](http://ddd" 这样的不完整的图片markdown
     */
    handleUncompletedImg (content) {
      const pattern = /!\[.*?\]\(https?:\/\/[^\)]*$/
      return content.replace(pattern, '![markdown-unfinished](/markdown-unfinished.png)')
    }
  }
};
</script>

<style scoped>
.message-container {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  margin-bottom: 30px;
}
.message-container.prologue.faq .message {
  width: 100%;
}

.avatar {
  width: 42px;
  height: 42px;
  flex-shrink: 0;
  flex-grow: 0;
  position: relative;
  top: 5px;
}
.avatar img {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  overflow: hidden;
}
.message {
  max-width: 100%;
  min-width: 80px;
  min-height: 46px;
  width: auto;
  height: auto;
  padding: 4px 16px;
  line-height: 2em;
  position: relative;
  font-size: 14px;
  color: #fff;
}
.message::v-deep p {
  margin-block-start: 0.5em;
  margin-block-end: 0.5em;
}
.message::v-deep .btn-list .btn.always-visible {
  visibility: visible;
}
.message::v-deep .btn-list .btn.hover-visible {
  visibility: hidden;
}

.message:hover::v-deep .btn-list .btn.hover-visible {
  visibility: visible;
}
.message.w0::v-deep .feedback-container.no-rate.no-tags,
.message.w1::v-deep .feedback-container.no-rate.no-tags {
  left: 0;
  right: unset;
}
.message:hover::v-deep .feedback-container.no-rate.no-tags {
  display: flex;
}

.message-container.answer .message {
  padding: 4px;
}

.message-container.question {
  align-items: flex-end;
}

.message-container.prologue .message {
  background: #1D5974;
  border-radius: 8px;
}

.message-container.question .message {
  background: #173567;
  border-radius: 8px;
}

.message-container.answer-error .message {
  color: red;
}

.message::v-deep .loader {
  display: inline-block;
  width: 6px;
  aspect-ratio: 1;
  border-radius: 50%;
  animation: l5 1s infinite linear alternate;
  position: relative;
  left: 10px;
  margin-right: 20px;
}
@keyframes l5 {
  0% {
    box-shadow: 12px 0 #fff, -12px 0 #666666;
    background: #fff;
  }
  33% {
    box-shadow: 12px 0 #fff, -12px 0 #666666;
    background: #666666;
  }
  66% {
    box-shadow: 12px 0 #666666, -12px 0 #fff;
    background: #666666;
  }
  100% {
    box-shadow: 12px 0 #666666, -12px 0 #fff;
    background: #fff;
  }
}
.message::v-deep a {
  color: CornflowerBlue;
  padding: 0 0.3em;
}
.message::v-deep a.goto-app {
  background-color: #ecf5ff;
  border-radius: 3px;
  padding: 3px 10px;
  color: #409eff;
}
.message::v-deep a.goto-app:hover {
  text-decoration-line: underline;
}
.citation-list-container {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: stretch;
  margin-bottom: 10px;
}
.citation-list-container > .cita-title-wrapper {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
}

.citation-list-container > .cita-title-wrapper > .cita-title {
  padding-right: 1em;
  color: dimgray;
}

.citation-list-container > .cita-title-wrapper::after {
  display: block;
  flex-grow: 1;
  flex-shrink: 1;
  content: " ";
  height: 1px;
  border: 0;
  border-bottom: 1px solid lightgray;
}
.tip {
  position: absolute;
  bottom: -28px;
  right: 10px;
  color: gray;
  font-size: 12px;
  text-wrap: nowrap;
}
.long-link {
  overflow-wrap: anywhere;
}
.plotlyDraw-css {
  width: 100px;
  height: 100px;
  background-color: #00f;
}
.scroll-mark {
  width: 0;
  height: 0;
  padding: 0;
  margin: 0;
  background-color: transparent;
  position: relative;
  top: 20px;
}
</style>

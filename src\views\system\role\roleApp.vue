
<template>
    <div class="app-container">
       <el-form :model="queryParams" ref="queryRef" v-show="showSearch" :inline="true">
          <el-form-item label="应用名称" prop="applicationName">
             <el-input
                v-model="queryParams.applicationName"
                placeholder="请输入应用名称"
                clearable
                style="width: 240px"
                @keyup.enter="handleQuery"
             />
          </el-form-item>
          <el-form-item label="应用类型" prop="applicationType">
             <el-select
                v-model="queryParams.applicationType"
                placeholder="请选择应用类型"
                clearable
                style="width: 240px"
             >
                <el-option
                   :label="'智能体'"
                   :value="'agent'"
                />
                <el-option
                   :label="'团队'"
                   :value="'team'"
                />
             </el-select>
          </el-form-item>
          <el-form-item>
             <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
             <el-button icon="Refresh" @click="resetQuery">重置</el-button>
          </el-form-item>
       </el-form>
 
       <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
             <el-button
                type="primary"
                plain
                icon="Plus"
                @click="openSelectUser"
                v-hasPermi="['system:role:add']"
             >添加应用</el-button>
          </el-col>
          <el-col :span="1.5">
             <el-button
                type="danger"
                plain
                icon="CircleClose"
                :disabled="multiple"
                @click="cancelAuthUserAll"
                v-hasPermi="['system:role:remove']"
             >批量取消授权</el-button>
          </el-col>
          <el-col :span="1.5">
             <el-button 
                type="warning" 
                plain 
                icon="Close"
                @click="handleClose"
             >关闭</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
       </el-row>
 
       <el-table v-loading="loading" :data="appList" @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="55" align="center" />
          <el-table-column label="应用名称" prop="applicationName" :show-overflow-tooltip="true" />
             <el-table-column label="应用类型" align="center" prop="applicationType" width="180">
                <template #default="scope">
                   <span>{{ scope.row.applicationType === 'agent' ? '智能体' : '团队'}}</span>
                </template>
             </el-table-column>
          <el-table-column label="应用描述" prop="applicationDesc" :show-overflow-tooltip="true" />
          <!-- <el-table-column label="邮箱" prop="email" :show-overflow-tooltip="true" />
          <el-table-column label="手机" prop="phonenumber" :show-overflow-tooltip="true" />
          <el-table-column label="状态" align="center" prop="status">
             <template #default="scope">
                <dict-tag :options="sys_normal_disable" :value="scope.row.status" />
             </template>
          </el-table-column>
          <el-table-column label="创建时间" align="center" prop="createTime" width="180">
             <template #default="scope">
                <span>{{ parseTime(scope.row.createTime) }}</span>
             </template>
          </el-table-column> -->
          <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
             <template #default="scope">
                <el-button link type="primary" icon="CircleClose" @click="cancelAuthUser(scope.row)" v-hasPermi="['system:role:remove']">取消授权</el-button>
             </template>
          </el-table-column>
       </el-table>
 
       <pagination
          v-show="total > 0"
          :total="total"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
       />
       <select-app ref="selectRef" :roleId="queryParams.roleId" @ok="handleQuery" />
    </div>
 </template>
 
 <script setup name="RoleApp">
 import selectApp from "./selectApp";
 import { allocatedAppList, authUserCancelApp, authUserCancelAllApp } from "@/api/system/role";
 
 const route = useRoute();
 const { proxy } = getCurrentInstance();
 const { sys_normal_disable } = proxy.useDict("sys_normal_disable");
 
 const appList = ref([]);
 const loading = ref(true);
 const showSearch = ref(true);
 const multiple = ref(true);
 const total = ref(0);
 const userIds = ref([]);
 
 const queryParams = reactive({
   pageNum: 1,
   pageSize: 10,
   roleId: route.params.roleId,
   applicationName: undefined,
   applicationType: undefined,
 });
 
 /** 查询授权用户列表 */
 function getList() {
   loading.value = true;
   allocatedAppList(queryParams).then(response => {
     appList.value = response.rows;
     total.value = response.total;
     loading.value = false;
   });
 }
 // 返回按钮
 function handleClose() {
   const obj = { path: "/system/role" };
   proxy.$tab.closeOpenPage(obj);
 }
 /** 搜索按钮操作 */
 function handleQuery() {
   queryParams.pageNum = 1;
   getList();
 }
 /** 重置按钮操作 */
 function resetQuery() {
   proxy.resetForm("queryRef");
   handleQuery();
 }
 // 多选框选中数据
 function handleSelectionChange(selection) {
   userIds.value = selection.map(item => {
      item.roleId = queryParams.roleId;
      return item;
   });
   multiple.value = !selection.length;
 }
 /** 打开授权用户表弹窗 */
 function openSelectUser() {
   proxy.$refs["selectRef"].show();
 }
 /** 取消授权按钮操作 */
 function cancelAuthUser(row) {
   const params = row
   params.roleId = queryParams.roleId
   proxy.$modal.confirm('确认要取消授权该角色"' + row.applicationName + '"应用吗？').then(function () {
     return authUserCancelApp(row);
   }).then(() => {
     getList();
     proxy.$modal.msgSuccess("取消授权成功");
   }).catch(() => {});
 }
 /** 批量取消授权按钮操作 */
 function cancelAuthUserAll(row) {
   proxy.$modal.confirm("是否取消选中应用授权数据项?").then(function () {
     return authUserCancelAllApp(userIds.value);
   }).then(() => {
     getList();
     proxy.$modal.msgSuccess("取消授权成功");
   }).catch(() => {});
 }
 
 getList();
 </script>
 <style lang="scss">
 .is-dark{
     max-width:500px!important;
     color: black;
 }
 </style>
 
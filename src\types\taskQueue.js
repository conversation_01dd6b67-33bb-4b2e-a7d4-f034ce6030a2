export class TaskQueue {
    constructor() {
        this.tasks = [];
        this.running = false;
        this.index = 0;
        this.paramsList = [];
        this.onComplete = null;
        this.lastTask = false
    }

    addTask(task,param) {
        this.tasks.push(task);
        this.paramsList.push(param);
        if (!this.running) {
            this.runTasks();
        }
    }

    runTasks() {
        this.running = true
        for (let i = 0; i < 1; i++) {
            this.run();
        }
    }

    run() {
        // 构造待执行任务 当该任务完成后 如果还有待完成的任务 继续执行任务
        new Promise((resolve, reject) => {
            const task = this.tasks[this.index];
            const params = this.paramsList[this.index];
            this.index++; // 这个是同步操作
            task(params).then(() => {
                    if(this.index == this.tasks.length){
                        this.running = false
                        if(this.onComplete){
                            this.onComplete()
                        }
                    }
                    resolve()
                }
            )
        }).then(() => {
            if(this.index < this.tasks.length) {
                this.run()
            }
        })
    }

    
    setOnComplete(callBack) {
        this.onComplete = callBack;
    }

    clearTasks() {
        this.tasks = [];
        this.running = false;
        this.index = 0;
        this.paramsList = [];
        this.lastTask = false
    }
}
import { reactive, ref } from "vue";
import md from "./markdown";

export class MessageItem {
  isUser;
  content;
}

class MsgRecord {
  role;
  content;
}

export class MessageItemManagerTeam {
  refMessageItemList = reactive([]);
  nowMessageText = "";
  nowMessageHtml = ref("");
  nowMessageRole = ref({});
  nowMessageLine = ref("");
  nowMessageChatMessage = ref({})
  
  loading = ref(false);
  waitting = ref(false);
  msgRecords = ref([]);

  pushUserMessage(content,annex) {
    this.refMessageItemList.push({ isUser: true, content: content,annex:annex });
    this.msgRecords.value.push({ role: "user", content: content,annex:annex });
    // this.autosuggestionHandle()
  }

  
  /**
   * 清空消息历史，包含界面显示的和记录的
   */
  cleanHistory() {
    this.cleanRecords();
    if (this.refMessageItemList.length > 0) {
      this.refMessageItemList.splice(0, this.refMessageItemList.length);
    }
    // this.nowMessageRole.value = {}
  }

  loadHistoryMessage(messages) {
    this.cleanHistory()
    messages.forEach((item) => {
      this.refMessageItemList.push({isUser: true,content: item.query})
      this.refMessageItemList.push({isUser: false,content: item.answer,roleInfo:{}})
    })
  }

  /**
   * 消息接收结束
   *
   * @param record 是否记录本次接收的消息
   */
  messageReceiveDone(record = true) {
    if (this.nowMessageLine.value !== "") {
      this.nowMessageText += this.nowMessageLine.value;
      this.nowMessageHtml.value = this.nowMessageText;
      this.nowMessageLine.value = "";
    }
    if (record) {
      this.msgRecords.value.push({ role: "assistant", content: this.nowMessageText });
    }
    this.nowMessageText = "";
    let refMessageItem = {
      isUser: false,
      content: this.nowMessageHtml.value,
      roleInfo: this.nowMessageRole.value,
      chat_message: this.nowMessageChatMessage.value,
      retrieverResources: [],
    } 
    this.refMessageItemList.push(refMessageItem);
    this.loading.value = false;
    this.nowMessageHtml.value = "";
    this.nowMessageRole.value = {}
    this.nowMessageChatMessage.value = {}
  }

  //人工介入
  humanIntervention(obj,role){
    let refMessageItem = {
      isUser: false,
      content: obj.data.inputs.question,
      roleInfo: role,
      chat_message: this.nowMessageChatMessage.value,
      retrieverResources: [],
    } 
    if(obj.data.inputs.choice){
      refMessageItem.chat_message.choice = obj.data.inputs.choice
    }
    this.refMessageItemList.push(refMessageItem);
    this.nowMessageChatMessage.value = {}
  }
  //人工介入结果处理
  humanInputHandle(obj,role){
    let refMessageItem = {
      isUser: false,
      content: obj.data.outputs.answer,
      roleInfo: role,
      chat_message: this.nowMessageChatMessage.value,
      retrieverResources: [],
    }
    this.nowMessageHtml.value = ''
    this.refMessageItemList.push(refMessageItem);
  }

  clearNowMessageRole(){
    this.nowMessageRole.value = {}
  }

  do() {
    this.loading.value = true;
  }

  stop(){
    this.loading.value = false;
  }

  //连接断开处理
  disconnected(){
    //会话是否等待状态（否）
    this.waitting.value = false
    this.nowMessageHtml.value = "会话已中断，请重新发起新的会话";
    this.nowMessageText = "会话已中断，请重新发起新的会话";
  }

  new() {
    //会话是否等待状态（否）
    this.waitting.value = false
    this.loading.value = false
    //消息内容列表清空
    if (this.refMessageItemList.length > 0) {
      this.refMessageItemList.splice(0, this.refMessageItemList.length);
    }
    this.nowMessageHtml.value = "";
    this.nowMessageText = "";
  }

  waittingStart() {
    this.waitting.value = true;
  }

  waittingEnd() {
    this.waitting.value = false;
  }


  /**
   * 清空消息记录
   */
  cleanRecords() {
    if (this.msgRecords.length > 0) {
      this.msgRecords.value = [];
    }
  }

  deleteLastMsgRecord() {
    return this.msgRecords.value.pop();
  }

  /**
   * 向现在的消息中添加新内容
   * TODO 优化md的转换，减少转化次数
   * @param newContent 新的内容
   */
  appendNowMessageContent(newContent,role, parse = true) {
    try {
      if (parse) {
        newContent = JSON.parse(`"${newContent}"`);
      }
      this.nowMessageText = newContent;
      this.nowMessageLine.value = "";
      this.nowMessageHtml.value += this.nowMessageText;
      this.nowMessageRole.value = role;
    } catch (e) {
      console.log(e);
    }
  }

  //给最后一条返回加上引用和归属
  appendRetrieverResources(metadata){
    if(metadata.retriever_resources){
      let retrieverResourcesList = []
      metadata.retriever_resources.forEach(element => {
        if(retrieverResourcesList.filter(x=>x.doc_id == element.document_id).length == 0){
          let retrieverResources = {
            doc_id:element.document_id,
            doc_name:element.document_name,
            knowledge_id:element.dataset_id,
            knowledge_name:element.dataset_name,
            segments:[{
              content:element.content,
              position:element.segment_position,
              segment_id:element.segment_id
            }]
          }
          retrieverResourcesList.push(retrieverResources)
        }else{
          retrieverResourcesList.filter(x=>x.doc_id == element.document_id)[0].segments.push({
            content:element.content,
            position:element.segment_position,
            segment_id:element.segment_id
          })
        }
      });
  
      this.refMessageItemList[this.refMessageItemList.length-1].retrieverResources = retrieverResourcesList
    }
    
  }

  //工作流内容处理
  workflowHandle(data,event){
    if(!this.nowMessageChatMessage.value.workflow){
      this.nowMessageChatMessage.value.workflow = {isOpen:false}
    }
    if(!this.nowMessageChatMessage.value.workflow.workflowList){
      this.nowMessageChatMessage.value.workflow.workflowList = []
    }
    if(event == 'node_started'){
      this.nowMessageChatMessage.value.workflow.workflowList.push(data)
    }else if(event == 'node_finished'){
      this.nowMessageChatMessage.value.workflow.workflowList.filter(x=>x.node_id == data.node_id)[0].status = data.status
    }
  }

}



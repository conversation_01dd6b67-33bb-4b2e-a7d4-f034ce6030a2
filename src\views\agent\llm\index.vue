<template>
<div class="common-container">
    <!--header-->
    <div class="common-header">
        <div class="title">
            可用大模型列表
        </div>
        <div class="header-right">
            <select-switch
                name="模型分类"
                :tabs="modelTypeOptions"
                tab-select-lable="所有"
                tab-select="all"
                @change-select="changeCategory"
            />
            <el-input
                v-model="keyword"
                class="keyword"
                placeholder="搜索"
                :prefix-icon="Search"
                :clearable="true"
                @keyup.enter="getModelData"
            />
            <el-button
                type="primary"
                @click="systemModelSettingDialogRef.open()"
            >系统模型设置</el-button>
        </div>
    </div>

    <!--content-->
    <div class="common-content" v-loading="isLoading">
        <el-row :gutter="10">
            <el-col
                v-for="item of configuredProviders"
                :key="item.provider"
                :span="8"
                >
                <provider-card
                    :provider="item"
                    :models="filteredModelListMap[item.provider] ? filteredModelListMap[item.provider] : []"
                    class="card-provider"
                    @add-model="(provider) => credentialDialogRef.openAddModel({provider})"
                    @edit-model="(provider, model) => credentialDialogRef.openEditModel({provider, model})"
                    @set-provider="(provider) => credentialDialogRef.openSetProvider({provider})"
                />
            </el-col>
        </el-row>
        <el-divider />
        <el-row>
            <el-col :span="24">
                <div style="font-size: 18px; margin-bottom: 20px;">待配置大模型列表</div>
            </el-col>
        </el-row>
        <el-row :gutter="10">
            <el-col
                v-for="item of notConfiguredProviders"
                :key="item.provider"
                :span="8"
            >
                <provider-card
                    :provider="item"
                    :models="filteredModelListMap[item.provider] ? filteredModelListMap[item.provider] : []"
                    class="card-provider"
                    @add-model="(provider) => credentialDialogRef.openAddModel({provider})"
                    @edit-model="(provider, model) => credentialDialogRef.openEditModel({provider, model})"
                    @set-provider="(provider) => credentialDialogRef.openSetProvider({provider})"
                />
            </el-col>
        </el-row>
    </div>

    <credential-dialog
        ref="credentialDialogRef"
        @save-success="refresh"
        @remove-success="refresh"
    />
    <system-model-setting-dialog
        ref="systemModelSettingDialogRef"
    />
</div>
</template>

<script setup>
import { getModelProviders, getAvailableModels } from '@/api/model';
import { onMounted, computed } from 'vue';
import ProviderCard from '@/components/Model/providerCard.vue';
import CredentialDialog from '@/components/Model/credentialDialog.vue';
import SystemModelSettingDialog from '@/components/Model/systemModelSettingDialog.vue'
import SelectSwitch from '@/components/SelectSwitch/index';
import { Search } from '@element-plus/icons-vue';
import { getAllModels } from '@/api/model';

// 已配置的供应商
const configuredProviders = ref([]);

// 未配置的供应商
const notConfiguredProviders = ref([]);

// 模型列表map, key是供应商名字, value是模型数组
const modelListMap =  ref({});

// 当前模型类型: all, llm, text-embedding, speech2text, tts, rerank
const curModelType = ref('all');

// 搜索的关键词
const keyword = ref('');

// 是否正在加载中
const isLoading = ref(false);

// 模型分类下拉框的内容
const modelTypeOptions = [
    { value: 'all', label: '所有'},
    { value: 'llm', label: 'llm'},
    { value: 'text-embedding', label: 'text-embedding'},
    { value: 'speech2text', label: 'speech2text'},
    { value: 'tts', label: 'tts'},
    { value: 'rerank', label: 'rerank'},
];

// 设置模型认证弹窗的引用
const credentialDialogRef = ref(null);

// 系统模型设置弹窗的引用
const systemModelSettingDialogRef = ref(null);

// 过滤后的模型列表map
const filteredModelListMap = computed(() => {
    const map = {};

    for (const providerName in modelListMap.value) {
        map[providerName] = [];
        for (const item of modelListMap.value[providerName]) {
            if ((curModelType.value === 'all' || curModelType.value === item.model_type) && 
                item.status === 'active'
            ) {
                map[providerName].push(item);
            }
        }
    }

    return map;
}, {immediate: true});

/**
 * 分类变更
 */
function changeCategory(option) {
    curModelType.value = option.value;
}

/**
 * 获取模型供应商，以及配置好的模型
 */
function getModelData() {
    isLoading.value = true;

    configuredProviders.value = [];
    notConfiguredProviders.value = [];
    getModelProviders().then((rs) => {
        rs.data.forEach((provider) => {
            if (provider.custom_configuration.status === 'active') {
                configuredProviders.value.push(provider);
            } else {
                notConfiguredProviders.value.push(provider);
            }
        });
    });

    modelListMap.value = {};
    getAllModels({model_name: keyword.value}).then((rs) => {
        rs.data.forEach((group) => {
            const provider = group.provider;
            if (!Array.isArray(modelListMap.value[provider])) {
                modelListMap.value[provider] = [];
            }
            group.models.forEach((item) => {
                modelListMap.value[provider].push(item);
            });
        });
    }).finally(() => {
        isLoading.value = false;
    });
}

/**
 * 刷新
 */
function refresh() {
    getModelData();
}

onMounted(() => {
    getModelData();
});
</script>

<style scoped lang="scss">

.common-container {
    display: flex;
    flex-direction: column;
    background-color: #F7F7FA;
    height: calc(100vh - 52px);
    width: 100%;
    margin: 0;
    padding: 0;
    background-size: cover;
    overflow: auto;
}

.common-header {
    height: 80px;
    padding: 28px 30px;
    line-height: 24px;
    display: flex;
    justify-content: space-between;

    .title {
        color: #032220;
        font-size: 18px;
        font-weight: 500;
    }

    .header-right {
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: row;
        text-wrap: nowrap;

        .keyword {
            margin: 0 20px;
            width: 200px;
        }
    }
}

.common-content {
    margin: 0 30px;
    height: calc(100% - 68px);
}

.card-provider {
    margin-bottom: 0px;
}
</style>
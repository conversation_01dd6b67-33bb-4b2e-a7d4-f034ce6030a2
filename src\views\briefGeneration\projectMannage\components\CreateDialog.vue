<template>
  <el-dialog class="junzhiDialog" :close-on-click-modal="false" :model-value="props.visible" :show-close="false"
    width="565" @close="handleClose" style="
      min-height: 288px;
      border-radius: 8px !important;
    ">
    <template #title>
      <div class="titlebk">
        <div class="titletxt">{{ props.selectedDetail.type == 'edit' ? '编辑项目' : "新建项目" }}</div>
        <i class="ri-close-line" style="font-size: 18px; cursor: pointer; color: #63e5fe" @click="handleClose"
          alt="关闭"></i>
      </div>
    </template>
    <div class="body">
      <el-form label-position="top" :model="form" :rules="rules" ref="ruleFormRef">
        <el-form-item label="项目名称" prop="projectName">
          <el-input v-model="form.projectName" placeholder="请输入项目名称" maxlength="20" />
        </el-form-item>
        <el-form-item label="项目成员" prop="memberIds">
          <el-cascader v-model="form.memberIds" :options="options" :props="{ multiple: true, emitPath: false }"
            :show-all-levels="false" collapse-tags collapse-tags-tooltip :max-collapse-tags="3" clearable
            class="junzhiCascader" popper-class="junzhiSelectPopper junzhiCascaderPopper" style="width: 100%;"
            placeholder="请选择项目成员" />
        </el-form-item>
      </el-form>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <!-- <el-button class="cancel-btn" @click="handleClose">取 消</el-button> -->
        <!-- <el-button class="delete-btn" type="primary" @click="handleConfirm"
          >确 定</el-button
        > -->
        <div class="btn orange" @click="handleConfirm('submit')">完成{{ props.selectedDetail.type == 'edit' ? '编辑' : '创建'
        }}
        </div>
        <div class="btn blue" @click="handleConfirm('save')">保存，上传知识</div>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, watch, onMounted, reactive } from "vue";
import { useRouter } from "vue-router";
import { ElMessage } from "element-plus";
import { addProject, userList, editProject } from "@/api/briefGeneration/projectMannage";
const router = useRouter();

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  selectedDetail: {
    required: false
  },
});
const options = ref([])
const emit = defineEmits(["update:visible", 'openUpload', 'getList']);
const ruleFormRef = ref()
const form = reactive({
  projectName: '',
  memberIds: null
});
const rules = reactive({
  projectName: [
    { required: true, message: '请输入项目名称', trigger: 'blur' },
  ],
  // memberIds: [
  //   {
  //     required: true,
  //     message: '请选择项目成员',
  //     trigger: 'change',
  //   },
  // ],
})
// 监听 visible 的变化
watch(
  () => props.visible,
  (newVisible) => {
    if (!newVisible) {
      // 弹窗关闭时，清空表单
      form.projectName = ''
      form.memberIds = null
    } else {
      let { type, projectName, memberIds } = props.selectedDetail
      if (type == 'edit') {
        form.projectName = projectName
        form.memberIds = memberIds
      } else if (type == 'add') {
        form.projectName = ''
        form.memberIds = null
      }
    }
  },
  { immediate: true }
);
// 成员列表option
const getUserList = async () => {
  try {
    let res = await userList();
    if (res.code == '200') {
      options.value = (res.data || []).map(item => {
        return {
          value: String(item.userId),
          label: item.nickName
        }
      })
    } else {
      options.value = []
    }
  } catch (e) {
    console.error(e);
  }
};
const handleClose = () => {
  emit("update:visible", false);
};

const handleConfirm = async (type) => {
  await ruleFormRef.value.validate(async (valid, fields) => {
    if (valid) {
      let params = {
        projectName: form.projectName,
        memberIds: form.memberIds ? form.memberIds.join(',') : null
      }
      let res = props.selectedDetail.type == 'edit' ? await editProject({ ...params, id: props.selectedDetail.projectId }) : await addProject(params);
      if (res.code == '200') {
        emit("update:visible", false);
        if (type == 'save') {
          emit("openUpload");
        }
        emit("getList");
      }
    } else {
      console.log('error submit!', fields)
      return;
    }
  })
};

onMounted(() => {
  getUserList()
});
</script>

<style lang="scss" scoped>
.selectKnowledgePoints {
  margin-top: 12px;
  position: relative;
}

.titlebk {
  width: 100%;
  height: 46px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid #00EDFF;

  .titletxt {
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 16px;
    color: #fff;
    line-height: 16px;
  }
}

.body {
  --el-text-color-regular: #00EDFF;
  --el-color-info: #fff;
  padding-top: 74px;
  padding-left: 66px;
  padding-right: 66px;

  .el-input {
    --el-text-color-regular: #fff;
  }

  :deep(.el-tag .el-tag__close:hover) {
    color: black;
  }

  :deep(.el-cascader__tags .el-tag:not(.is-hit)) {
    border-color: #A8A8A8;
  }
}

.dialog-footer {
  width: 100%;
  text-align: center;
  margin-bottom: 30px;
  margin-top: 30px;
}

.btn {
  background: #2442b3;
  border-radius: 20px;
  border: 1px solid #05c0dc;
  color: #fff;
  padding: 0 32px;
  height: 40px;
  line-height: 40px;
  text-align: center;
  display: inline-block;
  cursor: pointer;

  &:not(:last-child) {
    margin-right: 20px;
  }
}

.blue {
  background: #2442b3;
}

.orange {
  background: #FF9100;
}
</style>
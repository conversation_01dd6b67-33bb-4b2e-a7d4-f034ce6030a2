<template>
    <div style="display: flex;width: 100%;">
        <div class="common-container">
            <div class="common-header">
                <div class="title">应用商店</div>
                <div class="header-right">
                    <div style="width: 90px;margin-right: 8px;">
                        <el-select v-model="queryParams.approveStatus" @change="searchList">
                            <el-option 
                                :label="'所有'"
                                :value="'all'" />
                            <el-option 
                                v-for="item in approveStatusOptions"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </div>
                    <el-input
                        v-model="queryParams.applicationName"
                        placeholder="搜索"
                        clearable
                        :prefix-icon="Search"
                        style="width: 200px;"
                        @keyup.enter="searchList" 
                        maxlength="100"
                    />
                </div>
            </div>
            <application-store-label-filter @change="filterSearch" />
            <div class="common-content">
                <div class="app-list" 
                    v-loading="loading"
                    v-infinite-scroll="nextPage" 
                    :infinite-scroll-disabled="disabledload"
                    :infinite-scroll-distance="10"
                    >
                    <template v-for="item in list">
                        <ApplicationStoreCard :app="item" 
                         @submit="searchList" />
                    </template>
                </div>
                <div v-if="noMore" style="text-align: center;margin-top: 20px;color: rgb(191, 191, 191);font-size: 14px;">没有更多了</div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { getClassify } from "@/api/application/store"
import { listUserApp } from "@/api/application/store"
import { Search } from '@element-plus/icons-vue'
import ApplicationStoreCard from './applicationStoreCard'
import ApplicationStoreLabelFilter from './applicationStoreLabelFilter'

const queryParams = ref({
    approveStatus:'all',
    applicationName:'',
    pageNum:1,
    pageSize:15
})

const tagFilterParams = ref({
    mode:null,ids:null,
    pid:null,isRecommend:null
})
const currentCategory = ref(null)
const approveStatusOptions = ref([
    {label:'审核中',value:2},
    {label:'我的',value:1}
])

const list = ref([])
const total = ref(0)

const loading = ref(false)
const noMore = computed(() => list.value.length >= total.value)
const disabledload = computed(() => loading.value || noMore.value)

const getList = () => {
    loading.value = true
    const params = {
        ...queryParams.value,
        ...tagFilterParams.value
    }
    if(queryParams.value.approveStatus == 'all'){
        delete params.approveStatus
    }
    getClassify().then(classify => {
        const childClassify = classify.data.filter(item => item.id == currentCategory.value).reduce((prev,cur) =>{
            return [...prev,...cur.children]
        },[])
        listUserApp(params).then(res => {
            total.value = res.total
            const newList = res.rows.map(item => {
                const classifyNames = []
                if(currentCategory.value == 'mode'){
                    const modeTags = [
                        {label: '聊天助手',value: 'advanced-chat'},
                        {label: 'Agent',value: 'agent'},
                        {label: '工作流',value: 'workflow'}
                    ]
                    classifyNames.push(modeTags.find(tag => tag.value == item.mode).label)
                }else{
                    item.classifyIds?.split(',').forEach(id => {
                        const find = childClassify.find(c => c.id == id)
                        if(find){
                            classifyNames.push(find.classifyName)
                        }
                    })
                }
                return {
                    ...item,
                    classifyNames
                }
            })
            list.value = [...list.value, ...newList]
            loading.value = false
        })
    })
}

const filterSearch = (command) => {
    loading.value = true
    queryParams.value.pageNum = 1
    list.value = []
    tagFilterParams.value = {mode:null,ids:null,pid:null,isRecommend:null}
    if(command.value == 'category-change'){
        currentCategory.value = command.category
        if(command.category != 'mode'){
            tagFilterParams.value.pid = command.category
        }
    }
    if(command.value == 'tag-click'){
        if(command.tag == 'primary'){
            tagFilterParams.value.isRecommend = 1
        }else if(['advanced-chat','agent','workflow'].includes(command.tag)){
            tagFilterParams.value.mode = command.tag
        }else{
            tagFilterParams.value.ids = command.tag
        }
    }
    getList()
}

const searchList = () => {
    queryParams.value.pageNum = 1
    list.value = []
    getList()
}

const nextPage = () => {
    if (noMore.value) return
    queryParams.value.pageNum++
    getList()
}

</script>

<style lang='scss' scoped>
.common-container {
    display: flex;
    flex-direction: column;
    background-color: #F7F7FA;
    height: 100vh;
    width: 100%;
    margin: 0;
    padding: 0;
    background-size: cover;
}
.common-header {
    height: 80px;
    padding: 34px 30px 4px 30px;
    line-height: 24px;
    display: flex;
    justify-content: space-between;

    .title {
        color: #032220;
        font-size: 18px;
        font-weight: 500;
    }

    .header-right {
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: row;
    }
}
.common-content {
    margin: 0 30px;
    height: calc(100vh - 150px);
    max-height: calc(100vh - 150px);
    display: flex;
    flex-direction: column;
    flex-shrink: 0;
    flex-grow: 1;
    padding-bottom: 10px;
    overflow: auto;
    .app-list{
        display: grid;
        grid-template-columns: repeat(3, minmax(0, 1fr));
        gap: 1rem;
        align-content: flex-start;
    }
}
</style>
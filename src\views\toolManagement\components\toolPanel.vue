<template>

    <div style="padding: .75rem 1.5rem;">
        <div class="header">
            <div class="icon">
                <template v-if="tool.type === 'builtin'">
                    <div class="img" :style="{backgroundImage:`url(${tool.icon})`}"></div>
                </template>
                <template v-else>
                    <span :style="{backgroundColor:tool.icon.background}" class="emoji">
                        <single-emoji :content="tool.icon.content"></single-emoji>
                    </span>
                </template>
            </div>
            <div class="text">
                <div class="name">
                    <div style="overflow: hidden;white-space: nowrap;text-overflow: ellipsis;">{{ tool.provider || tool.label.zh_Hans }}</div>
                </div>
            </div>
        </div>
        <div class="desc">
            {{ tool.description.zh_Hans ||  tool.description  }}
        </div>
        <div class="handle-area">
            <template v-if="tool.type == 'builtin' && Object.keys(tool.team_credentials).length">
                <el-button :type="!tool.is_team_authorization ? 'primary':'' " @click="toAuth" style="width: 100%;margin: .75rem 0;">
                    {{!tool.is_team_authorization ?'去授权':'已授权'}}
                </el-button>    
            </template>
            <template v-if="tool.type == 'api'">
                <el-button icon="Setting" @click="settingTool" style="width: 100%;margin: .75rem 0;">编辑</el-button>
            </template>
        </div>
        <div class="tools">
            <div class="title">包含 {{ toolTools?.length }} 个工具</div>
            <div class="tool-list">
                <template v-for="toolTool in toolTools">
                    <div class="tool-item" :class="{'tool-item-disabled':!tool.is_team_authorization && tool.type == 'builtin'}">
                        <div class="tools-label">{{ toolTool.label.zh_Hans }}</div>
                        <div class="tools-desc">{{ toolTool.description.zh_Hans }}</div>
                    </div>
                </template>
            </div>
        </div>
        <el-dialog v-model="dialogVisible" title="编辑自定义工具" width="630px" style="height: 80vh;" >
            <div style="height: calc(-120px + 80vh);display: flex;flex-direction: column;">
                <edit-custom-tool
                    :tool="tool"
                     ref="editCustomToolRef"/>
            </div>
            <div style="display: flex;flex-direction: row;align-items: center;justify-content: space-between;margin-top: 20px;">
                <div style="display: flex;flex-direction: row;justify-content: flex-start;">
                    <el-button type="danger" @click="toDelete">删除</el-button>
                </div>
                <div style="display: flex;flex-direction: row;justify-content: flex-end;">
                    <el-button @click="dialogVisible = false">取 消</el-button>
                    <el-button type="primary" @click="toUpdate">保 存</el-button>
                </div>
            </div>
        </el-dialog>
        <el-dialog v-model="authDialogVisible" title="设置授权" width="400px" style="height: 80vh;">
            <div style="height: calc(-120px + 80vh);display: flex;flex-direction: column;overflow-y: auto;overflow-x: hidden;">
                <auth-config :auth-fields="authConfigFields" ref="authConfigRef"
                    :auth-fields-values="authConfigFieldsValues"></auth-config>
            </div>
            <div style="display: flex;flex-direction: row;align-items: center;justify-content: space-between;margin-top: 20px;">
                <div style="display: flex;flex-direction: row;justify-content: flex-start;">
                    <el-button type="danger" v-if="tool.is_team_authorization" @click="toDeleteAuth">删除</el-button>
                </div>
                <div style="display: flex;flex-direction: row;justify-content: flex-end;">
                    <el-button @click="authDialogVisible = false">取 消</el-button>
                    <el-button type="primary" @click="updateAuth">保 存</el-button>
                </div>
            </div>
        </el-dialog>
    </div>

</template>
<script setup>
import {
    updateTool,
    deleteTool,
    getBuiltinToolAuthFields,
    getBuiltinToolAuthFieldValues,
    authBuiltinTool,
    deleteAuthBuiltinTool
} from '@/api/newTool/index'
import singleEmoji from './singleEmoji'
import editCustomTool from './editCustomTool'
import authConfig from './authConfig'
import { ElMessage } from 'element-plus';
    
const props = defineProps({
    tool:{
        type:Object,
        default:() => {return {}}
    },
    toolTools:{
        type:Array,
        default:() => {return []}
    }
})

const emits = defineEmits(['update-tool','delete-tool'])

const dialogVisible = ref(false)
const authDialogVisible = ref(false)

const editCustomToolRef = ref(null)
const authConfigRef  = ref(null)
const authConfigFields = ref([])
const authConfigFieldsValues = ref({})

const toUpdate = () => {
    editCustomToolRef.value.validateValue().then(() => {
        const {credentials,custom_disclaimer,
            description,icon,labels,privacy_policy,
            provider,schema,schema_type} = editCustomToolRef.value.formData
        const postData = {
            credentials,custom_disclaimer,
            description,icon,labels,privacy_policy,
            provider,schema,schema_type,original_provider:props.tool.provider
        }
        updateTool(postData).then(() => {
            ElMessage.success('操作成功')
            dialogVisible.value = false
            const returnData = {
                id:props.tool.id,
                type:props.tool.type,
                provider:postData.provider,
            }
            emits('update-tool',returnData)
        })
    })
}

const toDelete = () => {
    deleteTool({provider:props.tool.provider}).then(() => {
        ElMessage.success('操作成功')
        dialogVisible.value = false
        emits('delete-tool')    
    })
}

const settingTool = () => {
    dialogVisible.value = true
    nextTick(() => {
        editCustomToolRef.value.formData = JSON.parse(JSON.stringify(
            {
                paramsSchemas:[...props.tool.tools],
                ...props.tool
            }
        ))
    })
}

const toAuth = () => {
    getBuiltinToolAuthFields(props.tool.name).then((res) => {
        getBuiltinToolAuthFieldValues(props.tool.name).then((values) => {
            authConfigFields.value = res
            authConfigFieldsValues.value = values
            nextTick(() => {
                authDialogVisible.value = true
            })
        })
    })
}

const updateAuth = () => {
    let params = {
        credentials:authConfigRef.value.authFieldsValue
    }
    authBuiltinTool(props.tool.name,params).then(() => {
        ElMessage.success('操作成功')
        authDialogVisible.value = false
        const returnData = {
            id:props.tool.id,
            type:props.tool.type,
            provider:props.tool.name,
        }
        emits('update-tool',returnData)
    })
}

const toDeleteAuth = () => {
    deleteAuthBuiltinTool(props.tool.name).then(() => {
        ElMessage.success('操作成功')
        authDialogVisible.value = false
        const returnData = {
            id:props.tool.id,
            type:props.tool.type,
            provider:props.tool.name,
        }
        emits('update-tool',returnData)
    })
}

</script>
<style scoped lang="scss">
.header{
    display: flex;
    gap: .5rem;
    align-items: center;
    padding: .25rem 0;
    .icon{
        position: relative;
        flex-shrink: 0;
        .img{
            height: 2.5rem;
            width: 2.5rem;
            background-size: cover;
            background-position: 50%;
            background-repeat: no-repeat;
            border-radius: .375rem;
        }
        .emoji{
            height: 40px;width: 40px;border-radius: .5rem;
            display: flex;position: relative;align-items: center;
            justify-content: center;line-height: 1.75rem;
        }
    }
    .text{
        width: 0;
        flex-grow: 1;
        padding: 1px 0;
        .name{
            font-weight: 600;
            color: rgb(29, 41, 57);
            line-height: 1.5rem;
            font-size: 1.25rem;
            display: flex;
            align-items: center;
        }
    }
}
.desc{
    color: rgb(102,112,133);
    font-size: .875rem;
    line-height: 1.25rem;
    min-height: 36px;
    margin-top: .5rem;
}
.handle-area{
    display: flex;
    gap: .25rem;
    border-bottom: .5px solid rgba(0,0,0,.05);
}
.tools{
    margin-top: .75rem;
    .title{
        font-size: .75rem;
        font-weight: 500;
        line-height: 1.5rem;
        color: rgb(102, 112, 133);
    }
    .tool-list{
        margin-top: .25rem;
        .tool-item{
            margin-bottom: .5rem;
            cursor: pointer;
            border-radius: .75rem;
            border-width: .5px ;
            border-color: rgb(234, 236, 240);
            border-style: solid;
            background-color: rgb(252, 252, 253);
            padding: 1rem .75rem;
            box-shadow: rgba(0, 0, 0, 0) 0px 0px 0px 0px, rgba(0, 0, 0, 0) 0px 0px 0px 0px, rgba(16, 24, 40, 0.05) 0px 1px 2px 0px;
            .tools-label{
                font-size: .875rem;
                font-weight: 600;
                line-height: 1.25rem;
                color: rgb(29, 41, 57)
            }
            .tools-desc{
                margin-top: .125rem;
                -webkit-line-clamp: 2;
                overflow: hidden;
                display: -webkit-box;
                -webkit-box-orient: vertical;
                font-size: .75rem;
                line-height: 18px;
                color: rgb(102, 112, 133);
            }
        }
        .tool-item-disabled{
            cursor: not-allowed;
            background-color: rgb(247, 248, 250);
        }
    }
}
</style>
<template>
    <div class="container">
        <div class="content">
            <div class="ai-chat-content" ref="recordsBox">
                <div v-for="(item, index) in sessionHistoryList" :class="item.isUser?'user-record-box':'robot-record-box'" 
                :key="index">
                    <div class="head-img" :style="{'background-color':!item.isUser?'#dfdfff':''}">
                        <img :src="currentInstruct.agentIconUrl?baseUrl+currentInstruct.agentIconUrl:AgentDefaultIcon" v-if="!item.isUser">
                    </div>
                    <div :class="item.isUser?userContentClassName:robotContentClassName" >
                        <div class="user-name">
                            {{ item.isUser ? '我' : currentInstruct.agentName?currentInstruct.agentName : '小智'}}
                        </div>
                        <!--状态信息-->
                        
                        <!--内容-->
                        <div :class="item.isUser?userBoxClassName:robotBoxClassName">
                            <div v-if="item.isUser" style="display: flex;align-items: center;">
                                <img :src="pdfRed" style="height: 25px;width: 20px;margin-right: 5px;"
                                v-if="item.content && item.content.includes('.pdf')">
                                {{ item.content }}
                            </div>
                            <template v-else>
                                <!--工具信息-->
                                <div class="tool-info-box" v-if="currentInstruct.agentType==2 && item.chat_message.mark
                                && item.chat_message.mark.toolInfoList && item.chat_message.mark.toolInfoList.length>0">
                                    <div v-for="(tool,index) in item.chat_message.mark.toolInfoList" class="tool-info">
                                        <div class="title" @click="tool.isOpen = !tool.isOpen">
                                            <img :src="tool.state?greenYes:''" class="img" />
                                            <div style="margin-left: 5px;">{{ tool.state?'已使用':'' }}</div>
                                            <div style="margin-left: 5px;">{{ tool.toolInfo.tool_name }}</div>
                                            <el-icon style="margin-left: 5px;" v-if="!tool.isOpen"><ArrowDownBold /></el-icon>
                                            <el-icon style="margin-left: 5px;" v-if="tool.isOpen"><ArrowUpBold /></el-icon>
                                        </div>
                                        <div class="content" v-if="tool.isOpen">
                                            <div class="in-response-to">
                                                <div class="hr">
                                                    请求来自 {{ tool.toolInfo.tool_name.toUpperCase() }}
                                                </div>
                                                <div class="br">
                                                    {"{{ tool.toolInfo.tool_name }}":{{ JSON.stringify(tool.toolInfo.tool_input) }}}
                                                </div>
                                            </div>
                                            <div class="in-response-to">
                                                <div class="hr">
                                                    响应来自 {{ tool.toolInfo.tool_name.toUpperCase() }}
                                                </div>
                                                <div class="br">
                                                    {"{{ tool.toolInfo.tool_name }}":{{ JSON.stringify(tool.toolInfo.observation) }}}
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <message-renderer :cnt="item.content"/>
                            </template>
                            <!--操作-->
                            <div class="hidden-area" v-if="!item.isUser">
                                <div class="operation">
                                    <!--复制-->
                                    <div class="box" v-if="item.content">
                                        <el-tooltip content="复制" effect="light" placement="top" popper-class="tooltip-pop">
                                            <el-button :icon="CopyDocument" link @click="copyClick(item.content)"></el-button>
                                        </el-tooltip>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!--more info-->
                        <div v-if="!item.isUser" class="more-info">
                            <div class="response-latency">
                                耗时 {{ item.chat_message.response_latency.toFixed(2) }} 秒
                            </div>
                            <div class="tokens">
                                花费 Token {{ item.chat_message.answer_tokens + item.chat_message.query_tokens }}
                            </div>
                        </div>
                        <!--引用出处-->
                        <div v-if="!item.isUser && item.chat_message.mark.knowledge_quote && item.chat_message.mark.knowledge_quote.length>0" class="knowledge-list">
                            <citation-list :data="item.chat_message.mark.knowledge_quote"></citation-list>
                        </div>
                        <!--选项-->
                        <!-- <div v-if="!item.isUser">
                            <div v-for="(choices,index) in item.choices" class="choices" @click="{item.choicesShow=false;choicesClick(choices)}" >
                                {{ choices.label }}</div>
                        </div> -->
                        <!--建议问题-->
                        <!-- <div v-if="!item.isUser && item.chat_message.mark.suggested_questions" style="margin-top: 5px;">
                            <div v-for="(autosuggestion,index) in item.chat_message.mark.suggested_questions" class="choices" @click="autosuggestionClick(autosuggestion)" >{{ autosuggestion }}</div>
                        </div> -->
                    </div>
                    <div class="head-img" v-if="item.isUser">
                        <!--baseUrl+-->
                        <img :src="userStore.avatar">
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
<script setup>
    import { defineProps, nextTick,onBeforeUnmount,ref, watch } from 'vue';
    import { MessageItemManager } from './utils/msgManager';
    import useUserStore from '@/store/modules/user'
    import MessageRenderer from '@/components/Chat/messageRenderer.vue'
    import { CopyDocument } from '@element-plus/icons-vue'
    import useClipboard from 'vue-clipboard3';
    import CitationList from '@/components/Chat/citationList.vue'
    import AgentDefaultIcon from '@/assets/icons/svg/agent-default.svg'
    import greenYes from '@/assets/icons/svg/green-yes.svg'

    const { proxy } = getCurrentInstance();
    const baseUrl = import.meta.env.VITE_APP_BASE_API;
    const userStore = useUserStore()

    const props = defineProps({
        messageItemManager:{
            type: Object,
            required: false,
            default: new MessageItemManager()
        },
        sessionHistoryList:{
            type:Array,
            required:true
        },
        currentInstruct:{
            type: Object,
            required: false,
            default:{}
        }
    });

    const userBoxClassName = "user-record-item";
    const userContentClassName = "user-content";

    const robotBoxClassName = "robot-record-item";
    const robotContentClassName = "robot-content";

    //会话记录
    const sessionHistoryList = ref([])
    watch(() => props.sessionHistoryList, val => {
        if(val){
            sessionHistoryList.value = []
            const sessionHistoryListNew = JSON.parse(JSON.stringify(val))
            sessionHistoryListNew.forEach(element => {
                sessionHistoryList.value.push({isUser:true,content:element.query})
                if(element.mark.chain && element.mark.chain.length>1){
                    const toolInfoList = []
                    for(var i = 0 ;i<element.mark.chain.length-1;i++){
                        toolInfoList.push({
                            state:true,
                            isOpen:false,
                            toolInfo:element.mark.chain[i]
                        })
                    }
                    element.mark.toolInfoList = toolInfoList
                }
                sessionHistoryList.value.push({isUser:false,content:element.content,chat_message:element})
            });
        }
    },{ deep: true, immediate: true });
    
    const recordsBox = ref(null)
    //定位到最新内容
    const moveScroll = () => {
        nextTick(()=> {
            recordsBox.value.scrollTop = recordsBox.value.scrollHeight;
        })
    }

    const { toClipboard } = useClipboard();
    //复制
    const copyClick = async item => {
      try {
        await toClipboard(item);
        proxy.$modal.msgSuccess("复制成功");
      } catch (e) {
        console.error(e);
      }
    };

    watch(sessionHistoryList.value, () => {
        moveScroll();
    })

</script>
<style lang="scss" scoped>
    .container{
        position: relative;
        .content{
            padding: 10px 3%;
            text-align: center;
            width: 100%;
            .ai-chat-content{
                height: calc(100vh - 120px);
                overflow-y: auto;
                overflow-x: hidden;
                .user-input{
                    background-color: white;
                    padding:20px 20px 1px 20px;
                    border-radius: 10px;
                    .key-input{
                        font-size: 15px;
                        display: flex;
                        justify-content: space-between;
                        margin-bottom: 20px;
                        // align-items: center;
                        text-align: left;
                    }
                }
                .chat-message{
                    display: flex;
                    flex-direction: column;
                    .user-chat-area{
                        align-self: flex-end;
                        display: flex;
                        flex-direction: column;
                        margin-bottom: 10px;
                        padding-left: 0;
                        .user-box{
                            display: flex;
                            flex-direction: row;
                            justify-content: end;
                            align-items: center;
                            margin-bottom: 3px;
                            .user-name{
                                line-height: 34px;
                                height: 34px;
                                color: #B2B2B2;
                                font-size: 13px;
                            }
                            .user-icon{
                                img{
                                    width:30px;
                                    height:30px;
                                    background-color: #6977F1;
                                    border-radius: 5px;
                                    border: 1px solid #E6E6E6;
                                }
                                margin-left:4px;
                            }
                        }
                        .user-chat-message{
                            background-color: #5050E6;
                            border-radius: 12px;
                            border-top-right-radius: 0;
                            box-sizing: border-box;
                            color: #fff;
                            // cursor: pointer;
                            display: inline-block;
                            font-size: 14px;
                            line-height: 22px;
                            min-height: 26px;
                            outline: none;
                            padding: 9px 14px;
                            white-space: normal;
                            word-break: break-word;
                        }
                    }
                    .robot-chat-area{
                        align-items: flex-start;
                        display: flex;
                        flex-direction: column;
                        justify-content: flex-start;
                        margin-bottom: 10px;
                        position: relative;
                        .robot-box{
                            display: flex;
                            flex-direction: row;
                            align-items: center;
                            margin-bottom: 3px;
                            .robot-name{
                                line-height: 34px;
                                height: 34px;
                                color: #B2B2B2;
                                font-size: 13px;
                            }
                            .robot-icon{
                                img{
                                    width:30px;
                                    height:30px;
                                    background-color: #6977F1;
                                    border-radius: 5px;
                                    border: 1px solid #E6E6E6;
                                }
                                    margin-right:4px;
                                // background-color: #dfdfff
                            }
                        }
                        .robot-chat-message{
                            background-color: #F9FBFC;
                            border-radius: 0 12px 12px;
                            max-width: 100%;
                            padding: 12px 14px;
                            // width: 100%;
                            box-sizing: border-box;
                            overflow: hidden;
                            ::v-deep(.github-markdown-body) {
                                padding: 0;
                                font-size: 13px !important;
                                p{
                                    margin-bottom: 0;
                                }
                            }
                        }
                    }
                }
                .preface{
                    display: flex;
                    justify-content: space-between;
                    .preface-left{
                        width: 6%;
                        img{
                            width:25px;
                            height:25px;
                            background-color: #6977F1;
                            border-radius: 5px;
                        }
                    }
                    .preface-right{
                        width: 94%;
                        background-color: white;
                        border-radius: 5px;
                        padding: 20px;
                        .preface-font{
                            line-height: 30px;
                            font-size: 14px;
                            text-align: left;
                            color: #747796;
                        }
                        .preface-tool{
                            background-color: #F7F8FD;
                            padding: 20px 10px;
                            border-radius: 5px;
                            cursor: pointer; 
                            .preface-title{
                                display: flex;
                                align-items: center;
                                img{
                                    width:20px;
                                    height:20px;
                                    border-radius: 5px;
                                }
                            }
                            .preface-content{
                                overflow: hidden; 
                                white-space: nowrap;
                                text-overflow:ellipsis;
                                width: 100%;
                                line-height: 30px;
                                font-size: 14px;
                                text-align: left;
                                color: #878AA5;
                            }
                        }
                    }
                }
                .open-question-box{
                    margin-top: 20px;
                    display: flex;
                    flex-direction: column;
                    .prologue{
                        border:1px solid #E9EAEE;
                        border-radius: 10px;
                        background-color: white;
                        color: black;
                        padding: 10px 20px;
                        font-size: 15px;
                        text-align:left;
                        margin-bottom: 5px;
                    }
                    .open-question{
                        border:1px solid #E9EAEE;
                        border-radius: 10px;
                        background-color: white;
                        color: #7A8698;
                        padding: 10px 20px;
                        display: inline-block;
                        font-size: 15px;
                        max-width: 100%;
                        text-align: left;
                        cursor: pointer;
                    }
                }
                .record-box-user {
                    width: calc(100%);
                    display: inline-flex;
                    .head-img{
                        margin-left: 10px;
                        img{
                            height: 40px;
                            width: 40px;
                            // border: 2px solid #E6E6E6;
                            border-radius: 8px;
                            background-color: #dfdfff;
                            margin:0;
                        }
                    }
                
                }
                .record-box-robot {
                    width: calc(100%);
                    // margin-left: 10px;
                    display: inline-flex;
                    .head-img{
                        height: 40px;
                        width: 44px;
                        border-radius: 8px;
                        margin-right: 10px;
                        img{
                            height: 40px;
                            width: 40px;
                            // border: 2px solid #E6E6E6;
                            border-radius: 8px;
                            background-color: #dfdfff;
                            margin:0;
                        }
                    }
                    ::v-deep .el-loading-mask {
                        position: absolute;
                        z-index: 2000;
                        background-color: #f5f0f08f;
                        margin: 0;
                        top: 0;
                        right: 0;
                        bottom: 0;
                        left: 0;
                        transition: opacity var(--el-transition-duration);
                        height: 40px;
                        width: 41px;
                        border-radius: 10px;
                        padding: 7px;
                    }
                    ::v-deep .el-loading-spinner {
                        top: 50%;
                        margin-top: calc((0px - var(--el-loading-spinner-size)) / 2);
                        width: 100%;
                        text-align: center;
                        position: absolute;
                        width: 41px;
                        height: 40px;
                        /* align-items: center; */
                        display: flex;
                        align-items: center;
                    }
                    ::v-deep .el-loading-spinner .circular {
                        display: inline;
                        -webkit-animation: loading-rotate 2s linear infinite;
                        animation: loading-rotate 2s linear infinite;
                        height: 23px;
                        width: 23px;
                    }
                
                }
                .record-item {
                    display: inline-block;
                    border-radius: 10px;
                    background-color: #fff;
                    padding: 8px 12px;
                    max-width: calc(100% - 60px);
                    margin-bottom: 10px;
                    font-size: 15px;
                }
                .user-record-box {
                    @extend .record-box-user;
                    justify-content: flex-end;
                    text-align: right;
                    float: right;
                }
                .robot-record-box {
                    @extend .record-box-robot;
                    text-align: left;
                    .tool-info-box{
                        margin-bottom: 5px;
                        display: inline-block;
                        .tool-info{
                            background-color: #F2F4F7;
                            border-radius: 5px;    
                            margin: 5px 0 5px 0;
                            cursor: pointer;
                            .title{
                                color: #667085;
                                font-size: 12px;
                                display: flex;
                                align-items: center;
                                padding: 7px 10px;
                                .img{
                                    width: 15px;
                                    height: 15px;
                                }
                            }
                            .content{
                                border-top: 1px solid white;
                                padding: 10px;
                                font-size: 12px;
                                color: #7C8593;
                                .in-response-to{
                                    border: 1px solid #E6E8EA;
                                    border-radius: 5px;
                                    margin-bottom: 10px;
                                    .hr{
                                        background-color: white;
                                        border-top-left-radius: 5px;
                                        border-top-right-radius: 5px;
                                        border-bottom: 1px solid #E6E8EA;
                                        padding: 5px 10px;
                                        text-align: left;
                                    }
                                    .br{
                                        background-color: #F9FAFB;
                                        border-bottom-left-radius: 5px;
                                        border-bottom-right-radius: 5px;
                                        padding: 10px;
                                        text-align: left;
                                    }
                                }
                            }
                        }
                    }
                }
                .user-name{
                    line-height: 20px;
                    height: 20px;
                    color: #B2B2B2;
                    font-size: 12px;
                }
                .robot-record-item {
                    @extend .record-item;
                    color: #032220;
                    background-color: #F9FBFC;
                    font-size: 14px;
                    font-weight: 500;
                    position:relative;
                    &:hover{
                        .hidden-area{
                            display:block;
                        }
                    }
                    .hidden-area{
                        cursor:auto;
                        display:none;
                        top:-30px;
                        right:0px;
                        position:absolute;
                        width: 100%;
                        padding:10px 20px;
                        .operation{
                            display: flex;
                            justify-content: right;
                            .box{
                                background-color: white;
                                padding: 5px 4px;
                                border-radius: 5px;
                                border: 1px solid #E9EBEF;
                            }
                        }
                    }
                }
                .stop-responding{
                    border: 1px solid #E9EAEE;
                    border-radius: 10px;
                    background-color: white;
                    color: #5050E6;;
                    padding: 10px 20px;
                    display: flex;
                    font-size: 15px;
                    align-items: center;
                    justify-content: center;
                    cursor: pointer;
                }
                .user-record-item {
                    @extend .record-item;
                    color: white;
                    background-color: #5050e6;
                    text-align: left;
                    font-size: 14px;
                    font-weight: 500;
                }
                .user-content{
                    display: flex;
                    flex-direction: column;
                    align-items: end;
                    width: 100%;
                }
                .robot-content {
                    display: flex;
                    flex-direction: column;
                    align-items: start;
                    width: 100%;
                    .more-info {
                        opacity: 0;
                        display: flex;
                        font-size: .75rem;
                        line-height: 1rem;
                        align-items: center;
                        height: 18px;
                        margin-left: .25rem;
                        margin-bottom: .25rem;
                        color: rgb(152, 162, 179);
                        width: 100%;

                        .response-latency {
                            margin-right: .5rem;
                            flex-shrink: 0;
                            overflow: hidden;
                            text-overflow: ellipsis;
                            white-space: nowrap;
                            max-width: 33.33%;
                        }

                        .tokens {
                            margin-right: .5rem;
                            flex-shrink: 0;
                            overflow: hidden;
                            text-overflow: ellipsis;
                            white-space: nowrap;
                            max-width: 33.33%;
                        }
                    }

                    &:hover {
                        .more-info {
                            opacity: 100;
                        }
                    }

                    &:hover {
                        .hidden-area {
                            display: block;
                        }
                    }
                }
                .process-status{
                    .status{
                        background-color: #F9FBFC;
                        padding: 10px;
                        border-radius: 10px;
                        margin-bottom: 5px;
                        color: #19CD56;
                        display: flex;
                        align-items: center;
                        cursor: pointer;
                    }
                    .process{
                        color: #032220;
                        background-color: #F9FBFC;
                        margin-bottom: 5px;
                        border-radius: 10px;
                        width: calc( 100% - 60px );
                        .process-top{
                            display: flex;
                            justify-content: space-between;
                            align-items:center;
                            font-size:18px;
                            padding: 8px 12px;
                            background-color: white;
                            border-top-left-radius: 10px;
                            border-top-right-radius: 10px;
                            cursor: pointer;
                            .process-left{
                                display: flex;
                                align-items: center;
                            }
                        }
                        .process-content{
                            padding: 5px;
                            .process-title{
                                display: flex;
                                align-items: center;
                                // margin-left: 5px;
                                padding: 10px;
                                background-color: white;
                                border-radius: 10px;
                                cursor: pointer;
                                img{
                                    height: 20px;
                                    width: 20px;
                                    margin-right: 10px;
                                }
                                &:hover{
                                    background-color: #F0F0F5;
                                }
                            }
                            .process-child-status{
                                background-color: #F7F7FA;
                                padding: 5px 15px;
                                font-size: 13px;
                                line-height: 20px;
                                border-bottom-right-radius: 10px;
                                border-bottom-left-radius: 10px;
                            }
                        }
                        .process-bottom{
                            background-color: #F9FBFC;
                            padding: 10px;
                            border-bottom-left-radius: 10px;
                            border-bottom-right-radius: 10px;
                            .bottom-content{
                                width: 100px;
                                padding: 5px;
                                font-size: 12px;
                                background-color: #ECF7EC;
                                color: #32A252;
                                display: flex;
                                justify-content: center;
                                border-radius: 5px;
                            }
                        }
                    }
                }
                .knowledge-list{
                    color: #032220;
                    background-color: #F9FBFC;
                    width: calc( 100% - 60px );
                    padding: 8px 12px;
                    border-radius: 10px;
                }
                .choices{
                    background-color: #F5F6F7;
                    // min-width: 150px;
                    padding: 10px;
                    display: flex;
                    align-items: center;
                    justify-content: left;
                    border-radius: 15px;
                    border: 1px solid #E8E9ED;
                    margin-bottom: 5px;
                    color: #032220;
                    font-size: 14px;
                    font-weight: 500;
                    cursor: pointer;

                    &:hover{
                        background-color: #ececec;
                    }
                }
            } 
        }
    }
</style>
export default {
  toolbar: {
    undo: 'Undo',
    redo: 'Redo',
    bold: 'Strong',
    italic: 'Italic',
    strikethrough: 'Strikethrough',
    underline: 'Underline',
    subscript: 'Subscript',
    superscript: 'Superscript',
    highlight: 'Highlight',
    heading1: 'Heading 1',
    heading2: 'Heading 2',
    heading3: 'Heading 3',
    bulletedList: 'Bulleted List',
    numberedList: 'Numbered List',
    taskList: 'Task List',
    quote: 'Quote',
    codeBlock: 'Code',
    link: 'Link',
    unlink: 'Unlink',
    insertTable: 'Insert Table',
    deleteTable: 'Delete Table',
    addColBefore: 'Insert Column to the Left',
    addColAfter: 'Insert Column to the right',
    deleteColumn: 'Delete Column',
    addRowBefore: 'Insert Row Above',
    addRowAfter: 'Insert Row Below',
    deleteRow: 'Delete Row',
    splitCell: 'Split Cell',
    mergeCells: 'Merge Cells',
    horizontalRule: 'Horizontal Rule',
    insertNetworkImage: 'Insert Network Image',
    uploadLocalImage: 'Upload Local Image',
  },
  insertLinkDialog: {
    title: 'Link',
    cancel: 'Cancel',
    ok: 'Insert',
    field: {
      title: {
        label: 'title',
        placeholder: 'title',
      },
      url: {
        label: 'URL',
        placeholder: 'https://',
        emptyErr: 'Please input the URL.',
        invalidErr: 'A valid URL must start with "http://" or "https://".',
      },
    },
  },
  insertNetworkImageDialog: {
    title: 'Image',
    cancel: 'Cancel',
    ok: 'Insert',
    field: {
      title: {
        label: 'title',
        placeholder: 'title',
        emptyErr: 'Please input the title.'
      },
      url: {
        label: 'URL',
        placeholder: 'https://',
        emptyErr: 'Please input the URL.',
        invalidErr: 'A valid URL must start with "http://" or "https://".',
      },
    },
  },
  insertTableBoard: {
    title: 'Insert Table',
    info: '{row} * {col} table',
  },
}


<template>
    <div class="app-container">
       <el-form :model="queryParams" ref="queryRef" v-show="showSearch" :inline="true">
          <el-form-item label="用户名称" prop="userName">
             <el-input
                v-model="queryParams.userName"
                placeholder="请输入用户名称"
                clearable
                style="width: 240px"
                @keyup.enter="handleQuery"
             />
          </el-form-item>
          <el-form-item label="手机号码" prop="phone">
             <el-input
                v-model="queryParams.phone"
                placeholder="请输入手机号码"
                clearable
                style="width: 240px"
                @keyup.enter="handleQuery"
             />
          </el-form-item>
          <el-form-item>
             <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
             <el-button icon="Refresh" @click="resetQuery">重置</el-button>
          </el-form-item>
       </el-form>
 
       <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
             <el-button 
                type="warning" 
                plain 
                icon="Close"
                @click="handleClose"
             >关闭</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
       </el-row>
 
       <el-table v-loading="loading" :data="userList">
          <el-table-column label="用户名称" prop="userName" :show-overflow-tooltip="true" />
          <el-table-column label="用户昵称" prop="nickName" :show-overflow-tooltip="true" />
          <el-table-column label="邮箱" prop="email" :show-overflow-tooltip="true" />
          <el-table-column label="手机" prop="phone" :show-overflow-tooltip="true" />
          <el-table-column label="申请时间" align="center" prop="applyTime" width="180">
             <template #default="scope">
                <span>{{ parseTime(scope.row.applyTime) }}</span>
             </template>
          </el-table-column>
          <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
             <template #default="scope">
                <el-button link type="primary" icon="CircleClose" @click="cancelAuthUser(scope.row)">取消授权</el-button>
             </template>
          </el-table-column>
       </el-table>

       <pagination
          v-show="total > 0"
          :total="total"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
       />
    </div>
 </template>
 
 <script setup>
 import { getAuthUser,cancelUserAuth } from "@/api/application/cancelUser"
 import {useRouter, useRoute} from "vue-router";

 const route = useRoute();
 const router = useRouter();
 const { proxy } = getCurrentInstance();
 
 const userList = ref([]);
 const loading = ref(true);
 const showSearch = ref(true);
 
 const queryParams = reactive({
   pageNum: 1,
   pageSize: 10,
   applicationId: route.query.applicationId,
   applicationType: route.query.applicationType,
   userName: undefined,
   phone: undefined,
 });

 const total = ref(0);
 
 /** 查询授权用户列表 */
 function getList() {
   loading.value = true;
   getAuthUser(queryParams).then(response => {
     userList.value = response.rows;
     total.value = response.total;
     loading.value = false;
   });
 }
 // 返回按钮
 function handleClose() {
    router.back()
 }
 /** 搜索按钮操作 */
 function handleQuery() {
   getList();
 }
 /** 重置按钮操作 */
 function resetQuery() {
   proxy.resetForm("queryRef");
   handleQuery();
 }
 /** 取消授权按钮操作 */
 function cancelAuthUser(row) {
   proxy.$modal.confirm('确认要取消该用户本应用授权吗？').then(function () {
     return cancelUserAuth({ userId: row.userId, applicationId: queryParams.applicationId, applicationType: queryParams.applicationType });
   }).then(() => {
     getList();
     proxy.$modal.msgSuccess("取消授权成功");
   }).catch(() => {});
 }
 
 getList();
 </script>
 
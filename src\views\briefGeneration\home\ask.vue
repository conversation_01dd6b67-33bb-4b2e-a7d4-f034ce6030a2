<template>
  <div class="ask-container">
    <div v-if="label" ref="labelRef" class="label" :style="{transform: `translateY(${-textareaScrollTop}px)`}">{{ label }}</div>
    <textarea
      class="input-area"
      v-model="cnt"
      ref="textareaRef"
      :placeholder="placeholder || '请输入格式：【品牌】+【行业】'"
      @keydown.enter="handleEnter"
      @scroll="handleScroll"
      @input="handleInput"
      :maxlength="maxChars > 0 ? maxChars : undefined"
      :style="{textIndent: `${labelWidth + 5}px`}"
    ></textarea>
    <!-- 格式错误提示 -->
    <div v-if="formatError" class="error-message">
      {{ formatError }}
    </div>
    <!-- 格式示例提示 -->
    <div v-if="!cnt.trim() && !formatError && !brandVariable" class="format-hint">
      格式示例：【苹果】+【手机】、【星巴克】+【咖啡】
    </div>
    <div class="fileView" v-show="uploadedFiles.length > 0">
      <img src="/icons/pdf-icon.png" class="file-icon" />
      <span class="file-name" :title="uploadedFiles[0]?.name">{{
        uploadedFiles[0]?.name
      }}</span>
      <span class="file-size">{{
        formatFileSize(uploadedFiles[0]?.size)
      }}</span>
    </div>
    <div class="settings-area">
      <div class="handleBox">
        <slot name="handle"></slot>
        <div class="switch-wrapper" v-show="isDataBank">
          <el-switch v-model="network" class="switch" />资料库
        </div>
        <div class="switch-wrapper" v-show="isNetWork">
          <el-switch v-model="knowledge" class="switch"/>联网搜索
        </div>
        <el-upload
          :action="uploadFileUrl"
          :headers="{ Authorization: 'Bearer ' + getToken() }"
          :on-change="handleFileChange"
          :auto-upload="true"
          :show-file-list="false"
          :before-upload="beforeUpload"
          :disabled="isUploading"
          :accept="validFileTypes.join(',')"
        >
        <div class="file" v-show="isFile">
          <i class="ri-attachment-2"></i></div
        ></el-upload>
      </div>
      <button
        v-if="!isResponsing"
        :class="['send-btn', {'is-disabled': !cnt.trim() || !brandVariable}]"
        @click="ask"
        :disabled="!cnt.trim() || !brandVariable"
      >
        <inline-svg src="/icons/send.svg" />
        发送
      </button>
      <el-tooltip
        v-else
        class="box-item"
        effect="dark"
        content="停止生成"
        placement="top"
      >
        <div class="stop-btn" @click="stop"></div>
      </el-tooltip>
    </div>
  </div>
</template>

<script setup>
import { nextTick, ref, watch } from "vue";
import { ElMessage } from "element-plus";
import { getToken } from '@/utils/auth';
import InlineSvg from 'vue-inline-svg';
// import { uploadFile } from '@/api/knowledge/point.js'

const props = defineProps({
  /**
   * 是否在响应中
   */
  isResponsing: {
    type: Boolean,
    required: false,
    default: false,
  },
  /**
   * 是否展示资料库
   */
  isDataBank: {
    type: Boolean,
    required: false,
    default: true,
  },
  /**
   * 是否展示联网搜索
   */
  isNetWork: {
    type: Boolean,
    required: false,
    default: true,
  },
  /**
   * 是否展示上传文件
   */
  isFile: {
    type: Boolean,
    required: false,
    default: false,
  },
  maxChars: {
    type: Number,
    required: false,
    default: 0,
  },
  /**
   * 占位字符串
   */
  placeholder: {
    type: String,
    default: ''
  },
  /**
   * 标签内容
   * 
   * 不为空，则在会输入框的起始位置显示一个标签
   */
  label: {
    type: String,
    default: ''
  }
});

const uploadFileUrl = ref(import.meta.env.VITE_APP_BASE_API + "/common/upload");
const network = ref(false);
const knowledge = ref(false);
const uploadedFiles = ref([]);
const cnt = ref("");
const labelRef = ref(null)
const labelWidth = ref(0);
const textareaScrollTop = ref(0);

// 格式验证相关
const brandVariable = ref(""); // 品牌变量
const formatError = ref(""); // 格式错误信息

const emit = defineEmits(["ask", "stop"]);

// 验证输入格式：【品牌】+【行业】
function validateFormat(text) {
  // 正则表达式匹配【品牌】+【行业】格式
  const regex = /^【(.+?)】\+【(.+?)】$/;
  const match = text.match(regex);

  if (match) {
    const brand = match[1].trim();
    const industry = match[2].trim();

    if (brand && industry) {
      brandVariable.value = brand; // 提取品牌作为变量a
      formatError.value = "";
      return true;
    }
  }

  // 提供更详细的错误提示
  brandVariable.value = "";

  if (!text) {
    formatError.value = "";
    return false;
  }

  // 检查是否包含【】符号
  if (!text.includes('【') || !text.includes('】')) {
    formatError.value = "请使用【】符号包围品牌和行业名称";
    return false;
  }

  // 检查是否包含+号
  if (!text.includes('+')) {
    formatError.value = "请在品牌和行业之间添加+号";
    return false;
  }

  // 检查格式是否完整
  const partialRegex = /【(.+?)】/g;
  const matches = text.match(partialRegex);

  if (!matches || matches.length < 2) {
    formatError.value = "请确保格式为：【品牌】+【行业】";
    return false;
  }

  formatError.value = "请检查格式：【品牌】+【行业】";
  return false;
}
const handleEnter = (event) => {
  if (event.ctrlKey) {
    const start = event.target.selectionStart;
    const end = event.target.selectionEnd;
    event.target.value =
      event.target.value.substring(0, start) +
      "\n" +
      event.target.value.substring(end);
    event.target.selectionStart = event.target.selectionEnd = start + 1;
    return;
  }
  event.preventDefault();
  ask();
};
const handleScroll = (event) => {
  textareaScrollTop.value = event.target.scrollTop;
}

// 处理输入事件，实时验证格式
const handleInput = () => {
  if (cnt.value.trim()) {
    validateFormat(cnt.value.trim());
  } else {
    brandVariable.value = "";
    formatError.value = "";
  }
}
function ask() {
  if (!cnt.value.trim()) return;

  // 验证输入格式
  if (!validateFormat(cnt.value.trim())) {
    ElMessage.error(formatError.value);
    return;
  }

  let params = {
    cnt: cnt.value,
    brand: brandVariable.value // 添加品牌变量a
  };
  if (props.isDataBank) {
    params.network = network.value;
  }
  if (props.isNetWork) {
    params.knowledge = knowledge.value;
  }
  if (props.isFile) {
    params.files = uploadedFiles.value;
  }
  emit("ask", params);
  cnt.value = "";
  brandVariable.value = "";
  formatError.value = "";
}

function stop() {
  emit("stop");
}
const validFileTypes = [".pdf"];
const maxFileSize = 10 * 1024 * 1024; // 500MB
const isUploading = ref(false);
const beforeUpload = (file) => {
  const fileType = "." + file.name.split(".").pop().toLowerCase();
  if (!validFileTypes.includes(fileType)) {
    ElMessage.error(
      `不支持的文件格式，请上传${validFileTypes.join("，")}格式文件`
    );
    return false;
  }
  if (file.size > maxFileSize) {
    ElMessage.error("文件大小不能超过10M");
    return false;
  }
  return true;
};
const handleFileChange = async (file) => {
  if (file.raw && beforeUpload(file.raw)) {
    const isDuplicate = uploadedFiles.value.some((f) => f.name === file.name);
    if (!isDuplicate) {
      isUploading.value = true;
      try {
        // const formData = new FormData();
        // formData.append("file", file.raw);
        // const res = await uploadFile(formData);
        // const uploadedFile = Object.assign({}, file, {
        //   resourceId: res.resourceId,
        // });
        // uploadedFiles.value.push(uploadedFile);
        uploadedFiles.value = [file];
        ElMessage.success(`文件 ${file.name} 上传成功`);
      } catch (error) {
        console.error("上传文件失败:", error);
        ElMessage.error(`文件 ${file.name} 上传失败`);
      } finally {
        isUploading.value = false;
      }
    } else {
      ElMessage.warning("文件已存在");
    }
  }
};
// 格式化文件大小
function formatFileSize(bytes) {
  if (bytes < 1024) {
    return `${bytes} B`;
  } else if (bytes < 1024 * 1024) {
    return `${(bytes / 1024).toFixed(1)} KB`;
  } else {
    return `${(bytes / (1024 * 1024)).toFixed(1)} MB`;
  }
}

function setContent(text) {
  cnt.value = text
}

watch(() => props.label, () => {
  if (!props.label) {
    labelWidth.value = 0;
    return;
  }

  nextTick(() => {
    labelWidth.value = labelRef.value.offsetWidth;
  })
}, {immediate: true});

defineExpose({ setContent })

</script>

<style lang="scss" scoped>
.ask-container {
  width: 100%;
  height: 160rem;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  overflow: hidden;
  background-color: rgba(43, 77, 122, 0.73);
  border-radius: 10rem;
  border: 2rem solid #63E5FE;
  box-shadow: 0 0 11rem 3rem #5BC4DA;
  position: relative;
}

.label {
  display: block;
  width: auto;
  background-color: rgba(0, 0, 0, 0.1);
  border: 1rem solid #63E5FE;
  border-radius: 2rem;
  color: #4BAFFF;
  padding: 0 9rem;
  position: absolute;
  left: 12rem;
  top: 16rem;
  font-size: 12rem;
  line-height: 1.5;
}

.input-area {
  height: 80rem;
  flex: none;
  width: 100%;
  padding: 0 12rem;
  margin-top: 16rem;
  box-sizing: border-box;
  border: 0;
  border-radius: 0;
  resize: none;
  background-color: transparent;
  color: #C7C7C7;
  font-size: 12rem;
  line-height: 1.6;
}

.input-area::placeholder {
  font-size: 10rem;
  color: #C7C7C7;
}

.input-area:focus {
  outline: 0;
}

.error-message {
  color: #ff4757;
  font-size: 10rem;
  padding: 0 12rem;
  margin-top: 5rem;
  animation: shake 0.3s ease-in-out;
}

.success-message {
  color: #2ed573;
  font-size: 10rem;
  padding: 0 12rem;
  margin-top: 5rem;
}

.format-hint {
  color: #a4b0be;
  font-size: 10rem;
  padding: 0 12rem;
  margin-top: 5rem;
  font-style: italic;
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-5px); }
  75% { transform: translateX(5px); }
}

.fileView {
  flex: 1;
  min-height: 0;
  display: flex;
  align-items: center;
  padding: 0 12rem;
  margin-top: 10rem;
  margin-bottom: 8rem;
  .file-icon {
    flex: none;
    width: 16rem;
    margin-right: 4rem;
  }
  .file-name {
    font-size: 12rem;
    line-height: 12rem;
    color: #1e1f24;
    margin-right: 7rem;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    max-width: 240rem;
  }

  .file-size {
    flex: none;
    font-size: 12rem;
    line-height: 12rem;
    color: #848691;
  }
}
.settings-area {
  height: 42rem;
  width: calc(100% - 12rem);
  position: relative;
  left: 6rem;
  flex: none;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 0;
  border-top: 1rem solid #63E5FE;

  .handleBox {
    display: flex;
    align-items: center;
    .switch-wrapper {
      display: flex;
      column-gap: 3rem;
      line-height: 20rem;
      color: #c7c7c7;
      font-size: 14rem;
      display: flex;
      align-items: center;
      user-select: none;
      margin-right: 15rem;

      .switch {
        --el-switch-on-color: #50AFFE;
        --el-switch-off-color: #45829B;
        margin-right: 5rem;
      }
    }
    .file {
      color: #737171;
      font-size: 12rem;
      width: 24rem;
      height: 24rem;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-left: 7rem;
      border-radius: 50%;
      &:hover {
        background: #e9e9e9;
      }
    }
  }
}

.send-btn {
  padding: 0;
  color: white;
  font-size: 12rem;
  position: relative;
  cursor: pointer;
  width: 54rem;
  height: 21rem;
  background: #4BA1EE;
  border-radius: 10rem;
  border: 1rem solid #63E5FE;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;

  svg {
    color: #253F7F;
    width: 13rem;
    height: 13rem;
    margin-right: 3rem;
  }
}

.send-btn:hover {
  color: white;
  background: #2d74b1;
}

.send-btn.is-disabled {
  color: rgb(76, 74, 74);
  cursor: not-allowed;
  box-shadow: 0rem 1rem 0rem 0rem #015e28;
}

.stop-btn {
  width: 32rem;
  height: 32rem;
  padding: 0;
  background: #6236ed;
  border: 1rem solid rgba(0, 0, 0, 0.1);
  border-radius: 16rem;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  cursor: pointer;
}

.stop-btn::before {
  content: " ";
  display: block;
  width: 12rem;
  height: 12rem;
  background: #ffffff;
  border-radius: 3rem;
}
</style>

<style lang="scss">
.ask-type-popper {
  width: 161rem;
}
</style>

<template>
  <div class="home-container">
    <!-- Main content area -->
    <main class="main-content">
      <div class="content-wrapper">

        <!-- Big title -->
        <div class="big-title"></div>
        
        <!-- Small title -->
        <h2 class="small-title">全球领先的战略咨询AI</h2>
        
        <!-- Button container -->
        <div class="try-container">
          <div class="prompt-text">你可以这样问:</div>
          <div class="try-buttons-row">
            <div class="try-buttons-wrapper">
              <div v-for="(button, index) in buttons" :key="index" class="try-button" @click="setAskContent(button.text)">
                {{ button.text }}
                <img src="/icons/home-caret-right.png" >
              </div>
            </div>
            <div class="refresh-button">
              <refresh class="icon" />
              换一批
            </div>
          </div>
        </div>

        <ask ref="askRef" @ask="handleAsk"
          :label="activeLabelIdx >= 0 && labels.length > 0 ? labels[activeLabelIdx].name : ''" placeholder="请输入【品牌】+【行业】"
          :isDataBank="false" :isNetWork="false">
          <template #handle>
            <el-select v-model="selected" placeholder="请选择" style="width: 160px" popper-class="junzhiSelectPopper" class="junzhiSelect">
              <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </template>
        </ask>

        <!-- Link cards using ./link.vue component -->
        <div class="link-cards">
          <Link
            v-for="(label, index) in labels"
            :key="index"
            :icon="label.icon"
            :title="label.name"
            :desc="label.remark"
            @choose="activeLabelIdx = index"
            :active="activeLabelIdx === index"
          />
        </div>
      </div>
    </main>
  </div>
</template>

<script setup>
import { onMounted, ref } from 'vue'
import Ask from './ask.vue'
import Link from './link.vue'
import { Refresh } from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'
import { getHomeLabels } from '@/api/briefGeneration'

const router = useRouter()
const askRef = ref(null)

const buttons = ref([
  { text: '如何分析市场竞争格局？' },
  { text: '生成行业趋势报告' },
  { text: '制定产品定价策略' }
]);
const selected = ref('empty')

const options = [
  {
    value: '九牧王项目',
    label: '九牧王项目',
  },
  {
    value: '都市丽人项目',
    label: '都市丽人项目',
  },
  {
    value: 'empty',
    label: '空',
  },
]

const activeLabelIdx = ref(0)
const labels = ref([])

function setAskContent(text) {
  askRef.value.setContent(text)
}

function handleAsk(params) {
  // console.log('params :>> ', params,params.brand);
  // 品牌：params.brand
  const configData = {
    title: '简报',
    brandName: params.cnt.trim(),
    dataBank: params.knowledge,
    internet: params.network,
    labelConfigId: labels.value[activeLabelIdx.value]?.id,
  }
  localStorage.setItem("configData", JSON.stringify(configData))
  router.push(`/briefGeneration/add/create/add`)
}

onMounted(() => {
  getHomeLabels().then((rs) => {
    labels.value = rs.data
  })
})

</script>

<style scoped>
.home-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: transparent;
}

.header {
  height: 90rem;
}

.main-content {
  padding: 0 20rem;
  color: white;
  flex-grow: 1;
  flex-shrink: 1;
}

.content-wrapper {
  width: 580rem;
  height: 100%;
  margin: 0 auto;
  flex: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.big-title {
  display: block;
  font-size: 52rem;
  font-weight: bold;
  margin-bottom: 20rem;
  color: #FFFFFF;
  background-image: url(/images/logo-name.png);
  background-size: 100% 100%;
  width: 254rem;
  height: 50rem;
}

.small-title {
  font-size: 24rem;
  margin-bottom: 60rem;
  color: #FFFFFF;
  text-shadow: 0rem 2rem 5rem #00297A;
}

.description {
  font-size: 12rem;
  opacity: 0;
  display: none;
}

.try-container {
  width: 100%;
  margin-bottom: 10rem;
}

.prompt-text {
  text-align: left;
  margin-bottom: 15rem;
  font-size: 12rem;
  color: #FFFFFF;
  text-shadow: 0rem 1rem 0rem #02226D;
}

.try-buttons-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.try-buttons-wrapper {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  gap: 22rem;
}

.try-button {
  padding: 0 12rem;
  background-color: transparent;
  cursor: pointer;
  height: 28rem;
  line-height: 28rem;
  font-size: 12rem;
  color: white;
  border: 2rem solid #5BC4DA;
  border-radius: 4rem;
  box-shadow: 0 0 11rem 3rem #5BC4DA;
  background-color: rgba(35, 77, 120, 0.5);
}

.try-button img {
  width: 7rem;
  height: 7rem;
  margin-left: 8rem;
}

.try-button:hover {
  border-color: white;
}

.refresh-button {
  padding: 0;
  border: none;
  cursor: pointer;
  transition: background-color 0.3s;
  font-size: 12rem;
  color: white;
  height: 14rem;
  line-height: 14rem;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
}

.refresh-button:hover {
  text-shadow: 0rem 1rem 0rem #02226D;
}

.refresh-button .icon {
  width: 14rem;
  height: 14rem;
  margin-right: 5rem;
}

.link-cards {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 15rem;
  width: 100%;
  max-width: 1000rem;
  margin-top: 32rem;
}
</style>

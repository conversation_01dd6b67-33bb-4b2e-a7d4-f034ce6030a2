<template>
    <div style="width: 50px;height: 20px">
      <el-upload
        multiple
        :action="uploadImgUrl"
        list-type="picture"
        :on-success="handleUploadSuccess"
        :before-upload="handleBeforeUpload"
        :limit="limit"
        :on-error="handleUploadError"
        :on-exceed="handleExceed"
        ref="imageUpload"
        :before-remove="handleDelete"
        :show-file-list="false"
        :headers="headers"
        :file-list="fileList"
        :on-preview="handlePictureCardPreview"
        class="upload-demo"
      >
          <el-button :icon="Picture" size="small" link style="font-size: 13px;display:none;" ref="uploadClick">图片</el-button>
      </el-upload>
        <el-tooltip :content="'最大可上传'+props.limit+'个图片（单个图片最大'+props.fileSize+'MB），支持jpeg/jpg/png/bmp'" 
        effect="light" placement="top" popper-class="tooltip-pop">
          <el-button :icon="Picture" size="small" link style="font-size: 13px;margin-top: -35px;" @click="upload">图片</el-button>
        </el-tooltip>
    </div>
  </template>
  
  <script setup>
  import { getToken } from "@/utils/auth";
  import { Picture } from '@element-plus/icons-vue'
  
  const props = defineProps({
    modelValue: [String, Object, Array],
    // 图片数量限制
    limit: {
      type: Number,
      default: 5,
    },
    // 大小限制(MB)
    fileSize: {
      type: Number,
      default: 5,
    },
    // 文件类型, 例如['png', 'jpg', 'jpeg', "bmp"]
    fileType: {
      type: Array,
      default: () => ["jpeg", "jpg", "png", "bmp"],
    },
    //是否可用
    isUse: {
      type: Boolean,
      default: true
    },
    //不可用提示文字
    doNotUsePrompts: {
      type: String,
      default: ''
    },
  });
  
  const uploadClick = ref(null)
  const { proxy } = getCurrentInstance();
  const emit = defineEmits();
  const number = ref(0);
  const uploadList = ref([]);
  const dialogImageUrl = ref("");
  const dialogVisible = ref(false);
  const baseUrl = import.meta.env.VITE_APP_CONVERSATION_CLIENT_API;
  const uploadImgUrl = ref(import.meta.env.VITE_APP_CONVERSATION_CLIENT_API + "/conversation/upload/image"); // 上传的图片服务器地址
  const headers = ref({ Authorization: "Bearer " + getToken() });
  const fileList = ref([]);
  
  watch(() => props.modelValue, val => {
    if (val) {
      // 首先将值转为数组
      const list = Array.isArray(val) ? val : props.modelValue.split(",");
      // 然后将数组转为对象数组
      fileList.value = list.map(item => {
        if (typeof item === "string") {
          if (item.indexOf(baseUrl) === -1) {
            item = { name: baseUrl + item, url: baseUrl + item };
          } else {
            item = { name: item, url: item };
          }
        }
        return item;
      });
    } else {
      fileList.value = [];
      return [];
    }
  },{ deep: true, immediate: true });

  
  const upload = () => {
    if(props.isUse){
      uploadClick.value.$el.click();
    }
    else{
      proxy.$modal.msgError(props.doNotUsePrompts);
    }
  }
  
  // 上传前loading加载
  function handleBeforeUpload(file) {
    let isImg = false;
    if (props.fileType.length) {
      let fileExtension = "";
      if (file.name.lastIndexOf(".") > -1) {
        fileExtension = file.name.slice(file.name.lastIndexOf(".") + 1);
      }
      isImg = props.fileType.some(type => {
        if (file.type.indexOf(type) > -1) return true;
        if (fileExtension && fileExtension.indexOf(type) > -1) return true;
        return false;
      });
    } else {
      isImg = file.type.indexOf("image") > -1;
    }
    if (!isImg) {
      proxy.$modal.msgError(
        `图片格式不正确, 请上传${props.fileType.join("/")}图片格式文件!`
      );
      return false;
    }
    if (props.fileSize) {
      const isLt = file.size / 1024 / 1024 < props.fileSize;
      if (!isLt) {
        proxy.$modal.msgError(`上传图片大小不能超过 ${props.fileSize} MB!`);
        return false;
      }
    }
    proxy.$modal.loading("正在上传图片，请稍候...");
    number.value++;
  }
  
  // 文件个数超出
  function handleExceed() {
    proxy.$modal.msgError(`上传图片数量不能超过 ${props.limit} 个!`);
  }
  
  // 上传成功回调
  function handleUploadSuccess(res, file) {
    if (res.code === 200) {
      uploadList.value.push({ name: res.originalFilename, url: res.url });
      uploadedSuccessfully();
    } else {
      number.value--;
      proxy.$modal.closeLoading();
      proxy.$modal.msgError(res.msg);
      proxy.$refs.imageUpload.handleRemove(file);
      uploadedSuccessfully();
    }
  }
  
  // 删除图片
  function handleDelete(file) {
    const findex = fileList.value.map(f => f.name).indexOf(file.name);
    if (findex > -1 && uploadList.value.length === number.value) {
      fileList.value.splice(findex, 1);
      emit("update:modelValue", fileList.value);
      return false;
    }
  }
  
  // 上传结束处理
  function uploadedSuccessfully() {
    if (number.value > 0 && uploadList.value.length === number.value) {
      fileList.value = fileList.value.filter(f => f.url !== undefined).concat(uploadList.value);
      uploadList.value = [];
      number.value = 0;
      emit("update:modelValue", fileList.value);
      proxy.$modal.closeLoading();
    }
  }
  
  // 上传失败
  function handleUploadError() {
    number.value--;
    proxy.$modal.msgError("上传图片失败");
    proxy.$modal.closeLoading();
  }
  
  // 预览
  function handlePictureCardPreview(file) {
    dialogImageUrl.value = file.url;
    dialogVisible.value = true;
  }
  
  // 对象转成指定字符串分隔
  function listToString(list, separator) {
    let strs = "";
    separator = separator || ",";
    for (let i in list) {
      if (undefined !== list[i].url && list[i].url.indexOf("blob:") !== 0) {
        strs += list[i].url.replace(baseUrl, "") + separator;
      }
    }
    return strs != "" ? strs.substr(0, strs.length - 1) : "";
  }
  </script>
  
  <style scoped lang="scss">
  // .el-upload--picture-card 控制加号部分
  :deep(.hide .el-upload--picture-card) {
      display: none;
  }
  ::v-deep .el-upload--picture-card{
    height: 100px;
    width: 100px;
  }
  ::v-deep .el-upload-list--picture-card .el-upload-list__item{
    height: 100px;
    width: 100px;
  }
  </style>
  <style lang="scss">
  .tooltip-pop.el-popper{
      max-width: 200px !important;
      word-break:break-all
  }
  </style>
import request from '@/utils/conversationClientRequest'

//开启会话
export function conversationGenerate(data) {
    return request({
        url: '/conversation/generate',
        method: 'post',
        data
    })
}

//会话日志列表
export function getHistoryList(app_id,params) {
    return request({
        url: '/app/history/'+app_id,
        method: 'get',
        params
    })
}

//会话记录
export function getSessionHistoryList(conversation_id) {
    return request({
        url: '/app/history/detail/'+conversation_id,
        method: 'get'
    })
}

//对文档进行索引documents
export function documents(data) {
    return request({
        url: '/dataset/documents',
        method: 'post',
        data
    })
}

//获取索引进度接口
export function indexingStatus(dataset_id,batch,conversationId) {
    return request({
        url: '/dataset/'+(dataset_id?dataset_id:conversationId)+'/'+batch+'/indexing-status'+(dataset_id?'':'?is_conversation=true'),
        method: 'get'
    })
}

//获取团队编排相关信息
export function deleteDocuments(dataset_id,document_id,conversationId) {
    return request({
        url: '/dataset/'+(dataset_id?dataset_id:conversationId)+'/documents/'+document_id+(dataset_id?'':'?is_conversation=true'),
        method: 'delete',
    })
}
import request from '@/utils/conversationClientRequest'

//开启会话
export function conversationGenerate(data) {
    return request({
        url: '/conversation/generate',
        method: 'post',
        data
    })
}

//会话日志列表
export function getHistoryList(app_id,params) {
    return request({
        url: '/app/history/'+app_id,
        method: 'get',
        params
    })
}

//会话记录
export function getSessionHistoryList(conversation_id) {
    return request({
        url: '/app/history/detail/'+conversation_id,
        method: 'get'
    })
}
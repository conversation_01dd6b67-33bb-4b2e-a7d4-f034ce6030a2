<template>
    <el-dialog v-model="showDialog" title="" width="1600px">
        <div class="tabs-wrapper">
            <el-input class="input-keyword" :prefix-icon="Search" placeholder="搜索" size="default" v-model="keyword"  />
            <el-tabs v-model="tab">
                <el-tab-pane label="我的" name="my" style="height: calc(100vh - 250px);overflow-y: auto;">
                    <my-page
                        :team-list="allocatedTeamList"
                        :agent-list="allocatedAgentList"
                        :keyword="keyword"
                    />
                </el-tab-pane>
                <el-tab-pane label="场景" name="scene" style="height: calc(100vh - 250px);overflow-y: auto;">
                    <team-page
                        :allocated-team-map="allocatedTeamMap"
                        :keyword="keyword" ref="teamPage"
                    />
                </el-tab-pane>
                <el-tab-pane label="职业" name="carieer" style="height: calc(100vh - 250px);overflow-y: auto;">
                    <agent-page
                        :allocated-agent-map="allocatedAgentMap"
                        :keyword="keyword" ref="agentPage"
                    />
                </el-tab-pane>
            </el-tabs>
        </div>
    </el-dialog>
</template>

<script setup>
import { ref, provide, onMounted, watch } from 'vue';
import TeamPage from './teamPage.vue';
import AgentPage from './agentPage.vue';
import MyPage from './myPage.vue';
import { ElMessage } from 'element-plus';
import { getOwnListOld, setIsIndex,applicationApply } from '@/api/session/session.js';
import { Search } from '@element-plus/icons-vue';

// 是否显示弹窗
const showDialog = ref(false);

// 当前标签页
const tab = ref('my');

// 关键词
const keyword = ref('');

// 有权限的智能体, map的key是id, value是智能体信息
const allocatedAgentList = ref([]);
const allocatedAgentMap = ref({});

// 有权限的团队, map的key是id, value是团队信息
const allocatedTeamList = ref([]);
const allocatedTeamMap = ref({});

// 事件定义
const emit = defineEmits(['use', 'apply']);

// 切换标签页时，清空搜索框中的关键词
watch(tab, () => {
    keyword.value = '';
});

/**
 * 打开弹窗
 */
function open() {
    showDialog.value = true;
}

/**
 * 关闭弹窗
 */
function close() {
    showDialog.value = false;
}

/**
 * 使用
 */
function use(app) {
    close();
    if (app.applicationId) {
        emit('use', app);
    } else if (app.teamName) {
        emit('use', allocatedTeamMap.value[app.id]);
    } else {
        emit('use', allocatedAgentMap.value[app.id]);
    }
}
provide('use', use);

/**
 * 申请
 */
const teamPage = ref(null)
const agentPage = ref(null)
function apply(apply,type) {
    let data ={}
    if(type=='agent'){
        data = {
            applicationId:apply.id,
            applicationName:apply.label,
            applicationType:type,
            applicationDesc:''
        }
    }else{
        data = {
            applicationId:apply.id,
            applicationName:apply.teamName,
            applicationType:type,
            applicationDesc:apply.teamDescribe
        }
    }
    applicationApply(data).then(response => {
        if(response.data){
            ElMessage({
                message: '申请已审核通过',
                type: 'success',
            })
            getAllocatedData();
            if(tab.value == 'scene'){
                teamPage.value.getTeamList()
            }else{
                agentPage.value.getTreeList()
            }
        }else{
            ElMessage({
                message: '感谢您的申请，我们会尽快为您处理，请耐心等待！',
                type: 'success',
            })
            getAllocatedData();
            if(tab.value == 'scene'){
                teamPage.value.getTeamList()
            }else{
                agentPage.value.getTreeList()
            }
        }
    })
}
provide('apply', apply);

/**
 * 设为首页
 */
async function pin(app, isPinned) {
    let params = {};

    if (app.applicationId) {
        params = {
            applicationId: app.applicationId,
            applicationType: app.applicationType,
            isIndex: isPinned ? 1 : 0
        };
    } else if (app.teamName) {
        params = {
            applicationId: app.id,
            applicationType: 'team',
            isIndex: isPinned ? 1 : 0
        }
    } else {
        params = {
            applicationId: app.id,
            applicationType: 'agent',
            isIndex: isPinned ? 1 : 0
        }
    }

    await setIsIndex(params);
    ElMessage({
        message: '操作成功',
        type: 'success',
    });
    if (params.applicationType === 'agent') {
        allocatedAgentMap.value[params.applicationId].isIndex = params.isIndex;
    } else {
        allocatedTeamMap.value[params.applicationId].isIndex = params.isIndex;
    }
}
provide('pin', pin);

/**
 * 获取有权限的团队应用及智能体
 */
async function getAllocatedData() {
    const res = await getOwnListOld();
    const rows = res.data;

    const agentList = [];
    const agentMap = {};
    const teamList = [];
    const teamMap = {};

    rows.forEach((item) => {
        if (item.applicationType === 'team') {
            teamList.push(item);
            teamMap[item.applicationId] = item;
        } else {
            agentList.push(item);
            agentMap[item.applicationId] = item;
        }
    });

    allocatedAgentList.value = agentList;
    allocatedAgentMap.value = agentMap;
    allocatedTeamList.value = teamList;
    allocatedTeamMap.value = teamMap;
}

onMounted(() => {
    getAllocatedData();
});

defineExpose({open});
</script>

<style scoped lang="scss">
.tabs-wrapper {
    position: relative;
}
.input-keyword {
    width: 150px;
    position: absolute;
    right: 15px;
    top: 2px;
    z-index: 999;
}
::v-deep .el-dialog:not(.is-fullscreen) {
    margin-top: 0vh !important;
}
</style>
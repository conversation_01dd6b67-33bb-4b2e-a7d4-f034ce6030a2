<template>
  <div
    :style="{
      'width': width,
      'height': height
    }"
    class="preview-container"
  >
    <img v-if="type === 'image'" :src="url">
    <video v-else-if="type === 'video'" ref="video" preload="metadata">
      <source :src="url">
    </video>
    <el-icon v-else><Loading /></el-icon>
  </div>
</template>

<script setup>
  import {Loading} from '@element-plus/icons-vue'
  const extTypeMap = {
    mp4: 'video',
    rmvb: 'video',
    mkv: 'video',
    avi: 'video',
    wmv: 'video',
    jpg: 'image',
    png: 'image',
    gif: 'image',
    webp: 'image',
    svg: 'image',
    mp3: 'audio',
    ogg: 'audio',
    wav: 'audio'
  };

  const props = defineProps({
    url: {
      type: String,
      required: true
    },
    mime: {
      type: String,
      required: false,
      default: ''
    },
    width: {
      type: String,
      required: false,
      default: '100%'
    },
    height: {
      type: String,
      required: false,
      default: '100%'
    }
  })

  const type = ref(null)
  watch(props,()=>{
    if (props.url.match(/^blob:/)) {
      type.value = props.mime.split('/')[0];
    }else{
      const ext = props.url.split('.').pop();
      type.value = extTypeMap[ext]
        ? extTypeMap[ext]
        : 'none'; 
    }
  },{immediate: true})

  const video = ref(null)
  watch(() => type.value, val => {
    if (val === 'video') {
      setTimeout(() => {
        video.value.load();
      }, 0);
    }
  });
</script>

<style scoped>
img, video {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}
video {
  border: 1px solid lightgray;
  border-radius: 2px;
}
</style>

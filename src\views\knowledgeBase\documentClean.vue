<template>
  <div class="body">
    <div class="content" v-loading="loading">
      <!-- 左边上传文件 -->
      <div class="left-content">
        <div>
          <el-upload
            :file-list="fileList"
            :on-success="successUpload"
            :action="baseURL + '/clean/'"
            :auto-upload="false"
            :limit="1"
            :multiple="false"
            :data="{
              script_name: selectedScript,
            }"
            name="file"
            drag
            ref="upload"
            :on-change="changeFile"
          >
            <el-icon :size="64" color="#c0c4cc">
              <upload-filled />
            </el-icon>
            <div class="el-upload__text">选择要清洗的文件</div>
            <div class="el-upload__text">
              将文件拖到此处，或<em>点击上传</em>
            </div>
          </el-upload>
        </div>
        <div class="marginTop">
          <span class="text">清洗脚本：</span>
          <el-select v-model="selectedScript" placeholder="请选择">
            <el-option
              v-for="item in scripts"
              :label="item.name"
              :value="item.name"
            >
            </el-option>
          </el-select>
        </div>
        <div class="marginTop align-top">
          <span class="text">规则说明：</span>
          <el-input
            class="textarea"
            :disabled="true"
            type="textarea"
            :autosize="{ minRows: 1, maxRows: 6 }"
            placeholder="规则说明"
            v-model="textarea"
          >
          </el-input>
        </div>
        <div class="marginTop width-button">
          <el-button type="primary" class="width-center" @click="toClean()"
            >提交</el-button
          >
        </div>
      </div>
      <!-- 右边展示清洗结果 -->
      <div class="right-content">
        <segment-list :clean-data="cleanData"></segment-list>
      </div>
    </div>
  </div>
</template>

<script>
import { getScripts } from "@/api/knowledgeBase/clean.js";
import SegmentList from "./components/SegmentList.vue";

export default {
  components: {
    SegmentList,
  },
  data() {
    return {
      /**
       * 上传的文件, File对像
       */
      file: null,
      fileList: [],
      /**
       * 脚本列表
       */
      scripts: [],
      selectedScript: "",
      textarea: "",
      name: "",
      // 基础URL，上传图标没有使用axios，因此需自行设置baseURL
      baseURL: import.meta.env.VITE_APP_CLEAN_API,
      cleanData: null,
      loading: false,
    };
  },
  watch: {
    selectedScript(val) {
      if (val) {
        this.textarea =
          this.scripts.find((item) => item.name === val).support_rules[0]
            ?.description || "";
      }
    },
  },
  methods: {
    changeFile(prefile) {
      this.file = prefile;
      if (this.file) {
        this.getInfo();
      }
    },
    // 获取参数
    getInfo() {
      if (this.file) {
        getScripts(this.file.name).then((res) => {
          this.scripts = res.support_scripts;
          if (this.scripts.length > 0) {
            this.selectedScript = this.scripts[0].name;
          }
        });
      }
    },
    toClean() {
      this.$refs.upload.submit();
      this.loading = true; // 开始加载
    },
    successUpload(response) {
      console.log("response", response);
      this.cleanData = response;
      this.loading = false; // 加载完成
      this.$message({
        type: "success",
        message: "清洗成功",
      });
    },
  },
};
</script>
<style scoped>
.body {
  height: 100%;
}
.text {
  color: #606266;
}
.textarea {
  width: 500px;
}
.marginTop {
  margin-top: 20px;
  align-items: flex-start;
}
.upload {
  width: 500px;
}
:deep(.el-upload-dragger) {
  width: 570px;
  height: 215px;
}
:deep(.el-upload-list__item-name) {
  width: 570px;
}
:deep(.el-upload-list--text) {
  width: 570px;
}
.width-center {
  display: flex;
  margin: auto;
}
.width-button {
  width: 600px;
}
.align-top {
  display: flex;
  align-items: flex-start; /* 顶部对齐 */
}
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.5);
  z-index: 9999;
  display: flex;
  justify-content: center;
  align-items: center;
}

.loading-content {
  text-align: center;
}
.custom-spinner .el-spinner-inner .path {
  stroke: #46a6ff; /* 设置转圈圈的颜色为蓝色 */
}
.content {
  display: flex;
  height: 100%;
}
.left-content {
  padding: 30px;
}
.right-content {
  height: 100%;
}
</style>

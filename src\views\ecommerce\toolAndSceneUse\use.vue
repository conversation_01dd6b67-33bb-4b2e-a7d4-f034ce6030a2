<template>
    <div>
        <div class="common-container">
            <div class="common-header">
                <el-row>
                    <el-col class="title" :span="15" style="padding:0 50px">
                        <el-row>
                            <el-col :span="2" style="text-align:right;">
                                <el-image :src="baseUrl+props.userInfo.toolImage"/>
                            </el-col>
                            <el-col :span="19" style="padding:0 30px 0 10px">
                                <el-row>
                                    <el-col style="font-weight: 600;font-size: 16px;">
                                        {{ props.userInfo.toolName }}
                                    </el-col>
                                    <el-col style="font-size:14px;color:rgba(0,0,0,.70);font-weight: 500">
                                        {{ props.userInfo.toolDesc }}
                                    </el-col>
                                </el-row>
                            </el-col>
                        </el-row>
                    </el-col>
                    <!-- <el-col class="header-right" :span="7">
                        <el-button type="primary" style="color: white;font-size: 14px;font-weight: 500;"
                            @click="closeDrawer()">返回</el-button>
                    </el-col> -->
                </el-row>
            </div>
            <div style="height:calc(100vh - 290px);width:100%;border-top:2px solid #ccc" v-loading="loading">
                <el-row>
                    <el-col :span="12" style="height:calc(100vh - 290px);border-right:2px solid #ccc">
                        <div style="height:calc(100vh - 390px);overflow-y: auto;width:100%;padding:50px 100px">
                            <el-form ref="keyInfoForm" :model="keyInfo" :rules="rules" label-width="120px" :label-position="'top'" :validate-on-rule-change="false">
                                <el-row>
                                    <el-col v-for="(item,index) in keyList" :key="index">
                                        <el-form-item :label="item.keyName" :prop="item.keyCode">
                                            <el-input v-model="keyInfo[item.keyCode]" :placeholder="item.keyDesc"/>
                                        </el-form-item>
                                    </el-col>
                                    <el-col v-if="props.userInfo.toolType==2">
                                        <el-form-item label="内容类型" prop="contentType">
                                            <el-select v-model="keyInfo.contentType" placeholder="请选择内容类型" style="width:100%" @change="contentTypeChange()">
                                                <el-option label="文本" :value="1"></el-option>
                                                <el-option label="以图生图" :value="2"></el-option>
                                                <el-option label="视频脚本" :value="3"></el-option>
                                            </el-select>
                                        </el-form-item>
                                    </el-col>
                                    <el-col v-if="(props.userInfo.toolType==2 && keyInfo.contentType==2)">
                                        <el-form-item label="图片描述" prop="image_prompt">
                                            <el-input v-model="keyInfo['image_prompt']" placeholder="描述您对图片的需求"/>
                                        </el-form-item>
                                    </el-col>
                                    <el-col v-if="(props.userInfo.toolType==1 && props.userInfo.contentType==2)
                                    ||(props.userInfo.toolType==2 && keyInfo.contentType==2)">
                                        <el-form-item label="上传图片" prop="images">
                                            <Image-Upload v-model="keyInfo.images" :limit="1"></Image-Upload>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                            </el-form>
                        </div>
                        <div style="height:50px;width:100%;text-align: center;line-height: 100px;">
                            <el-button type="primary" style="color: white;font-size: 14px;width:200px;"
                            @click="startGenerating()" :disabled="startGeneratingDisable">开始生成</el-button>
                        </div>
                    </el-col>
                    <el-col :span="12" style="height:calc(100vh - 290px);padding:40px 10px">
                        <yimai-chat-bot style="height: calc(100vh - 360px);" :chat-url="'ws://***********:29999'" 
                        :message-item-manager="messageItemManager" ref="chatBot"></yimai-chat-bot>
                    </el-col>
                </el-row>
            </div>
        </div>
    </div>
</template>
<script setup>
import { ref, reactive, toRefs, nextTick } from 'vue'
import YimaiChatBot from'@/components/YimaiChatBot/index'
import { MessageItemManager } from '@/types/chat-types';
import faceBook from "@/assets/images/antFill-facebook1.svg"
import { getKeyList } from "@/api/ecommerce/toolUse";
import { v4 as uuidv4 } from 'uuid';
// 图片上传组件
import ImageUpload from "@/components/ImageUpload"
import { omit } from 'lodash';
const { proxy } = getCurrentInstance();

const messageItemManager = new MessageItemManager();

const props = defineProps({
    editDrawer:{
        required:false,
        type:Object
    },
    userInfo:{
        required:true,
        type:Object
    }
})

const baseUrl = import.meta.env.VITE_APP_BASE_API;

//开始生产按钮
const startGeneratingDisable = ref(false)

//规则校验
const rules = ref({})

const loading = ref(false)

//会话调用链接
const chatUrl = ref('ws://***********:29999')
//变量列表
const keyList = ref([])
const keyInfo = ref({
    //内容类型
    contentType:'',
    //图片
    images:'',
})
const chatBot = ref(null)

//获取变量列表
const getKeyListMethod = () => {
    loading.value=true;
    const toolId = props.userInfo.toolId;
    getKeyList(toolId).then(response=>{
        keyList.value=response.data;
        handleRules();
        loading.value=false;
    })
}

//内容类型变化
const contentTypeChange = () => {
    handleRules();
    if(keyInfo.value.contentType!=2){
        keyInfo.value.images=''
        keyInfo.value.image_prompt=''
        // keyList.value=keyList.value.filter(item => item.keyCode !== 'image_prompt')
    }
    if(keyInfo.value.contentType==2){
        // keyList.value.push({toolId:props.userInfo.toolId,keyCode:'image_prompt',keyDesc:'描述您对图片的需求',keyName:'图片描述',isCare:2})
    }
    // proxy.resetForm("keyInfoForm");
    // proxy.$forceUpdate();
}

//开始生成
const startGenerating = () => {
    proxy.$refs["keyInfoForm"].validate(valid => {
        if (valid) {
            // 要移除的字段列表
            const keyInfoToRemove = ['contentType', 'images']
            if(props.userInfo.toolType==2 && keyInfo.value.contentType!=2){
                keyInfoToRemove[2]='image_prompt';
            }
            const json = {
                shopId: props.userInfo.shopId,
                toolId: props.userInfo.toolId,
                uuid:uuidv4(),
                toolType:props.userInfo.toolType,
                contentType:props.userInfo.toolType==1?props.userInfo.contentType:keyInfo.value.contentType,
                imageUrl:keyInfo.value.images,
                keywords: omit(keyInfo.value, ...keyInfoToRemove)
            }
            // console.log(json)
            chatBot.value.connectWS(json)
            startGeneratingDisable.value = true
        }
    });
}

//修改开始按钮的状态
const startGeneratingDisableChange = (state) =>{
    startGeneratingDisable.value = state
}

provide('startGeneratingChange',startGeneratingDisableChange)

//处理规则校验
const handleRules = () => {
    if(props.userInfo.toolType==2){
        rules.value['contentType']=[{required: true, message: "内容类型不能为空", trigger: "change"}]
    }
    if(props.userInfo.toolType==2 && keyInfo.value.contentType==2){
        rules.value['image_prompt']=[{required: true, message: "图片描述不能为空", trigger: "blur"}]
        rules.value['images']=[{required: true, message: "图片不能为空", trigger: "blur"}]
    }
    if(props.userInfo.toolType==2 && keyInfo.value.contentType!=2){
        rules.value['images']=[{}]
        rules.value['image_prompt']=[{}]
    }
    if(props.userInfo.toolType==1 && props.userInfo.contentType==2){
        rules.value['images']=[{required: true, message: "图片不能为空", trigger: "blur"}]
    }
    keyList.value.forEach(element => {
        if(element.isCare==1){
            rules.value[element.keyCode]=[{required: true, message: element.keyName+"不能为空", trigger: "blur"}]
        }
    });
}

//关闭弹框
const closeDrawer = () => {
    messageItemManager.cleanHistory();
    nextTick(() => {
        props.editDrawer.close()
    })
}

getKeyListMethod();

</script>
<style scoped lang="scss">
.common-container {
    display: flex;
    flex-direction: column;
    // background-color: #F7F7FA;
    height: calc(100vh - 200px);
    width: 100%;
    margin: 0;
    padding: 0;
    background-size: cover;
}
    .common-header {
        // height: 100px;
        padding: 10px;
        line-height: 24px;
        // display: flex;
        justify-content: space-between;

        .title {
            color: #032220;
            font-weight: 50px;
        }

        .header-right {
            display: flex;
            justify-content: right;
            align-items: center;
            flex-direction: row;
        }
    }

</style>
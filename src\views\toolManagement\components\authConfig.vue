<template>

    <div>
        <template v-for="authField in authFields">
            <div class="auth-field-area">
                <template v-if="authField.type === 'text-input' || 
                    authField.type === 'number-input' || 
                    authField.type === 'secret-input' ">
                    <div class="auth-field-area-label">
                        {{ authField.label.zh_Hans }}
                        <span v-show="authField.required" class="auth-field-area-label-required">*</span>
                        <el-tooltip :content="authField.help.zh_Hans" placement="top" effect="light">
                            <el-icon style="margin-left: 5px; cursor: pointer;" size="12">
                                <QuestionFilled />
                            </el-icon>
                        </el-tooltip>
                    </div>
                    <el-input v-model="authFieldsValue[authField.name]" 
                        :placeholder="authField.placeholder.zh_Hans" />
                </template>
                <template v-if="authField.type === 'boolean'" >
                    <div class="auth-boolean-field">
                        <div class="auth-boolean-field-label-area">
                            <span class="auth-boolean-field-label">{{ authField.label.zh_Hans }}</span>
                            <el-tooltip :content="authField.help.zh_Hans" placement="top" effect="light">
                                <el-icon style="margin-left: 5px; cursor: pointer;" size="12">
                                    <QuestionFilled />
                                </el-icon>
                            </el-tooltip>
                        </div>
                        <div class="auth-boolean-field-value">
                            <el-switch v-model="authFieldsValue[authField.name]">
                            </el-switch>
                        </div>
                    </div>
                </template>
            </div>
        </template>
    </div>

</template>

<script setup>

import {QuestionFilled} from '@element-plus/icons-vue'

const props = defineProps({
    authFields:{
        type:Array,
        default: () => {return []}
    },
    authFieldsValues:{
        type:Object,
        default: () => {return {}}
    }
})

const authFieldsValue = ref({})

console.log(authFieldsValue.value)

const handleAuthFieldsValue = () => {
    authFieldsValue.value = props.authFields.reduce((obj, item) => {
        let defaultValue = ''
        if(item.type === 'boolean'){
            defaultValue = false
        }
        obj[item.name] = authFieldsValues.value[item.name] ||  defaultValue
        return obj
    }, {})
}

const authFieldsValues = ref({})
watch(() => props.authFieldsValues, val => {
    authFieldsValues.value = val
    handleAuthFieldsValue()
},{ deep: true, immediate: true });

// onMounted(() => {
//     authFieldsValue.value = props.authFields.reduce((obj, item) => {
//         let defaultValue = ''
//         if(item.type === 'boolean'){
//             defaultValue = false
//         }
//         obj[item.name] = props.authFieldsValues[item.name] ||  defaultValue
//         return obj
//     }, {})
// })

defineExpose({
    authFieldsValue
})

</script>
<style lang="scss" scoped>
.auth-field-area{
    padding: .75rem;
    .auth-field-area-label{
        padding-top: .5rem;
        padding-bottom: .5rem;
        font-size: .875rem;
        line-height: 1.25rem;
        color: rgb(16, 24, 40);
        display: flex;
        align-items: center;
        .auth-field-area-label-required{
            margin-left: .25rem;
            color: rgb(239, 68, 68);
        }
    }
    .auth-boolean-field{
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-size: .875rem;
        line-height: 1.25rem;
        color: rgb(16, 24, 40);
        .auth-boolean-field-label-area{
            display: flex;
            align-items: center;
            .auth-boolean-field-label{
                padding: .5rem 0;
                font-size: .875rem;
                line-height: 1.25rem;
                color: rgb(16, 24, 40);
            }
        }
        .auth-boolean-field-value{
            display: flex;
            align-items: center;
            padding: 4px;
        }
    }
}
</style>
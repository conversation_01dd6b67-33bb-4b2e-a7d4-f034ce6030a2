<template>
    <div class="toolContainer">
        <el-row>
            <el-col :span="14" style="border: 1px solid #f0f0f0;padding: 20px;height: calc(100vh - 210px);">
                <div style="font-size: 18px;font-weight: 800;margin-bottom: 20px;">
                    编辑
                </div>
                <el-form label-width="80px" style="height:calc(100vh - 330px);" label-position="top">
                    <el-form-item label="工具名称" prop="toolName" style="padding-bottom: 20px;">
                        <el-input v-model="toolInfo.toolName"></el-input>
                    </el-form-item>

                    <el-form-item label="工具描述" prop="toolDesc" style="padding-bottom: 20px;">
                        <el-input type="textarea" v-model="toolInfo.toolDesc" rows="6"></el-input>
                    </el-form-item>

                    <el-form-item label="内容类型" prop="contentType" style="padding-bottom: 20px;">
                        <el-select v-model="toolInfo.contentType" placeholder="请选择内容类型">
                            <el-option label="文字" :value="1"></el-option>
                            <el-option label="以图生图" :value="2"></el-option>
                            <el-option label="视频脚本" :value="3"></el-option>
                        </el-select>
                    </el-form-item>
                </el-form>
                <div style="float: right;">
                    <el-button @click="handler">下一步</el-button>
                </div>

            </el-col>
            <el-col :span="10" style="border: 1px solid #f0f0f0;padding: 20px;height: calc(100vh - 210px);">
                <div style="font-size: 18px;font-weight: 800;margin-bottom: 20px;padding-left: 10px;">
                    预览
                </div>

                <div>
                    <el-card :body-style="{ padding: '0px' }" style="height: calc(100vh - 295px);overflow: scroll;">
                        <div slot="header" class="clearfix">
                            <img :src="baseUr1 + '/' + props.toolInfo.toolImage" style="width: 88px;float: left;">
                            <div>
                                <div style="font-size: 16px;font-weight: 600;padding-top: 10px;">{{ toolInfo.toolName }}
                                </div>
                                <div style="font-size: 14px;padding: 10px 0px;">
                                    {{ toolInfo.toolDesc }}
                                </div>
                            </div>
                        </div>

                        <el-row v-for="item in toolInfo.ecommerceToolKeys">
                            <el-col :span="24">
                                <div style="padding: 10px 0px;">
                                    {{ item.keyName }}</div>
                                <div>
                                    <el-input type="textarea" v-model="testText" :placeholder="item.keyDesc"></el-input>
                                </div>
                            </el-col>
                        </el-row>
                    </el-card>
                </div>
            </el-col>
        </el-row>

    </div>
</template>
 
<script setup name="toolEditStep1">

const baseUr1 = import.meta.env.VITE_APP_BASE_API

const { proxy } = getCurrentInstance();
const { sys_show_hide, sys_normal_disable } = proxy.useDict("sys_show_hide", "sys_normal_disable");

const props = defineProps({
    toolInfo: {
        type: Object,
        default: {
            id: 0,
            toolImage: '',
            toolName: '',
            toolDesc: '',
            toolStage: '',
            contentType: '',
            prompt: '',
            toolType: '',
            ecommerceToolKeys: []
        }
    },
})

console.log(props.toolInfo)

let $emit = defineEmits(["nextStep", "click"]);

const handler = () => {
    $emit("nextStep", 2);
}

</script>
 

<style>
.toolContainer {
    display: flex;
    flex-direction: column;
    height: calc(100vh - 200px);
    width: 100%;
    margin: 0;
    padding: 0;
    background-size: cover;
}
</style>























<!-- 


<template>
    <div>
        <div>
            <el-form ref="form" :model="form" label-width="80px">
                <el-form-item label="工具名称">
                    <el-input v-model="form.name"></el-input>
                </el-form-item>
                
                <el-form-item label="工具描述">
                    <el-input type="textarea" v-model="form.desc"></el-input>
                </el-form-item>

                <el-form-item label="内容类型">
                    <el-select v-model="form.contentType" placeholder="请选择内容类型">
                        <el-option label="文本" value="text"></el-option>
                        <el-option label="图文" value="imgText"></el-option>
                        <el-option label="视频脚本" value="videoScript"></el-option>
                    </el-select>
                </el-form-item>

                <el-form-item>
                    <el-button type="primary" @click="handler">下一步</el-button>
                </el-form-item>
            </el-form>
        </div>
    </div>
</template>

<script setup>

const data = reactive({
  form: {
    name:"",
    desc:"",
    contentType:"text"
  }
});

const { form, rules } = toRefs(data);

let $emit = defineEmits(["nextStep", "click"]);

const handler = () => {
    $emit("nextStep", 2);
}

</script>


<style lang='scss' scoped>
.bottom {
    margin-top: 13px;
    line-height: 12px;
}

.button {
    padding: 0;
    float: right;
}

.image {
    width: 100%;
    display: block;
}

.clearfix:before,
.clearfix:after {
    display: table;
    content: "";
}

.clearfix:after {
    clear: both
}
</style> -->
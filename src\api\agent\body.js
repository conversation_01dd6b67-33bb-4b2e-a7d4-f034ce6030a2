import request from '@/utils/request'

// 获取列表
export function getList(params) {
    return request({
      url: '/agent/aiagent/list',
      method: 'get',
      params
    })
}

// 获取详情
export function getDetail(id) {
    return request({
        url: '/agent/aiagent/'+id,
        method: 'get'
    })
}
//添加智能体
export function addAgent(data) {
    return request({
        url: '/agent/aiagent',
        method: 'post',
        data
    })
}

//删除智能体
export function delAgent(id) {
    return request({
        url: '/agent/aiagent/'+id,
        method: 'delete'
    })
}

//编辑智能体
export function updateAgent(data) {
    return request({
        url: '/agent/aiagent',
        method: 'put',
        data
    })
}

//获取部门列表
export function getDeptListTree() {
    return request({
        url: '/system/dept/list/tree',
        method: 'get'
    })
}
//获取智能体视图列表
export function getAiagentTreeList() {
    return request({
        url: '/agent/aiagent/tree-list',
        method: 'get'
    })
}

//获取大模型列表
export function getModelList() {
    return request({
        url: '/model/modeltree/list?type=1',
        method: 'get'
    })
}


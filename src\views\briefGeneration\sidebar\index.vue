<template>
  <div
    :class="{ 'has-logo': showLogo }"
    :style="{
      backgroundColor: 'transparent'
    }"
  >
    <!--
    <img :src="collapseIcon" class="toggle" @click="toggleSideBar" />
    -->
    <logo v-if="showLogo" :collapse="isCollapse" />
    <el-scrollbar>
      <el-menu
        :default-active="activeMenu"
        :collapse="isCollapse"
        :unique-opened="true"
        :collapse-transition="false"
        mode="vertical"
        popper-class="popperBox"
        class="menu"
      >
        <sidebar-item
          v-for="(route, index) in sidebarRouters"
          :key="route.path + index"
          :item="route"
          :base-path="route.path"
          :collapse="isCollapse"
        />
      </el-menu>
    </el-scrollbar>
  </div>
</template>

<script setup>
import Logo from "./Logo";
import SidebarItem from "./SidebarItem";
import variables from "@/assets/styles/variables.module.scss";
import useAppStore from "@/store/modules/app";
import useSettingsStore from "@/store/modules/settings";
import usePermissionStore from "@/store/modules/permission";
import router, { constantRoutes } from "@/router";
import collapseIcon from "@/assets/logo/collapseIcon.png";

const route = useRoute();
const appStore = useAppStore();
const settingsStore = useSettingsStore();
const permissionStore = usePermissionStore();
function toggleSideBar() {
  appStore.toggleSideBar();
}
// const sidebarRouters =  computed(() => permissionStore.sidebarRouters);
const sidebarRouters = computed(() => {
  const briefRouters = permissionStore.sidebarRouters.filter(item => item.path === '/brief')
  const sidebarRouters = JSON.parse(JSON.stringify(briefRouters[0].children))

  sidebarRouters.forEach((item) => {
    if (!item.path.startsWith('/')) {
      item.path = '/brief/' + item.path
    }
  })

  return sidebarRouters
});
const showLogo = computed(() => settingsStore.sidebarLogo);
const sideTheme = computed(() => settingsStore.sideTheme);
const theme = computed(() => settingsStore.theme);
const isCollapse = computed(() => !appStore.sidebar.opened);

const activeMenu = computed(() => {
  const { meta, path } = route;
  // if set path, the sidebar will highlight the path you set
  if (meta.activeMenu) {
    return meta.activeMenu;
  }
  return path;
});

onMounted(() => {
  appStore.openSideBar({
    withoutAnimation: true
  });
})

</script>
<style lang='scss' scoped>
.toggle {
  height: 15px;
  position: absolute;
  top: 22px;
  right: 18px;
  cursor: pointer;
}
.has-logo {
  .el-scrollbar {
    height: calc(100% - 140px) !important;
  }
}

.menu {
    --el-menu-text-color: white;
    --el-menu-hover-text-color: transparent;
    --el-menu-bg-color: transparent;
    --el-menu-hover-bg-color: transparent;
    --el-menu-active-color: transparent;
}

.el-menu {
  --el-menu-item-height: 36px;
  --el-menu-sub-item-height: 30px;
  --el-menu-base-level-padding: 10px;
}
</style>
<style>
.popperBox {
  --el-menu-item-height: 36px;
  --el-menu-sub-item-height: 30px;
  --el-menu-base-level-padding: 10px;
}
</style>

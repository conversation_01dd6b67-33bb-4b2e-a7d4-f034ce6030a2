<template>
  <el-dialog
    v-model="visible"
    :title="title"
    class="doc-viewer-dialog"
    :close-on-click-modal="false"
    :destroy-on-close="true"
  >
    <!-- 只在body内放一个容器 -->
    <div class="docx-scroll-wrapper">
      <vue-office-docx :key="index" :src="fileUrl" />
    </div>
  </el-dialog>
</template>

<script setup>
import { ref } from 'vue'
import VueOfficeDocx from '@vue-office/docx'
import '@vue-office/docx/lib/index.css'

const visible = ref(false)
const fileUrl = ref('')
const title = ref('')
const open = (data) => {
  fileUrl.value = data.fileUrl || data.file_url
  title.value = data.fileName || data.file_name
  visible.value = true
}

defineExpose({ open })
</script>

<style scoped>
/* 弹窗整体宽度 */
.doc-viewer-dialog {
  max-width: 80vw;
  min-width: 60vw;
}
/* 让滚动区域100%撑满body */
.docx-scroll-wrapper {
  flex: 1 1 0;
  height: 100%;
  overflow-y: auto;
  padding: 20px;
  background: #fff;
  box-sizing: border-box;
  border-radius: 4px;
}

/* 防止内容撑开 */
:deep(.vue-office-docx) {
  width: 100%;
  box-sizing: border-box;
}
:deep(.vue-office-docx .docx-wrapper) {
  max-width: 100%;
  overflow: hidden;
}
</style>

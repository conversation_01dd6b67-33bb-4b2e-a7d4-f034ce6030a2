<template>
  <div class="small-internet-card" style="cursor: pointer" @click="handleClick">
    <div class="title">{{ title }}</div>
    <div class="source-info">
      <img :src="icon" class="icon" />
      <span class="source">{{ source }}</span>
    </div>
  </div>
</template>

<script setup>
import { defineProps } from 'vue'

const props = defineProps({
  icon: {
    type: String,
    required: true
  },
  source: {
    type: String,
    required: true
  },
  title: {
    type: String,
    required: true
  },
  url: {
    type: String,
    required: true
  }
})

const handleClick = () => {
  window.open(props.url, '_blank')
};
</script>

<style scoped>
.small-internet-card {
  width: 210px;
  height: 84px;
  background: rgba(0, 0, 0, 0.06);
  padding: 12px;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.title {
  height: 36px;
  font-size: 12px;
  color: #1e1f24;
  line-height: 1.5;
  overflow: hidden;
  text-overflow: ellipsis;
}

.source-info {
  display: flex;
  align-items: center;
  gap: 4px;
}

.icon {
  width: 14px;
  height: 14px;
  border-radius: 50%;
  object-fit: cover;
}

.source {
  font-size: 11px;
  color: #848691;
}
</style>

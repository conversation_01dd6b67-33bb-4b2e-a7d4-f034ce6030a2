<template>
  <!-- 删除弹窗 -->
  <el-dialog
    :close-on-click-modal="false"
    v-model="props.visible"
    title="确认删除？"
    width="364px"
    @close="cancel"
    :show-close="false"
  >
    <template #title>
      <div class="titlebx">
        <img
          class="prompt-icon"
          src="/icons/prompt.png"
          alt="感叹号"
          style="margin-right: 5px"
        />
        确认删除？
      </div>
    </template>
    <div style="padding-left: 28px">
      删除后将不可恢复，确认删除【{{ props.title }}】吗？
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button class="cancel-btn" @click="cancel">取 消</el-button>
        <el-button class="delete-btn" type="primary" @click="confirmDelete"
          >删 除</el-button
        >
      </span>
    </template>
  </el-dialog>
</template>
  
  <script setup>
import { ref, defineProps, defineEmits } from 'vue'

const props = defineProps({
  visible: {
    type: Boolean,
    required: true
  },
  title: {
    type: String,
    required: false,
    default: ''
  }
})

const emit = defineEmits(['visible'])

const cancel = () => {
  emit('toVisible', false)
}
const confirmDelete = () => {
  emit('toVisible', false)
}


  </script>
  <style scoped lang='scss'>
.titlebx {
  display: flex;
  align-items: center;
  .prompt-icon {
    width: 24px;
    height: 24px;
  }
}

.cancel-btn {
  width: 64px;
  height: 32px;
  background: rgba(0, 0, 0, 0.06);
  border: 0 !important;
  border-radius: 8px;
  &:hover {
    color: #1e1f24;
  }
}
.delete-btn {
  width: 64px;
  height: 32px;
  background: #f53f3f;
  border: 0 !important;
  border-radius: 8px;
}
</style>
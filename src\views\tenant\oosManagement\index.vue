<template>
    <div class="whole">
        <!-- <div class="single-page-container">
            <div class="single-page-header">
                <div class="left-box">
                    <el-button :icon="Back" @click="back()" text style="margin-right: 10px;"></el-button>
                    <div class="text-box">
                        <div class="title">
                            存储平台管理
                        </div>
                    </div>
                </div>
                <div  class="right-box">
                </div>
            </div>
        </div> -->
        <div style="padding: 20px; background-color: white; height: calc(100vh - 70px); border-radius: 8px;">
            <div style="margin-bottom: 20px;font-size: 15px;" v-if="showSearch">
                平台名称
                <el-input v-model="queryParams.platformName" placeholder="请输入平台名称" clearable @keyup.enter="selPlatformListMethod" :maxlength="100"
                style="width: 200px;margin-left: 5px;"/>
                <el-button type="primary" icon="Search" @click="selPlatformListMethod" v-hasPermi="['tenant:platform:query']" style="margin-left: 20px;">搜索</el-button>
                <el-button icon="Refresh" @click="resetQuery" v-hasPermi="['tenant:platform:query']">重置</el-button>
            </div> 
            <el-row :gutter="10" class="mb8">
                <el-col :span="1.5">
                    <el-button
                    type="primary"
                    plain
                    icon="Plus"
                    @click="addPlatformMethod"
                    v-hasPermi="['tenant:platform:add']"
                    >新增</el-button>   
                </el-col>
                <right-toolbar v-model:showSearch="showSearch" @queryTable="selPlatformListMethod"></right-toolbar>
            </el-row>

            <el-table v-loading="loading" :data="platformList" max-height="calc( 100vh - 380px )">
                <el-table-column label="平台" align="center" prop="platformName" />
                <el-table-column label="目录名" align="center" prop="platformIdentity" :show-overflow-tooltip="true">
                    <template #default="scope">
                        {{ JSON.parse(scope.row.platformSetting)['base-path']?JSON.parse(scope.row.platformSetting)['base-path']:'' }}
                    </template>
                </el-table-column>
                <el-table-column label="默认" align="center" prop="isDefault" width="100">
                    <template #default="scope">
                        {{ scope.row.isDefault==1?'开启':'关闭' }}
                    </template>
                </el-table-column>
                <el-table-column label="操作" align="center" width="150" class-name="small-padding fixed-width">
                    <template #default="scope">
                        <el-button link type="primary" icon="Edit" size="small" @click="editPlatformMethod(scope.row)" v-hasPermi="['tenant:platform:edit']" >修改</el-button>
                        <el-button link type="primary" icon="Delete" size="small" @click="handleDelete(scope.row)" v-hasPermi="['tenant:platform:remove']">删除</el-button>
                    </template>
                </el-table-column>
            </el-table>
            <pagination
                v-show="total > 0"
                :total="total"
                v-model:page="queryParams.pageNum"
                v-model:limit="queryParams.pageSize"
                @pagination="getPlatformListMethod"
            />
        </div>
        <!--新增/修改平台-->
        <el-dialog v-model="platformDialog" :title="platformTitle" center width="550px" v-if="platformDialog">
            <div>
                <el-form class="platform-form" :model="platformInfo" ref="platformForm"  :label-position="'top'" :rules="platformRules" 
                :validate-on-rule-change="false">
                    <el-form-item prop="platformValue" label="平台" >
                        <el-select v-model="platformInfo.platformValue" placeholder="请选择平台" style="width: 100%;"
                        @change="platformChaneg">
                            <el-option
                                v-for="(item,index) in platforms"
                                :key="item.platformValue"
                                :label="item.platformName"
                                :value="item.platformValue"
                            />
                        </el-select>
                    </el-form-item>
                    <el-form-item :prop="'platformSettingParse.'+item.keyCode" :label="item.keyName" v-for="(item,index) in platformKeyList">
                        <el-input v-model="platformInfo.platformSettingParse[item.keyCode]" type="text" placeholder="请输入描述" />
                    </el-form-item>
                    <el-form-item prop="isDefault" label="默认">
                        <el-radio-group v-model="platformInfo.isDefault">
                            <el-radio :label="1" size="large">开启</el-radio>
                            <el-radio :label="0" size="large">关闭</el-radio>
                        </el-radio-group>
                    </el-form-item>
                </el-form>
            </div>
            <template #footer>
                <div>
                    <el-button type="primary" @click="platformSubmit">确认</el-button>
                    <el-button @click="platformDialog = false">取消</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>
<script setup>
    import { nextTick, ref,watch } from 'vue'
    import { getPlatformList,getPlatforms,addPlatform,editPlatform,delPlatform } from '@/api/tenant/oosManagement/platform'
    import {Back} from '@element-plus/icons-vue'
    import { ElMessage, ElMessageBox } from 'element-plus'
    import router from "@/router";

    const { proxy } = getCurrentInstance();
    const showSearch = ref(true)
    const queryParams = ref({
        pageNum:1,
        pageSize:10,
        platformName:null
    })
    const loading = ref(false)

    //列表
    const platformList = ref([])
    const total = ref(0)
    const getPlatformListMethod = () => {
        getPlatformList(queryParams.value).then(response => {
            platformList.value = response.rows
            total.value = response.total
        })
    }
    getPlatformListMethod()
    const selPlatformListMethod = () => {
        queryParams.value.pageNum = 1
        getPlatformListMethod()
    }

    //重置
    const resetQuery = () =>{
        queryParams.value = {
            pageNum:1,
            pageSize:10,
            platformName:null
        }
        getPlatformListMethod()
    }

    const platformDialog = ref(false)
    const platformTitle = ref('')
    const platformInfo = ref({
        platformValue:null,
        platformName:null,
        isDefault:0,
        platformSettingParse:{},
        platformSetting:null
    })
    const platforms = ref([])
    const platformForm = ref(null)
    const platformRules = ref({
        platformValue: [{ required: true, message: '请选择平台', trigger: 'change' }],
    })
    //平台下拉框列表
    const getPlatformsList = () => {
        getPlatforms().then(response => {
            platforms.value = response.data
        })
    }
    getPlatformsList()

    //新增
    const addPlatformMethod = () => {
        platformTitle.value = '新增平台'
        platformInfo.value = {
            platformValue:null,
            platformName:null,
            isDefault:0,
            platformSettingParse:{},
            platformSetting:null
        }
        platformKeyList.value = []
        platformDialog.value = true
        platformRules.value = {
            platformValue: [{ required: true, message: '请选择平台', trigger: 'change' }],
        }
        nextTick(()=>{
            proxy.resetForm("platformForm");
        })
    }
    const platformKeyList = ref([])
    //平台下拉框切换
    const platformChaneg = () => {
        if(platformInfo.value.platformValue){
            platformKeyList.value = platforms.value.filter(x=>x.platformValue == platformInfo.value.platformValue)[0].platformKeyList
            platformKeyList.value.forEach(platform => {
                if(!platformInfo.value.id){
                    platformInfo.value.platformSettingParse[platform.keyCode] = null
                }
                platformRules.value['platformSettingParse.'+platform.keyCode] = [{ required: true, message: '请输入'+platform.keyName, trigger: 'blur' }]
            });
        }
    }

    //编辑
    const editPlatformMethod = (row) => {
        platformTitle.value = '修改平台'
        platformInfo.value = JSON.parse(JSON.stringify(row))
        platformChaneg()
        platformDialog.value = true
        // nextTick(()=>{
        //     proxy.resetForm("platformForm");
        // })
        platformInfo.value.platformSettingParse = platformInfo.value.platformSetting?JSON.parse(platformInfo.value.platformSetting):{}
    }

    //列表根据id获取平台名称
    const getPlatformName = (platformValue) =>{
        return platforms.value.filter(x=>x.platformValue == platformValue)[0]?platforms.value.filter(x=>x.platformValue == platformValue)[0].platformName:''
    }

    //确认
    const platformSubmit = () => {
        platformForm.value.validate(valid => {
            if(valid){
                platformInfo.value.platformName = getPlatformName(platformInfo.value.platformValue)
                //新增
                if(!platformInfo.value.id){
                    platformInfo.value.platformSetting = JSON.stringify(platformInfo.value.platformSettingParse)
                    addPlatform(platformInfo.value).then(response => {
                        ElMessage.success('新增成功')
                        platformDialog.value = false
                        selPlatformListMethod()
                    })
                    
                }//修改
                else{
                    platformInfo.value.platformSetting = JSON.stringify(platformInfo.value.platformSettingParse)
                    editPlatform(platformInfo.value).then(response => {
                        ElMessage.success('修改成功')
                        platformDialog.value = false
                        selPlatformListMethod()
                    })
                }
            }
        })
    }

    //删除
    const handleDelete = (row) => {
        ElMessageBox.confirm( '确认删除['+row.platformName+']数据项吗？', '系统提示',{
            confirmButtonText: '确认',
            cancelButtonText: '取消',
            type: 'warning',
        }).then(() => {
            delPlatform(row.id).then(response => {
                ElMessage.success('删除成功')
                selPlatformListMethod()
            })
        }).catch(() => {
        })
    }

    //返回
    const back = () => {
        router.back();
    }

</script>
<style lang="scss" scoped>
    .whole{
        height: calc(100vh - 50px);
        background-color: #F7F7FA;
        padding: 10px 20px;
        .single-page-container{
            // background-color: white;
            // height: 100vh;
            .single-page-header{
                height: 80px;
                display: flex;
                justify-content: space-between;
                align-items: center;
                flex-direction: row;
                border-bottom: 1px solid #ccc;
                .left-box{
                    margin-left:24px;
                    display: flex;
                    align-items: center;
                    .text-box{
                        display: flex;
                        flex-direction: column;
                        margin-left: 10px;
                        .title{
                    display: flex;
                    align-items: center;
                            height: 25px;
                            font-size: 18px;
                            font-weight: 500;
                            color: #032220;
                        }
                        .detail{
                            height: 17px;
                            font-size: 12px;
                            color: #999999;
                            font-weight: 400;
                            margin-top: 4px;
                        }
                    }
                    img {
                        height: 40px;
                        width: 40px;
                        border-radius: 4px;
                        border: 1px solid #E6E6E6;
                    }
                }
                .right-box{
                    margin-right: 20px;
                }
            }
            .single-page-content{
                display: flex;
                flex-direction: row;
                height: calc(100vh - 80px);
                overflow-y: auto;
                .content-left-box{
                    width: calc(60% - 48px);
                    height: calc(100% - 60px);
                    border-right: 1px solid #ccc;
                    background-color: white;
                    margin-top: 30px;
                    .content-left-box-title{
                        padding-left: 24px;
                        font-size: 14px;
                        font-weight: bold;
                        color: #032220;
                        margin-bottom: 16px;
                    }
                    .left-box-half-top{
                        border-bottom: 1px solid #ccc;
                    }
                    .custom-form-item-slider{
                        display: flex;
                        flex-direction: row;
                        width: 100%;
                        align-items: center;
                        justify-content: space-between;
                        margin-bottom: 5px;
                        .custom-slider-title{
                            margin-right: 20px;
                            width: 120px;
                            text-align: left;
                            font-size: 12px;
                            color: #999999;
                        }
                        .custom-slider-value{
                            width: calc(100% - 120px);
                            display: flex;
                            flex-direction: row;
                        }
                    }
                }
                .content-right-box-title{
                    color: #032220;
                    font-size: 14px;
                    font-weight: 500;
                    margin-bottom: 10px;
                }
                
                
            }
        }
    }
    ::v-deep .el-dialog:not(.is-fullscreen) {
        margin-top: 0vh !important;
    }
</style>
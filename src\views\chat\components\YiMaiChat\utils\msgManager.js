import { reactive, ref } from "vue";
import md from "./markdown";

export class MessageItem {
  isUser;
  content;
}

class MsgRecord {
  role;
  content;
}

export class MessageItemManager {
  refMessageItemList = reactive([]);
  nowMessageText = "";
  nowMessageHtml = ref("");
  nowMessageRole = ref({});
  nowMessageLine = ref("");
  nowMessageChatMessage = ref({})
  nowMessageToolInfo = ref([])
  
  loading = ref(false);
  waitting = ref(false);
  msgRecords = ref([]);

  pushUserMessage(content) {
    this.refMessageItemList.push({ isUser: true, content: content });
    this.msgRecords.value.push({ role: "user", content: content });
    this.autosuggestionHandle()
  }

   /**
   * 清空消息历史，包含界面显示的和记录的
   */
   cleanHistory() {
    this.cleanRecords();
    if (this.refMessageItemList.length > 0) {
      this.refMessageItemList.splice(0, this.refMessageItemList.length);
    }
  }

  loadHistoryMessage(messages) {
    this.cleanHistory()
    messages.forEach((item) => {
      this.refMessageItemList.push({isUser:true,content:item.query})
      if(item.mark.chain && item.mark.chain.length>1 && item.role!='human'){
        let toolInfoList = []
        for(var i = 0 ;i<item.mark.chain.length-1;i++){
            toolInfoList.push({
                state:true,
                isOpen:false,
                toolInfo:item.mark.chain[i]
            })
        }
        item.mark.toolInfoList = toolInfoList
      }
      this.refMessageItemList.push({isUser:false,content:item.content,chat_message:item})
    })
  }

  /**
   * 消息接收结束
   *
   * @param record 是否记录本次接收的消息
   */
  messageReceiveDone(record = true) {
    if (this.nowMessageLine.value !== "") {
      this.nowMessageText += this.nowMessageLine.value;
      this.nowMessageHtml.value = this.nowMessageText;
      this.nowMessageLine.value = "";
    }
    if (record) {
      this.msgRecords.value.push({ role: "assistant", content: this.nowMessageText });
    }
    //处理上一条数据为不能重新生成
    this.refMessageItemList[this.refMessageItemList.length-1].isRegenerationState = false
    this.nowMessageText = "";
    let refMessageItem = {
      isUser: false,
      content: this.nowMessageHtml.value,
      roleInfo: this.nowMessageRole.value,
      chat_message: this.nowMessageChatMessage.value,
      isRegenerationState:true
    } 
    if(this.nowMessageChatMessage.value.mark.suggested_questions && this.nowMessageChatMessage.value.mark.suggested_questions.length>0){
      refMessageItem.choicesShow=true
    }
    if(!refMessageItem.chat_message.mark){
      refMessageItem.chat_message.mark = {}
    }
    //工具展示处理
    refMessageItem.chat_message.mark.toolInfoList = this.nowMessageToolInfo.value
    this.refMessageItemList.push(refMessageItem);
    this.loading.value = false;
    this.nowMessageHtml.value = "";
    this.nowMessageToolInfo.value = []
    // this.nowMessageRole.value = {}
  }

  //处理猜你想问 只有最新一条回答才展示
  autosuggestionHandle(){
    let length = this.refMessageItemList.filter(x=>x.isUser==false).length
    if(length>0){
      this.refMessageItemList.filter(x=>x.isUser==false)[length-1].chat_message.mark.suggested_questions=[]
    }
  }

  clearNowMessageRole(){
    this.nowMessageRole.value = {}
  }

  do() {
    this.loading.value = true;
  }

  stop(){
    
  }

  //连接断开处理
  disconnected(){
    //会话是否等待状态（否）
    this.waitting.value = false
    this.nowMessageHtml.value = "会话已中断，请重新发起新的会话";
    this.nowMessageText = "会话已中断，请重新发起新的会话";
  }

  new() {
    //会话是否等待状态（否）
    this.waitting.value = false
    this.loading.value = false
    //消息内容列表清空
    if (this.refMessageItemList.length > 0) {
      this.refMessageItemList.splice(0, this.refMessageItemList.length);
    }
    this.nowMessageHtml.value = "";
    this.nowMessageText = "";
    this.nowMessageToolInfo.value = []
  }

  waittingStart() {
    this.waitting.value = true;
  }

  waittingEnd() {
    this.waitting.value = false;
  }

  /**
   * 清空消息记录
   */
  cleanRecords() {
    if (this.msgRecords.length > 0) {
      this.msgRecords.value = [];
    }
  }

  deleteLastMsgRecord() {
    return this.msgRecords.value.pop();
  }

  /**
   * 向现在的消息中添加新内容
   * TODO 优化md的转换，减少转化次数
   * @param newContent 新的内容
   */
  appendNowMessageContent(newContent,role,chat_message, parse = true) {
    try {
      if (parse) {
        newContent = JSON.parse(`"${newContent}"`);
      }
      this.nowMessageText = newContent;
      this.nowMessageLine.value = "";
      this.nowMessageHtml.value = this.nowMessageText;
      this.nowMessageRole.value = role;
      this.nowMessageChatMessage.value = chat_message;
    } catch (e) {
      console.log(e);
    }
  }

  //工具调用开始
  toolHandle(chat_message,isBegin){
    try {
      if(isBegin){
        this.nowMessageToolInfo.value.push({state:false,isOpen:false})
      }else{
        this.nowMessageToolInfo.value[this.nowMessageToolInfo.value.length-1].state = true 
        this.nowMessageToolInfo.value[this.nowMessageToolInfo.value.length-1].toolInfo = chat_message
      }
    }catch (e) {
      console.log(e);
    }
  }
  //删除最后一条数据
  deleteLast(){
    try{
      this.refMessageItemList.splice(this.refMessageItemList.length-1, this.refMessageItemList.length);
    }catch(e){
      console.log(e);
    }
  }
}

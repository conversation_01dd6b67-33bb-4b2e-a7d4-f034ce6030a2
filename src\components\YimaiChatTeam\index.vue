<template>
    <div class="chat-bot-box">
        <template v-if="chatUrl != ''">
            <el-row style="">
                <el-col :span="20" style="padding:10px 0 0 10px">
                    <div class="chat-records-box">
                        <ChatRecords :message-item-manager="props.messageItemManager" :team-agent-selected-list="teamAgentSelectedList"></ChatRecords>
                    </div>
                    <div class="input-send">
                        <MessageCtrlByWS :message-item-manager="props.messageItemManager" 
                            :chat-url="props.chatUrl" :init-message="initMessage" :is-disabled-send-set="props.isDisabledSendSet"
                            ref="messageCtrl"
                            ></MessageCtrlByWS>
                    </div>
                </el-col>
                <!-- border-right:1px solid #909399; -->
                <el-col :span="4" style="border-left:1px solid #E6E6E6;padding:20px 30px;
                background-color: #F7F7FA;height: calc(100vh - 205px);overflow-y: scroll;">
                    <el-row>
                        <el-col :span="24" v-for="(item,index) in props.teamAgentSelectedList" style="text-align: center;margin-top: 20px;display:flex;align-items:center;justify-content:center;">
                            <div style="width: 80px;height:80px;display:flex;align-items:center;justify-content:center;flex-direction: column;" 
                            :class="nowSayRole == item.agentId?'say':''">
                                <img :src="baseUrl+item.agentIconUrl" 
                                style="width:30px;border-radius: 10px;background-color: #dfdfff;"/>
                                <div style="font-size: 12px;color:#909399;margin-top: 5px;line-height: 14px;">{{item.agentName}}</div>
                            </div>
                        </el-col>
                    </el-row>
                </el-col>
            </el-row>
        </template>
        <template v-else>
            <div class="empty-url">
                <div style="width: 100vw;">请先配置chatUrl</div>
            </div>
        </template>
    </div>
</template>
<script setup>
import { MessageItemManager } from '@/types/chat-types';
import ChatRecords from './records';
import MessageCtrlByWS from './messageCtrlByWS';
import { defineProps } from 'vue';

const baseUrl = import.meta.env.VITE_APP_BASE_API;

const messageCtrl = ref(null);

const props = defineProps({
    messageItemManager: {
        type: Object,
        required: false,
        default: new MessageItemManager()
    },
    chatUrl:{
        type:String,
        required:false,
        default:''
    },
    isDisabledSendSet:{
        type:Boolean,
        required: false,
        default:true
    },
    teamAgentSelectedList:{
        type:Object,
        required: false,
        default:[]
    },
});

const initMessage = ref({})

const initChat = (initMessage1) => {
    props.messageItemManager.cleanHistory();
    // messageCtrl.value.clearTasks();
    initMessage.value = initMessage1;
    messageCtrl.value.initConnect().then(() => {
        messageCtrl.value.customSend(initMessage.value,false)
    })
}

const clearTimeres = () => {
    messageCtrl.value.clearTimeres();
}

//监听谁在说话
let nowSayRole = ref({
    key:''
})
watch(props.messageItemManager.nowMessageRole,(newVal,oldVal)=>{
    nowSayRole.value = newVal.key
})


defineExpose({
    initChat,
    clearTimeres
})

</script>
<style lang="scss" scoped>
.chat-bot-box {
    height: 100%;
    position: relative;
    .input-send {
        // position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 6%;
        max-height: 6%;
        align-items: center;
        justify-content: space-between;
    }
    .chat-records-box {
        width: 100%;
        height: 65vh;
        max-height: 90%;
        // overflow-y: scroll;
        // padding: 10px 0 0 0;
    }
    .empty-url{
        height: 100%;
        width: 100%;
        background-color: #FEF0F0;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-self: center;
        text-align: center;
        color: #F67E7E;
    }
    ::-webkit-scrollbar {
        width: 6px;
        height: 6px;
    }

    ::-webkit-scrollbar-thumb {
        border-radius: 3px;
        background-color: rgba(0, 0, 0, 0.2);
    }

    ::-webkit-scrollbar-track {
        border-radius: 3px;
        background-color: rgba(0, 0, 0, 0.1);
    }
}
.say{
    border-color: #5050E6;
    box-shadow: 0 0 10px rgba(80,80,230,.6), inset 0 0 10px rgba(80,80,230,.4), 0 1px 0 #ffffff;
}
</style>
<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="申请用户" prop="userName">
        <el-input
          v-model="queryParams.userName"
          placeholder="请输入申请用户"
          clearable
          :maxlength="50"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="应用名称" prop="applicationName">
        <el-input
          v-model="queryParams.applicationName"
          placeholder="请输入应用名称"
          clearable
          :maxlength="50"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="应用类型" prop="applicationType">
        <el-select v-model="queryParams.applicationType"
          placeholder="请选择应用类型" clearable
          @change="handleQuery" style="width: 150px;">
          <el-option
            v-for="dict in applicationTypeOptions"
            :key="dict.dictValue"
            :label="dict.dictLabel"
            :value="dict.dictValue"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="审核状态" prop="approveStatus">
        <el-select v-model="queryParams.approveStatus"
          placeholder="请选择审核状态" clearable
          @change="handleQuery" style="width: 150px;">
          <el-option
            v-for="dict in app_apply_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="申请时间" prop="applyTime">
        <el-date-picker clearable
          @change="handleQuery"
          v-model="queryParams.applyTime"
          type="daterange"
          range-separator="至"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          value-format="YYYY-MM-DD">
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <!-- <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['application:apply:export']"
        >导出</el-button>
      </el-col> -->
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="applyList" @selection-change="handleSelectionChange">
      <!-- <el-table-column type="selection" width="55" align="center" /> -->
      <el-table-column label="申请用户" align="center" prop="userName" />
      <el-table-column label="应用名称" align="center" prop="applicationName" />
      <el-table-column label="应用类型" align="center" prop="applicationType" >
        <template #default="scope">
          {{ scope.row.applicationType == 'team'?'团队':'智能体' }}
        </template>
      </el-table-column>
      <el-table-column label="应用描述" align="center" prop="applicationDesc" show-overflow-tooltip />
      <el-table-column label="审批状态" align="center" prop="approveStatus">
            <template #default="scope">
               <dict-tag :options="app_apply_status" :value="scope.row.approveStatus" />
            </template>
         </el-table-column>
      <!-- <el-table-column label="审批理由" align="center" prop="approveReason" /> -->
      <el-table-column label="申请时间" align="center" prop="createdTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createdTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="审批人" align="center" prop="approveBy" />
      <el-table-column label="审批时间" align="center" prop="approveTime" width="180">
        <template #default="scope">
          <span>{{ scope.row.approveTime ? parseTime(scope.row.approveTime, '{y}-{m}-{d}') : '' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" v-if="scope.row.approveStatus == 2"
            @click="handleUpdate(scope.row)" v-hasPermi="['application:apply:edit']">审批</el-button>
          <el-button link type="primary" icon="View" v-if="scope.row.approveStatus != 2"
            @click="handleCheck(scope.row)" v-hasPermi="['application:apply:edit']">查看</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改应用申请对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="applyRef" :model="form" :rules="rules" label-width="140px">
        <el-form-item label="申请用户：" prop="userName">
          {{ form.userName }}
        </el-form-item>
        <el-form-item label="申请应用名称：" prop="applicationName">
          {{ form.applicationName }}
        </el-form-item>
        <el-form-item label="应用类型：" prop="applicationType">
          {{ form.applicationType == 'team'?'团队':'智能体' }}
        </el-form-item>
        <el-form-item label="应用描述：" prop="applicationDesc">
          {{ form.applicationDesc }}
        </el-form-item>
        <el-form-item label="是否通过：" prop="approveStatus">
          <el-radio-group v-model="form.approveStatus" @click="approveStatusChange" v-show="handleMode == 'edit'">
            <el-radio :value="1">通过</el-radio>
            <el-radio :value="0">拒绝</el-radio>
          </el-radio-group>
          <div v-show="handleMode == 'check'">
            {{ form.approveStatus == 0 ? '拒绝' : '通过'}}
          </div>
        </el-form-item>
        <el-form-item label="拒绝理由：" prop="approveReason" 
          v-show="form.approveStatus == 0" 
          :required="form.approveStatus == 0">
          <el-input  v-model="form.approveReason" type="textarea"  :disabled="handleMode == 'check'"
            :rows="5" :maxlength="200" placeholder="请输入拒绝理由" />
        </el-form-item>
        <el-form-item label="审批人：" prop="approveBy">
          {{ form.approveBy || userStore.name }}
        </el-form-item>
        <el-form-item label="审批时间：" prop="approveTime">
          {{ form.approveTime ||  getCurrentDateTime()}}
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm" v-show="handleMode == 'edit'">确 定</el-button>
          <el-button @click="cancel" v-show="handleMode == 'edit'">取 消</el-button>
          <el-button @click="cancel" v-show="handleMode == 'check'">关 闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Apply">
import { listApply, getApply, approveApply } from "@/api/application/apply";
import useUserStore from "@/store/modules/user";

const userStore = useUserStore();
const { proxy } = getCurrentInstance();

const { app_apply_status } = proxy.useDict("app_apply_status");

const applyList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const handleMode = ref("")

const getCurrentDateTime = () => {
  return new Date().toLocaleString()
}


const applicationTypeOptions = ref([
  {dictValue: 'agent', dictLabel: '智能体'},
  {dictValue: 'team', dictLabel: '团队'}
])

const validateReason = (rule,value,callback) => {
  if(form.value.approveStatus == 0 && !value){
    return callback(new Error('请输入审批理由'));
  }else{
    callback();
  }
}

const approveStatusChange = () => {
  proxy.$refs["applyRef"].validateField('approveReason')
}

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    userName: null,
    applicationName: null,
    applicationType: null,
    approveStatus: null,
    applyTime: null,
  },
  rules: {
    approveReason:[{validator: validateReason, trigger: 'blur'}]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询应用申请列表 */
function getList() {
  loading.value = true;
  let query = { ...queryParams.value };
  query.startDate = null;
  query.endDate = null;
  if (query.applyTime) {
    query.startDate = query.applyTime[0]+ ' 00:00:00';
    query.endDate = query.applyTime[1]+ ' 23:59:59';
  }
  listApply(query).then(response => {
    applyList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    userId: null,
    userName: null,
    applicationId: null,
    applicationType: null,
    applicationDesc: null,
    approveStatus: null,
    approveReason: null,
    isDeleted: null,
    approveBy: null,
    approveTime: null,
    createdBy: null,
    createdTime: null,
    updatedBy: null,
    updatedTime: null
  };
  proxy.resetForm("applyRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _id = row.id || ids.value
  getApply(_id).then(response => {
    handleMode.value = 'edit'
    form.value = response.data;
    if(!form.value.approveReason){
      form.value.approveReason = ''
    }
    if(form.value.approveStatus == 2){
      form.value.approveStatus = 1
    }
    open.value = true;
    title.value = "审批";
  });
}

/** 修改按钮操作 */
function handleCheck(row) {
  reset();
  const _id = row.id || ids.value
  getApply(_id).then(response => {
    handleMode.value = 'check'
    form.value = response.data;
    if(!form.value.approveReason){
      form.value.approveReason = ''
    }
    if(form.value.approveStatus == 2){
      form.value.approveStatus = 1
    }
    open.value = true;
    title.value = "查看审批结果";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["applyRef"].validate(valid => {
    console.log(valid);
    if (valid) {
      const {id,approveStatus,approveReason} = form.value
      let postData = {
        id,
        approveStatus,
        approveReason
      }
      approveApply(postData).then(response => {
        proxy.$modal.msgSuccess("审批成功");
        open.value = false;
        getList();
      });
    }
  });
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('application/apply/export', {
    ...queryParams.value
  }, `apply_${new Date().getTime()}.xlsx`)
}

getList();
</script>

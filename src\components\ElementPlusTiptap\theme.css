.tiptap {
  text-wrap: wrap;
  word-break: break-word;
  font-size: 16px;
  line-height: 1.5;
  --tiptap-primary-color: teal;
}

.tiptap table {
  border-spacing: 0;
  width: auto;
  overflow: auto;
  word-break: normal;
  word-break: keep-all;
  border-radius: 3px;
  border-collapse: collapse;
  overflow: hidden;
}

.tiptap table p {
  margin-block-start: 0;
  margin-block-end: 0;
  margin: 0;
}

.tiptap table th {
  background-color: #f6f8fa;
  color: var(--tiptap-primary-color);
  font-weight: bold;
  text-align: left;
}

.tiptap td,
.tiptap th {
  padding: 0.5em 1em;
  border: 1px solid lightgray;
  height: 2em;
  min-width: 4em;
}

.tiptap td.selectedCell,
.tiptap th.selectedCell {
  background-color: #eaf3f3;
}

.tiptap.resize-cursor {
  cursor: ew-resize;
  cursor: col-resize;
}

.tiptap img {
  max-width: 100%;
}

.tiptap a {
  color: var(--tiptap-primary-color);
  font-weight: 600;
  text-decoration: none;
}

.tiptap a:hover {
  text-decoration: underline;
}

.tiptap a > code {
  color: #476582;
  text-decoration: none;
}

.tiptap p > code,
.tiptap li > code {
  color: saddlebrown;
  background-color: #f1f1f1;
  padding: 0 0.5em;
  border-radius: 4px;
}

.tiptap p {
  line-height: 1.6em;
  letter-spacing: 0.2px;
}

.tiptap strong {
  color: saddlebrown;
  font-weight: bold;
}

.tiptap mark {
  padding: 0.15em 0.5em;
}

.tiptap blockquote {
  padding: 20px;
  border-left: 4px solid var(--tiptap-primary-color);
  background-color: #f3f5f7;
  white-space: nowrap;
  color: #57606a;
}

.tiptap ul {
  margin: 0.5em 0;
  padding-left: 2em;
  list-style: disc;
}

.tiptap li + li {
  margin-top: 0.25em;
}

.tiptap ul.contains-task-list {
  list-style: none;
}
.tiptap .task-list-item-checkbox {
  vertical-align: middle;
  display: inline-block;
}

.tiptap ol {
  padding-left: 1.25em;
}

.tiptap h1 {
  margin: 1em 0 16px 0;
  font-size: 2.4em;
  line-height: 1.4em;
  font-weight: 600;
  padding-bottom: 0.7em;
  width: 100%;
}

.tiptap h2 {
  font-size: 1.5em;
  font-weight: 600;
  margin: 1em 0 16px 0;
  padding-bottom: 0.7em;
  width: 100%;
}

.tiptap h3 {
  font-size: 1.2em;
  font-weight: 600;
  margin: 1em 0 16px 0;
}

.tiptap h4 {
  font-size: 1em;
  margin: 1em 0 16px 0;
}

.tiptap h5 {
  font-size: 0.85em;
  margin: 1em 0 16px 0;
}

.tiptap h6 {
  font-size: 0.7em;
  margin: 1em 0 16px 0;
}

.tiptap hr {
  margin: 1.5em 0;
  width: 100%;
}

.tiptap .nothing {
  display: none;
}

.tiptap p,
.tiptap ul,
.tiptap ol,
.tiptap summary,
.tiptap table,
.tiptap pre,
.tiptap blockquote {
  margin-bottom: 1.2em;
}

.tiptap blockquote > p {
  text-wrap: wrap;
  margin-bottom: 0;
}

.tiptap pre {
  display: block;
  width: calc(100% - 10px);
  height: auto;
  position: relative;
  padding: 10px;
  background-color: black;
  border-radius: 5px;
  color: white;
}

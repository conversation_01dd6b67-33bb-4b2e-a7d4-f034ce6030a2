<template>
  <div>
    <el-dialog v-model="isVisible"
      title="上传资源文件"
      width="1200px"
      :before-close="close"
    >
      <p>已支持MP4、RMVB、MKV、AVI，PDF, DOCX, WPS每个文件不超过 100MB</p>
      <el-form ref="formBody" :model="form">
        <el-table
          :data="form.items"
          style="width: 100%"
          :row-style="rowStyle"
          :row-class-name="rowClass"
          :max-height="500"
        >
          <el-table-column prop="name" label="文件" width="180" class-name="file">
            <template #default="scope">
              <el-form-item
                v-if="scope.row.status === 'waiting'"
                :prop="`items.${scope.$index}.path`"
                :rules="rules.path"
                label=""
              >
                <el-input
                  v-model="scope.row.path"
                  type="hidden"
                  style="display: none"
                />
                <input
                  :id="scope.row.id"
                  type="file"
                  accept="image/*,video/*,audio/*,application/pdf,application/x-pdf,application/vnd.openxmlformats-officedocument.wordprocessingml.document,application/kswps"
                  style="display: none"
                  @change="selectFile(scope.row, $event)"
                >
                <label :for="scope.row.id">
                  <file-preview
                    v-if="scope.row.url"
                    :url="scope.row.url"
                    :mime="scope.row.mime"
                    height="60px"
                  />
                  <el-icon v-else><Plus /></el-icon>
                </label>
              </el-form-item>
              <template v-else>
                <file-preview
                  :url="scope.row.url"
                  :mime="scope.row.mime"
                  height="60px"
                />
              </template>
            </template>
          </el-table-column>
          <el-table-column prop="assort" label="资源名称" width="250">
            <template #default="scope">
              <el-form-item
                v-if="
                  scope.row.status === 'waiting' || scope.row.status === 'editing'
                "
                :prop="`items.${scope.$index}.name`"
                :rules="rules.name"
                label=""
              >
                <el-input v-model="scope.row.name" placeholder="请输入名称" />
              </el-form-item>
              <template v-else>
                {{ scope.row.name }}
              </template>
            </template>
          </el-table-column>
          <el-table-column prop="assort" label="资源分类" width="250">
            <template #default="scope">
              <el-form-item
                v-if="
                  scope.row.status === 'waiting' || scope.row.status === 'editing'
                "
                :prop="`items.${scope.$index}.classifyId`"
                :rules="rules.classifyId"
                label=""
              >
                <el-select v-model="scope.row.classifyId" placeholder="请选择分类">
                  <el-option
                    v-for="aitem in assortList"
                    :key="aitem.classifyId"
                    :label="aitem.classifyName"
                    :value="aitem.classifyId"
                  />
                </el-select>
              </el-form-item>
              <template v-else>
                {{ formatAssortName(scope.row.classifyId) }}
              </template>
            </template>
          </el-table-column>
          <el-table-column prop="desc" label="描述">
            <template #default="scope">
              <el-form-item
                v-if="
                  scope.row.status === 'waiting' || scope.row.status === 'editing'
                "
                :prop="`items.${scope.$index}.desc`"
                :rules="rules.desc"
                label=""
              >
                <el-input
                  v-model="scope.row.desc"
                  type="textarea"
                  :rows="2"
                  placeholder="请输入内容"
                />
              </el-form-item>
              <template v-else>
                {{ scope.row.desc }}
              </template>
            </template>
          </el-table-column>
          <el-table-column prop="operate" label="操作" width="100" v-if="isAppendable">
            <template #default="scope">
              <span v-if="scope.row.status === 'completed'" class="completed">
                <i class="el-icon-success" />
                完成
              </span>
              <el-button
                v-else-if="scope.row.status === 'waiting'"
                size="mini"
                type="text"
                icon="el-icon-delete"
                @click="removeRow(scope.row)"
              >删除</el-button>
              <el-button
                v-else-if="scope.row.status === 'uploading'"
                size="mini"
                type="text"
                icon="el-icon-loading"
                @click="stop(scope.row)"
              >停止上传</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-form>
      <div v-if="isAppendable" class="add-row">
        <el-button link :icon="Plus" @click="appendEmptyRow" class="plus-icon"></el-button>
      </div>
      <template #footer>
          <div style="display: flex;justify-content: center;">
            <el-button
              v-show="isSubmitBtnVisible"
              type="primary"
              :disabled="isSubmitBtnDisabled()"
              @click="submit"
            >提 交</el-button>
            <el-button @click="close">{{ areAllCompleted ? '退 出' : '取 消'}}</el-button>
          </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
  import FilePreview from '@/components/FilePreview';
  import {Plus} from '@element-plus/icons-vue'
  import { addResource,editResource } from '@/api/resourceManagement/resource'
  import { ElMessage, ElMessageBox } from 'element-plus'
  import { getClassifyList } from '@/api/resourceManagement/resource'

  const emit = defineEmits(['update']);

  const props = defineProps({
    /**
     * 分类列表
     */
    assortList: {
      type: Array,
      required: false,
      default:[]
    }
  }) 

  const assortList = ref([])

  //资源分类列表完整
  async function  getClassifyListAllMethod(){
    await getClassifyList().then(response => {
        assortList.value =  response.data
      })
  }

  /**
   * items: 列表, 为了能使用element的表单组件，放到form中
   *
   * items格式:
   * [
   *   {
   *      id: 3,                  // 资源ID，如果是待上传的，则是temp1, temp2之类的临时id，
   *      file: xxxx,             // 待上传的文件的File对像
   *      name: 'robot',          // 待上传的文件的名称
   *      mime: 'image/png',      // 待上传的文件mime
   *      path: 'xxxxx/xxx/x.mp4',// 待上传的文件路径
   *      url: '',                // 上传完成后，生成的文件URL
   *      desc: '机器人3号视频',   // 描述
   *      classifyId: 3,            // 分类ID
   *      status: 'waiting',      // 状态, 字符串枚举值: waiting等待上传中 uploading上传中 uploaded上传完毕 saving保存数据中(上传之后的保存或编辑后的保存) completed保存完成 editing编辑(修改一条已经上传并保存完成的记录, 修改一条已上传但添加失败的记录)
   *      progress: 30            // 上传的百分比进度，30表示已上传30%
   *   },
   * ]
   */
  const form = ref({
    items: []
  })
  /**
   * 最大并行上传/保存数量
   */
  const maxParalNum = ref(1)
  /**
   * 队列, 值form.items中的元素的传址
   * 改变queue中某个元素中的值，则form.items中的也相应变化
   */
  const queue = ref([])
  /**
   * 弹窗是否可见
   */
  const isVisible = ref(false)
  /**
   * 是否可添加
   */
   const isAppendable = ref(true)
  /**
   * 计数器，用于生成文件input的id
   */
  const counter = ref(0)
  /**
   * SAS令牌
   */
  const SASToken = ref('')
  /**
   * 校验规则
   */
  const rules = ref({
    path: [{ required: true, message: '请选择文件', trigger: 'change' }],
    name: [
      { required: true, message: '请输入资源名称', trigger: 'change' },
      {
        max: 100,
        message: '资源名称长度不能超过100个字符',
        trigger: 'blur'
      }
    ],
    desc: [
      {
        max: 1000,
        message: '资源描述长度不能超过1000个字符',
        trigger: 'blur'
      }
    ],
    classifyId: [
      { required: true, message: '请选择资源分类', trigger: 'change' }
    ]
  })

  /**
   * 分类map, key是分类ID，value是接口中获取的分类信息
   *
   * 格式: {
   *    3: {
   *      classifyId: 3,
   *      classifyName: '机器人',
   *      assortDesc: '机器人PDF'
   *    }
   * }
   */
  const formatAssortName = (classifyId) => {
    if(assortList.value){
        for (let i = 0; i < assortList.value.length; i++) {
            if (assortList.value[i].classifyId === classifyId) {
              return assortList.value[i].classifyName;
            }
        }
    }
  }

  /**
   * 提交按钮是否置灰
   */
  const isSubmitBtnDisabled = () => {
    // 有正在上传/保存时，置灰，并退出
    for (const item of form.value.items) {
      if (item.status === 'uploading' || item.status === 'saving') {
        return true;
      }
    }
    // 有待上传或待保存时，取消置灰，并退出
    for (const item of form.value.items) {
      if (item.status === 'waiting' || item.status === 'editing') {
        return false;
      }
    }

    return true;
  }

  /**
   * 提交按钮是否可见
   */
  const isSubmitBtnVisible = () => {
    if (form.value.items.length === 0) {
      return false;
    }

    if (areAllCompleted()) {
      return false;
    }

    return true;
  }

  /**
   * 是否所有任务已完成
   */
  const areAllCompleted = () => {
    if (form.value.items.length === 0) {
      return false;
    }

    const uncompletedItemIdx = form.value.items.findIndex((item) => {
      return item.status !== 'completed';
    })

    return uncompletedItemIdx < 0;
  }
  
  /**
   * 生成一个唯一id
   */
  const tempId = () => {
    counter.value += 1;
    return `temp${counter.value}`;
  }

  /**
   * 添加空行
   */
  const appendEmptyRow = () => {
    if(!assortList.value){
      assortList.value = []
    }
    let classifyId =
    assortList.value.length > 0 ? assortList.value[0].classifyId : '';
    const lastRow = form.value.items.length > 0
        ? form.value.items[form.value.items.length - 1]
        : null;
    if (lastRow && lastRow.classifyId) {
      classifyId = lastRow.classifyId;
    }

    form.value.items.push({
      id: tempId(),
      file: null,
      mime: '',
      url: '',
      name: '',
      path: '',
      size: 0,
      classifyId: classifyId,
      desc: '',
      status: 'waiting',
      progress: 0
    });
  }

  /**
   * 删除一行
   */ 
  const removeRow = (row) => {
    for (let idx = 0; idx < form.value.items.length; idx++) {
      if (form.value.items[idx].id === row.id) {
        form.value.items.splice(idx, 1);
        return;
      }
    }
  }

  const formBody = ref(null)
  /**
   * 关闭
   */
  const close = () => {
    for (let i = 0; i < form.value.items.length; i++) {
      const status = form.value.items[i].status;
      if (status === 'uploading') {
        ElMessage.warning('正在上传中，请稍候关闭。')
        return;
      }
    }

    for (let i = 0; i < form.value.items.length; i++) {
      const status = form.value.items[i].status;
      if (status === 'saving') {
        ElMessage.warning('正在上传中，请稍候关闭。')
        return;
      }
    }

    emit('refresh');
    isVisible.value = false;
    form.value.items = [];
    formBody.value.clearValidate();
  }

  /**
   * 打开上传窗口
   */
  const openAdd = async () => {
    form.value.items = [];
    await getClassifyListAllMethod()
      appendEmptyRow();
      isAppendable.value = true;
      isVisible.value = true;
  }

  /**
   * 打开编辑窗口
   */
  const openEdit = (item) => {
    const emptyItem = {
      id: '',
      file: null,
      mime: '',
      url: '',
      name: '',
      path: '',
      size: 0,
      classifyId: '',
      desc: '',
      status: 'waiting',
      progress: 0
    };
    const nitem = Object.assign({}, emptyItem, item);
    nitem.status = 'editing';

    form.value.items = [];
    form.value.items.push(nitem);
    isAppendable.value = false;
    getClassifyListAllMethod()
    isVisible.value = true;
  }

  /**
   * 选中文件
   */
  const selectFile = (item, event) => {
    const file = event.target.files[0];
    if(file.size>104857600){
      ElMessage.warning('抱歉，文件大小不能超过100M，请选择其他文件')
      return
    }
    item.file = file;
    item.mime = file.type;
    item.name = file.name.replace(/\.[^.]*$/, '').replace(/-/g, '');
    item.size = file.size;
    item.url = URL.createObjectURL(item.file);

    const now = new Date();
    const year = now.getFullYear();
    const month = ('0' + (now.getMonth() + 1).toString()).slice(-2);
    const path = `resources/${year}${month}/${file.name.replace(/-/g, '')}`;
    item.path = path;
  }
  
  /**
   * 行样式, 使用背景色显示进度
   */
  const rowStyle = ({ row, rowIdx }) => {
    if (row.status !== 'uploading') {
      return '';
    }

    return {
      '--progress': row.progress + '%'
    };
  }

  /**
   * 行样式
   */
  const rowClass = ({ row, rowIdx }) => {
    return row.status;
  }

  /**
   * 停止上传
   */
  const stop = (item) => {
    if (item.status !== 'uploading') {
      return;
    }

    if (item.abortController) {
      item.abortController.abort();
    }

    dequeue(item);
    item.status = 'waiting';
  }

  /**
   * 保存
   */
  async function save(item){
    if (item.status !== 'uploaded' && item.status !== 'editing') {
      return;
    }

    item.status = 'saving';
    //添加
    if (typeof item.id === 'string' && item.id.match(/^temp/)) {
      try {
        const formData = new FormData();
        formData.append('resourceName',item.name)
        formData.append('file',item.file)
        formData.append('resourceDesc',item.desc)
        formData.append('classifyId',item.classifyId)
        formData.append('source','system')
        await addResource(formData)
          item.status = 'completed';
      } catch (e) {
        console.log('e');
        item.status = 'editing';
      }
      // 修改
    } else {
      try {
        let data = {
          resourceId:item.id,
          resourceName:item.name,
          resourceDesc:item.desc,
          classifyId:item.classifyId,
        }
        await editResource(data);
        item.status = 'completed';
        close();
        ElMessage.success('修改成功')
      } catch (e) {
        item.status = 'editing';
      }
    }
  }

  /**
   * 加入队列
   */
  const enqueue = (item) => {
    queue.value.push(item);
  }

  /**
   * 从队列中移除
   */
  const dequeue = (item) => {
    for (let idx = 0; idx < queue.value.length; idx++) {
      if (queue.value[idx].id === item.id) {
        queue.value.splice(idx, 1);
        return;
      }
    }
  }

  /**
   * 从队列中取出一项，执行上传/保存任务
   */
  async function doTask(){
    // 计算当前正在上传/保存的数量, 超过最大并行数则退出
    let curParalNum = 0;
    for (const item of queue.value) {
      if (item.status === 'uploading' || item.status === 'saving') {
        curParalNum += 1;
      }
    }
    if (curParalNum >= maxParalNum.value) {
      return;
    }

    // 上传/保存
    for (const item of queue.value) {
      if (item.status === 'waiting') {
        item.status = 'uploaded';
        setTimeout(() => {
          save(item).then(() => {
            dequeue(item);
            doTask();
          });
        },1000)
        break;
      } else if (item.status === 'editing' || item.status === 'uploaded') {
        setTimeout(() => {
          save(item).then(() => {
            dequeue(item);
            doTask();
          });
        },1000)
        break;
      }
    }
  }

  /**
   * 提交
   */
  async function submit(){
    // 校验表单
    try {
      await formBody.value.validate();
    } catch (e) {
      return;
    }
    // 将待上传，或编辑中的，加入队列
    for (const item of form.value.items) {
      if (item.status === 'failed') {
        item.status = 'uploaded';
      }
      if (item.status === 'waiting' || item.status === 'uploaded' || item.status === 'editing') {
        enqueue(item);
      }
    }

    doTask();
  }

  defineExpose({
    openAdd,
    openEdit
  });
</script>

<style scoped lang="scss">
  .upload-text {
    color: #1890ff;
    justify-content: center;
    align-items: right;
  }
  textarea {
    background: none;
    outline: none;
    border: none;
    overflow-x: hidden;
    overflow-y: auto;
    max-height: 300px;
    resize: none;
    width: 100%;
    padding-top: 10px;
  }
  .add-row {
    text-align: center;
    background-color: #f8f8f9;
    border: #dfe6ec 1px solid;
    /* margin-top: 10px; */
  }
  .plus-icon {
    width: 30px;
    height: 30px;
  }
  .add-row span {
    cursor: pointer;
    font-size: 20px;
    /* color: #409eff; */
    display: inline-block;
  }

  .add-row span:hover {
    text-decoration: underline;
  }
  .upload-demo {
    display: inline-block;
    margin-right: 10px;
  }

  .dialog::v-deep .el-dialog__body {
    padding: 0px 30px 50px 30px;
  }

  .completed {
    color: green;
  }

  ::v-deep label {
    cursor: pointer;
  }

  ::v-deep td {
    height: 40px;
  }

  ::v-deep tr.uploading:hover td {
    background-color: unset !important;
  }
  ::v-deep tr.uploading {
    background-size: var(--progress) 100%;
    background-image: linear-gradient(90deg, skyblue 0%, skyblue 100%) !important;
    background-repeat: no-repeat;
  }

  ::v-deep .el-dialog:not(.is-fullscreen) {
    margin-top: 0vh !important;
  }
</style>

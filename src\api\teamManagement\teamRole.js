import request from '@/utils/request'

//获取已授权的角色列表
export function getRoleList(params) {
    return request({
        url: '/system/role/application/roleList',
        method: 'get',
        params
    })
}

//获取未授权的角色列表
export function getUnAuthRoleList(params) {
    return request({
        url: '/system/role/application/unallocatedroleList',
        method: 'get',
        params
    })
}

//取消角色授权应用
export function authTeamCancelRole(query) {
    return request({
        url: '/system/role/application/cancel',
        method: 'put',
        data: query
    })
}

//批量取消角色授权应用
export function authTeamAllCancelRole(query) {
    return request({
        url: '/system/role/application/cancelAll',
        method: 'put',
        data: query
    })
}

//批量给角色授权
export function authTeamAllRole(data) {
    return request({
        url: '/system/role/application/selectRole',
        method: 'put',
        data
    })
}
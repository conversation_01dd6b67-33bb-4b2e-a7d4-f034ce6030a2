<template>
  <el-dialog
    :close-on-click-modal="false"
    :model-value="ishumanInput"
    :show-close="false"
    width="364px"
    @close="handleClose"
    style="
      width: 500px;
      min-height: 220px;
      background-color: #f5f7fd;
      border-radius: 8px !important;
    "
  >
    <template #title>
      <div class="titlebk">
        <div class="titletxt">{{ "确认竞品" }}</div>
      </div>
    </template>
    <div class="formBox">
      <el-form
        ref="formRef"
        style="max-width: 600px"
        :model="dynamicValidateForm"
        label-width="auto"
        class="demo-dynamic"
      >
        <el-form-item prop="type">
          <!-- :rules="[
            {
              required: true,
              message: 'Please input email address',
              trigger: 'blur',
            },
          ]" -->
          <el-checkbox-group v-model="dynamicValidateForm.type">
            <el-checkbox
              :value="v"
              :name="v"
              v-for="(v, i) in top5Options"
              :key="i"
            >
              {{ v }}
            </el-checkbox>
          </el-checkbox-group>
        </el-form-item>

        <el-checkbox v-model="custom">自定义竞品</el-checkbox>

        <div class="customBox" v-if="custom">
          <!-- :rules="{
              required: true,
              message: '竞品名称不能为空',
              trigger: 'blur',
            }" -->
          <el-form-item
            v-for="(domain, index) in dynamicValidateForm.domains"
            :key="domain.key"
            :prop="'domains.' + index + '.value'"
          >
            <el-input
              v-model="domain.value"
              placeholder="请输入品牌名称"
              style="width: 75%"
            />
            <div class="handleIcon" @click.prevent="addDomain">
              <i class="ri-add-line"></i>
            </div>
            <div
              class="handleIcon"
              @click.prevent="removeDomain(domain)"
              v-show="index > 0"
            >
              <i class="ri-subtract-line"></i>
            </div>

            <!-- <el-button class="mt-2" @click.prevent="removeDomain(domain)">
            Delete
          </el-button> -->
          </el-form-item>
        </div>
      </el-form>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button class="delete-btn" type="primary" @click="handleConfirm"
          >确 定</el-button
        >
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, watch, onMounted } from "vue";
import { useRouter } from "vue-router";
import { ElMessage } from "element-plus";
import {
  ishumanInput,
  competitorParams,
  top5Options,
} from "@/views/briefGeneration/add/create/js/sharedState.js";
import { addCompetitor } from "@/api/briefGeneration/index.js";
const router = useRouter();

const dynamicValidateForm = reactive({
  type: [],
  domains: [
    {
      key: 1,
      value: "",
    },
  ],
});
const custom = ref(false);
// 监听 visible 的变化
watch(
  () => ishumanInput.value,
  (newVisible) => {
    if (!newVisible) {
      dynamicValidateForm.type = [];
      dynamicValidateForm.domains = [{ key: 1, value: "" }];
    } else {
    }
  },
  { immediate: true }
);
watch(
  () => top5Options.value,
  (newValue) => {
    dynamicValidateForm.type = newValue;
  },
  { immediate: true }
);
watch(
  () => custom.value,
  (newValue) => {
    if (newValue) {
      dynamicValidateForm.domains = [
        {
          key: 1,
          value: "",
        },
      ];
    }
  },
  { immediate: true }
);
const removeDomain = (item) => {
  const index = dynamicValidateForm.domains.indexOf(item);
  if (index !== -1) {
    dynamicValidateForm.domains.splice(index, 1);
  }
};

const addDomain = () => {
  dynamicValidateForm.domains.push({
    key: Date.now(),
    value: "",
  });
};
const handleClose = () => {
  ishumanInput.value = false;
};

const handleConfirm = async () => {
  try {
    // 校验是否有选择竞品或自定义竞品
    const hasType =
      dynamicValidateForm.type && dynamicValidateForm.type.length > 0;
    const hasCustom =
      custom.value &&
      dynamicValidateForm.domains.some((d) => d.value.trim() !== "");

    if (!hasType && !hasCustom) {
      ElMessage.warning("请至少选择或填写一个竞品！");
      return;
    }
    // 组装 variable_value
    const domainArr = custom.value
      ? dynamicValidateForm.domains.map((d) => d.value.trim()).filter((v) => v)
      : [];
    const variable_value = [
      ...(dynamicValidateForm.type || []),
      ...domainArr,
    ].join(";");
    let res = await addCompetitor({
      ...competitorParams.value,
      variable_value,
      app_id: "cf2250e6-b96f-4b63-9010-1ed258c8ec02",
    });
    if (res.message == "success") {
      ishumanInput.value = false;
    }
  } catch (e) {
    console.error(e);
  }
};
onMounted(() => {});
</script>

<style lang="scss" scoped>
.titlebk {
  width: 100%;
  height: 24px;
  margin-top: 8px;
  display: flex;
  align-items: center;
  justify-content: space-between;

  .titletxt {
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 16px;
    color: #1e1f24;
    line-height: 16px;
  }
}
.formBox {
  .el-checkbox-group {
    display: flex;
    flex-direction: column;
  }
  .el-form-item {
    margin-bottom: 0;
  }
}
.customBox {
  .el-form-item__content {
    display: flex;
    align-items: center;
  }
  .el-form-item--default {
    margin-bottom: 0;
  }
  .handleIcon {
    margin-left: 10px;
    cursor: pointer;
    font-size: 16px;
    padding: 4px;
  }
}
</style>
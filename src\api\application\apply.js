import request from '@/utils/request'

// 查询应用申请列表
export function listApply(query) {
  return request({
    url: '/application/apply/list',
    method: 'get',
    params: query
  })
}

// 查询应用申请详细
export function getApply(id) {
  return request({
    url: '/application/apply/' + id,
    method: 'get'
  })
}

// 新增应用申请
export function addApply(data) {
  return request({
    url: '/application/apply',
    method: 'post',
    data: data
  })
}

// 修改应用申请
export function updateApply(data) {
  return request({
    url: '/application/apply',
    method: 'put',
    data: data
  })
}

// 删除应用申请
export function delApply(id) {
  return request({
    url: '/application/apply/' + id,
    method: 'delete'
  })
}


// 审核通过/不通过
export function approveApply(data) { 
  return request({ 
    url: '/application/apply/approve', 
    method: 'post', 
    data 
  }) 
}

<template>
  <div class="tabBox">
    <div
      :class="{ tabItem: true, active: configData.version == v.value }"
      v-for="(v, i) in options"
      :key="i"
      @click="
        () => {
          selectTab(v);
        }
      "
    >
      {{ v.label }}
    </div>
  </div>
  <div class="ask-container">
    <textarea
      class="input-area"
      v-model="configData.brandName"
      :placeholder="placeholder"
      @keydown.enter="handleEnter"
      :maxlength="maxChars > 0 ? maxChars : undefined"
    ></textarea>
    <div class="settings-area">
      <div class="handleBox">
        <div class="switch-wrapper">
          <el-switch v-model="configData.dataBank" class="switch" />资料库
        </div>
        <div class="switch-wrapper">
          <el-switch v-model="configData.internet" class="switch" />联网搜索
        </div>
      </div>
      <div v-if="showEmitBtn">
        <!-- <el-button
          class="send-btn"
          circle
          @click="ask"
          :disabled="!configData.brandName.trim()"
        >
          <i
            class="ri-arrow-right-line"
            style="font-size: 24px; color: #fff"
          ></i>
        </el-button> -->
        <button
          :class="[
            'send-btn',
            {
              'is-disabled': !configData.brandName?.trim(),
            },
          ]"
          @click="ask"
          :disabled="!configData.brandName?.trim()"
        >
          <inline-svg src="/icons/send.svg" />
          发送
        </button>
      </div>
    </div>
  </div>
</template>
  
  <script setup>
import { onMounted, ref } from "vue";
import InlineSvg from "vue-inline-svg";
import { useRouter } from "vue-router";
import { configData } from "@/views/briefGeneration/add/create/js/sharedState.js";

const router = useRouter();
const props = defineProps({
  maxChars: {
    type: Number,
    required: false,
    default: 0,
  },
  showEmitBtn: {
    type: Boolean,
    required: false,
    default: false,
  },
  placeholder: {
    type: String,
    required: false,
    default: "请输入您想搜索的问题或需求",
  },
});

const options = ref([
  { value: 1, label: "简报研究1.0" },
  // { value: 2, label: "简报研究2.0" },
  // { value: 3, label: "简报研究3.0" },
]);

const emit = defineEmits(["ask", "stop"]);
const selectTab = (item) => {
  configData.value.version = item.value;
};

const handleEnter = (event) => {
  if (event.ctrlKey) {
    const start = event.target.selectionStart;
    const end = event.target.selectionEnd;
    event.target.value =
      event.target.value.substring(0, start) +
      "\n" +
      event.target.value.substring(end);
    event.target.selectionStart = event.target.selectionEnd = start + 1;
    return;
  }
  event.preventDefault();
  ask();
};
function ask() {
  if (configData.value.brandName && configData.value.brandName.trim()) {
    localStorage.setItem("configData", JSON.stringify(configData.value));
    router.push(`/briefGeneration/add/create/add`);
  }
}
</script>
  
<style lang="scss" scoped>
.ask-container {
  width: 100%;
  height: 169px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  border-radius: 10px;
  background-color: rgba(43, 77, 122, 0.73);
  border: 2px solid #63e5fe;
  box-shadow: 0 0 11px 3px #5bc4da;
}
.tabBox {
  display: flex;
  margin-bottom: 18px;
  .tabItem {
    line-height: 20px;
    border-radius: 8px;
    background-color: rgba(5, 30, 70, 0.6);
    color: rgba(158, 158, 158, 1);
    font-size: 14px;
    text-align: center;
    box-shadow: 1px 1px 4px 1px rgba(0, 229, 255, 0.3);
    font-family: PingFangSC-regular;
    border: 1px solid rgba(0, 229, 255, 1);
    cursor: pointer;
    padding: 7px 12px;
    margin-right: 14px;
  }
  .active {
    opacity: 1;
    color: #fff;
  }
}
.input-area {
  height: 100px;
  width: 100%;
  padding: 0 12px;
  margin-top: 16px;
  box-sizing: border-box;
  border: 0;
  border-radius: 0;
  resize: none;
  background-color: transparent;
  color: #c7c7c7;
}

.input-area:focus {
  outline: 0;
}
.input-area::placeholder {
  font-size: 10px;
  color: #c7c7c7;
}
.settings-area {
  height: 42px;
  width: calc(100% - 12px);
  position: relative;
  left: 6px;
  flex: none;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 0;
  border-top: 1px solid #63e5fe;

  .handleBox {
    display: flex;
    align-items: center;
    .switch-wrapper {
      display: flex;
      column-gap: 3px;
      line-height: 20px;
      color: #c7c7c7;
      font-size: 14px;
      display: flex;
      align-items: center;
      user-select: none;
      margin-right: 15px;

      .switch {
        --el-switch-on-color: #50affe;
        --el-switch-off-color: #45829b;
        margin-right: 5px;
      }
    }
  }
}

.send-btn {
  padding: 0;
  color: white;
  font-size: 12px;
  position: relative;
  cursor: pointer;
  width: 54px;
  height: 21px;
  background: #4ba1ee;
  border-radius: 10px;
  border: 1px solid #63e5fe;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;

  svg {
    color: #253f7f;
    width: 13px;
    height: 13px;
    margin-right: 3px;
  }
}

.send-btn:hover {
  color: white;
  background: #2d74b1;
}

.send-btn.is-disabled {
  color: rgb(76, 74, 74);
  cursor: not-allowed;
  box-shadow: 0px 1px 0px 0px #015e28;
}
</style>
  
<style lang="scss">
.ask-type-popper {
  width: 161px;
}
</style>
  
<template>
  <div class="link-wrapper">
    <div
      v-for="(item, idx) of items"
      :key="item.url + idx"
      class="link"
      target="_blank"
      style="cursor: pointer"
      @click="itemClick(item)"
    >
      {{ item }}
      <inline-svg src="/icons/arrow-right.svg" />
    </div>
  </div>
</template>

<script>
import InlineSvg from 'vue-inline-svg'
import { linkTxt, linkRequest } from '@/views/briefGeneration/chat/js/index'
export default {
  components: { InlineSvg },
  props: {
    items: {
      type: Array,
      required: true,
      default: () => []
    }
  },
  methods: {
    itemClick (item) {
      if (item) {
        linkTxt.value = item
        linkRequest.value = true
      }
    }
  }
}
</script>


<style lang="scss" scoped>
.link-wrapper {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;

  .link {
    display: inline-flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
    width: auto;
    height: 32px;
    padding: 5px 12px;
    background: rgba(0, 0, 0, 0.06);
    border-radius: 8px;
    color: #1e1f24;
    margin-top: 8px;
    font-size: 14px;

    &:hover {
      background: rgba(0, 0, 0, 0.12);
    }

    svg {
      width: 14px;
      height: 14px;
      margin-left: 6px;
    }
  }
}
</style>
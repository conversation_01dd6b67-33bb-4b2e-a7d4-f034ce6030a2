<template>
    <div>
        <div class="common-container">
            <div class="common-header">
                <div class="title">知识库列表</div>
                <div class="header-right">
                    <div style="margin: 0 20px;">
                        <el-input
                            v-model="queryParams.keyword"
                            class="w-50 m-2"
                            placeholder="搜索"
                            :prefix-icon="Search"
                            @keyup.enter="handleQuery"
                            clearable
                        />
                    </div>
                    <el-button type="primary" style="color: white;font-size: 14px;font-weight: 500;" @click="createknowledgeBase()">新建知识库</el-button>
                </div>
            </div>
            <div v-loading="loading" class="common-content" v-if="knowledgeBaseList && knowledgeBaseList.length > 0">
                <el-row>
                    <el-col :span="8" v-for="(item,index) in knowledgeBaseList" :key="index"  style="margin-bottom: 20px;">
                    <!--item.description-->
                        <custom-card :img="knowledgeBaseIcon" :title="item.name" :detail="item.description" :is-need-footer="false" 
                        :jump-url="'/knowledgeBase/documentManagement?id='+item.id">
                            <template #tool>
                                <el-dropdown :hide-on-click="false">
                                    <el-button text :icon="More" @click.stop></el-button>
                                    <template #dropdown>
                                    <el-dropdown-menu>
                                        <el-dropdown-item @click="editKnowledgeBaseMethod(item)">编辑</el-dropdown-item>
                                        <el-dropdown-item @click="deleteKnowledgeBase(item)" style="color:red">删除</el-dropdown-item>
                                    </el-dropdown-menu>
                                    </template>
                                </el-dropdown>
                                <!-- <el-button text :icon="Tools" @click="settingKnowledgeBase(item)"></el-button> -->
                                <!-- <el-button text :icon="Edit" @click.stop="editKnowledgeBaseMethod(item)"></el-button>
                                <el-button text :icon="Delete" @click.stop="deleteKnowledgeBase(item)"></el-button> -->
                            </template>
                        </custom-card>
                    </el-col>
                </el-row>
                <pagination
                v-show="total > 0"
                :total="total"
                v-model:page="queryParams.page"
                v-model:limit="queryParams.limit"
                layout="prev, pager, next"
                @pagination="getknowledgeBaseListMethod"
                style="margin-right:20px"
                />
            </div>
            <!--无数据-->
            <div class="common-content" style="display: flex;flex-direction: column;align-items: center;justify-content: center;" v-else>
                <img :src="noData" style="height: 100px;width: 100px;"/>
                <div style="color:#dbdbdb;font-weight:550">
                    暂无数据
                </div>
            </div>
        </div>
        <!--新建、编辑知识库弹框-->
        <el-dialog v-model="knowledgeBaseVisible" :title="knowledgeBaseTitle">
            <div style="padding:0 20px">
                <el-form ref="knowledgeBaseInfoForm" :model="knowledgeBaseInfo" :rules="rules" label-width="120px"  :label-position="'top'" :validate-on-rule-change="false">
                    <!-- <div style="text-align:center">
                        <Image-Upload-Min v-model="knowledgeBaseInfo.imageUrl" :limit="1" ></Image-Upload-Min>
                    </div> -->
                    <el-row>
                        <el-col>
                            <el-form-item label="知识库名称" prop="name">
                                <el-input v-model="knowledgeBaseInfo.name" placeholder="请输入知识库名称" :maxlength="40"/>
                            </el-form-item>
                        </el-col>
                        <el-col v-if="knowledgeBaseInfo.id" class="visible-permissions">
                            <el-form-item label="权限设置" prop="permission" >
                                <el-row style="width: 100%;" >
                                    <el-col :span="12" style="padding-right:10px">
                                        <div class="radio" @click="knowledgeBaseInfo.permission='only_me'" 
                                        :class="knowledgeBaseInfo.permission=='only_me'?'radio-sel':''">
                                            <el-row>
                                                <el-col :span="5" class="image-box">
                                                    <img :src="onlyme" style="background-color:#52acff54;" />
                                                </el-col>
                                                <el-col :span="16">
                                                    <span>只有我</span>
                                                </el-col>
                                                <el-col :span="3" style="text-align: end;">
                                                    <el-radio-group v-model="knowledgeBaseInfo.permission">
                                                        <el-radio :label="'only_me'">{{ '' }}</el-radio>
                                                    </el-radio-group>
                                                </el-col>
                                            </el-row>
                                        </div>
                                    </el-col>
                                    <el-col :span="12" style="padding-left:10px">
                                        <div class="radio" @click="knowledgeBaseInfo.permission='all_team_members'"
                                        :class="knowledgeBaseInfo.permission=='all_team_members'?'radio-sel':''">
                                            <el-row>
                                                <el-col :span="5" class="image-box">
                                                    <img :src="team" style="background-color:#52acff54;" />
                                                </el-col>
                                                <el-col :span="16">
                                                    <span>租户内用户</span>
                                                </el-col>
                                                <el-col :span="3" style="text-align: end;">
                                                    <el-radio-group v-model="knowledgeBaseInfo.permission">
                                                        <el-radio :label="'all_team_members'">{{ '' }}</el-radio>
                                                    </el-radio-group>
                                                </el-col>
                                            </el-row>
                                        </div>
                                    </el-col>
                                </el-row>
                            </el-form-item>
                        </el-col>
                        <el-col v-if="knowledgeBaseInfo.id">
                            <el-form-item label="知识库概述" prop="description">
                                <el-input v-model="knowledgeBaseInfo.description" placeholder="请输入知识库概述" type="textarea" rows="7" :maxlength="400" />
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
            </div>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="cancel">取消</el-button>
                    <el-button type="primary" @click="submitKnowledgeBase">保存</el-button>
                </span>
            </template>
        </el-dialog>
    </div>
</template>
<script setup>
import { ref, reactive, toRefs } from 'vue'
import { Tools,Edit,Delete,Star,StarFilled,Search, Top,More } from '@element-plus/icons-vue'
import CustomCard from '@/components/CustomCard/index'
import router from "@/router";
import { getKnowledgeBaseList,addKnowledgeBase,editKnowledgeBase,delKnowledgeBase } from "@/api/knowledgeBase/knowledgeBase";
import knowledgeBaseIcon from "@/assets/icons/svg/knowledgeBaseIcon.svg";
import onlyme from "@/assets/icons/svg/onlyme.svg";
import team from "@/assets/icons/svg/team.svg";
import noData from "@/assets/icons/svg/no-data.svg";

const route = useRoute();
const { proxy } = getCurrentInstance();
const baseUrl = import.meta.env.VITE_APP_BASE_API;
const loading = ref(false)
//搜索
const queryParams = ref({
    keyword:'',
    page: 1,
    limit: 12,
});

const total = ref(0);
//编辑、新增知识库弹框开关
const knowledgeBaseVisible = ref(false);
//编辑、新增知识库弹框标题
const knowledgeBaseTitle = ref('');
//知识库信息
const knowledgeBaseInfo = ref({
    // imageUrl:'',
    name:'',
    description:''
});
//规则校验
const rules = ref({
    name: [{ required: true, message: "知识库名称不能为空", trigger: "blur" }],
    // description: [{ required: true, message: "知识库概述不能为空", trigger: "blur" }]
});
//知识库列表
const knowledgeBaseList = ref([])

//编辑
const editKnowledgeBaseMethod = (knowledgeBase) => {
    reset()
    knowledgeBaseInfo.value=JSON.parse(JSON.stringify(knowledgeBase))
    knowledgeBaseTitle.value = '编辑知识库';
    knowledgeBaseVisible.value = true;
}

//取消
const cancel = () => {
    knowledgeBaseVisible.value=false;
    reset()
}

//创建知识库
const createknowledgeBase = () => {
    reset()
    knowledgeBaseTitle.value = '新建知识库';
    knowledgeBaseVisible.value = true;
}

/** 表单重置 */
const  reset = () => {
    knowledgeBaseInfo.value={
        // imageUrl:'',
        name:'',
        description:'',
    };
  proxy.resetForm("knowledgeBaseInfoForm");
}

//知识库列表
const getknowledgeBaseListMethod = () => {
    loading.value=true;
    getKnowledgeBaseList(queryParams.value).then(response => {
        knowledgeBaseList.value=response.data;
        total.value=response.total;
        loading.value=false;
    })
}

//保存
const submitKnowledgeBase = () => {
    proxy.$refs["knowledgeBaseInfoForm"].validate(valid => {
        if (valid) {
            if(knowledgeBaseInfo.value.id){
                editKnowledgeBase(knowledgeBaseInfo.value).then(response => {
                    proxy.$modal.msgSuccess("修改成功");
                    knowledgeBaseVisible.value=false;
                    reset()
                    getknowledgeBaseListMethod();
                });
            }else{
                addKnowledgeBase(knowledgeBaseInfo.value).then(response => {
                    proxy.$modal.msgSuccess("新增成功");
                    knowledgeBaseVisible.value=false;
                    reset()
                    getknowledgeBaseListMethod();
                });
            }
        }
    })
}

//删除知识库
const deleteKnowledgeBase = (knowledgeBase) => {
    proxy.$modal.confirm('是否确认删除知识库名称为"' + knowledgeBase.name + '"的数据项？').then(function() {
        return delKnowledgeBase(knowledgeBase.id);
    }).then(() => {
        getknowledgeBaseListMethod();
        proxy.$modal.msgSuccess("删除成功");
    }).catch(() => {});
}
//列表搜索
const handleQuery = () => {
    queryParams.value.page=1;
    getknowledgeBaseListMethod()
}

//文档列表
const settingKnowledgeBase = (knowledgeBase) => {
    router.push("/knowledgeBase/documentManagement?id=" + knowledgeBase.id);
}

const pageJumpProcessing = () => {
    if(route.query.type && route.query.type=='add'){
        createknowledgeBase()
    }
}

//获取知识库列表
getknowledgeBaseListMethod()
pageJumpProcessing()
</script>
<style lang='scss' scoped>
.common-container {
    display: flex;
    flex-direction: column;
    background-color: #F7F7FA;
    height: calc(100vh - 50px);
    width: 100%;
    margin: 0;
    padding: 0;
    background-size: cover;
}
.common-header {
    height: 80px;
    padding: 28px 30px;
    line-height: 24px;
    display: flex;
    justify-content: space-between;

    .title {
        color: #032220;
        font-size: 18px;
        font-weight: 500;
    }

    .header-right {
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: row;
    }
}

.common-content {
    margin: 0 20px;
    height: calc(100vh - 68px);
}
::v-deep .el-card__footer {
    padding: calc(var(--el-card-padding) - 15px) var(--el-card-padding);
    border-top: 1px solid var(--el-card-border-color);
    box-sizing: border-box;
}
::v-deep .pagination-container{
    background:#F7F7FA
}

::v-deep .el-dialog{
    display: flex;
    flex-direction: column;
    margin:0 !important;
    position:absolute;
    top:50%;
    left:50%;
    transform:translate(-50%,-50%);
    max-height:calc(100% - 30px);
    max-width:calc(100% - 30px);
}
::v-deep  .el-dialog .el-dialog__body{
    flex:1;
    overflow: auto;
}

.visible-permissions{
    .title{
        line-height: 35px;
    }
    .radio{
        height:45px;
        line-height:45px;
        border:1px solid #dcdfe6;
        border-radius:10px;
        cursor: pointer;
        .image-box{
            text-align:center;
            padding-top:6px;
            img{
                height: 25px;
                width: 25px;
                border-radius: 4px;
            }
        }
    }
    .radio-sel{
        border: 1px solid #5050E6;
    }
}

</style>
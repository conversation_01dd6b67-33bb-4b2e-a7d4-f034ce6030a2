import request from '@/utils/conversationRequest'
import { pa } from 'element-plus/es/locale/index.mjs'
import request2 from '@/utils/request'

export function startAsk (data) {
    return request({
        url: '/conversation/search/generate',
        method: 'post',
        data: data
    })
}

export function historyAsk () {
    return request2({
        url: `/proxy/conversations?appSecretKey=search_portal`,
        method: 'get'
    })
}
export function getHistoricalDetails (id) {
    return request2({
        url: `/proxy/messages?appSecretKey=search_portal&conversationId=${id}`,
        method: 'get'
    })

}
export function getHistoricalDetails_zj (id) {
    return request2({
        url: `/proxy/messages?appSecretKey=ai_assistant&conversationId=${id}`,
        method: 'get'
    })
}
export function getHistoricalDetails_pg (id) {
    return request2({
        url: `/proxy/messages?appSecretKey=ai_assessment&conversationId=${id}`,
        method: 'get'
    })
}

export function deleteAsk (data) {
    return request2({
        url: `/proxy/conversations/delete`,
        method: 'post',
        data: data
    })
}

export function stopAsk (data) {
    return request2({
        url: `/proxy/stop`,
        method: 'post',
        data: data
    })
}
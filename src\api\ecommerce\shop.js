import request from '@/utils/request'

// 店铺列表
export function getShopList() {
    return request({
        url: '/ecommerce/shop/list',
        method: 'get'
    })
}

//店铺设计
export function ecommerceEdit(query) {
    return request({
        url: '/ecommerce/shop',
        method: 'put',
        data:query
    })
}

// 店铺设计信息
export function getEcommerceInfo(id) {
    return request({
        url: '/ecommerce/shop/'+id,
        method: 'get'
    })
}
import request from '@/utils/request'

// 获取列表
export function getApiKeyList(params) {
    return request({
        url: '/agent/apikey/list',
        method: 'get',
        params
    })
}

/**
 * 删除apikey 
 * @param {*} id 
 * @returns 
 */
export function delApiKey(id) {
    return request({
        url: `/agent/apikey/${id}`,
        method: 'delete'
    })
}

/**
 * 新增apikey 
 * @param {*} id 
 * @returns 
 */
export function addApiKey(params) {
    return request({
        url: '/agent/apikey',
        method: 'post',
        data: params
    })
}

/**
 * 修改apikey 
 * @param {*} id 
 * @returns 
 */
export function updateApiKey(params) {
    return request({
        url: '/agent/apikey',
        method: 'put',
        data: params
    })
}

/**
 * 获取apikey详情
 * @param {*} id 
 * @returns 
 */
export function getApiKeyDetail(id) {
    return request({
        url: `/agent/apikey/${id}`,
        method: 'get'
    })
}


<template>
    <teleport :disabled="!teleport" :to="teleport">
        <div class="teleportable">
            <template v-if="props.nodeInfo.type != 'end'">
                <Handle type="source" :position="Position.Right" style="background-color: #5050E6;height: 8px;width: 8px;" />
            </template>
            <div style="display: flex;
                    justify-content: center;
                    align-items: center;
                    flex-direction: column;">
                <div style="width: 100%;">
                    <div style="display: flex;flex-direction: row;justify-content: space-between;align-items: center;">
                        <div style="font-size: 10px;font-weight: 400;display: flex;flex-direction: row;align-items: center;height: 12px;">
                            <div style="height: 12px;width: 12px;border-radius: 4px;margin-right: 5px;" 
                                :style="{'background-color': props.nodeInfo.type == 'start' || props.nodeInfo.type == 'end' ? '#5050E6' :'none'}">
                                <template v-if="props.nodeInfo.type == 'agent'">
                                    <img :src="baseUrl + props.nodeInfo.data.agentIconUrl" style="height: 12px;width: 12px;" />
                                </template>
                                <template v-else-if="props.nodeInfo.type == 'tool_code'">
                                    <img :src="baseUrl + props.nodeInfo.data.toolImageUrl" style="height: 12px;width: 12px;" />
                                </template>
                                <template v-else-if="props.nodeInfo.type == 'tool_api'">
                                    <img :src="baseUrl + props.nodeInfo.data.toolImageUrl" style="height: 12px;width: 12px;" />
                                </template>
                            </div>
                            <span style="line-height: 12px;">
                                <template v-if="props.nodeInfo.type == 'start' || props.nodeInfo.type == 'end'">
                                    {{ props.nodeInfo.data.label }}
                                </template>
                                <template v-else-if="props.nodeInfo.type == 'agent'">
                                    {{ props.nodeInfo.data.agentName }}
                                </template>
                                <template v-else-if="props.nodeInfo.type == 'tool_code'">
                                    {{ props.nodeInfo.data.toolName }}
                                </template>
                                <template v-else-if="props.nodeInfo.type == 'tool_api'">
                                    {{ props.nodeInfo.data.toolName }}
                                </template>
                            </span>
                        </div>
                        <div v-if="props.nodeInfo.type == 'agent' || props.nodeInfo.type == 'tool_code' || props.nodeInfo.type == 'tool_api'">
                            <el-dropdown @command="handleCommand" size="small">
                                <span class="el-dropdown-link">
                                    <el-icon><MoreFilled /></el-icon>
                                </span>
                                <template #dropdown>
                                    <el-dropdown-menu>
                                        <el-dropdown-item :command="beforeHandleCommand(props.id,'remove')">删除节点</el-dropdown-item>
                                    </el-dropdown-menu>
                                </template>
                            </el-dropdown>
                        </div>
                    </div>
                    <div style="font-size: 8px;color: #999999;line-height: 12px; margin-top: 4px;">
                        {{ props.nodeInfo.data.desc }}
                        <template v-if="props.nodeInfo.type == 'start' || props.nodeInfo.type == 'end'">
                                {{ props.nodeInfo.data.desc }}
                            </template>
                            <template v-else-if="props.nodeInfo.type == 'agent'">
                                {{ props.nodeInfo.data.agentDescribe }}
                            </template>
                            <template v-else-if="props.nodeInfo.type == 'tool_code'">
                                {{ props.nodeInfo.data.agentDescribe }}
                            </template>
                            <template v-else-if="props.nodeInfo.type == 'tool_api'">
                                {{ props.nodeInfo.data.agentDescribe }}
                            </template>
                    </div>
                </div>
                <template v-if="props.nodeInfo.type == 'agent'">
                    <!--model-->
                    <el-row style="background: #F7F7FA;width: 280px;height: 48px;border-radius: 4px;margin-top: 5px;">
                        <el-col :span="12">
                            <div style="display: flex;justify-content: start;flex-direction: column;">
                                <div style="font-size: 8px;font-weight: bold;margin-left: 8px;margin-top: 8px;">
                                    <span>模型</span>
                                </div>
                                <div style="display: flex;justify-content: start;position: relative;height: 30px;">
                                    <el-select size="small" v-model="props.nodeInfo.data.modelId" style="transform: scale(0.7);position: absolute;top: 2px;left: -18px;">
                                        <el-option v-for="(item,index) in modelList" :key="index" :label="item.modelName" :value="item.id" />
                                    </el-select>
                                </div>
                            </div>
                        </el-col>
                        <el-col :span="12">
                            <div style="display: flex;justify-content: start;flex-direction: column;">
                                <div style="font-size: 8px;font-weight: bold;margin-left: 8px;margin-top: 8px;">
                                    <span>随机性</span>
                                </div>
                                <div style="display: flex;justify-content: start;position: relative;height: 30px;">
                                    <el-input-number v-model="props.nodeInfo.data.temperature"
                                        :max="2" :min="0" :step="0.1" style="transform: scale(0.7);position: absolute;top: 2px;left: -11px;"
                                        size="small" controls-position="right"
                                    />
                                </div>
                            </div>
                        </el-col>
                    </el-row>
                    <!--input-->
                    <div style="background: #F7F7FA;width: 280px;min-height: 48px;border-radius: 4px;margin-top: 5px;">
                        <div style="display: flex;justify-content: space-between;align-items: center;max-height: 18px;position: relative;margin-top: 4px;">
                            <span style="font-size: 8px;font-weight: bold;margin-left: 8px;margin-top: 4px;">输入</span>
                            <el-button type="primary" size="small" @click="addInputItem" style="transform: scale(0.6);position: absolute;top: -3px;right: -6px;" text :icon="Plus"></el-button>
                        </div>
                        <div style="width: 280px;position: relative;padding-bottom: 8px;">
                            <el-row style="font-size: 6px;margin-left: 8px;margin-top: 8px;color: #999999;">
                                <el-col :span="8">名称</el-col>
                                <el-col :span="10">变量</el-col>
                                <el-col :span="4">操作</el-col>
                            </el-row>
                            <template v-for="(item,index) in props.nodeInfo.data.input" :key="index">
                                <el-row style="height: 24px;">
                                    <el-col :span="8" style="position: relative;">
                                        <el-input style="transform: scale(0.7);position: absolute;top: 2px;left: -8px;" size="small"  v-model="item.key" placeholder="请输入名称" />
                                    </el-col>
                                    <el-col :span="10" style="position: relative;">
                                        <el-input style="transform: scale(0.7);position: absolute;top: 2px;left: -14px;" size="small"  v-model="item.value" placeholder="请输入变量" />
                                    </el-col>
                                    <el-col :span="4" style="position: relative;">
                                        <el-button type="danger" size="small" @click="delInputItem(index)" style="transform: scale(0.6);position: absolute;top: 2px;left: -12px;" text :icon="Delete"></el-button>
                                    </el-col>
                                </el-row>
                            </template>
                        </div>
                    </div>
                    <!--prompt-->
                    <div style="background: #F7F7FA;width: 280px;min-height: 48px;border-radius: 4px;margin-top: 5px;">
                        <div style="display: flex;justify-content: space-between;align-items: center;max-height: 18px;position: relative;margin-top: 4px;">
                            <span style="font-size: 8px;font-weight: bold;margin-left: 8px;margin-top: 4px;">提示词</span>
                        </div>
                        <div style="position: relative;height: 78px;margin-top: 4px;">
                            <el-input type="textarea" :rows="4" v-model="props.nodeInfo.data.prompt"
                                style="transform: scale(0.7);position: absolute;top: -12px;left: -50px;width: 380px;"></el-input>
                        </div>
                    </div>
                    <!--output-->
                    <div style="background: #F7F7FA;width: 280px;min-height: 48px;border-radius: 4px;margin-top: 5px;">
                        <div style="display: flex;justify-content: space-between;align-items: center;max-height: 18px;position: relative;margin-top: 4px;">
                            <span style="font-size: 8px;font-weight: bold;margin-left: 8px;margin-top: 4px;">输出</span>
                            <el-button type="primary" size="small" style="transform: scale(0.6);position: absolute;top: -3px;right: -6px;" text :icon="Plus"></el-button>
                        </div>
                        <div style="width: 280px;position: relative;padding-bottom: 8px;">
                            <el-row style="font-size: 6px;margin-left: 8px;margin-top: 8px;color: #999999;">
                                <el-col :span="8">名称</el-col>
                                <el-col :span="14">类型</el-col>
                            </el-row>
                            <template v-for="(item,index) in props.nodeInfo.data.output" :key="index">
                                <el-row style="height: 24px;">
                                    <el-col :span="8" style="position: relative;">
                                        <el-input style="transform: scale(0.7);position: absolute;top: 2px;left: -8px;" size="small"  v-model="item.name" placeholder="请输入名称" disabled />
                                    </el-col>
                                    <el-col :span="14" style="position: relative;">
                                        <el-input style="transform: scale(0.7);position: absolute;top: 2px;left: -22px;" size="small"  v-model="item.type" placeholder="请输入类型" disabled />
                                    </el-col>
                                </el-row>
                            </template>
                        </div>
                    </div>
                </template>
                <template v-if="props.nodeInfo.type == 'tool_code'">
                    <div style="background: #F7F7FA;width: 280px;min-height: 48px;border-radius: 4px;margin-top: 5px;">
                        <div style="display: flex;justify-content: space-between;align-items: center;max-height: 18px;position: relative;margin-top: 4px;">
                            <span style="font-size: 8px;font-weight: bold;margin-left: 8px;margin-top: 4px;">输入</span>
                            <el-button type="primary" size="small" @click="addInputItem" 
                                style="transform: scale(0.6);position: absolute;top: -3px;right: -6px;" text :icon="Plus"></el-button>
                        </div>
                        <div style="width: 280px;position: relative;padding-bottom: 8px;">
                            <el-row style="font-size: 6px;margin-left: 8px;margin-top: 8px;color: #999999;">
                                <el-col :span="8">名称</el-col>
                                <el-col :span="10">变量</el-col>
                                <el-col :span="4">操作</el-col>
                            </el-row>
                            <template v-for="(item,index) in props.nodeInfo.data.input" :key="index">
                                <el-row style="height: 24px;">
                                    <el-col :span="8" style="position: relative;">
                                        <el-input style="transform: scale(0.7);position: absolute;top: 2px;left: -8px;" size="small"  v-model="item.key" placeholder="请输入名称" />
                                    </el-col>
                                    <el-col :span="10" style="position: relative;">
                                        <el-input style="transform: scale(0.7);position: absolute;top: 2px;left: -14px;" size="small"  v-model="item.value" placeholder="请输入变量" />
                                    </el-col>
                                    <el-col :span="4" style="position: relative;">
                                        <el-button type="danger" size="small" @click="delInputItem(index)" style="transform: scale(0.6);position: absolute;top: 2px;left: -12px;" text :icon="Delete"></el-button>
                                    </el-col>
                                </el-row>
                            </template>
                        </div>
                    </div>
                    <div style="background: #F7F7FA;width: 280px;min-height: 48px;border-radius: 4px;margin-top: 5px;">
                        <div style="display: flex;justify-content: space-between;align-items: center;max-height: 18px;position: relative;margin-top: 4px;">
                            <span style="font-size: 8px;font-weight: bold;margin-left: 8px;margin-top: 4px;">代码</span>
                        </div>
                        <div style="position: relative;height: 154px;margin-top: 4px;">
                            <custom-code-mirror 
                                style="transform: scale(0.7);position: absolute;top: -26px;left: -50px;width: 380px;" 
                                :height="'200px'" 
                                :value="props.nodeInfo.data.toolProcedure" 
                                @update:bindVal="updatetoolProcedure" 
                                >
                            </custom-code-mirror>
                        </div>
                    </div>
                    <div style="background: #F7F7FA;width: 280px;min-height: 48px;border-radius: 4px;margin-top: 5px;">
                        <div style="display: flex;justify-content: space-between;align-items: center;max-height: 18px;position: relative;margin-top: 4px;">
                            <span style="font-size: 8px;font-weight: bold;margin-left: 8px;margin-top: 4px;">输出</span>
                            <el-button type="primary" size="small" style="transform: scale(0.6);position: absolute;top: -3px;right: -6px;" text :icon="Plus"></el-button>
                        </div>
                        <div style="width: 280px;position: relative;padding-bottom: 8px;">
                            <el-row style="font-size: 6px;margin-left: 8px;margin-top: 8px;color: #999999;">
                                <el-col :span="8">名称</el-col>
                                <el-col :span="14">类型</el-col>
                            </el-row>
                            <template v-for="(item,index) in props.nodeInfo.data.output" :key="index">
                                <el-row style="height: 24px;">
                                    <el-col :span="8" style="position: relative;">
                                        <el-input style="transform: scale(0.7);position: absolute;top: 2px;left: -8px;" size="small"  v-model="item.name" placeholder="请输入名称" disabled />
                                    </el-col>
                                    <el-col :span="14" style="position: relative;">
                                        <el-input style="transform: scale(0.7);position: absolute;top: 2px;left: -22px;" size="small"  v-model="item.type" placeholder="请输入类型" disabled />
                                    </el-col>
                                </el-row>
                            </template>
                        </div>
                    </div>
                </template>
                <template v-if="props.nodeInfo.type == 'tool_api'">
                    <!--工具URL-->
                    <div style="background: #F7F7FA;width: 280px;min-height: 48px;border-radius: 4px;margin-top: 5px;">
                        <div style="display: flex;justify-content: space-between;align-items: center;max-height: 18px;position: relative;margin-top: 4px;">
                            <span style="font-size: 8px;font-weight: bold;margin-left: 8px;margin-top: 4px;">工具URL</span>
                        </div>
                        <div style="position: relative;margin-top: 4px;">
                            <el-input v-model="props.nodeInfo.data.toolUrl" style="transform: scale(0.7);position: absolute;top: -5px;left: -50px;width: 380px;" ></el-input>
                        </div>
                    </div>
                    <!--请求方式-->
                    <div style="background: #F7F7FA;width: 280px;min-height: 48px;border-radius: 4px;margin-top: 5px;">
                        <div style="display: flex;justify-content: space-between;align-items: center;max-height: 18px;position: relative;margin-top: 4px;">
                            <span style="font-size: 8px;font-weight: bold;margin-left: 8px;margin-top: 4px;">请求方式</span>
                        </div>
                        <div style="position: relative;margin-top: 4px;">
                            <el-select v-model="props.nodeInfo.data.requestMethod" style="transform: scale(0.7);position: absolute;top: -5px;left: -50px;width: 380px;" >
                                <el-option :key="1" label="GET" value="GET"/>
                                <el-option :key="2" label="POST" value="POST"/>
                                <el-option :key="3" label="PUT" value="PUT"/>
                                <el-option :key="4" label="DELETE" value="DELETE"/>
                            </el-select>
                        </div>
                    </div>
                    <!--header List-->
                    <div style="background: #F7F7FA;width: 280px;min-height: 48px;border-radius: 4px;margin-top: 5px;">
                        <div style="display: flex;justify-content: space-between;align-items: center;max-height: 18px;position: relative;margin-top: 4px;">
                            <span style="font-size: 8px;font-weight: bold;margin-left: 8px;margin-top: 4px;">输入</span>
                            <el-button type="primary" size="small" @click="addHeaderListItem" 
                                style="transform: scale(0.6);position: absolute;top: -3px;right: -6px;" text :icon="Plus"></el-button>
                        </div>
                        <div style="width: 280px;position: relative;padding-bottom: 8px;">
                            <el-row style="font-size: 6px;margin-left: 8px;margin-top: 8px;color: #999999;">
                                <el-col :span="8">参数名</el-col>
                                <el-col :span="10">参数值</el-col>
                                <el-col :span="4">操作</el-col>
                            </el-row>
                            <template v-for="(item,index) in props.nodeInfo.data.headerList" :key="index">
                                <el-row style="height: 24px;">
                                    <el-col :span="8" style="position: relative;">
                                        <el-input style="transform: scale(0.7);position: absolute;top: 2px;left: -8px;" size="small"  v-model="item.paramName" placeholder="请输入参数名" />
                                    </el-col>
                                    <el-col :span="10" style="position: relative;">
                                        <el-input style="transform: scale(0.7);position: absolute;top: 2px;left: -14px;" size="small"  v-model="item.paramValues" placeholder="请输入参数值" />
                                    </el-col>
                                    <el-col :span="4" style="position: relative;">
                                        <el-button style="transform: scale(0.7);position: absolute;top: 2px;left: -12px;" type="danger" text size="small" @click="deleteHeader(index)" :icon="Delete"></el-button>
                                    </el-col>
                                </el-row>
                            </template>
                        </div>
                    </div>
                    <!--入参-->
                    <div style="background: #F7F7FA;width: 280px;min-height: 48px;border-radius: 4px;margin-top: 5px;">
                        <div style="display: flex;justify-content: space-between;align-items: center;max-height: 18px;position: relative;margin-top: 4px;">
                            <span style="font-size: 8px;font-weight: bold;margin-left: 8px;margin-top: 4px;">入参</span>
                            <el-button type="primary" size="small" @click="addInkeyItem" 
                                style="transform: scale(0.6);position: absolute;top: -3px;right: -6px;" text :icon="Plus"></el-button>
                        </div>
                        <div style="width: 280px;position: relative;padding-bottom: 8px;">
                            <el-row style="font-size: 6px;margin-left: 8px;margin-top: 8px;color: #999999;">
                                <el-col :span="5">参数名</el-col>
                                <el-col :span="5">参数描述</el-col>
                                <el-col :span="5">参数类型</el-col>
                                <el-col :span="5">是否必要</el-col>
                                <el-col :span="4">操作</el-col>
                            </el-row>
                            <template v-for="(item,index) in props.nodeInfo.data.inKey" :key="index">
                                <el-row style="height: 24px;">
                                    <el-col :span="5" style="position: relative;">
                                        <el-input style="transform: scale(0.7);position: absolute;top: 2px;left: -1px;" size="small"  v-model="item.name" placeholder="请输入参数名" />
                                    </el-col>
                                    <el-col :span="5" style="position: relative;">
                                        <el-input style="transform: scale(0.7);position: absolute;top: 2px;left: -3px;" size="small"  v-model="item.desc" placeholder="请输入参数描述" />
                                    </el-col>
                                    <el-col :span="5" style="position: relative;">
                                        <el-select style="transform: scale(0.7);position: absolute;top: 2px;left: -7px;" size="small"  v-model="item.type" placeholder="请选择参数类型" >
                                            <el-option :key="1" label="String" value="String"/>
                                            <el-option :key="2" label="Integrt" value="Integrt"/>
                                            <el-option :key="3" label="Number" value="Number"/>
                                            <el-option :key="4" label="Object" value="Object"/>
                                            <el-option :key="5" label="Array" value="Array"/>
                                            <el-option :key="6" label="Boolen" value="Boolen"/>
                                        </el-select>
                                    </el-col>
                                    <el-col :span="5" style="position: relative;">
                                        <el-switch style="transform: scale(0.7);position: absolute;top: 2px;left: -2px;" size="small"  v-model="item.ifNece"/>
                                    </el-col>
                                    <el-col :span="4" style="position: relative;">
                                        <el-button style="transform: scale(0.7);position: absolute;top: 2px;left: -12px;" type="danger" text size="small" @click="delInkeyItem(index)" :icon="Delete"></el-button>
                                    </el-col>
                                </el-row>
                            </template>
                        </div>
                    </div>
                    <!--出参-->
                    <div style="background: #F7F7FA;width: 280px;min-height: 48px;border-radius: 4px;margin-top: 5px;">
                        <div style="display: flex;justify-content: space-between;align-items: center;max-height: 18px;position: relative;margin-top: 4px;">
                            <span style="font-size: 8px;font-weight: bold;margin-left: 8px;margin-top: 4px;">出参</span>
                            <el-button type="primary" size="small" @click="addOutKeyItem" 
                                style="transform: scale(0.6);position: absolute;top: -3px;right: -6px;" text :icon="Plus"></el-button>
                        </div>
                        <div style="width: 280px;position: relative;padding-bottom: 8px;">
                            <el-row style="font-size: 6px;margin-left: 8px;margin-top: 8px;color: #999999;">
                                <el-col :span="5">参数名</el-col>
                                <el-col :span="5">参数描述</el-col>
                                <el-col :span="5">参数类型</el-col>
                                <el-col :span="5">是否必要</el-col>
                                <el-col :span="4">操作</el-col>
                            </el-row>
                            <template v-for="(item,index) in props.nodeInfo.data.outKey" :key="index">
                                <el-row style="height: 24px;">
                                    <el-col :span="5" style="position: relative;">
                                        <el-input style="transform: scale(0.7);position: absolute;top: 2px;left: -1px;" size="small"  v-model="item.name" placeholder="请输入参数名" />
                                    </el-col>
                                    <el-col :span="5" style="position: relative;">
                                        <el-input style="transform: scale(0.7);position: absolute;top: 2px;left: -3px;" size="small"  v-model="item.desc" placeholder="请输入参数描述" />
                                    </el-col>
                                    <el-col :span="5" style="position: relative;">
                                        <el-select style="transform: scale(0.7);position: absolute;top: 2px;left: -7px;" size="small"  v-model="item.type" placeholder="请选择参数类型" >
                                            <el-option :key="1" label="String" value="String"/>
                                            <el-option :key="2" label="Integrt" value="Integrt"/>
                                            <el-option :key="3" label="Number" value="Number"/>
                                            <el-option :key="4" label="Object" value="Object"/>
                                            <el-option :key="5" label="Array" value="Array"/>
                                            <el-option :key="6" label="Boolen" value="Boolen"/>
                                        </el-select>
                                    </el-col>
                                    <el-col :span="5" style="position: relative;">
                                        <el-switch style="transform: scale(0.7);position: absolute;top: 2px;left: -2px;" size="small"  v-model="item.ifNece"/>
                                    </el-col>
                                    <el-col :span="4" style="position: relative;">
                                        <el-button style="transform: scale(0.7);position: absolute;top: 2px;left: -12px;" type="danger" text size="small" @click="delOutKeyItem(index)" :icon="Delete"></el-button>
                                    </el-col>
                                </el-row>
                            </template>
                        </div>
                    </div>
                </template>
            </div>
            <template v-if="props.nodeInfo.type != 'start'">
                <Handle type="target" :position="Position.Left" style="background-color: #5050E6;height: 8px;width: 8px;" />
            </template>
        </div>
    </teleport>
</template>

<script setup>
import { Handle, Position,useVueFlow } from '@vue-flow/core'
import { useTeleport } from './useTeleport.js'
import CustomCodeMirror from '@/components/CustomCodeMirror/index'
import {Edit,Plus,MoreFilled,Delete} from '@element-plus/icons-vue'
import {ref,defineEmits} from 'vue'
import {getModelList} from '@/api/agent/body'

const baseUrl = import.meta.env.VITE_APP_BASE_API
const { removeNodes } = useVueFlow()

const emit = defineEmits(['openNodeDrawer'])
const props = defineProps({
    id: {
        type: String,
        required: true,
    },
    nodeInfo: {
        type: Object,
        required: false,
    },
})

const { teleport } = useTeleport(props.id)

const updatetoolProcedure = (val) => {
    props.nodeInfo.data.value.toolProcedure = val
}

//大模型列表
const modelList = ref([])
const getModelListMethod= () =>{
    getModelList().then(response=>{
        modelList.value=response.rows
    })
}

getModelListMethod()

function editNode(){
    emit('openNodeDrawer')
}

function handleCommand(command){
    if(command.command == 'remove'){
        removeNodes([command.data])
    }
}

function beforeHandleCommand(data,command){
    return {
        command,
        data
    }
}

function addInputItem(){
    props.nodeInfo.data.input.push({key:'',value:''})
}

function delInputItem(delIndex){
    props.nodeInfo.data.input.splice(delIndex,1)
}

function addHeaderListItem(){
    props.nodeInfo.data.headerList.push({paramName:'',paramValues:''})
}

function deleteHeader(delIndex){
    props.nodeInfo.data.headerList.splice(delIndex,1)
}

function addInkeyItem(){
    props.nodeInfo.data.inKey.push({name:'',desc:'',type:'',ifNece:false})
}

function delInkeyItem(delIndex){
    props.nodeInfo.data.inKey.splice(delIndex,1)
}

function addOutKeyItem(){
    props.nodeInfo.data.outKey.push({name:'',desc:'',type:'',ifNece:false})
}

function delOutKeyItem(){
    props.nodeInfo.data.outKey.push({name:'',desc:'',type:'',ifNece:false})
}

</script>

<style scoped>

.teleportable {
    padding: 10px;
    background: white;
    border: 1px solid #5050E6;
    border-radius: 10px;
    color: #000;
    max-width: 300px;
}
</style>
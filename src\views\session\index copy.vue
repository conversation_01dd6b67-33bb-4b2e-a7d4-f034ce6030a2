<template>
    <div class="whole">
        <div class="left" v-if="showDiv" :style="{'width':showDiv?'20%':'0'}">
            <div class="content">
                <el-divider content-position="left" style="background-color: #d6cfe180;"> 历史记录 </el-divider>
                <div style="margin-bottom: 20px;">
                    <el-input v-model="historySel" placeholder="搜索历史记录" :suffix-icon="Search"
                    style="width: 85%;padding-right: 10px;"/>
                    <el-button type="info" :icon="Delete" circle 
                    style="background-color: #e3e0f0;border-color: #e3e0f0;color: #9494B0;"/>
                </div>
                <div v-for="(item,index) in historyList" class="font">{{ item.history }}</div>
            </div>
            <div class="suspended-button">
                <div class="box">
                    <el-button link text :icon="ArrowLeft" @click="showDiv=!showDiv"></el-button>
                </div>
            </div>
        </div>
        <div class="right" :style="{'width':showDiv?'80%':'100%'}">              
            <div class="suspended-button" v-if="!showDiv">
                <div class="box">
                    <el-button link text :icon="ArrowRight" @click="showDiv=!showDiv"></el-button>
                </div>
            </div>
            <div class="content" :style="{'width':showDiv?'100%':'98%'}">
                <div class="top">
                    AgentFactory
                </div>
                <div class="ai-chat-content">
                    <div class="preface" v-if="prefaceState">
                        <div class="preface-left">
                            <img :src="sessionmodel"/>
                        </div>
                        <div class="preface-right">
                            <div class="preface-font">
                                <div>作为你的智能伙伴，我既能写文案、想点子，又能陪你聊天、答疑解惑。</div>
                                <div>想知道我还能做什么?点这里快速上手!可以浏览器收藏文心一言官网地址，下次使用更高效哦~</div>
                                <div style="margin-top: 20px;">你可以试着问我：</div>
                            </div>
                            <el-row>
                                <el-col v-for="(item,index) in toolList" style="padding: 5px 5px;" :span="12">
                                    <div class="preface-tool" @click="clickInstructItem(item)">
                                        <div class="preface-title">
                                            <img :src="item.img"/><span style="margin-left: 10px;font-weight: 600;color: #291E59;">{{ item.name }}</span>
                                        </div>
                                        <div class="preface-content">
                                            {{ item.content }}
                                        </div>
                                    </div>
                                </el-col>
                            </el-row>
                        </div>
                    </div>
                    <div v-if="!prefaceState" ref="chatContentBox">
                        <template v-for="msg in msgManager.refMessageItemList" >
                            <div class="chat-message">
                                <template v-if="msg.isUser">
                                    <div class="user-chat-area">
                                        <div class="user-box">
                                            <div class="user-name">我</div>
                                            <div class="user-icon"><img :src="mockUserImg"/></div>
                                        </div>
                                        <div class="user-chat-message">{{ msg.content }}</div>
                                    </div>
                                </template>
                                <template v-if="!msg.isUser">
                                    <div class="robot-chat-area">
                                        <div class="robot-box">
                                            <div class="robot-icon">
                                                <img :src="teamAgentSelectedList?
                                                (teamAgentSelectedList.find(teamAgent => teamAgent.id == msgManager.nowMessageRole.value.key)?
                                                baseUrl+teamAgentSelectedList.find(teamAgent => teamAgent.id == msgManager.nowMessageRole.value.key).agentIconUrl:mockDefaultImg
                                                ):sessionmodel
                                                ">
                                            </div>
                                            <div class="robot-name">{{ msgManager.nowMessageRole.value.name || '小智'}}</div>
                                        </div>
                                        <v-md-preview class="robot-chat-message" :text="msg.content"/>
                                    </div>
                                </template>
                            </div>
                        </template>
                        <div class="robot-record-box" v-if="msgManager.loading.value">
                            <div class="chat-message">
                                <template>
                                    <div class="robot-chat-area">
                                        <div class="robot-box">
                                            <div class="robot-icon">
                                                <img :src="teamAgentSelectedList?
                                                (teamAgentSelectedList.find(teamAgent => teamAgent.id == msgManager.nowMessageRole.value.key)?
                                                baseUrl+teamAgentSelectedList.find(teamAgent => teamAgent.id == msgManager.nowMessageRole.value.key).agentIconUrl:mockDefaultImg
                                                ):sessionmodel
                                                ">
                                            </div>
                                            <div class="robot-name">{{ msgManager.nowMessageRole.value.name || '小智'}}</div>
                                        </div>
                                        <template v-if="msgManager.waitting.value">
                                            wait...
                                        </template>
                                        <template v-else>
                                            <v-md-preview class="robot-chat-message" :text="msgManager.nowMessageHtml.value"/>
                                        </template>
                                    </div>
                                </template>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="bottom-area">
                    <div class="bar">
                        <div class="tools-list" >
                            <div class="tools-item" @click="">
                                <img class="tool-icon" :src="picture" width="20px" height="20px"/>
                            </div>
                            <div class="tools-item" @click="">
                                <img class="tool-icon" :src="exportsvg" width="20px" height="20px"/>
                            </div>
                        </div>
                    </div>
                    <div class="submit-area">
                        <div class="content-area">
                            <div class="user-input-area">
                                <div class="top-area">
                                    <div class="text-area">
                                        <textarea ref="userInputTextarea" v-model="userInputTextareaValue" :placeholder="userInputTextareaPlaceholer" autocomplete="off" 
                                            class="user-input" :class="{'show-tool-tip':userInputIsShowToolTip}" :style="{'text-indent': textAreaIndent}" 
                                            @keydown="handleKeyDown" @keydown.enter.shift.prevent="handleShiftEnter" @input="handleInput" 
                                            :disabled="isDisabledUserInputTextareaValue"></textarea>
                                        <span ref="toolTip" class="tool-tip" 
                                            :style="{
                                                'display':userInputIsShowToolTip?'block':'none'
                                                ,'transform':toolTipTranslateY}"
                                        >{{ toolTipContent }}</span>
                                    </div>
                                </div>
                                <div class="bottom-info-area">
                                    <div class="send-btn" :class="{'disabled':isDisabledSend}" @click="runTest"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="tip">内容由AI生成，无法确保真实准确。</div>
                </div>
            </div>
        </div>
    </div>
</template>
<script setup> 
    import { nextTick, ref } from 'vue'
    import { ArrowLeft , ArrowRight , Delete ,Search } from '@element-plus/icons-vue'
    import picture from "@/assets/icons/svg/picture.svg"
    import exportsvg from "@/assets/icons/svg/export.svg"
    import sessionmodel from "@/assets/icons/svg/session-model.svg"
    import productdesign from "@/assets/icons/svg/productdesign.svg"
    import aftersaleservice from "@/assets/icons/svg/after-sale-service.svg"
    import bomgenerate from "@/assets/icons/svg/bom-generate.svg"
    import { MessageItemManager } from '@/types/chat-types';
    import { TaskQueue } from './utils/taskQueue'
    import { ElMessage } from 'element-plus'
    import VMdPreview from '@kangc/v-md-editor/lib/preview';
    import githubTheme from '@kangc/v-md-editor/lib/theme/github.js';
    import hljs from 'highlight.js';
    import { getList } from '@/api/agent/body'

    //baseUrl
    const baseUrl = import.meta.env.VITE_APP_BASE_API;
    //初始化md样式
    VMdPreview.use(githubTheme,{
        Hljs: hljs,
    })

    let ws = null;

    const showDiv = ref(true)

    const historyList = ref([
        {id:1,history:'什么是公园20分钟效应?请分析'},
        {id:2,history:'出一份mysql面试题，面试高'},
        {id:3,history:'出一份Python高级工程师的'},
        {id:4,history:'社会学书籍推荐'},
        {id:5,history:'苏格拉底的哲学理念是什么'},
        {id:6,history:'软件产品的非功能需求，界面操作'},
        {id:7,history:'苹果电脑和手机的邮件账户设置如'},
        {id:8,history:'按照时间的先后顺序，简述西方哲按照时间的先后顺序，简述西方哲'}
    ])
    const historySel = ref('')
    //用户输入框的ref元素
    const userInputTextarea = ref(null)
    //用户输入框的placeholder
    const userInputTextareaPlaceholer=ref('使用“/”创建和收藏指令，可通过shift+回车换行')
    //发送消息的禁用状态
    const isDisabledSend = ref(true)
    //用户输入框的禁用状态
    const isDisabledUserInputTextareaValue  = ref(false)
    //用户输入框的值
    const userInputTextareaValue  = ref('')
    //是否展示用户输入时的提示
    const userInputIsShowToolTip = ref(false)
    //用户输入框的提示的ref元素
    const toolTip = ref(null)
    //当输入框内容过多时，tip的偏移量
    const toolTipTranslateY = ref('translateY(0px)')
    //用户输入框的提示的偏移量
    const textAreaIndent = ref('0px')
    //用户输入框的提示
    const toolTipContent = ref('')
    //前言展示状态
    const prefaceState = ref(true)
    //当前点击选中的指令
    const currentInstruct = ref(null)
    //模拟的用户icon
    const mockUserImg = 'https://gimg2.baidu.com/image_search/src=http%3A%2F%2Fsafe-img.xhscdn.com%2Fbw1%2F4d40b566-1f0a-4f8d-bc97-c513df8775b3%3FimageView2%2F2%2Fw%2F1080%2Fformat%2Fjpg&refer=http%3A%2F%2Fsafe-img.xhscdn.com&app=2002&size=f9999,10000&q=a80&n=0&g=0n&fmt=auto?sec=1706239065&t=82418a0dc2a04830de8580a08a94ff20'


    const toolList = ref([
        {id:1,name:'产品设计',content:'快速生成产品设计',img:productdesign},
        {id:2,name:'售后服务',content:'售后服务团队',img:aftersaleservice},
        {id:3,name:'BOM生成',content:'这是一个可以设置BoM表的工具',img:bomgenerate},
        {id:4,name:'智约履约团队',content:'专注于智能解析协议内容，精准捕捉许可费条款与履约日期等关键信息，并据此设定高效的履约提醒功能，助力企业实现合同管理的智能化与高效化。',img:bomgenerate}
    ])

    //输入框输入时的事件
    const handleInput = () => {
        if(userInputTextareaValue.value){
            isDisabledSend.value = false
            
        }else{
            isDisabledSend.value = true
        }
    }

    //输入框滑动事件
    const handleScroll = () => {
        if (userInputTextarea.value) {
            toolTipTranslateY.value = 'translateY(-' + userInputTextarea.value.scrollTop + 'px)'
        }  
    }

    //点击指令触发事件
    const clickInstructItem = (instruct) => {
        currentInstruct.value = instruct
        toolTipContent.value ='选中【'+instruct.name+'】'
        if(currentInstruct.value.id==1){
            toolTipContent.value+='，请输入产品：'
        }
        if(currentInstruct.value.id==4){
            toolTipContent.value+='，请输入用户问题：'
        }
        userInputIsShowToolTip.value = true
        nextTick(() => {
            textAreaIndent.value = toolTip.value.clientWidth + 5 +'px'
            userInputTextareaPlaceholer.value = ''
            userInputTextarea.value.focus()
        })
    }

    //选中的智能体列表
    const teamAgentSelectedList = ref([])
    //获取智能体列表  智能团队编排
    const getTeamAgentSelectedList = () =>{
        let params = {
            pageNum: 1,
            pageSize: 999
        }
        getList(params).then((resp) => {
            teamAgentSelectedList.value = resp.rows
        })
    }

    //消息列表ref
    const chatContentBox = ref(null)
    const isMessageEnding = ref(false)
    //初始化消息管理者
    const msgManager = new MessageItemManager()
    //初始化消息队列对象
    const taskQueue = new TaskQueue()
    //初始化消息队列对象的Complete
    taskQueue.setOnComplete(() => {
        msgManager.messageReceiveDone()
        if(currentInstruct.value){
            currentInstruct.value = null
        }
        if(!taskQueue.running && !isMessageEnding.value && ws.readyState == 1 && taskQueue.index == taskQueue.tasks.length ){
            msgManager.clearNowMessageRole();
            msgManager.do();
            msgManager.waittingStart();
            taskQueue.clearTasks()
        }else{
            userInputTextareaValue.value = ''
            if(ws.readyState == 3){
                msgManager.clearNowMessageRole();
            }
        }
        if(ws.readyState == 2 || ws.readyState == 3 || ws.readyState == 0){
            userInputTextareaValue.value = ''
            isDisabledSend.value = true;
            isDisabledUserInputTextareaValue.value = true;
        }
    })

    let timeres = []
    //是否第一次发送
    const firstState = ref(true)
    const initMessage = ref({})
    //点击发送
    const runTest = () =>{
        if(currentInstruct.value && userInputTextareaValue.value){
            if(currentInstruct.value.id==1){
                initMessage.value={"id":"1","user_message":{
                "teamId": 54,
                "agentIds": [
                    "16",
                    "17",
                    "18",
                    "38",
                    "29"
                ],
                "sessionId": "0f3bc749-1fa6-474e-bdd2-26fbc912c332",
                "keywords": {
                    "product": userInputTextareaValue.value,
                    "user_situation": "",
                    "usage_scenario": "",
                    "product_type": "",
                    "function": "",
                    "use_flow": "",
                    "proposals_number": "",
                    "supplement": "",
                    "image_url": ""
                },
                "userName": 1
                }}
                
            }
            if(firstState.value){
                sendMessage()
            }else{
                sendMsgHandle()
            }
        }
    }

    //发送消息
    const sendMessage = () => {
        if(msgManager.loading.value){
            ElMessage.warning('正在处理中，请稍后再试')
            return;
        }
        if(userInputTextareaValue.value){
            msgManager.pushUserMesage(userInputTextareaValue.value)
            firstState.value=false
            prefaceState.value=false
            msgManager.cleanHistory();
            initConnect().then(() => {
                customSend(initMessage.value,false)
            })
            userInputTextareaValue.value = ''
            userInputIsShowToolTip.value = false
            textAreaIndent.value = '0px'
            userInputTextareaPlaceholer.value = '使用“/”创建和收藏指令，可通过shift+回车换行'
        }
    }

    //服务地址
    const chatUrl='ws://***********:29993'
    const initConnect = () => {
        return new Promise((resolve,reject) => {
            isDisabledSend.value = true;
            isDisabledUserInputTextareaValue.value = true;
            ws = new WebSocket(chatUrl);
            ws.addEventListener('open', () => {
                console.log('WebSocket连接已打开');
                isDisabledSend.value = false;
                isDisabledUserInputTextareaValue.value = false;
                resolve();
            });
            ws.addEventListener('message', (event) => {
                msgManager.waittingEnd();
                const message = event.data;
                let obj = JSON.parse(message)
                if(obj.status == 0){
                    isMessageEnding.value = false;
                }else{
                    userInputTextareaValue.value = ''
                    isDisabledSend.value = false;
                    isDisabledUserInputTextareaValue.value = false;
                    isMessageEnding.value = true;
                }
                taskQueue.addTask(printBotMessage,obj)
            });
            ws.addEventListener('close', () => {
                isDisabledSend.value = true;
        isDisabledUserInputTextareaValue.value = true;
                console.log('WebSocket连接已断开');
            });
            ws.addEventListener('error', (error) => {
                isDisabledSend.value = true;
        isDisabledUserInputTextareaValue.value = true;
                console.error('发生错误：', error);
            });
        })
        
    }

    const printBotMessage = (message) => {
        console.log(message);
        return new Promise((resolve) => {
            msgManager.do();
            msgManager.waittingEnd();
            let role = {
                name:message.role,
                key:message.agentId
            };
            if(message.key=='text' || message.key=='tips'){
                let txt = message.messages.split('')
                let tempTxt = ''
                txt.forEach((item,index) => {
                    let timer = setTimeout(() => {
                        msgManager.appendNowMessageContent(tempTxt += item,role,false);
                        if(index == txt.length-1){
                            msgManager.deleteLastMsgRecord();
                            msgManager.messageReceiveDone();
                            resolve(message)
                        }
                    }, 30 * index );
                    timeres.push(timer) 
                })
            }else if(message.key=='image'){
                let imgs = message.messages
                let tempImgs = ''
                imgs.forEach(element => {
                    if(element){
                        tempImgs+='<div style="display: inline-block;width: 18%;margin-right:5px"><img src="'+element+'" /></div>'
                    }
                });
                msgManager.appendNowMessageContent(tempImgs,role,false);
                msgManager.deleteLastMsgRecord();
                msgManager.messageReceiveDone();
                resolve(message)
            }
        })
    }

    function sendMsgHandle() {
        if (userInputTextareaValue.value.trim() === '') {
            ElMessage.warning("你要发送什么呢？")
            return;
        }
        let obj = {
            message:userInputTextareaValue.value
        }
        customSend(obj);
    }

    const customSend = (message,needPushUserMessage = true) => {
        msgManager.do();
        msgManager.waittingStart();
        clearTasks()
        isDisabledSend.value = true;
        isDisabledUserInputTextareaValue.value = true;
        if(needPushUserMessage){
            msgManager.pushUserMesage(userInputTextareaValue.value);
        }
        ws.send(JSON.stringify(message))
    }

    //输入框按下键时触发事件
    const handleKeyDown = (e) => {
        if (e.key === 'Enter') {  
            e.preventDefault();
            if (e.shiftKey) {
                userInputTextareaValue.value += '\n';  
            } else {
                runTest();  
            }  
        }
        if(e.key === 'Backspace' && !userInputTextareaValue.value){
            userInputIsShowToolTip.value = false
            textAreaIndent.value = '0px'
            toolTipTranslateY.value = 'translateY(0px)'
            toolTipContent.value = ''
            userInputTextareaPlaceholer.value = '使用“/”创建和收藏指令，可通过shift+回车换行'
        }
    }
    const clearTasks = () => {
        taskQueue.clearTasks()
    }

    //定位到最新内容
    const moveScroll = () => {
        nextTick(()=> {
            chatContentBox.value.scrollTop = chatContentBox.value.scrollHeight;
        })
    }

    watch(msgManager.nowMessageHtml, () => {
        moveScroll();
    })

    watch(msgManager.nowMessageLine, () => {
        moveScroll();
    });

    watch(msgManager.refMessageItemList, () => {
        moveScroll();
    });

    onMounted(() => {
        if (userInputTextarea.value) {  
            userInputTextarea.value.addEventListener('scroll', handleScroll);
        }  
    })

    onUnmounted(() => {
        if (userInputTextarea.value) {  
            userInputTextarea.value.removeEventListener('scroll', handleScroll);  
        }  
    })

    getTeamAgentSelectedList()
</script>
<style scoped lang="scss">
    .whole{
        display: flex;
        justify-content: space-between;
        height: calc( 100vh );
        background-color: #F7F7FA;
        .left{
            display: flex;
            justify-content: space-between;
            .content{
                width: 90%;
                background-color: #EEECF6;
                padding: 10px;
                ::v-deep .el-divider__text.is-left {
                    left:0 !important
                }
                ::v-deep .el-divider__text {
                    background-color: #EEECF6 !important;
                    padding: 0 10px !important;
                    color: #8687A8;
                }
                .font{
                    line-height: 35px;
                    font-size: 12px;
                    color: #636384;
                    overflow: hidden; 
                    white-space: nowrap;
                    text-overflow:ellipsis;
                    width: 100%;
                }
                ::v-deep .el-input__wrapper{
                    background-color: #E3E0F0;
                    border-radius: 20px;
                }
            }
            .suspended-button{
                width: 10%;
                display: flex;
                align-items: center;
                padding: 0 5px;
                .box{
                    display: flex;
                    align-items: center;
                    height: 30px;
                    background-color: #EEECF6;
                    border-radius: 5px;
                    ::v-deep .el-button.is-link {
                        padding: 0;
                        color: #909090;
                    }
                }
            }
        }
       
        .right{
            display: flex;
            justify-content: space-between;
            .suspended-button{
                width: 2%;
                display: flex;
                align-items: center;
                padding: 0 5px;
                .box{
                    display: flex;
                    align-items: center;
                    height: 30px;
                    background-color: #EEECF6;
                    border-radius: 5px;
                    ::v-deep .el-button.is-link {
                        padding: 0;
                        color: #909090;
                    }
                }
            }
            .content{
                padding: 0 20%;
                text-align: center;
                width: 100%;
                .top{
                    height: 80px;
                    line-height: 80px;
                    font-size: 30px;
                    color: #5050E6;
                    font-weight: 550;
                }
                .ai-chat-content{
                    height: calc( 100vh - 280px );
                    overflow-y: auto;
                    overflow-x: hidden;
                    .chat-message{
                        display: flex;
                        flex-direction: column;
                        .user-chat-area{
                            align-self: flex-end;
                            display: flex;
                            flex-direction: column;
                            margin-bottom: 10px;
                            padding-left: 0;
                            .user-box{
                                display: flex;
                                flex-direction: row;
                                justify-content: end;
                                align-items: center;
                                margin-bottom: 3px;
                                .user-name{
                                    line-height: 34px;
                                    height: 34px;
                                    color: #B2B2B2;
                                    font-size: 13px;
                                }
                                .user-icon{
                                    img{
                                        width:30px;
                                        height:30px;
                                        background-color: #6977F1;
                                        border-radius: 5px;
                                        border: 1px solid #E6E6E6;
                                    }
                                    margin-left:4px;
                                }
                            }
                            .user-chat-message{
                                background-color: #5050E6;
                                border-radius: 12px;
                                border-top-right-radius: 0;
                                box-sizing: border-box;
                                color: #fff;
                                // cursor: pointer;
                                display: inline-block;
                                font-size: 14px;
                                line-height: 22px;
                                min-height: 26px;
                                outline: none;
                                padding: 9px 14px;
                                white-space: normal;
                                word-break: break-word;
                            }
                        }
                        .robot-chat-area{
                            align-items: flex-start;
                            display: flex;
                            flex-direction: column;
                            justify-content: flex-start;
                            margin-bottom: 10px;
                            position: relative;
                            .robot-box{
                                display: flex;
                                flex-direction: row;
                                align-items: center;
                                margin-bottom: 3px;
                                .robot-name{
                                    line-height: 34px;
                                    height: 34px;
                                    color: #B2B2B2;
                                    font-size: 13px;
                                }
                                .robot-icon{
                                    img{
                                        width:30px;
                                        height:30px;
                                        background-color: #6977F1;
                                        border-radius: 5px;
                                        border: 1px solid #E6E6E6;
                                    }
                                        margin-right:4px;
                                    // background-color: #dfdfff
                                }
                            }
                            .robot-chat-message{
                                background-color: #F9FBFC;
                                border-radius: 0 12px 12px;
                                max-width: 100%;
                                padding: 12px 14px;
                                // width: 100%;
                                box-sizing: border-box;
                                overflow: hidden;
                                ::v-deep(.github-markdown-body) {
                                    padding: 0;
                                    font-size: 13px !important;
                                    p{
                                        margin-bottom: 0;
                                    }
                                }
                            }
                        }
                    }
                    .preface{
                        display: flex;
                        justify-content: space-between;
                        .preface-left{
                            width: 6%;
                            img{
                                width:25px;
                                height:25px;
                                background-color: #6977F1;
                                border-radius: 5px;
                            }
                        }
                        .preface-right{
                            width: 94%;
                            background-color: white;
                            border-radius: 5px;
                            padding: 20px;
                            .preface-font{
                                line-height: 30px;
                                font-size: 12px;
                                text-align: left;
                                color: #878AA5;
                            }
                            .preface-tool{
                                background-color: #F7F8FD;
                                padding: 20px 10px;
                                border-radius: 5px;
                                cursor: pointer; 
                                .preface-title{
                                    display: flex;
                                    align-items: center;
                                    img{
                                        width:20px;
                                        height:20px;
                                        border-radius: 5px;
                                    }
                                }
                                .preface-content{
                                    overflow: hidden; 
                                    white-space: nowrap;
                                    text-overflow:ellipsis;
                                    width: 100%;
                                    line-height: 30px;
                                    font-size: 12px;
                                    text-align: left;
                                    color: #878AA5;
                                }
                            }
                        }
                    }
                } 
                .bottom-area{
                    background-color: transparent;
                    padding-top: 10px;
                    bottom: 0;
                    // position: absolute;
                    text-align: center;
                    width: 100%;
                    z-index: 11;
                    .bar{
                        border-top: 1px solid transparent;
                        background-color: transparent;
                        box-sizing: border-box;
                        padding: 0 16px;
                        width: 100%;
                        align-items: center;
                        display: flex;
                        justify-content: flex-start;
                        .tools-list{
                            background: transparent;
                            display: flex;
                            height: 48px;
                            padding-top: 10px;
                            .tools-item{
                                background: #fff;
                                box-shadow: 1px 1px 5px 0 #d9d9ff;
                                align-items: center;
                                border-radius: 8px;
                                cursor: pointer;
                                display: flex;
                                height: 30px;
                                margin-right: 8px;
                                outline: none;
                                padding: 6px 8px;
                                .tool-icon{
                                    background-repeat: no-repeat;
                                    background-size: cover;
                                    display: inline-block;
                                    height: 20px;
                                    width: 20px;
                                }
                                .tool-name{
                                    color: #1c1f1e;
                                    font-family: PingFangSC-Regular;
                                    font-size: 12px;
                                    line-height: 12px;
                                    margin-left: 6px;
                                }
                            }
                        }
                    }
                    .submit-area{
                        // padding: 0 16px 12px;
                        box-sizing: border-box;
                        width: 100%;
                        .content-area{
                            background-image: linear-gradient(90deg, #5050E6, #b6b6ff);
                            border-radius: 12px;
                            box-shadow: 0 0 12px 0 rgba(0,155,109,.15);
                            box-sizing: border-box;
                            height: 100%;
                            padding: 2px;
                            width: 100%;
                            .user-input-area{
                                border: 0 !important;
                                border-radius: 10px;
                                padding: 8px 10px;
                                position: relative;
                                transition: opacity .5s;
                                width: 100%;
                                background-color: #fff;
                                .top-area{
                                    display: flex;
                                    position: relative;
                                    .text-area{
                                        line-height: 1.4;
                                        flex: 1;
                                        font-size: 14px;
                                        overflow: hidden;
                                        position: relative;
                                        .tool-tip{
                                            align-content: center;
                                            align-items: center;
                                            background-color: #f0f2f2;
                                            border-radius: 4px;
                                            display: flex;
                                            height: 26px;
                                            line-height: 26px;
                                            padding-left: 10px;
                                            padding-right: 0;
                                            cursor: text;
                                            left: 0;
                                            position: absolute;
                                            top: 0;
                                            font-size: 14px;
                                        }
                                        .user-input{
                                            font-size: 14px;
                                            height: 62px;
                                            border: none;
                                            box-sizing: border-box;
                                            color: #222;
                                            line-height: 20px;
                                            outline: 0;
                                            overflow-y: auto;
                                            width: 100%;
                                        }
                                        .show-tool-tip{
                                            line-height: 26px;
                                        }
                                    }
                                    textarea{
                                        font: 100% arial, helvetica, clean;
                                        overflow: auto;
                                        vertical-align: top;
                                        resize: none;
                                    }
                                }
                                .bottom-info-area{
                                    display: flex;
                                    justify-content: flex-end;
                                    margin-top: 5px;
                                    .send-btn{
                                        background: linear-gradient(316deg, #5050E6 16.71%, #5050E6 116.53%);
                                        border-radius: 10px;
                                        height: 26px;
                                        position: relative;
                                        width: 36px;
                                        &:after{
                                            background: url(https://edu-wenku.bdimg.com/v1/pc/aigc/presentation-sample/send-1702458556573.svg) no-repeat;
                                            background-size: cover;
                                            content: "";
                                            height: 16px;
                                            position: absolute;
                                            top: 50%;
                                            transform: translate(-50%, -50%);
                                            width: 16px;
                                        }
                                    }
                                    .disabled{
                                        cursor: not-allowed;
                                    }
                                }
                            }
                        }
                    }
                    .tip{
                        line-height: 25px;
                        font-size: 12px;
                        text-align: left;
                        color: #9E9EBB;
                    }
                }
            }
        }
    }
    
</style>
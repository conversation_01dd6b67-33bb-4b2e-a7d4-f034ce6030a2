<template>
    <div class="common-container">
        <div class="single-page-container">
            <div class="single-page-header">
                <div class="left-box">
                    <!-- <el-icon style="margin-right: 10px;"><Back /></el-icon> -->
                    <el-button :icon="Back" @click="back()" text style="margin-right: 10px;" link></el-button>
                    <!-- <img :src="baseUrl+documentSegmentationInfo.imageUrl" /> -->
                    <div class="text-box">
                        <div class="title">
                            {{documentSegmentationInfo.name}}
                            <!-- <el-button text :icon="Edit" @click="editDocumentName"></el-button> -->
                        </div>
                        <div class="detail">
                            <el-tag type="info" style="margin-right: 5px;">
                            {{ documentSegmentationInfo.data_source_type==='upload_file'?'本地':'其他'}}
                            </el-tag>
                            <!-- <el-tag type="info" style="margin-right: 5px;">
                            {{ documentSegmentationInfo.dataset_process_rule?
                                (documentSegmentationInfo.dataset_process_rule.mode=='automatic'?'自动分段':
                                 documentSegmentationInfo.dataset_process_rule.mode=='custom'?   '自定义分段':
                                 documentSegmentationInfo.dataset_process_rule.mode=='advanced'? '智能分段':
                                 '其他'):'' }}
                            </el-tag> -->
                            <el-tag type="info" style="margin-right: 5px;">
                            {{ documentSegmentationInfo.segment_count+'分段'}}
                            </el-tag>
                        </div>
                    </div>
                </div>
                <div  class="right-box">
                    <el-dropdown trigger="click">
                        <el-button class="add-Segmention" :disabled="!(tabSelectLable=='分段视图')" :icon="Plus">添加分段</el-button>
                        <template #dropdown>
                            <el-dropdown-menu>
                                <el-dropdown-item @click="creatSegmentation">添加新分段</el-dropdown-item>
                                <el-dropdown-item @click="creatSegmentationBatch">批量添加</el-dropdown-item>
                            </el-dropdown-menu>
                        </template>
                    </el-dropdown>
                    <el-dropdown trigger="click" style="margin-left: 10px;">
                        <el-button>
                            设置<el-icon class="el-icon--right"><arrow-down /></el-icon>
                        </el-button>
                        <template #dropdown>
                            <el-dropdown-menu>
                                <!-- <el-dropdown-item @click="editDocumentName">编辑名称</el-dropdown-item>
                                <el-dropdown-item @click="">更新内容</el-dropdown-item> -->
                                <el-dropdown-item @click="reEmbedDoc" v-if="documentSegmentationInfo.data_source_type=='upload_file' && documentSegmentationInfo.enabled">重新分段</el-dropdown-item>
                                <el-dropdown-item @click="updateFrequency" v-if="documentSegmentationInfo.data_source_type!='upload_file'">更新频率</el-dropdown-item>
                                <el-dropdown-item divided style="color:red" @click="delDocumentMethod">删除</el-dropdown-item>
                            </el-dropdown-menu>
                        </template>
                    </el-dropdown>
                </div>
            </div>
        </div>
        <div class="common-content">
            <el-row style="padding: 0 20px;">
                <el-col :span="2">
                    <!-- <el-popover :visible="statePopover" placement="bottom" :width="0" :show-arrow="false" popper-class="customPopper">
                        <div class="custom-cursor">
                            <div v-for="(item,index) in tabs" :key="index" :class="tabSelectLable==item?'tab_click':'tab'" @click="changeSelect(item)">
                                <el-icon :size="11" :style="{'color': tabSelectLable==item?'#5050E6':'white'}" style="margin:0 8px">
                                    <Check/>
                                </el-icon>
                                {{ item }}</div>
                        </div>
                        <template #reference>
                            <div ref="stateRef" @click="checkState" class="custom-cursor state">
                                <span style="font-size:14px">{{tabSelectLable}}</span>
                                <el-icon :size="13">
                                    <CaretBottom/>
                                </el-icon>
                            </div> 
                        </template>
                    </el-popover> -->
                </el-col>
                <el-col :span="22" style="text-align: right;">
                    <el-select v-model="queryParams.enabled" @change="getDocumentSegmentsListMethod" style="width: 100px;margin-right: 10px;">
                        <el-option key="all" label="全部" value="all" />
                        <el-option key="true" label="启用" value="true" />
                        <el-option key="false" label="禁用" value="false" />
                    </el-select>
                    <el-input style="width:25%;"
                        v-model="queryParams.keyword"
                        placeholder="搜索"
                        :prefix-icon="Search"
                        @keyup.enter="getDocumentSegmentsListMethod"
                        clearable
                    />
                </el-col>
            </el-row>
            <el-row style="margin-top: 20px;padding:0 10px;max-height: calc(100vh - 230px);overflow-y: auto;" 
            v-if="tabSelectLable=='分段视图' && documentSegmentationList && documentSegmentationList.length>0">
                <el-col :span="8" style="padding:5px 10px;" v-for="(item,index) in documentSegmentationList" :key="index">
                    <el-card class="segment">
                        <div @click="documentSegmentationEdit(item)">
                            <div class="head">
                                <div class="title">#<span style="margin-left:5px">{{ item.position }}</span></div>
                                <div class="enabled">
                                    <div>{{ item.enabled?'已启用':'已禁用' }}</div>
                                    <div class="icon" :style="{'background-color':item.enabled?'#31C48D':'#ACB2BC'}"></div>
                                    <div class="switch">
                                        <el-divider direction="vertical" />
                                        <el-switch size="small" v-model="item.enabled" @click.stop="" @change="enabledChange(item)" /> 
                                    </div>
                                </div>
                            </div>
                            <div class="content" :style="{'color':item.enabled?'#1F2A37':'#8C9299'}">
                                {{ item.content && item.content.length>300?item.content.substr(0,300)+'...':item.content }}
                            </div>
                        </div>
                        <div class="hidden-area">
                            <div class="prompt">
                                <div style="display:flex;align-items:center">
                                    <el-icon><Reading /></el-icon> 
                                    <span style="margin-left:5px">{{ item.word_count }}</span>

                                    <el-icon style="margin-left:10px"><Aim /></el-icon> 
                                    <span style="margin-left:5px">{{ item.hit_count }} </span>
                                </div>
                                <div style="display: flex;margin-left: 10px; width: 50%;">
                                    <img :src="vector" style="height: 15px;width: 15px;margin-right:5px" />
                                    <div style="overflow: hidden; white-space: nowrap;text-overflow:ellipsis;">
                                        {{ item.index_node_hash }}
                                    </div>
                                </div>
                                <el-button text style="color:#cccccc" :icon="Delete" link @click="delDocumentSegmentationMethod(item)"></el-button>
                            </div>
                        </div>
                    </el-card>
                </el-col>
            </el-row>
            <!--无数据-->
            <div class="common-content" style="display: flex;flex-direction: column;align-items: center;justify-content: center;" v-else>
                <img :src="noData" style="height: 100px;width: 100px;"/>
                <div style="color:#dbdbdb;font-weight:550">
                    暂无数据
                </div>
            </div>
            <div style="margin-top: 20px;padding:0 10px;height: calc(100vh - 210px);overflow-y: auto;" v-if="tabSelectLable=='内容视图'">
                <div class="slice-article">
                    <div class="slice-section">
                        <span>{{ documentSegmentationList[0].content+documentSegmentationList[1].content }}</span>
                    </div>
                </div>
            </div>
        </div>

        <!--新增、编辑分段-->
         <el-dialog v-model="documentSegmentationVisible">
            <template #header="{ close }">
                <div class="my-header" v-if="documentSegmentation.id">
                    <div class="title">#<span style="margin-left:5px">{{ documentSegmentation.position }}</span></div>
                    <div style="margin-right: 20px;">
                        <el-button @click="isEditSeg=false" size="small"
                            v-show="isEditSeg">退出编辑</el-button>
                        <el-button type="primary" :disabled="!documentSegmentation.enabled" 
                            v-show="isEditSeg" @click="submitDocumentSegmentationMethod" size="small">保存</el-button>
                        <el-button type="primary" v-show="!isEditSeg" :disabled="!documentSegmentation.enabled"
                            @click="isEditSeg = true" size="small">编辑内容</el-button>
                    </div>
                </div>
                <div class="my-header-add" v-if="!documentSegmentation.id">
                    <div class="title">#<span style="margin-left:5px">新文本分段</span></div>
                </div>
            </template>
            <div>
                <seg-render v-if="documentSegmentation.id" :seg="documentSegmentation.content" v-show="!isEditSeg" />
                <el-form ref="documentSegmentationForm" :model="documentSegmentation"  v-show="!documentSegmentation.id || isEditSeg"
                    label-width="120px"  :label-position="'top'" :validate-on-rule-change="false">
                    <el-form-item prop="content">
                        <el-input v-model="documentSegmentation.content" 
                            :disabled="!documentSegmentation.enabled" placeholder="" 
                            type="textarea" rows="20"
                            show-word-limit />
                    </el-form-item>
                </el-form>
            </div>
            <div v-if="documentSegmentation.id" style="max-height: 100px;overflow-y: auto;">
                <el-tag v-for="(item,index) in keywordsList" :key="index" type="info" 
                    :closable="isEditSeg && documentSegmentation.enabled" 
                    :disable-transitions="false" @close="handleClose(item)" 
                    style="margin-right: 5px;margin-top:5px" >
                {{ item }}
                </el-tag>
                <el-input v-if="inputVisible" ref="InputRef" v-model="inputValue" class="w-20" size="small" style="margin-top:5px;width:100px"
                @keyup.enter="handleInputConfirm"
                @blur="handleInputConfirm"
                />
                <el-button v-else-if="documentSegmentation.enabled" v-show="isEditSeg" class="button-new-tag" size="small" @click="showInput"  style="margin-top:5px">
                添加关键词
                </el-button>
            </div>
            <template #footer >
                <span class="dialog-footer">
                    <div class="prompt" v-if="documentSegmentation.id">
                        <div style="display:flex;align-items:center">
                            <el-icon><Reading /></el-icon> 
                            <span style="margin-left:5px">{{ documentSegmentation.word_count }} 字符</span>
                        </div>
                        <div style="display:flex;align-items:center;margin-left:10px">
                            <el-icon><Aim /></el-icon> 
                            <span style="margin-left:5px">{{ documentSegmentation.hit_count }} 召回次数</span>
                        </div>
                        <div style="display: flex;margin-left: 10px; width: 40%;margin-right: 20px;">
                            <img :src="vector" style="height: 15px;width: 15px;margin-right:5px" />
                            <div style="overflow: hidden; white-space: nowrap;text-overflow:ellipsis;">
                                向量哈希：{{ documentSegmentation.index_node_hash }}
                            </div>
                        </div>
                        <div class="enabled">
                            <div>{{ documentSegmentation.enabled?'已启用':'已禁用' }}</div>
                            <div class="icon" :style="{'background-color':documentSegmentation.enabled?'#31C48D':'#ACB2BC'}"></div>
                            <div class="switch">
                                <el-divider direction="vertical" />
                                <el-switch size="small" v-model="documentSegmentation.enabled" @click.stop="" @change="enabledChange(documentSegmentation)" /> 
                            </div>
                        </div>
                    </div>
                    <div v-if="!documentSegmentation.id">
                        <el-button @click="documentSegmentationVisible=false">取消</el-button>
                        <el-button type="primary" @click="submitDocumentSegmentationMethod" :disabled="codeDisabled">保存</el-button>
                    </div>
                </span>
            </template>
        </el-dialog>

        <!--批量添加-->
        <el-dialog v-model="documentSegmentationBatchVisible" class="document-segmentation-batch" width="520px">
            <template #header="{ close }">
                <div class="head-title">批量添加分段 </div>
            </template>
            <div class="batch-prompt">
                <div>
                    <no-auto-upload :limit="1" :fileType="['csv']" ref="uploadRef" v-model="documentSegmentationBatch.documentSegmentationBatchFile"
                    :upload-file-url="documentSegmentationBatch.url" @submitFileFormSuccess="submitFileFormSuccess"></no-auto-upload>
                </div>
                <div class="prompt-title">CSV 文件必须符合以下结构：</div>
                <div class="batch-table">
                    <div class="table-th">分段内容</div>
                    <div class="table-tr">内容1</div>
                    <div class="table-tr">内容2</div>
                </div>
                <div>
                    <el-link type="primary" :underline="false" class="import-Template" @click="importTemplate">
                        <el-icon style="color: #5050E6;margin-right: 3px;"><Download /></el-icon>
                        <div>下载模板</div>
                    </el-link>
                </div>
            </div>
            <template #footer >
                <span class="dialog-footer">
                    <div>
                        <el-button @click="documentSegmentationBatchVisible=false">取消</el-button>
                        <el-button type="primary" @click="submitFileForm">导入</el-button>
                    </div>
                </span>
            </template>
        </el-dialog>

        <!-- 编辑名称 -->
        <el-dialog v-model="documentNameEditVisible" title="编辑名称">
            <div style="padding:0 20px">
                <el-form ref="documentNameEditForm" :model="documentNameEdit"
                     label-width="120px"  :label-position="'top'" :validate-on-rule-change="false">
                    <el-form-item prop="name">
                        <el-input v-model="documentNameEdit.name" placeholder="" type="text" />
                    </el-form-item>
                </el-form>
            </div>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="documentNameEditVisible=false">取消</el-button>
                    <el-button type="primary" @click="submitDocumentNameEdit">保存</el-button>
                </span>
            </template>
        </el-dialog>
        <!--更新频率-->
        <el-dialog v-model="updateFrequencyVisible" title="更新频率">
            <div style="padding:0 20px">
                <el-select v-model="frequency" style="width: 100%">
                    <el-option :key="1" label="不自动更新" :value="1"/>
                    <el-option :key="2" label="每天" :value="2"/>
                    <el-option :key="3" label="每3天" :value="3"/>
                    <el-option :key="4" label="每7天" :value="4"/>
                    <el-option :key="5" label="每30天" :value="4"/>
                </el-select>
            </div>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="updateFrequencyVisible=false">取消</el-button>
                    <el-button type="primary" @click="submitFrequency">保存</el-button>
                </span>
            </template>
        </el-dialog>
    </div>
</template>
<script setup>
import { nextTick, ref } from 'vue'
import {Back,Search,Edit,Delete,Reading,Aim, Plus,Download} from '@element-plus/icons-vue'
import { useRoute } from 'vue-router';
import { getDocumentSegmentsList,getDocumentdInfo,editDocumentSegmentationt,addDocumentSegmentation,delDocument,delDocumentSegmentation,
    enableDocumentSegmentation,disableDocumentSegmentation
} 
from "@/api/knowledgeBase/knowledgeBase";
import router from "@/router";
import { ElMessage, ElMessageBox, ElRadioGroup } from 'element-plus'
import vector from "@/assets/icons/svg/vector.svg";
import { saveAs } from 'file-saver';
import * as XLSX from 'xlsx';
import { getToken } from "@/utils/auth";
import noAutoUpload from '@/components/noAutoUpload/index.vue'
import segRender from './components/segRender'
import noData from "@/assets/icons/svg/no-data.svg";

const { proxy } = getCurrentInstance();

const route = useRoute();
const baseUrl = import.meta.env.VITE_APP_BASE_API;

const id = route.query.id
const knowledgeId = route.query.knowledgeId

const documentSegmentationInfo = ref({})

const documentNameEditVisible = ref(false)
const documentNameEdit = ref({})

const statePopover = ref(false);
const tabSelectLable = ref('分段视图')
const tabs = ref(['内容视图','分段视图'])

const isEditSeg = ref(false)

const queryParams = ref({
    keyword:'',
    enabled:'all'
})

const documentSegmentationList = ref([])

// const getocumentSegmentationInfo = () => {
//     documentSegmentationInfo.value.tagList=['在线','不自动更新','自动分段','45分段']
//     documentSegmentationInfo.value.id=id
//     documentSegmentationInfo.value.name='探索人工智能的未来'
// }

//编辑名称
const editDocumentName = () => {
    documentNameEdit.value.id=documentSegmentationInfo.value.id
    documentNameEdit.value.name=documentSegmentationInfo.value.name
    documentNameEditVisible.value=true
}

//触发下拉
const checkState = () => {
    statePopover.value=!statePopover.value;
}
//切换
const changeSelect = (item) => {
    tabSelectLable.value=item;
    statePopover.value=!statePopover.value;
}

const documentSegmentationVisible = ref(false)
const documentSegmentation = ref({})
//添加新分段
const creatSegmentation = () => {
    documentSegmentation.value={content:'', enabled: true}
    documentSegmentationVisible.value=true
}
const documentSegmentationBatchVisible = ref(false)
/*** 参数 */
const documentSegmentationBatch = ref({
    documentSegmentationBatchFile:'',
    // 上传的地址/console/api/datasets/728083e3-048b-4895-a2f8-0a6a5d06cc31/documents/39baf55b-be8d-4155-b32e-639e4a110bd6/segments/batch_import
    url: import.meta.env.VITE_APP_DIFY_API+'/console/api/datasets/'+knowledgeId+'/documents/'+id+'/segments/batch_import',
});
const uploadRef = ref(null)
//批量添加
const creatSegmentationBatch = () => {
    documentSegmentationBatch.value.documentSegmentationBatchFile=''
    documentSegmentationBatchVisible.value=true
}

/** 提交上传文件 */
function submitFileForm() {
    uploadRef.value.submitFileForm();
};

//上传成功
const submitFileFormSuccess = () => {
    documentSegmentationBatchVisible.value=false
    getDocumentSegmentsListMethod()
}

//导出模板
const importTemplate = () => {
    // 创建数据
    let data = [
        ["分段内容"],
        ["内容1"],
        ["内容2"],
    ];

    // 创建工作簿
    const ws = XLSX.utils.aoa_to_sheet(data);
    // 创建工作簿并添加工作表
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, "Sheet1");
    // 生成SCV字符串
    const csv = XLSX.utils.sheet_to_csv(ws);
 
    // 创建blob对象
    const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });

    // 设置文件名
    const fileName = "template.csv";

    // 触发下载
    saveAs(blob, fileName);
}

const inputValue = ref('')
const inputVisible = ref(false)
const keywordsList = ref([])
const InputRef = ref(null)

const handleClose = (tag) => {
    if(keywordsList.value.length == 1){
        ElMessage.warning("需要至少保留一个关键词")
        return
    }
    keywordsList.value.splice(keywordsList.value.indexOf(tag), 1)
}
const showInput = () => {
  inputVisible.value = true
  nextTick(() => {
    InputRef.value.input.focus()
  })
}

const handleInputConfirm = () => {
  if (inputValue.value) {
    keywordsList.value.push(inputValue.value)
  }
  inputVisible.value = false
  inputValue.value = ''
}

const documentSegmentationEdit = (item) => {
    documentSegmentation.value=item
    keywordsList.value=JSON.parse(JSON.stringify(documentSegmentation.value.keywords))
    documentSegmentationVisible.value=true
    isEditSeg.value = false
}

const  codeDisabled = ref(false)
//
const submitDocumentSegmentationMethod = () =>{
    if(!documentSegmentation.value.content){
        ElMessage.warning("内容不能为空")
        return
    }
    codeDisabled.value=true
    setTimeout(() => {
        codeDisabled.value=false
    }, 30000);
    if(documentSegmentation.value.id){
        let query={
            knowledgeId:knowledgeId,
            documentsId:id,
            id:documentSegmentation.value.id,
            content:documentSegmentation.value.content,
            keywords:JSON.parse(JSON.stringify(keywordsList.value))
        }
        editDocumentSegmentationt(query).then(response=>{
            proxy.$modal.msgSuccess("修改成功")
            documentSegmentationVisible.value=false
            isEditSeg.value = false
            getDocumentSegmentsListMethod()
            codeDisabled.value=false
        })
    }
    else{
        let query={
            content:documentSegmentation.value.content,
            enabled: true
        }
        addDocumentSegmentation(query,knowledgeId,id).then(response=>{
            proxy.$modal.msgSuccess("新增成功")
            documentSegmentationVisible.value=false
            isEditSeg.value = false
            getDocumentSegmentsListMethod()
            codeDisabled.value=false
        })
    }
}

const back = () => {
    if(knowledgeId){
        router.push("/knowledgeBase/documentManagement?id=" + knowledgeId);
    }else{
        router.push("/knowledgeBase");
    }
}

//更新频率
const updateFrequencyVisible = ref(false)
const frequency = ref(1)
const updateFrequency = () => {
    frequency.value=documentSegmentationInfo.value.frequency
    updateFrequencyVisible.value=true
}
const submitFrequency = () => {


    updateFrequencyVisible.value=false
}


//重新分段
const reEmbedDoc = () => {
    router.push('/knowledgeBase/upload?type=doc&opt=resegment&doc_id='+id+'&knowledge_id='+knowledgeId)
}

//删除
const delDocumentMethod = () => {
    let data={
        id:id,
        knowledgeId:knowledgeId
    }
    ElMessageBox.confirm(
        '删除后关联文档中的引用将失效',
        '要删除这个文档吗？',
        {
            confirmButtonText: '确认',
            cancelButtonText: '取消',
            type: 'warning',
        }
    ).then(() => {
        delDocument(data).then(response=>{
            proxy.$modal.msgSuccess("删除成功");
            router.push("/knowledgeBase/documentManagement?id=" + knowledgeId);
        })
    }).catch(() => {
    })
}

//文档详情
const getDocumentdInfoMethod = () =>{
    getDocumentdInfo(knowledgeId,id).then(response=>{
        documentSegmentationInfo.value= JSON.parse(JSON.stringify(response)) 
    })
}

//分段列表
const getDocumentSegmentsListMethod = () => {
    getDocumentSegmentsList(queryParams.value,knowledgeId,id).then(response=>{
        if(documentSegmentationList.value.length>0 && queryParams.value.last_id){
            documentSegmentationList.value=[...documentSegmentationList.value , ...response.data]
        }else{
            documentSegmentationList.value=response.data
        }
        if(response.has_more){ 
            queryParams.value.last_id=documentSegmentationList.value[documentSegmentationList.value.length-1].id
            getDocumentSegmentsListMethod()
        }else{
            delete queryParams.value.last_id
        }
    })
} 

//删除分段
const delDocumentSegmentationMethod = (item) =>{
    proxy.$modal.confirm('是否确认删除#'+item.position+'分段？').then(function() {
        return delDocumentSegmentation(knowledgeId,id,item.id);     
    }).then(() => {
        proxy.$modal.msgSuccess("删除成功");  
        getDocumentdInfoMethod();
        getDocumentSegmentsListMethod()
    }).catch(() => {});
} 

//分段开启、关闭
const enabledChange = (item) => {
    let query = {
        knowledgeId:knowledgeId,
        SegmentationId:item.id
    }
    if(item.enabled){
        enableDocumentSegmentation(query).then(response=>{
            getDocumentSegmentsListMethod()
        })
    }else{
        disableDocumentSegmentation(query).then(response=>{
            getDocumentSegmentsListMethod()
        })
    }
}

getDocumentdInfoMethod()
getDocumentSegmentsListMethod()

</script>

<style scoped lang="scss">
    .common-container {
        display: flex;
        flex-direction: column;
        background-color: #F7F7FA;
        height: calc(100vh - 50px);
        width: 100%;
        margin: 0;
        padding: 0;
        background-size: cover;
        .enabled{
            font-size: 14px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            .icon{
                margin-left: 5px;
                width: 10px;
                height:10px;
                border-radius: 3px;
            }
        }
    }

    .single-page-container{
        background-color: #F7F7FA;
        .single-page-header{
            height: 80px;
            display: flex;
            background-color: white;
            margin: 0 20px;
            justify-content: space-between;
            align-items: center;
            flex-direction: row;
            border-radius: 8px;
            .left-box{
                margin-left:24px;
                display: flex;
                align-items: center;
                .text-box{
                    display: flex;
                    flex-direction: column;
                    margin-left: 10px;
                    .title{
                        display: flex;
                        align-items: center;
                        height: 25px;
                        font-size: 18px;
                        font-weight: 500;
                        color: #032220;
                    }
                    .detail{
                        height: 17px;
                        font-size: 12px;
                        color: #999999;
                        font-weight: 400;
                        margin-top: 4px;
                    }
                }
                img {
                    height: 40px;
                    width: 40px;
                    border-radius: 4px;
                    border: 1px solid #E6E6E6;
                }
            }
            .right-box{
                margin-right: 20px;
                .add-Segmention{
                    font-size: 14px;
                    font-weight: 500;
                    &:hover{
                        .hidden-area{
                            display:block;
                        }
                    }
                    .add-line{
                        height: 17px;
                        margin-right: 5px;
                    }
                }
            }
        }
        .single-page-content{
            display: flex;
            flex-direction: row;
            height: calc(100vh - 80px);
            overflow-y: auto;
            .content-left-box{
                width: calc(60% - 48px);
                height: calc(100% - 60px);
                border-right: 1px solid #ccc;
                background-color: white;
                margin-top: 30px;
                .content-left-box-title{
                    padding-left: 24px;
                    font-size: 14px;
                    font-weight: bold;
                    color: #032220;
                    margin-bottom: 16px;
                }
                .left-box-half-top{
                    border-bottom: 1px solid #ccc;
                }
                .custom-form-item-slider{
                    display: flex;
                    flex-direction: row;
                    width: 100%;
                    align-items: center;
                    justify-content: space-between;
                    margin-bottom: 5px;
                    .custom-slider-title{
                        margin-right: 20px;
                        width: 120px;
                        text-align: left;
                        font-size: 12px;
                        color: #999999;
                    }
                    .custom-slider-value{
                        width: calc(100% - 120px);
                        display: flex;
                        flex-direction: row;
                    }
                }
            }
            .content-right-box-title{
                color: #032220;
                font-size: 14px;
                font-weight: 500;
                margin-bottom: 10px;
            }
            
            
        }
}

.common-content{
    height: calc(100vh - 150px);
    background-color: white;
    padding: 20px;
    margin: 10px 20px 0 20px;
    border-radius: 8px;
    .custom-cursor { cursor: pointer; }
    .state{
        line-height: 32px;
        border-radius: 8px;
        padding:0 5px
    }
    .my-header {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        gap: 16px;
    }
    .slice-article {
        background-color: #fff;
        border: 1px solid rgba(29, 28, 35, .12);
        border-radius: 8px;
        display: flex;
        flex-direction: column;
        height: calc(100% - 24px);
        margin: 0 24px;
        overflow-y: auto;
        padding: 12px 100px;
        position: relative;
    }
    .slice-section {
        line-height: 24px;
        padding: 8px;
    }
    .segment{
        cursor: pointer;
        background-color:#e5e5e538;
        position:relative;
        .head{
            height: 24px;
            margin-bottom: 10px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        &:hover{
            background-color:white;
            .hidden-area{
                display:block;
            }
            .switch{
                display:block;
            }
            .title{
                background-color: white;
            }
        }
        .switch{
            margin-left: 10px;
            display:none;
        }
        .hidden-area{
            cursor:auto;
            display:none;
            bottom:0px;
            right:0px;
            position:absolute;
            width: 100%;
            padding:10px 20px;
            .prompt{
                padding:10px 0;
                font-size: 12px;
                color: #999999;
                font-weight: 400;
                display:flex;
                justify-content:space-between;
                align-items: center;
                background-color:white;
            }
        }
        .title{
            font-weight: 500;
            padding: 2px 0 2px 7px;
            border: 1px solid #ece7e7;
            width: 60px;
            border-radius: 8px;
            color:#9e9e9e;
            font-size: 14px;
            font-style: italic;
        }
        .content{
            padding:5px;
            height:200px;
            text-overflow: ellipsis;
            overflow: hidden;
        }
        
    }
}
.custom-cursor { cursor: pointer; }

.my-header {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    gap: 16px;
    .title{
        font-weight: 500;
        padding: 2px 0 2px 7px;
        border: 1px solid #ece7e7;
        width: 60px;
        border-radius: 8px;
        color:#9e9e9e;
        font-size: 14px;
        font-style: italic;
    }
    
}
.my-header-add{
    .title{
        font-weight: 500;
        padding: 2px 0 2px 7px;
        border: 1px solid #ece7e7;
        width: 100px;
        border-radius: 8px;
        color:#9e9e9e;
        font-size: 14px;
        font-style: italic;
    }
}
.prompt{
    font-size: 12px;
    color: #999999;
    font-weight: 400;
    display:flex;
    align-items: center;
    justify-content: space-between;
    background-color:white;
}

.document-segmentation-batch{
    .head-title{
        font-weight:500;
        font-size: 18px;
    }
    .batch-prompt{
        padding:0 20px;
        .prompt-title{
            margin: 10px 0;
            font-weight: 500;
            color: #111928;
        }
        .batch-table{
            border: 1px solid #E5E7EB;
            border-radius: 5px;
            min-height: 50px;
            .table-th{
                padding-left: 10px;
                color:#6B7280;
                font-size: 12px;
                line-height: 35px;
            }
            .table-tr{
                border-top:1px solid #E5E7EB;
                padding-left: 10px;
                color:#374151;
                line-height: 35px;
            }
        }
        .import-Template{
            font-size:12px;
            vertical-align: baseline;
            margin-top: 5px;
            display: flex;
            justify-content: left;
            align-items: center;
        }
    }
}

.tab{
    line-height:20px;
    font-size:13px;
    color: #032220;
    padding: 5px;
    // margin: 2px;
    // border-radius: var(--radius-sm, 4px);
}
.tab_click{
    line-height:20px;
    font-size:13px;
    color: #032220;
    padding: 5px;
    // margin: 2px;
    // border-radius: var(--radius-sm, 4px);
    background: #F4F4F6; 
}
::v-deep .el-dialog{
    display: flex;
    flex-direction: column;
    margin:0 !important;
    position:absolute;
    top:50%;
    left:50%;
    transform:translate(-50%,-50%);
    max-height:calc(100% - 30px);
    max-width:calc(100% - 30px);
}
::v-deep  .el-dialog .el-dialog__body{
    flex:1;
    overflow: auto;
}
</style>
<style >
    .customPopper {
        min-width: 120px!important;
    }
    .el-popover {
        --el-popover-padding:0!important; 
    }
</style>
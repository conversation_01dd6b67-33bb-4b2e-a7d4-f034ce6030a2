<template>
  <!-- 编辑弹窗 -->
  <el-dialog
    :close-on-click-modal="false"
    :modelValue="modelValue"
    title="请编辑作品名称"
    width="334px"
    class="edu-dialog-input"
    @udpate:value="(val) => $emit('update:modelValue', val)"
  >
    <el-input
      v-model="newTitle"
      placeholder="请输入新的标题"
      :maxlength="props.inputlength"
      show-word-limit
    ></el-input>
    <template #footer>
      <span class="dialog-footer">
        <el-button class="cancel-btn" @click="cancel">取 消</el-button>
        <el-button type="primary" @click="confirmEdit">确 定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, defineProps, defineEmits, watch } from 'vue'

const props = defineProps({
  modelValue: {
    type: Boolean,
    required: true
  },
  title: {
    type: String,
    required: false,
    default: ''
  },
  inputlength: {
    type: Number,
    required: false,
    default: 50
  }
})

const emit = defineEmits(['update:value', 'newTitle'])
const newTitle = ref(props.title)

watch(() => props.modelValue , (isVisible) => {
  if (isVisible) {
    newTitle.value = props.title
  }
})

const cancel = () => {
  emit('update:modelValue', false)
}

const confirmEdit = () => {
  emit('newTitle', newTitle.value)
  emit('update:modelValue', false)
}
</script>

<style scoped lang='scss'>
.cancel-btn {
  width: 64px;
  height: 32px;
  background: rgba(0, 0, 0, 0.06);
  border: 0 !important;
  border-radius: 8px;
  color: #1e1f24;
  &:hover {
    background-color: rgba(0, 0, 0, 0.15);
  }
}
</style>
<template>
  <div class="agent-card">
    <img class="app-img" :src="img">
    <span class="app-title">{{ title }}</span>
    <div style="position: absolute; left: 55px; bottom: 40px;">
      <el-tooltip content="聊天助手" effect="light" placement="top" popper-class="tooltip-pop" v-if="app.agentType==1">
          <img :src="advancedChat" style="width:20px;height:20px;" />
      </el-tooltip>
      <el-tooltip content="Agent" effect="light" placement="top" popper-class="tooltip-pop"  v-else>
          <img :src="agent" style="width:20px;height:20px;"/>
      </el-tooltip>
    </div>
    <el-button type="primary" link v-if="app.approveStatus==1" @click="use(app)" class="app-btn">使用</el-button>
    <el-button type="primary" link v-else-if="!app.approveStatus || app.approveStatus==0" @click="apply(app,'agent')" class="app-btn">申请使用</el-button>
    <el-button type="primary" link v-else :disabled="true" class="app-btn">审核中</el-button>


     <inline-svg
        v-if="app.approveStatus==1 && props.isPinned"
        :src="thumbtackFilledSvg"
        @click="() => {pin(props.app, false)}"
        class="btn-pinned"
    />
    <inline-svg
        v-if="app.approveStatus==1 && !props.isPinned"
        :src="thumbtackOutlineSvg"
        @click="() => {pin(props.app, true)}"
        class="btn-unpinned"
    />
  </div>
</template>
<script setup>
import { inject } from 'vue';
import thumbtackFilledSvg from '@/assets/icons/svg/thumbtack-filled.svg';
import thumbtackOutlineSvg from '@/assets/icons/svg/thumbtack-outline.svg';
import InlineSvg from 'vue-inline-svg';
import advancedChat from '@/assets/images/chat_bot.png'
import agent from "@/assets/icons/svg/agent.svg"

const props = defineProps({
  app: {
    type: Object,
    required: true
  },
  img: {
    type: String,
    required: false,
  },
  title: {
    type: String,
    required: false,
  },
  detail: {
    type: String,
    required: false,
  },
  jumpUrl: {
    type: String,
    required: false,
    default: null,
  },
  hasPermission: {
    type: Boolean,
    required: false,
    default: false,
  },
  isPinned: {
    type: Boolean,
    required: false,
    default: false
  }
});

const use = inject('use');
const apply = inject('apply');
const pin = inject('pin');

/**
 * 跳转
 */
function jump() {
  if (props.jumpUrl) {
    router.push(props.jumpUrl);
  }
}
</script>
<style scoped lang="scss">
.agent-card {
    width: 200px;
    height: 60px;
    position: relative;

    img {
        width: 40px;
        height: 40px;
        border-radius: 4px;
        position: absolute;
        left: 10px;
        top: 10px;
    }

    .app-title {
        position: absolute;
        left: 65px;
        top: 10px;
        width: 120px;
        height: 20px;
        overflow: hidden;
        text-overflow: ellipsis;
        text-align: left;
    }

    .app-btn {
        position: absolute;
        right: 10px;
        bottom: 10px;
    }

    .btn-pinned, .btn-unpinned {
        width: 14px;
        height: 14px;
        position: absolute;
        right: 10px;
        top: 10px;
        cursor: pointer;
        fill: #5e6d82;

        &:hover {
            background-color: #fafafa;
            fill: black;
        }
    }

    .btn-unpinned {
        display: none;
    }

    &:hover .btn-unpinned {
        display: block;
    }
}
</style>

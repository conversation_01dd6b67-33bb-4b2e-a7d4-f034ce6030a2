<template>
  <div class="agent-team-item">
    <div
      class="agent-team-item-main"
      @click="jump"
      :class="jumpUrl ? 'custom-cursor' : ''"
    >
      <el-row>
        <el-col :span="4">
          <div>
            <img :src="props.img" />
          </div>
        </el-col>
        <el-col :span="20" class="item-text">
          <el-row style="line-height: 20px">
            <el-col :span="20">
              <div class="text-title">
                {{ props.title }}
              </div>
            </el-col>
            <el-col :span="2">
              <el-tooltip content="工作流" effect="light" placement="top" popper-class="tooltip-pop" v-if="app.mode=='workflow'">
                <img :src="chatTask" style="width:20px;height:20px;border: 0;" />
              </el-tooltip>
              <el-tooltip content="聊天助手" effect="light" placement="top" popper-class="tooltip-pop" v-else-if="app.mode=='advanced-chat'">
                <img :src="advancedChat" style="width:20px;height:20px;border: 0;" />
              </el-tooltip>
              <el-tooltip content="聊天助手" effect="light" placement="top" popper-class="tooltip-pop" v-else-if="app.applicationType=='agent' && app.agentType==1">
                <img :src="advancedChat" style="width:20px;height:20px;border: 0;" />
              </el-tooltip>
              <el-tooltip content="Agent" effect="light" placement="top" popper-class="tooltip-pop" v-else>
                <img :src="agent" style="width:20px;height:20px;border: 0;" />
              </el-tooltip>
            </el-col>
            <el-col :span="2">
              <div class="right-buttons">
                <el-button
                  v-if="props.hasPermission && props.isPinned"
                  text
                  @click="() => {pin(props.app, false)}"
                  class="btn-pinned"
                >
                  <template #icon>
                    <inline-svg :src="thumbtackFilledSvg" />
                  </template>
                </el-button>
                <el-button
                  v-if="props.hasPermission && !props.isPinned"
                  text
                  @click="() => {pin(props.app, true)}"
                  class="btn-unpinned"
                >
                  <template #icon>
                    <inline-svg :src="thumbtackOutlineSvg" />
                  </template>
                </el-button>
              </div>
            </el-col>
          </el-row>
          <el-row style="margin-top: 10px">
            <el-col>
              <div class="text-content">
                <el-tooltip
                  :content="props.detail"
                  placement="bottom"
                  effect="customized"
                  popper-class="card-pop"
                >
                  {{
                    props.detail && props.detail.length > 80
                      ? props.detail.substr(0, 80) + "..."
                      : props.detail
                  }}
                </el-tooltip>
              </div>
            </el-col>
          </el-row>
        </el-col>
      </el-row>
    </div>
    <div class="agent-team-item-footer">
      <div v-if="props.hasPermission && props.hasPermission==1" class="agent-team-item-button">
        <el-button
          type="info"
          size="default"
          @click="() => {use(props.app)}"
          text
        >
          <span style="font-weight: 600">使用</span>
        </el-button>
      </div>
      <div v-else-if="!props.hasPermission || props.hasPermission==0" class="agent-team-item-button">
        <el-button
          type="primary"
          size="default"
          @click="() => {apply(props.app,'team')}"
          text
        >
          <span style="font-weight: 400">申请使用</span>
        </el-button>
      </div>
      <div v-else-if="props.hasPermission || props.hasPermission==2" class="agent-team-item-button">
        <el-button
          type="primary"
          size="default"
          @click="() => {apply(props.app,'team')}"
          text
          :disabled="true"
        >
          <span style="font-weight: 400">审核中</span>
        </el-button>
      </div>
    </div>
  </div>
</template>
<script setup>
import { inject } from 'vue';
import thumbtackFilledSvg from '@/assets/icons/svg/thumbtack-filled.svg';
import thumbtackOutlineSvg from '@/assets/icons/svg/thumbtack-outline.svg';
import InlineSvg from 'vue-inline-svg';
import chatTask from "@/assets/icons/svg/chat-task.svg"
import advancedChat from '@/assets/images/chat_bot.png'
import agent from "@/assets/icons/svg/agent.svg"

const props = defineProps({
  app: {
    type: Object,
    required: true
  },
  img: {
    type: String,
    required: false,
  },
  title: {
    type: String,
    required: false,
  },
  detail: {
    type: String,
    required: false,
  },
  jumpUrl: {
    type: String,
    required: false,
    default: null,
  },
  hasPermission: {
    type: Boolean,
    required: false,
    default: false,
  },
  isPinned: {
    type: Boolean,
    required: false,
    default: false
  }
});

const use = inject('use');
const apply = inject('apply');
const pin = inject('pin');

/**
 * 跳转
 */
function jump() {
  if (props.jumpUrl) {
    router.push(props.jumpUrl);
  }
}
</script>
<style scoped lang="scss">
.agent-team-item {
  margin: 0 10px;
  padding: 24px;
  padding-bottom: 10px;
  border-radius: 8px;
  background: #fff;
  box-shadow: 0 4px 10px 0 #0000000d;
  display: flex;
  flex-direction: column;
  // height: 156px;
  overflow: hidden;


  .btn-unpinned {
    display: none;
  }

  &:hover .btn-unpinned {
    display: block;
  }

  .agent-team-item-main {
    // display: flex;
    // flex-direction: row;
    // justify-content: space-between;
    height: 90px;
    img {
      height: 85%;
      width: 85%;
      border-radius: 4px;
    }

    .item-text {
      // margin-left: 16px;
      // margin-right: 16px;
      min-width: 100px;
      padding-left: 5px;
      .text-title {
        max-height: 20px;
        color: #032220;
        overflow: hidden;
        font-size: 18px;
        font-weight: 600;
        white-space: nowrap;
        // margin-bottom: 4px;
      }
      .text-content {
        // height: 51px;
        max-height: 51px;
        overflow: hidden;
        text-overflow: ellipsis;
        font-size: 12px;
        color: #999999;
        // line-height:18px
      }
    }
  }
  .agent-team-item-footer {
    margin-top: 10px;
    display: flex;
    flex-direction: row;
    justify-content: flex-end;
    align-items: center;
    border-top: 1px solid #e6e6e6;

    .agent-team-item-button {
      display: block;
      place-items: center;
      margin-top: 4px;
    }
  }
}
.right-buttons {
  display: flex;
  box-sizing: border-box;
  justify-content: end;
  align-items: center;
  max-height: 24px;
  ::v-deep .el-button {
    padding: 0 3px !important;
    height: 20px !important;
  }
}
.custom-cursor {
  cursor: pointer;
}
</style>
<style lang="scss">
.card-pop.el-popper {
  max-width: 450px !important;
}
.el-popper.is-customized {
  /* Set padding to ensure the height is 32px */
  // padding: 6px 12px;
  background: #e6e6fa;
  //#E6E6FA
  //linear-gradient(90deg, rgb(159, 229, 151), rgb(204, 229, 129));
}
.el-popper.is-customized .el-popper__arrow::before {
  background: #e6e6fa;
  right: 0;
}
</style>

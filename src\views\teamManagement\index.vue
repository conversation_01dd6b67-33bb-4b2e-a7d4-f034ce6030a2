<template>
    <div>
        <div class="common-container">
            <div class="common-header">
                <div class="title">团队列表</div>
                <div class="header-right">
                    <select-switch name="状态" :tabs="tabs" tabSelectLable="所有团队" tabSelect="1" @changeSelect="changeStatus"></select-switch>
                    <div style="margin: 0 20px;">
                        <el-input
                            v-model="queryParams.teamName"
                            class="w-50 m-2" :maxlength="50"
                            placeholder="搜索"
                            :prefix-icon="Search"
                            @keyup.enter="handleQuery"
                            clearable
                        />
                    </div>
                    <el-button type="primary" style="color: white;font-size: 14px;font-weight: 500;" @click="createTeam()">新建团队</el-button>
                </div>
            </div>
            <div v-loading="loading" class="common-content" v-if="teamList && teamList.length>0">
                <el-row style="max-height: calc(100% - 100px);overflow: auto;">
                    <el-col :span="8" v-for="(item,index) in teamList" :key="index"  style="margin-bottom: 20px;">
                        <custom-card :img="item.teamImgUrl ? baseUrl+item.teamImgUrl : TeamDefaultIcon" :title="item.teamName" :detail="item.teamDescribe"  :is-need-footer="false"
                        :jump-url="item.appId?'/team/graph?id='+item.id:((item.teamType==2 || item.teamType==3)?'/team/flowConfig?id='+item.id:'')">
                            <template #tool>
                                <!-- <el-button text :icon="Tools" @click="settingTeam(item,true)"></el-button> -->
                                <!-- <el-button text :icon="Edit" @click="editTeamMethod(item)"></el-button>
                                <el-button text :icon="Delete" @click="delTeamMethod(item)"></el-button> -->
                                <el-button text :icon="item.isCollected?StarFilled:Star" @click.stop="collectMethod(item.id)"></el-button>
                                <el-dropdown :hide-on-click="false" style="margin-left: 10px;">
                                    <el-button text :icon="More" @click.stop></el-button>
                                    <template #dropdown>
                                    <el-dropdown-menu>
                                        <el-dropdown-item @click="editTeamMethod(item)" icon="Edit">编辑</el-dropdown-item>
                                        <el-dropdown-item @click="handleCopyTeam(item)" icon="CopyDocument">复制</el-dropdown-item>
                                        <el-dropdown-item @click="roleAuth(item.id)" v-if="item.publishStatus && item.publishStatus != 0">
                                            <svg-icon style="margin-right: 5px;" 
                                                :icon-class="'app-auth'" 
                                                :size="'1em'" />
                                                应用授权
                                        </el-dropdown-item>
                                        <el-dropdown-item @click="apiKeyAuth(item.id)" v-if="item.publishStatus && item.publishStatus != 0">
                                            <svg-icon style="margin-right: 5px;" 
                                                :icon-class="'api-key-auth'" 
                                                :size="'1em'" />
                                                API授权
                                        </el-dropdown-item>
                                        <el-dropdown-item @click="delTeamMethod(item)" icon="Delete" style="color:red">删除</el-dropdown-item>
                                    </el-dropdown-menu>
                                    </template>
                                </el-dropdown>
                            </template>
                        </custom-card>
                    </el-col>
                </el-row>
                <pagination
                    v-show="total > 0"
                    :total="total"
                    v-model:page="queryParams.pageNum"
                    v-model:limit="queryParams.pageSize"
                    layout="prev, pager, next"
                    @pagination="getTeamList"
                    style="float: right;"
                    class="mt-4"
                />
            </div>
            <!--无数据-->
            <div class="common-content" style="display: flex;flex-direction: column;align-items: center;justify-content: center;" v-else>
                <img :src="noData" style="height: 100px;width: 100px;"/>
                <div style="color:#dbdbdb;font-weight:550">
                    暂无数据
                </div>
            </div>
        </div>
        <!--新建、编辑团队弹框-->
        <el-dialog v-model="teamVisible" :title="teamTitle">
            <div style="height: calc(100vh - 295px);overflow-y: auto;padding:10px 30px">
                <el-form ref="teamInfoForm" :model="teamInfo" :rules="rules" label-width="120px" :label-position="'top'">
                    <div style="text-align:center">
                        <cropper-image-upload :imageUrl="teamInfo.teamImgUrl" @update:imageUrl="uploadTeamImgUrl" />
                        <!-- <Image-Upload-Min v-model="teamInfo.teamImgUrl" :limit="1" ></Image-Upload-Min> -->
                    </div>
                    <el-row>
                        <el-col>
                            <el-form-item label="团队名称" prop="teamName">
                                <el-input v-model="teamInfo.teamName" :maxlength="50" placeholder="请输入团队名称"/>
                            </el-form-item>
                        </el-col>
                        <el-col :span="24">
                            <el-form-item label="选择团队类型" prop="mode" style="width: 100%;">
                                <team-type-radio v-model="teamInfo.mode" :disabled="!!teamInfo.id"></team-type-radio>
                            </el-form-item>
                        </el-col>
                        <el-col :span="24">
                            <el-form-item label="权限设置" prop="permission" class="visible-permissions" style="width: 100%;">
                                <el-row style="width: 100%;">
                                    <el-col :span="12" style="padding-right:10px">
                                        <div class="radio" @click="teamInfo.permission='only_me'" 
                                        :class="teamInfo.permission=='only_me'?'radio-sel':''">
                                            <el-row>
                                                <el-col :span="5" class="image-box">
                                                    <img :src="onlyme" style="background-color:#52acff54;" />
                                                </el-col>
                                                <el-col :span="16">
                                                    <span>只有我</span>
                                                </el-col>
                                                <el-col :span="3" style="text-align: end;">
                                                    <el-radio-group v-model="teamInfo.permission">
                                                        <el-radio :label="'only_me'">{{ '' }}</el-radio>
                                                    </el-radio-group>
                                                </el-col>
                                            </el-row>
                                        </div>
                                    </el-col>
                                    <el-col :span="12" style="padding-left:10px">
                                        <div class="radio" @click="teamInfo.permission='all_tenant_members'"
                                        :class="teamInfo.permission=='all_tenant_members'?'radio-sel':''">
                                            <el-row>
                                                <el-col :span="5" class="image-box">
                                                    <img :src="team" style="background-color:#52acff54;" />
                                                </el-col>
                                                <el-col :span="16">
                                                    <span>租户内用户</span>
                                                </el-col>
                                                <el-col :span="3" style="text-align: end;">
                                                    <el-radio-group v-model="teamInfo.permission">
                                                        <el-radio :label="'all_tenant_members'">{{ '' }}</el-radio>
                                                    </el-radio-group>
                                                </el-col>
                                            </el-row>
                                        </div>
                                    </el-col>
                                </el-row>
                            </el-form-item>
                        </el-col>
                        <el-col>
                            <el-form-item label="团队概述" prop="teamDescribe">
                                <el-input v-model="teamInfo.teamDescribe"
                                    :maxlength="500" placeholder="请输入团队概述" type="textarea" rows="7" />
                            </el-form-item>
                        </el-col>
                        <!-- <el-col :span="24" >
                            <el-form-item label="选择团队类型" prop="teamType" style="width: 100%;">
                                <el-radio-group v-model="teamInfo.teamType" style="width: 100%;" :disabled="!!teamInfo.id" @change="teamTypeChange()">
                                    <template v-for="teamType in teamTypeOptions">
                                        <el-radio-button :label="teamType.value" style="width: 33%;">
                                            <div class="custom-radio-button">
                                                <div style="display: flex;flex-direction: column;height: 100%;text-align: left;">
                                                    <div style="font-size: 18px;height: 30px;width: 120px;line-height: 30px;">
                                                        {{teamType.label}}
                                                    </div>
                                                    <div style="font-size:12px;height: 50px;line-height:25px;white-space: pre-wrap;overflow: hidden;">
                                                        {{teamType.desc}}
                                                    </div>
                                                </div>
                                            </div>
                                        </el-radio-button>
                                    </template>
                                </el-radio-group>
                            </el-form-item>
                        </el-col> -->
                        <!-- <el-col :span="24" >
                            <el-row style="" class="team-options" :class="{left:teamInfo.teamType == 1,mid:teamInfo.teamType == 2,right:teamInfo.teamType == 3}">
                                <el-form-item label="" prop="teamTypeTemplate" style="width: 100%;">
                                    <template v-for="templateOption in teamTypeTemplateOptions[teamInfo.teamType]">
                                        <el-col :span="6" style="padding: 10px ;">
                                            <div class="team-options-item" 
                                                :class="{
                                                    active:teamInfo.teamTypeTemplate == templateOption.id && !teamInfo.id,
                                                    'is-hover':teamInfo.teamTypeTemplate != templateOption.id && !teamInfo.id,
                                                    'active-disable':teamInfo.teamTypeTemplate == templateOption.id && teamInfo.id,
                                                    'is-hover-disable':teamInfo.teamTypeTemplate != templateOption.id && teamInfo.id,
                                                    }" 
                                                @click="teamInfo.id?'':selectTeamTemplate(templateOption)">
                                                {{templateOption.label}}
                                            </div>
                                        </el-col>
                                    </template>
                                </el-form-item>
                            </el-row>
                        </el-col> -->
                        <!-- <el-col>
                            <el-form-item label="选择团队模板" prop="teamModelId">
                                <div style="overflow-x: auto;display:flex;flex-direction: row;">
                                    <el-card shadow="always" v-for="(item,index) in teamModelList" style="min-width:270px;height:100px;margin-right:10px;" 
                                    @click="selectTeamModel()">
                                        <div style="font-size:14px;padding:5px 10px">{{ item.name }}</div>
                                    </el-card>
                                </div>
                            </el-form-item>
                        </el-col> -->
                    </el-row>
                </el-form>
            </div>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="cancel">取消</el-button>
                    <el-button type="primary" @click="submitTeam">保存</el-button>
                </span>
            </template>
        </el-dialog>

        <!--开始协作-->
        <el-dialog v-model="sessionVisible" :title="sessionDetail.teamName+'协作'" width="70%" v-if="sessionVisible" 
        style="background-color: #F7F7FA;height: calc(100vh - 150px);"
        :close-on-click-modal="false" :close-on-press-escape="false" @close="sessionClose">
            <el-row style="border-top:1px solid #E6E6E6">
                <el-col style="background-color: #ffffff;">
                    <yimai-chat-team style="" 
                    :chat-url="sessionDetail.teamType==1?'ws://172.16.1.19:29998':sessionDetail.id==115?'ws://172.16.1.19:29992':'ws://172.16.1.19:29997'" 
                    :is-disabled-send-set="true" :team-agent-selected-list="teamAgentSelectedList"
                    ref="sessionTeamChat" v-if="sessionContentVisible">
                    </yimai-chat-team>
                </el-col> 
            </el-row>
            <el-dialog v-model="sessionKeyVisible" title="协作需求" width="50%" :align-center="true" v-if="sessionKeyVisible" style="background-color: #F7F7FA;"
            :close-on-click-modal="false" :close-on-press-escape="false" :show-close="false" center>
                <div style="padding: 20px;">
                        <el-form :label-width="'100px'" :label-position="'top'">
                            <el-row>
                                <el-col :span="8" v-for="(key) in keyList" :key="key.keyCode">
                                    <el-form-item  :label="key.keyName" >
                                        <el-input v-model="sessionPamrams[key.keyCode]" style="width: 80%;"></el-input>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                        </el-form>
                    </div>
                    <template #footer>
                        <span class="dialog-footer">
                            <el-button @click="cancelSessionKey">取消</el-button>
                            <el-button type="primary" @click="runSession">开始协作</el-button>
                        </span>
                    </template>
            </el-dialog>
        </el-dialog>

        <el-dialog v-model="historyVisible" :title="'历史记录'" width="70%" style="background-color: #F7F7FA;height:75vh"
            :close-on-click-modal="false" :close-on-press-escape="false" @close="hideHistory">
            <history  style="width: 100%;height: calc(75vh - 54px);"  :team-info="historyTeamInfo"></history>
        </el-dialog>

        <!--点击弹出抽屉，覆盖整个页面-->
        <agent-drawer ref="configEditDrawer">
            <template #content>
                <config-body-container :edit-model="configEditForm" :edit-drawer="configEditDrawer" :is-edit="editState"></config-body-container>
            </template>
        </agent-drawer>
        <!--点击弹出抽屉，覆盖整个页面-->
        <agent-drawer ref="configFlowEditDrawer">
            <template #content>
                <config-flow-body-container :edit-model="configFlowEditForm" :edit-drawer="configFlowEditDrawer"></config-flow-body-container>
            </template>
        </agent-drawer>

        <!--弹出抽屉，覆盖整个页面-->
        <agent-drawer ref="runTestDrawer">
            <template #content>
                <run-test-demo :edit-drawer="runTestDrawer" :edit-model="teamDetail" :is-edit="editState"></run-test-demo>
            </template>
        </agent-drawer>
        <ai-robot-chat />
    </div>
</template>
<script setup>
import {useRouter} from 'vue-router'
import { ref, reactive, toRefs, nextTick } from 'vue'
import { Tools,Edit,Delete,Star,StarFilled,Search,Check,More } from '@element-plus/icons-vue'
import {useResizeObserver} from "@vueuse/core"
import { ElMessage, ElMessageBox } from 'element-plus'
import CustomCard from '@/components/CustomCard/index'
import SelectSwitch from '@/components/SelectSwitch/index'
import {getTeamTypeList,addTeam,newAddTeam,getTeamInfoList,collect,delTeam,editTeam,copyTeam} from '@/api/teamManagement/team'
import YimaiChatTeam from '@/components/YimaiChatTeam/index';
import {getTeamAgentList,getTeamFormList,teamKeyList} from '@/api/teamManagement/config'
import AgentDrawer from '@/components/AgentDrawer/index'
import ConfigBodyContainer from './config';
import ConfigFlowBodyContainer from './flowConfig';
import { getToken } from "@/utils/auth";
import RunTestDemo from './components/runTestDemo'
import {getTeamDetail} from '@/api/teamManagement/configFlow'
import History from './components/history'
import teamTypeRadio from './components/teamType'
import TeamDefaultIcon from '@/assets/icons/svg/team-default.svg'
import AiRobotChat from "@/layout/components/AiRobotChat/index"
import CropperImageUpload from '@/components/CropperImageUpload/index'
import onlyme from "@/assets/icons/svg/onlyme.svg";
import team from "@/assets/icons/svg/team.svg";
import noData from "@/assets/icons/svg/no-data.svg";

const { proxy } = getCurrentInstance();

const configEditDrawer = ref(null)
const configFlowEditDrawer = ref(null)
//编辑
const configEditForm = ref({});
const configFlowEditForm = ref({});

const baseUrl = import.meta.env.VITE_APP_BASE_API;

const statePopover = ref(false);
const stateRef =ref();
const stateColWith =ref(0);

const loading = ref(false)
const router = useRouter()

const tabs = ref([{value:'3',label:'我的收藏'},{value:'1',label:'所有团队'}])
const teamList = ref([])

//搜索
const queryParams = ref({
    listType:'1',
    teamName:'',
    pageNum: 1,
    pageSize: 12,
});
const total = ref(0);
//弹框
const teamVisible = ref(false);
//历史记录弹框
const historyVisible = ref(false);
const historyTeamInfo = ref({})
//标题
const teamTitle = ref('');
//团队信息
const teamInfo = ref({
    image:'',
    teamName:'',
    teamDescribe:'',
    permission: 'only_me',
    teamType:3,
    teamTypeTemplate:null,
    teamModelId:null,
    mode:'advanced-chat'
});
//团队类型选项
const teamTypeOptions = ref([])
//团队类型对应的模板选项
const teamTypeTemplateOptions = ref([])
//规则校验
const rules = ref({
    teamName: [{ required: true, message: "团队名称不能为空", trigger: "blur" }],
    teamDescribe: [{ required: true, message: "团队概述不能为空", trigger: "blur" }],
    teamType: [{ required: true, message: "请选择团队类型", trigger: "blur" }],
    teamTypeTemplate: [{ required: true, message: "请选择模板", trigger: "blur" }],
});
//团队模板列表
const teamModelList = ref([{id:1,name:'数据分析团队'},{id:2,name:'产品设计研发团队'},{id:3,name:'文案生成团队'},{id:4,name:'文案生成团队22'}])

//会话弹框
const sessionVisible = ref(false)
const sessionKeyVisible = ref(false)
const sessionContentVisible = ref(false)
//会话内容
const sessionDetail = ref({})
const teamAgentList = ref([])
//选中的智能体列表
const teamAgentSelectedList = ref([])
const sessionTeamChat = ref(null)
const keyList = ref([])
const sessionPamrams = ref({})

const editState = ref(true)

const runTestDrawer = ref(null)
const teamDetail = ref({})

//触发下拉
const checkState = () => {
    statePopover.value=true;
}
//获取容器宽度
useResizeObserver(stateRef ,(entries) => {
    const entry = entries[0]
    const { width, height } = entry.contentRect;
    stateColWith.value = width;
})

//切换状态
const changeStatus = (states) => {
    queryParams.value.listType=states.value;
    handleQuery();
}
//搜索
const handleQuery = () => {
    queryParams.value.pageNum=1;
    getTeamList();
}

//团队列表
const getTeamList = () => {
    loading.value=true;
    getTeamInfoList(queryParams.value).then(res => {
        teamList.value = res.rows;
        loading.value=false;
        total.value = res.total
    })
    // teamList.value=[{id:1,name:'汽车售后处理团队',teamDescribe:'在中台产品的研发过程中，会出现不同的设计规范和实现方式',img:'',isSc:true},
    //     {id:2,name:'程序开发团队',teamDescribe:'在中台产品的研发过程中，会出现不同的设计规范和实现方式',img:'',isSc:false}];
    // total.value=2;
}

//新建团队
const createTeam = () => {
    reset();
    teamTitle.value='新增团队';
    teamVisible.value=true;
}

//编辑团队
const editTeamMethod = (team) => {
    teamInfo.value=JSON.parse(JSON.stringify(team));
    teamInfo.value.teamTypeTemplate=teamInfo.value.taskId;
    teamTitle.value='编辑团队';
    teamVisible.value=true;
    nextTick(() => {
        teamTypeChange()
    })
}

//API授权
const apiKeyAuth = (id) => {
    router.push('/team/apikey?id='+id)
}

const roleAuth = (id) => {
    router.push('/team/team-role/team/'+id)
}

//配置团队
const settingTeam = (team , type) => {
    editState.value=type
    if(team.teamType==1){
        configEditDrawer.value.open()
        configEditForm.value.teamId=team.id
        // router.push({path:'/team/config',query:{teamId:team.id}})
    }
    if(team.teamType==2){
        configFlowEditDrawer.value.open()
        configFlowEditForm.value.teamId=team.id
        // router.push({path:'/team/configFlow',query:{teamId:team.id}})
    }
    if(team.teamType==3 && type){
        configFlowEditDrawer.value.open()
        configFlowEditForm.value.teamId=team.id
        // router.push({path:'/team/configFlow',query:{teamId:team.id}})
    }
    if(team.teamType==3 && !type){
        getTeamDetail(team.id).then(res => {
            teamDetail.value = res.data
            runTestDrawer.value.open()
        })
    }
}



/** 表单重置 */
const reset = () => {
    teamInfo.value={
        image:'',
        teamName:'',
        teamDescribe:'',
        permission: 'only_me',
        teamType:3,
        teamTypeTemplate:null,
        teamModelId:null,
        mode:'advanced-chat'
    };
  proxy.resetForm("teamInfoForm");
}

//取消
const cancel = () => {
    teamVisible.value=false;
    reset()
}

const uploadTeamImgUrl = (url) => {
    teamInfo.value.teamImgUrl = url
}

//保存
const submitTeam = () => {
    proxy.$refs["teamInfoForm"].validate(valid => {
        if (valid) {
            if(teamInfo.value.id)
            {
                let params = {
                    id:teamInfo.value.id,
                    teamImgUrl:teamInfo.value.teamImgUrl,
                    teamName:teamInfo.value.teamName,
                    permission: teamInfo.value.permission,
                    teamDescribe:teamInfo.value.teamDescribe,
                }
                editTeam(params).then(res => {
                    ElMessage.success(res.msg)
                    teamVisible.value=false;
                    handleQuery()
                })
            }else{
                let params = {
                    teamImgUrl:teamInfo.value.teamImgUrl,
                    teamName:teamInfo.value.teamName,
                    teamDescribe:teamInfo.value.teamDescribe,
                    permission: teamInfo.value.permission,
                    teamType:teamInfo.value.teamType,
                    teamJson:"",
                    mode:teamInfo.value.mode,
                    taskId:teamInfo.value.teamTypeTemplate || '',
                    arrangeJson:JSON.stringify({"nodes":[{"type":"custom","data":{"type":"start","data":{"label":"开始","desc":"工作流的起始节点，用于设置启动工作流所需的信息。"}},"events":{},"id":"start","position":{"x":300,"y":300}},{"type":"custom","data":{"type":"end","data":{"label":"结束","desc":"工作流的最后一个节点，用于在工作流运行后返回结果信息。"}},"events":{},"id":"end","position":{"x":1000,"y":300}}],"edges":[],"position":[-334,-12],"zoom":1,"viewport":{"x":-334,"y":-12,"zoom":1}})
                }
                newAddTeam(params).then(res => {
                    ElMessage.success(res.msg)
                    proxy.$modal.confirm('是否进入团队编排页面？').then(function () {
                        let item=params
                        item.id=res.data
                        router.push('/team/graph?id='+item.id)
                        // settingTeam(item,true)
                    }).then(() => {
                    }).catch(() => {});
                    teamVisible.value=false;
                    handleQuery()
                })
            }
        }   
    })
}

//选择团队模板
const selectTeamModel = () => {
}

const initTeamTypeList = () => {
    getTeamTypeList().then(res => {
        teamTypeOptions.value = res.rows.map(item => {
            return {label: item.tyeName, value: item.id,desc:item.typeDesc}
        })
        let obj ={}
        res.rows.forEach(item => {
            obj[item.id] = item.aiTeamTaskTypeList.map(task => {return {
                label:task.taskName,
                value:task.templateJson,
                id:task.id
            }})
        })
        teamTypeTemplateOptions.value = obj;
    })
}

const selectTeamTemplate = (value) => {
    teamInfo.value.teamTypeTemplate = value.id
    if(proxy.$refs["teamInfoForm"]){
        proxy.$refs["teamInfoForm"].validateField('teamTypeTemplate');
    }
}

//收藏、取消收藏
const collectMethod = (id) =>{
    var query={
        teamId:id
    }
    collect(query).then(res => {
        proxy.$modal.msgSuccess("操作成功");
        getTeamList()
    })
}

//删除
const delTeamMethod = (item) => {
    proxy.$modal.confirm('是否确认删除"' + item.teamName + '"？').then(function () {
        return delTeam(item.id);
    }).then(() => {
        proxy.$modal.msgSuccess("删除成功");
        getTeamList()
    }).catch(() => {});
}

//复制
const handleCopyTeam = (item) => {
  ElMessageBox.prompt('团队名称', '复制团队', {
    confirmButtonText: '复制',
    cancelButtonText: '取消',
    inputPattern: /[^\s]+/,
    inputErrorMessage: '团队名称不能为空',
  })
    .then(({ value }) => {
        copyTeam({
            name: value,
            app_id: item.appId,
            icon: null,
            icon_background: null,
            mode: item.mode
        }).then(() => {
            proxy.$modal.msgSuccess("复制成功");
        })
    })
    .catch(() => {});
}

//团队类型变化
const teamTypeChange = () =>{
    if(teamInfo.value.teamType==1){
        rules.value.teamTypeTemplate=[{ required: true, message: "请选择模板", trigger: "blur" }]
    }else{
        rules.value.teamTypeTemplate=[{required: false}]
        // teamInfo.value.taskId=0;
        if(proxy.$refs["teamInfoForm"]){
            proxy.$refs["teamInfoForm"].validateField('teamTypeTemplate');
        }
    }
}

//开始协作
const initiateConversation = (item) =>{
    sessionDetail.value=item
    sessionVisible.value=true;
    if(item.teamType==1 && item.taskId){
        getTeamAgentListMethod(item.taskId)
        getTeamFormListMethod(item.taskId)
    }else if(item.teamType==2){
        handleTeamAgentList(item.arrangeJson)
        teamKeyListMethod(item.id)
    }else{
        runSession()
    }
}

//获取智能体列表 智能团队
const getTeamAgentListMethod = (taskId) =>{
    let params = {
        taskId:taskId
    }
    getTeamAgentList(params).then((res) => {
        teamAgentList.value = res.rows
        //处理选中的智能体
        teamAgentSelectedList.value=[]
        teamAgentList.value.forEach(item=>{
            if(sessionDetail.value.teamJson.split(',').find(res=>{return res==item.agentId})){
                teamAgentSelectedList.value.push(item)
            }
        })
    });
}
//获取智能体列表  智能团队编排
const handleTeamAgentList = (arrangeJson) =>{
    if(arrangeJson){
        let arrangeJsonNodes = JSON.parse(arrangeJson).nodes
        //处理智能体
        teamAgentSelectedList.value=[]
        arrangeJsonNodes.forEach(element => {
            if(element.data.type=='agent'){
                let teamAgentSelected=element.data.data
                teamAgentSelected.agentId=element.id
                teamAgentSelectedList.value.push(teamAgentSelected)
            }
        });
    }
}
//获取参数列表 智能团队
const getTeamFormListMethod = (taskId) => {
    getTeamFormList(taskId).then ((res) => {
        if(res.data.templateJson && JSON.parse(res.data.templateJson).keys){
            let paramsObj = {}
            keyList.value = JSON.parse(res.data.templateJson).keys
            keyList.value.forEach((item) => {
                paramsObj[item.keyCode] = ''
            })
            sessionPamrams.value=paramsObj;
            sessionKeyVisible.value=true;
        }else{
            runSession()
        }
    })
}
//获取参数列表 智能团队编排
const teamKeyListMethod = (teamId) => {
    teamKeyList(teamId).then((res) => {
        if(res.total>0){
            let paramsObj = {}
            keyList.value =res.rows
            keyList.value.forEach((item) => {
                paramsObj[item.keyCode] = ''
            })
            sessionPamrams.value=paramsObj;
            sessionKeyVisible.value=true;
        }else{
            runSession()
        }
    })
}

//取消参数弹框
const cancelSessionKey = () => {
    sessionKeyVisible.value=false;
    sessionVisible.value=false;
    sessionContentVisible.value=false;
}
//关闭弹窗
const sessionClose = () => {
    sessionContentVisible.value=false;
}
//开始协作
const runSession = () => {
    sessionKeyVisible.value=false;
    sessionContentVisible.value=true;
    // console.log(testPamrams.value)
    nextTick(() => {
        let params={}
        if(sessionDetail.value.teamType==1){
            params={
                teamId: sessionDetail.value.id,
                agentIds:sessionDetail.value.teamJson.split(','),
                keywords:sessionPamrams.value
            }
        }else{
            params={
                "teamId": sessionDetail.value.id,
                "token":getToken(),
                "keywords": sessionPamrams.value,
                "prompt": "{{singer}}",
                "arrangeJson": JSON.parse(sessionDetail.value.arrangeJson)
            }
        }
        sessionTeamChat.value.initChat(params)
    })
}

const showHistory = (item) => {
    historyTeamInfo.value=item
    historyVisible.value = true
}

const hideHistory = () => {
    historyVisible.value = false
}

getTeamList();
initTeamTypeList();

</script>
<style lang='scss' scoped>
.common-container {
    display: flex;
    flex-direction: column;
    background-color: #F7F7FA;
    height: calc(100vh - 52px);
    width: 100%;
    margin: 0;
    padding: 0;
    background-size: cover;
}
.common-header {
    height: 80px;
    padding: 28px 30px;
    line-height: 24px;
    display: flex;
    justify-content: space-between;

    .title {
        color: #032220;
        font-size: 18px;
        font-weight: 500;
    }

    .header-right {
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: row;
    }
}
.common-content {
    margin: 0 20px;
    height: calc(100% - 68px);
}
::v-deep .el-card__footer {
    padding: calc(var(--el-card-padding) - 15px) var(--el-card-padding);
    border-top: 1px solid var(--el-card-border-color);
    box-sizing: border-box;
}
.state{
    line-height: 32px;
    width:125px;
    background-color:#E6E6E6;
    border-radius: 8px;
    padding:0 5px
}
.tab{
    line-height:20px;
    font-size:12px;
    color: #032220;
    padding: 5px;
    margin: 2px;
    border-radius: var(--radius-sm, 4px);
}
.tab_click{
    line-height:20px;
    font-size:12px;
    color: #032220;
    padding: 5px;
    margin: 2px;
    border-radius: var(--radius-sm, 4px);
    background: #F4F4F6; 
}
.custom-cursor { cursor: pointer; }
.agent-team-item-button {
    display: grid;
    place-items: center;
    margin-top: 4px;
    width: 50%;
}
::v-deep .pagination-container{
    background:#F7F7FA
}
.custom-radio-button{
    height: 80px;
    width: 100%;
    display: flex;
    align-items: center;
}
.team-options{
    text-align: center;
    background-color: #eee;
    height: 100px;
    line-height: 80px;
    position: relative;
    border-radius: 4px;
    .team-options-item{
        height: 80px;
        line-height: 80px;
        border: 1px solid #C7C7C7;
        border-radius: 4px;
        background-color: white;
        cursor: pointer;
    }
    .is-hover{
        &:hover{
            color: #5050E6;
        }
    }
    .active{
        color: white;
        background-color: #5050E6;
    }
    .is-hover-disable{
        // &:hover{
        //     color: #f2f6fc;
        // }
        cursor: no-drop;
    }
    .active-disable{
        color: #909399;
        background-color: #f2f6fc;
        cursor: no-drop;
    }
}
.visible-permissions{
    margin-top: 0;
    width: 100%;
    .title{
        line-height: 35px;
    }
    .radio{
        height:45px;
        line-height:45px;
        border:1px solid #dcdfe6;
        border-radius:10px;
        cursor: pointer;
        .image-box{
            text-align:center;
            padding-top:6px;
            img{
                height: 25px;
                width: 25px;
                border-radius: 4px;
            }
        }
    }
    .radio-sel{
        border: 1px solid #5050E6;
    }
}
.right::before{
    box-sizing: content-box;
    position: absolute;
    top: -18px;
    right:151px;
    border-bottom:9px solid #eee;
    border-top:9px solid transparent;
    border-left:9px solid transparent;
    border-right:9px solid transparent;
    display: block;
    content:'';
    z-index: 2;
}
.mid::before{
    box-sizing: content-box;
    position: absolute;
    top: -18px;
    right:451px;
    border-bottom:9px solid #eee;
    border-top:9px solid transparent;
    border-left:9px solid transparent;
    border-right:9px solid transparent;
    display: block;
    content:'';
    z-index: 2;
}
.left::before{
    box-sizing: content-box;
    position: absolute;
    top: -18px;
    left:151px;
    border-bottom:9px solid #eee;
    border-top:9px solid transparent;
    border-left:9px solid transparent;
    border-right:9px solid transparent;
    display: block;
    content:'';
    z-index: 2;
}
::v-deep .el-radio-button__inner{
    width: 100%;
}
::v-deep .el-dialog {
    display: flex;
    flex-direction: column;
    margin: 0 !important;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    max-height: calc(100% - 30px);
    max-width: calc(100% - 30px);
}

::v-deep(.el-dialog__body) {
  padding: 0;
}
</style>
<style lang="scss">
    .customPopper {
        min-width: 120px!important;
    }
    .el-popover {
        --el-popover-padding:0!important; 
    }
</style>
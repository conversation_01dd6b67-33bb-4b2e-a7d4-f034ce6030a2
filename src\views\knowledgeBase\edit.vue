<template>
    <div class="content">
        <div class="header">
            <el-button :icon="Back" text @click="prePage"></el-button>
            <div class="title">{{ title }}</div>
        </div>
        <div class="body">
            <template v-if="docType == 'doc' && opt == ''">
                <doc :doc-id="docId" :knowledge-id="route.query.knowledge_id"></doc>
            </template>
            <template v-if="docType == 'url' && opt == ''">
                <url :doc-id="docId" :knowledge-id="route.query.knowledge_id"></url>
            </template>
            <template v-if="opt == 'resegment'">
                <re-embeding :doc-id="docId" :knowledge-id="route.query.knowledge_id"></re-embeding>
            </template>
        </div>
    </div>
</template>

<script>
import { defineComponent } from 'vue';
import defaultSettings from '@/settings'
import useSettingsStore from '@/store/modules/settings'
export default defineComponent({
    beforeRouteEnter(to, from, next) {
        if(!to.query.type){
            next(false)
        }else{
            let title = '新增文档'
            if(to.query.doc_id){
                title = '更新内容'
            }
            to.matched[to.matched.length-1].meta.title = title
            const settingsStore = useSettingsStore();
            if (settingsStore.dynamicTitle) {
                document.title = title + ' - ' + defaultSettings.title;
            } else {
                document.title = defaultSettings.title;
            }
            next()
        }
    }
})
</script>

<script setup>

import { useRoute,useRouter } from 'vue-router';
import { onMounted } from 'vue';
import Doc from './components/doc'
import Url from './components/url'
import ReEmbeding from './components/reEmbeding'
import {Back} from '@element-plus/icons-vue'

const route = useRoute();
const router = useRouter();

const docId = ref('')
const knowledgeId = ref('')
const docType = ref('')
const title = ref('')
const opt = ref('')

const prePage = () => {
    router.back()
}

onMounted(() => {
    if(route.query.doc_id){
        docId.value = route.query.doc_id
        title.value = '更新内容'
    }else{
        title.value = '新增文档'
    }
    if(route.query.type){
        docType.value = route.query.type
    }
    if(route.query.knowledge_id){
        knowledgeId.value = route.query.knowledge_id
    }
    if(route.query.opt){
        opt.value = route.query.opt
    }
})

defineExpose()
</script>
<style scoped lang="scss">
.content{
    .header{
        margin-bottom: 20px;
        height: 60px;
        display: flex;
        flex-direction: row;
        justify-content: start;
        align-items: center;
        box-shadow: 0 2px 2px 0 rgba(29,28,35,.04);
        .title{
            color: #000;
            font-size: 18px;
            font-weight: 600;
            line-height: 60px;
            margin-left: 12px;
        }
    }
    .body{
        padding: 30px 24px;
    }
}
</style>
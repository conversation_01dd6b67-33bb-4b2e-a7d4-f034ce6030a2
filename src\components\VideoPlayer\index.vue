<template>
  <video ref="videoRef" controls :key="Date.now()">
    <source :src="videoUrl" type="video/mp4" />
  </video>
</template>

<script setup>
import subtitleConverter from 'subtitle-converter'
import { ref } from 'vue'

const videoRef = ref(null)

const props = defineProps({
  videoUrl: {
    type: String,
    required: true
  },
  subtitleUrl: {
    type: String,
    required: false
  }
})

// 将 srt 格式转换为 vtt
function srtToVtt(srt) {
  const options = {
    removeTextFormatting: true,
  }

  const { subtitle, status } = subtitleConverter.convert(srt, '.vtt', options)

  return subtitle
}

async function loadSubtitle() {
  if (!props.subtitleUrl) return
  
  try {
    const response = await fetch(props.subtitleUrl)
    const text = await response.text()
    
    if (props.subtitleUrl.endsWith('.srt')) {
      const vtt = srtToVtt(text)
      const blob = new Blob([vtt], { type: 'text/vtt' })
      const url = URL.createObjectURL(blob)
      
      const track = document.createElement('track')
      track.kind = 'subtitles'
      track.label = '中文'
      track.src = url
      track.default = true
      
      videoRef.value.appendChild(track)
    } else if (props.subtitleUrl.endsWith('.vtt')) {
      const track = document.createElement('track')
      track.kind = 'subtitles'
      track.label = '中文'
      track.src = props.subtitleUrl
      track.default = true
      
      videoRef.value.appendChild(track)
    }
  } catch (error) {
    console.error('加载字幕失败:', error)
  }
}

watch(() => props.subtitleUrl, () => {
  loadSubtitle()
})
</script>
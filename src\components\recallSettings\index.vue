<template>
    <el-row class="retrieve-settings">
        <el-col :span="24" v-for="(retrieval,index) in retrievalList" :key="index" style="margin-top: 10px;">
            <div class="radio" 
            :class="recallModel.search_method==retrieval.search_method?'radio-sel':''">
                <div style="display:flex;justify-content:white-space;cursor: pointer;" @click="retrievalClick(retrieval)">
                    <div  class="title-image-box">
                        <img :src="retrieval.img" style="background-color:#7d50e630;" />
                    </div>
                    <div style="width:100%;margin-left:30px">
                        <div class="title">
                            <span>{{retrieval.title}}</span>
                            <el-radio-group v-model="recallModel.search_method">
                                <el-radio :label="retrieval.search_method">{{ '' }}</el-radio>
                            </el-radio-group>
                        </div>
                        <div class="content">
                            {{retrieval.content}}
                        </div>
                    </div>
                </div>
                <div class="launch-content" v-if="recallModel.search_method==retrieval.search_method && retrieval.detailsState">
                    <div style="display:flex;align-items:center;">
                        <span class="font">Rerank 模型</span>  
                        <el-tooltip content="重排序模型将根据候选文档列表与用户问题语义匹配度进行重新排序，从而改进语义排序的结果。" 
                        effect="light" placement="top" popper-class="tooltip-pop">
                            <el-icon class="icon" style="margin-left:5px"><QuestionFilled /></el-icon>
                        </el-tooltip>
                    </div>
                    <el-row>
                        <!-- <el-col :span="24" class="icon-group">
                            <img :src="rerankmodel" style="background-color:#f4f4f5;" />
                            <img :src="warning" style="margin-left: 5px;"/>
                        </el-col> -->
                        <el-col>
                            <model-selector
                                type="rerank"
                                v-model:model-name="recallModel.reranking_model.reranking_model_name"
                                v-model:model-provider="recallModel.reranking_model.reranking_provider_name"
                                :enable-default-model="true"
                                style="width: 100%; margin-top: 10px;"
                            />
                        </el-col>
                    </el-row>
                    <el-row style="margin-top:20px">
                        <el-col :span="12">
                            <div style="display:flex;align-items:center;">
                                <span class="font">Top K</span>
                                <el-tooltip content="用于筛选与用户问题相似度最高的文本片段。系统同时会根据选用模型上下文窗口大小动态调整分段数量。" 
                                effect="light" placement="top" popper-class="tooltip-pop">
                                    <el-icon class="icon" style="margin-left:5px"><QuestionFilled /></el-icon>
                                </el-tooltip>
                            </div>
                        </el-col>
                        <el-col :span="12">
                            <div style="display:flex;align-items:center;">
                                <el-switch v-model="recallModel.score_threshold_enabled" @change="scoreThresholdEnabledChange" /> 
                                <span class="font" style="margin-left:10px">Score 阈值</span>
                                <el-tooltip content="用于设置文本片段筛选的相似度阈值。" 
                                effect="light" placement="top" popper-class="tooltip-pop">
                                    <el-icon class="icon" style="margin-left:5px"><QuestionFilled /></el-icon>
                                </el-tooltip>
                            </div>
                        </el-col>
                        <el-col :span="12" class="slider-demo-block" style="padding-right:10px">
                            <span class="demonstration">
                            {{ recallModel.top_k }}</span>
                            <el-slider v-model="recallModel.top_k" :min="0" :max="10" :step="1" />
                        </el-col>
                        <el-col :span="12" class="slider-demo-block" style="padding-left:10px">
                            <span class="demonstration">
                            {{ recallModel.score_threshold_enabled==true?recallModel.score_threshold:'' }}
                            </span>
                            <el-slider v-model="recallModel.score_threshold" :min="0.01" :max="0.99" :step="0.01" 
                            :disabled="recallModel.score_threshold_enabled==true?false:true"/>
                        </el-col>
                    </el-row>
                </div>
            </div>
        </el-col>
    </el-row>
</template>
<script setup>
    import { nextTick, ref } from 'vue'
    import model from "@/assets/icons/svg/model.svg";
    import vectorretrieval from "@/assets/icons/svg/vectorretrieval.svg";
    import fullTextSearch from "@/assets/icons/svg/fullTextSearch.svg";
    import mixedRetrieval from "@/assets/icons/svg/mixedRetrieval.svg";
    import ModelSelector from '@/components/Model/modelSelector.vue';

    const props = defineProps({
        modelValue: [String, Object, Array],
    });

    const { proxy } = getCurrentInstance();
    const emit = defineEmits();
    const recallModel = ref({});
    watch(() => props.modelValue, val => {
        if (val) {
            recallModel.value = val
            return val
        } else {
            recallModel.value = {
                search_method:'single'
            }
            emit("update:modelValue", recallModel.value);
            return {}
        }
    },{ deep: true, immediate: true });

    //检索设置列表
    const retrievalList = ref([
        {search_method:'single',title:'N选1召回',img:vectorretrieval,detailsState:false,
        content:'根据用户意图和知识库描述，由 Agent 自主判断选择最匹配的单个知识库来查询相关文本，适合知识库区分度大目知识库数量偏少的应用。'},
        {search_method:'multiple',title:'多路召回',img:fullTextSearch,detailsState:true,
        content:'根据用户意图同时匹配所有知识库，从多路知识库查询相关文本片段，重排序步骤，从多路查询结果中选择匹配用户问题的最佳结果，需配置Rerank 模型 APl。'},
    ])

    //切换检索方式处理
    const retrievalClick = (retrieval) => {
        if(retrieval.search_method!=recallModel.value.search_method){
            recallModel.value.search_method=retrieval.search_method;
            if(retrieval.detailsState){
                if(!recallModel.value.reranking_model){
                    recallModel.value.reranking_model={
                        reranking_provider_name:'',
                        reranking_model_name:''
                    }
                    recallModel.value.top_k=2
                    recallModel.value.score_threshold_enabled=false
                    recallModel.value.score_threshold=0.5
                }
            }
            emit("update:modelValue", recallModel.value);
        }
    }

    //Rerank 模型 选择模型处理
    const rerankModelClick = (item) => {
        recallModel.value.reranking_model.reranking_model_name=item.model
        recallModel.value.reranking_model.reranking_provider_name=item.reranking_provider_name
    }

    //点击Rerank 模型下拉 获取宽度赋值给下拉弹框
    const rerankModelWidth = ref(0)
    const rerank_search = ref(null);
    const rerankClick = () => {
        rerankModelWidth.value=rerank_search.value[0].offsetWidth;
    }

    const scoreThresholdEnabledChange = () => {
        
    }
    
  //开启rerank模型，默认值设置第一个 ，关闭默认值设置为空
    // const rerankEnabledChange = (item) => {
    //     if(item){
    //         recallModel.value.reranking_model.reranking_model_name=rerankModelList.value[0].modelList[0].model
    //         recallModel.value.reranking_model.reranking_provider_name=rerankModelList.value[0].modelList[0].reranking_provider_name
    //     }else{
    //         recallModel.value.reranking_model.reranking_model_name=''
    //         recallModel.value.reranking_model.reranking_provider_name=''
    //     }
    // }

</script>
<style scoped lang="scss">
.retrieve-settings{
    .radio{
        line-height: 20px;
        // width:45%;
        border:1px solid #dcdfe6;
        border-radius:10px;
        .title-image-box{
            text-align:center;
            padding-top:10px;
            padding-left:30px;
            img{
                padding:5px;
                height: 35px;
                width: 35px;
                border-radius: 4px;
            }
        }
        .title{
            margin-top:5px;
            display: flex;
            justify-content: space-between;
            align-items: center;

        }
        .content{
            font-size: 13px;
            color:#999999;
            padding-right: 5px;
            min-height:30px;
            padding-bottom: 10px;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        .launch-content{
            min-height: 100px;
            border-top:1px solid #dcdfe6;
            padding: 20px 40px;
            .icon{
                color: #999999;
            }
            .icon-group{
                height:40px;
                background-color:#ffd65236;
                border-radius: 10px; 
                padding: 5px;
                margin-top:5px;
                img{
                    padding:5px;
                    height: 30px;
                    width: 30px;
                    border-radius: 4px;
                }
            }
            .slider-demo-block {
                max-width: 600px;
                display: flex;
                align-items: center;
                .el-slider {
                    margin-top: 0;
                    margin-left: 12px;
                }
                .demonstration {
                    width:80px;
                    font-size: 16px;
                    line-height: 44px;
                    color:#706f6f;
                    font-weight: 540;
                }
            }
            .image-box{
                text-align:center;
                padding-top:10px;
                img{
                    height: 25px;
                    width: 25px;
                    border-radius: 4px;
                }
            }
            .option-title{
                cursor: pointer; 
                line-height: 32px;
                // width:125px;
                background-color:#f3f3f3;
                border-radius: 8px;
                padding:0 5px;
            }
        }
    }
    .font{
        font-size: 1vw; /* 这里的5vw表示字体大小为视口宽度的5% */
        white-space: nowrap; /* 防止文字换行 */
        overflow: hidden; /* 超出容器部分隐藏 */
        text-overflow: ellipsis; /* 超过部分显示省略号 */
    }
    .radio-sel{
        border: 1px solid #5050E6;
    }
}
</style>
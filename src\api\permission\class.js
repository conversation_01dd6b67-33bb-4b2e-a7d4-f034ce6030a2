import request from '@/utils/request'
import qs from 'qs'

// 获取权限列表 /ClassInfo/list?className=
export function getClassList(params) {
  return request({
    url: '/ClassInfo/list',
    method: 'get',
    params,
    paramsSerializer: params => qs.stringify(params, { skipNulls: false, indices: false })
  })
}

// 新增教师查询服务/ClassInfo/selectTeacherList?userName
export function getTeacherList(params) {
  // 确保 userName 始终存在，即使为空字符串
  const userName = params.userName !== undefined ? params.userName : '';

  return request({
    url: `/ClassInfo/selectTeacherList?userName=${encodeURIComponent(userName)}&pageNum=${params.pageNum || 1}&pageSize=${params.pageSize || 10}`,
    method: 'get'
  })
}


// 新增学生查询服务 /ClassInfo/selectStudentList?userName=
export function getStudentList(params) {
  // 确保 userName 始终存在，即使为空字符串
  const userName = params.userName !== undefined ? params.userName : '';

  return request({
    url: `/ClassInfo/selectStudentList?userName=${encodeURIComponent(userName)}&pageNum=${params.pageNum || 1}&pageSize=${params.pageSize || 10}`,
    method: 'get'
  })
}

// 新增班级 /ClassInfo/insCass
export function addClass(data) {
  return request({
    url: '/ClassInfo/insCass',
    method: 'post',
    data
  })
}

// 删除班级 /ClassInfo/delClass
export function deleteClass(data) {
  return request({
    url: '/ClassInfo/delClass',
    method: 'post',
    data
  })
}
// 修改班级名称 /ClassInfo/upClassInfo
export function updateClass(data) {
  return request({
    url: '/ClassInfo/upClassInfo',
    method: 'post',
    data
  })
}
// 班级教师学生已绑定用户查询 /ClassInfo/getClassRoleUser?roleType=1&classId=4
export function getClassRoleUser(params) {
  return request({
    url: '/ClassInfo/getClassRoleUser',
    method: 'get',
    params
  })
}

// 已添加课程绑定用户修改 /ClassInfo/editClassRole
export function editClassRole(data) {
  return request({
    url: '/ClassInfo/editClassRole',
    method: 'post',
    data
  })
}
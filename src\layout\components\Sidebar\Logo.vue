<template>
  <!-- <div class="sidebar-logo-container" :class="{ 'collapse': collapse }" 
    :style="{ backgroundColor: sideTheme === 'theme-dark' ? 
    variables.menuLightBackground : 
    variables.menuLightBackground }"> -->
  <div
    class="sidebar-logo-container"
    :class="{ collapse: collapse }"
    :style="{ backgroundColor: '#F7F7FA' }"
  >
    <transition name="sidebarLogoFade">
      <router-link
        v-if="collapse"
        key="collapse"
        class="sidebar-logo-link"
        to="/"
      >
        <!-- <img v-if="logo" :src="logo" class="sidebar-logo" /> -->
        <template v-if="tenantInfo.tenantLogo">
          <img
            :src="baseUrl + tenantInfo.tenantLogo"
            class="sidebar-logo-img"
          />
        </template>
        <template v-else-if="logo">
          <div class="sidebar-logo">AI</div>
        </template>
        <h1
          v-else
          class="sidebar-title"
          :style="{
            color:
              sideTheme === 'theme-dark'
                ? variables.logoLightTitleColor
                : variables.logoLightTitleColor,
          }"
        >
          {{ tenantInfo.loginName || title }}
        </h1>
      </router-link>
      <router-link v-else key="expand" class="sidebar-logo-link" to="/">
        <!-- <img v-if="logo" :src="logo" class="sidebar-logo" /> -->
        <template v-if="tenantInfo.tenantLogo">
          <img
            :src="baseUrl + tenantInfo.tenantLogo"
            class="sidebar-logo-img"
          />
        </template>
        <template v-else-if="logo">
          <div class="sidebar-logo">AI</div>
        </template>
        <h1
          class="sidebar-title"
          :style="{
            color:
              sideTheme === 'theme-dark'
                ? variables.logoLightTitleColor
                : variables.logoLightTitleColor,
          }"
        >
          {{ tenantInfo.loginName || title }}
        </h1>
      </router-link>
    </transition>
  </div>
</template>

<script setup>
import variables from '@/assets/styles/variables.module.scss'
import logo from '@/assets/logo/logo.png'
import useSettingsStore from '@/store/modules/settings'
import { getTenantInfo } from "@/api/login"

defineProps({
  collapse: {
    type: Boolean,
    required: true
  }
})

const title = import.meta.env.VITE_APP_TITLE
const settingsStore = useSettingsStore()
const sideTheme = computed(() => settingsStore.sideTheme)

const tenantInfo = ref({})
const baseUrl = import.meta.env.VITE_APP_BASE_API

getTenantInfo().then(res => {
  tenantInfo.value = res.data
})


</script>

<style lang="scss" scoped>
.sidebarLogoFade-enter-active {
  transition: opacity 1.5s;
}

.sidebarLogoFade-enter,
.sidebarLogoFade-leave-to {
  opacity: 0;
}

.sidebar-logo-container {
  position: relative;
  width: 100%;
  height: 50px;
  line-height: 50px;
  background: #2b2f3a;
  text-align: center;
  overflow: hidden;

  & .sidebar-logo-link {
    display: flex !important;
    padding: 0 25px;
    flex-direction: row;
    align-items: center;
    height: 100%;
    width: 100%;

    & .sidebar-logo {
      width: 30px;
      height: 30px;
      line-height: 30px;
      border-radius: 4px;
      margin-right: 12px;
      color: white;
      font-size: 20px;
      background-color: #5050e6;
    }

    & .sidebar-logo-img {
      width: 30px;
      height: 30px;
      line-height: 30px;
      border-radius: 4px;
      margin-right: 12px;
      color: white;
      font-size: 20px;
    }

    & .sidebar-title {
      display: inline-block;
      margin: 0;
      color: #fff;
      font-weight: 600;
      line-height: 50px;
      font-size: 14px;
      font-family: Avenir, Helvetica Neue, Arial, Helvetica, sans-serif;
      vertical-align: middle;
    }
  }

  &.collapse {
    .sidebar-logo {
      margin-right: 0px;
    }
  }
}
</style>
<template>
  <el-container class="wrapper">
    <el-header class="header-container">
      <!-- <chat-header /> -->
      <common-header />
    </el-header>

    <el-main
      class="main-container"
      :style="{ marginLeft: sidebarExpands ? '240px' : '0 !important' }"
    >
      <div v-if="sidebarExpands" class="sidebar">
        <!-- 侧边栏内容 -->
        <div class="putAwayIcon" @click="sidebarExpands = false">
          <img src="/icons/putAwayIcon.png" alt="" />
        </div>
        <div class="search-container">
          <el-input
            v-model="searchKeyword"
            class="w-50 m-2 searchInput"
            :maxlength="50"
            placeholder="请输入关键词查询"
            :prefix-icon="Search"
            @keyup.enter="handleQuery"
            clearable
          />
        </div>
        <div
          class="card-container"
          :class="{ 'is-active': route.query.type == item.type }"
          v-for="(item, index) in filteredCardList"
          :key="index"
          @click="handleCardClick(item.type)"
        >
          <img :src="item.icon" />
          <div class="card-title">{{ item.title }}</div>
          <div class="card-profile">{{ item.profile }}</div>
        </div>
      </div>

      <div v-else class="sidebar-close">
        <img
          src="/icons/expandTheIcon.png"
          alt=""
          @click="sidebarExpands = true"
        />
      </div>

      <div class="chat-header">
        {{ titleName }}
      </div>
      <div class="chat-container">
        <message-list
          :is-loading="false"
          :message-list="messageList"
          :deep-think-comp="DeepThinkInternet"
          :enable-auto-scroll="true"
          @regenerate="regenerate"
          :fnBtnList="
            (msg) => {
              return msg.type === 'answer'
                ? ['copy', 'regenerate']
                : msg.type === 'question'
                ? ['copy']
                : [];
            }
          "
          class="msg-list"
        />
        <div class="empty-after"></div>
      </div>
      <div class="input-section">
        <ask
          @askCompleted="sendMessage"
          @stopResponse="stopResponse"
          :isResponsing="isResponsing"
        />
      </div>
    </el-main>
  </el-container>
</template>

<script setup>
// import ChatHeader from './Header.vue'
import CommonHeader from '@/views/education/Header.vue'
import MessageList from '../../chat/MessageList.vue'
import Ask from '../../chat/ask2.vue'
import DeepThinkInternet from '../../chat/DeepThinkInternet.vue'
import { sseRequest } from '@/utils/request.js'
import { getTheInitialLanguage } from '@/api/education/aIInterface.js'
import { ref, watch } from 'vue'
import { linkTxt, linkRequest } from '@/views/education/chat/js/index'
import { stopAsk } from '@/api/chat'
import { Search } from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'

const titleName = ref('')
const isResponsing = ref(false)
const messageList = ref([])
const conversationId = ref('')
const operatingTextIndex = ref(0)
const task_id = ref('')
const ctrl = ref(null)
const onMessage = (event) => {
  const data = JSON.parse(event.data)
  // console.log('接收到了消息:', data)
  task_id.value = data.task_id
  if (data.event === 'agent_message' && data.answer) {
    if (data.conversation_id) {
      conversationId.value = data.conversation_id
    }
    addParagraphContent(operatingTextIndex.value, data.answer)
  }

}
const sidebarExpands = ref(true)
const searchKeyword = ref('')
const cardList = ref([{
  type: 'aICounselor',
  title: 'AI辅导员',
  profile: '智能辅导，为学生日常、学业等问题答疑解惑。',
  icon: '/icons/aICounselor.png'
}, {
  type: 'officeAssistant',
  title: '办公助手',
  profile: '助力老师的校园办公，高效处理各类事务。',
  icon: '/icons/officeAssistant.png'
}, {
  type: 'employmentAssistant',
  title: '就业助手',
  profile: '综合能力分析，精准推送岗位，助力学生求职就业。',
  icon: '/icons/employmentAssistant.png'
}, {
  type: 'internetAssistant',
  title: '网信助手',
  profile: '支持校园网络信息答疑，助力师生高效使用校园网络，获取信息。',
  icon: '/icons/internetAssistant.png'
}, {
  type: 'admissionsAssistant',
  title: '招生助手',
  profile: '提供招生资讯，辅助师生了解政策。',
  icon: '/icons/admissionsAssistant.png'
}])

const filteredCardList = computed(() => {
  if (!searchKeyword.value) return cardList.value
  return cardList.value.filter(card =>
    card.title.toLowerCase().includes(searchKeyword.value.toLowerCase()) ||
    card.profile.toLowerCase().includes(searchKeyword.value.toLowerCase())
  )
})

const router = useRouter()
const handleCardClick = (type) => {
  console.log('点击卡片', type, conversationId.value)
  conversationId.value = ''
  router.push({ path: '/edu/assistant/chat', query: { type: type } })
}

function addParagraphContent (index, content) {
  // if (index >= 0 && index < messageList.value.length) {
  //   messageList.value[index].content += content
  // } else if (index === messageList.value.length) {
  //   messageList.value.push({
  //     id: messageList.value.length,
  //     type: 'answering',
  //     content: content
  //   })
  // }
  console.log('messageList', messageList.value, index, messageList.value[index])
  messageList.value[index].content += content
}

// 定义关闭事件的处理函数
const onClose = () => {
  console.log('SSE 连接已关闭')
  isResponsing.value = false
  messageList.value[operatingTextIndex.value].type = 'answer'
}
// 定义错误事件的处理函数
const onError = (error) => {
  console.error('SSE 出现错误:', error)
  isResponsing.value = false
  messageList.value[operatingTextIndex.value].type = 'answer'
}

watch(linkRequest, (newVal) => {
  if (newVal) {
    if (isResponsing.value) {
      linkRequest.value = false
      return
    }
    sendMessage(linkTxt.value, 'web_link')
    linkRequest.value = false
  }
})

const sendMessage = (question, type) => {
  console.log(question, type)

  ctrl.value = new AbortController()

  messageList.value.push({
    id: messageList.value.length,
    type: 'question',
    content: question
  })
  operatingTextIndex.value = messageList.value.length
  messageList.value.push({
    id: messageList.value.length,
    type: 'answering',
    content: ''
  })

  isResponsing.value = true

  let touchesObj = {
    query: question,
    appSecretKey: appSecretKey.value,
    conversationId: conversationId.value
  }
  sseRequest('/proxy/chat', touchesObj, onMessage, onClose, onError, ctrl.value.signal)
}
const route = useRoute()
const appSecretKey = ref('assistant_counselor')
const getAICounselor = () => {
  let type = route.query.type

  appSecretKey.value = type == 'aICounselor' ? "assistant_counselor"
    : type == 'officeAssistant' ? "assistant_business"
      : type == 'employmentAssistant' ? "assistant_employment"
        : type == 'internetAssistant' ? "assistant_network"
          : type == 'admissionsAssistant' ? "assistant_enroll"
            : "assistant_counselor"
  getTheInitialLanguage({
    appSecretKey: appSecretKey.value
  }).then((res) => {
    console.log('初始语:', res)
    if (res.code == 200) {
      messageList.value.push({
        id: 'a0',
        type: 'prologue',
        content: res.data.opening_statement,
        links: res.data.suggested_questions
      })
    }
  })

}

function stopResponse () {
  console.log('---stop-----')
  if (task_id.value) {
    stopAsk({
      'appSecretKey': appSecretKey.value,
      'taskId': task_id.value
    }).then(res => {
      console.log('停止会话', res)
      if (res.data.result == "success") {
        if (ctrl.value) {
          ctrl.value.abort()
          const lastIdx = messageList.value.length - 1
          messageList.value[lastIdx].type = 'answer'
          isResponsing.value = false
          // messageList.value[lastIdx].content += '\n\n```info-error\n当前用户已暂停```\n'
          ctrl.value = null
          task_id.value = ''
        }
      }
    })
  }
}


const regenerate = (index) => {
  // console.log('重新请求', index)
  // queryIndex.value = index
  messageList.value[index].content = '重新生成中...'
  messageList.value[index].type = 'answering'
  messageList.value[index].id = messageList.value[index].id + '1'
  messageList.value[index].observation = null
  // console.log('重新请求', messageList.value)
  ctrl.value = new AbortController() // 初始化 AbortController 实例
  sseRequest(`/proxy/chat`, {
    'inputs': JSON.stringify({
      tool_code: 'web_search'
    }),
    'query': messageList.value[index - 1].content,
    'appSecretKey': appSecretKey.value,
    'conversationId': conversationId.value
  }, (msg) => {
    const response = JSON.parse(msg.data)
    conversationId.value = response.conversation_id
    task_id.value = response.task_id

    if (response.event == 'agent_thought') {
      if (response.observation) {
        // 有引用
        let observation = JSON.parse(response.observation)
        let observationData = JSON.parse(observation.BochaWebSearch)
        let tool_input = JSON.parse(response.tool_input)
        console.log('引用', response)
        console.log('原始查询', tool_input)

        messageList.value[index].observation = {
          searchTheWeb: tool_input.BochaWebSearch.query,
          valueList: observationData.webpage,
        }

        isResponsing.value = true
      }
    } else if (response.event == "agent_message") {
      // 回答，更新已有消息
      // console.log('回答', messageList.value[index].content)
      if (messageList.value[index].content == '重新生成中...') {
        messageList.value[index].content = response.answer
      } else {
        messageList.value[index].content += response.answer
      }
      isResponsing.value = true
      // scrollToBottom()
    } else if (response.event == 'message_end') {
      messageList.value[index].type = 'answer'
      isResponsing.value = false
      scrollToBottom()
    }
  }, () => { }, (e) => { }, ctrl.value.signal)
}


function updateTitleName () {
  messageList.value = []
  getAICounselor()
  const type = route.query.type
  console.log('type', type)
  titleName.value = type == 'aICounselor' ? "AI辅导员"
    : type == 'officeAssistant' ? "办公助手"
      : type == 'employmentAssistant' ? "就业助手"
        : type == 'internetAssistant' ? "网信助手"
          : type == 'admissionsAssistant' ? "招生助手"
            : "AI辅导员"
}

watch(
  () => route.query.type,
  (newType) => {
    console.log('type changed to', newType)
    updateTitleName()
  }
)


onMounted(() => {
  // AI辅导员
  getAICounselor()

  let type = route.query.type
  console.log('type', type)
  titleName.value = type == 'aICounselor' ? "AI辅导员"
    : type == 'officeAssistant' ? "办公助手"
      : type == 'employmentAssistant' ? "就业助手"
        : type == 'internetAssistant' ? "网信助手"
          : type == 'admissionsAssistant' ? "招生助手"
            : "AI辅导员"
})
</script>

<style lang="scss" scoped>
.wrapper {
  background-color: #f5f7fd;
}
.main-container {
  padding-left: 0;
  padding-right: 0;
}
.chat-header {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  width: 786px;
  height: 50px;
  border-bottom: 1px solid #e4e6eb;
  margin: 0 auto;
  text-align: center;
  line-height: 50px;
  background-color: #f5f7fd;
  z-index: 5;
}
.chat-container {
  /* border: 1px solid red; */
  width: 100%;
  padding-top: 24px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  height: 100%;
  position: relative;
  overflow-y: auto;
}

.chat-container .msg-list {
  width: 784px;
  margin: 0 auto;
  padding-top: 50px;
}

.input-section {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  background-color: #f5f7fd;
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  bottom: 0;
  width: 784px;
  height: 190px;
}

.link-wrapper {
  text-align: left;
  margin-bottom: 30px;
}

.empty-before {
  width: 100%;
  height: 50px;
}

.empty-after {
  width: 100%;
  height: 190px;
  flex-grow: 0;
  flex-shrink: 0;
}

.ask {
  box-shadow: 0px 4px 10px 0px rgba(45, 47, 52, 0.1);
  border-radius: 16px;
  border: 1px solid #eaeaec;
}

:deep(.scroll-mark) {
  top: 200px;
}

.sidebar {
  position: fixed;
  top: 54px;
  left: 0;
  bottom: 0;
  width: 266px;
  background: #ffffff;
  .putAwayIcon {
    width: 24px;
    height: 24px;
    position: absolute;
    top: 16px;
    right: -12px;
    cursor: pointer;
    img {
      width: 100%;
      height: 100%;
    }
  }
  .search-container {
    width: 242px;
    height: 32px;
    margin: 12px auto 0;
    .searchInput {
      width: 242px;
      height: 32px;
      background: #ffffff;
      border-radius: 8px;
    }
  }
  .card-container {
    width: 242px;
    height: 104px;
    border-radius: 8px;
    margin: 12px auto 0;
    position: relative;
    cursor: pointer;

    img {
      width: 28px !important;
      height: 28px !important;
      position: absolute;
      top: 16px;
      left: 16px;
    }
    .card-title {
      font-family: PingFangSC, PingFang SC;
      font-weight: 600;
      font-size: 16px;
      color: #1e1f24;
      position: absolute;
      top: 19px;
      left: 56px;
    }
    .card-profile {
      width: 204px;
      height: 12px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 12px;
      color: #848691;
      position: absolute;
      top: 56px;
      left: 16px;
    }
  }

  .is-active {
    background: #e9f1fe;
    .card-title,
    .card-profile {
      color: #2a74f9 !important;
    }
  }
}
.sidebar-close {
  width: 24px;
  height: 24px;
  position: absolute;
  top: 13px;
  left: 15px;
  cursor: pointer;
  img {
    width: 100%;
    height: 100%;
  }
}
</style>

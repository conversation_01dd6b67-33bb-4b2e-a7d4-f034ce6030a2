import request from '@/utils/difyRequest'

// 处理文档embeding
export function embedingDoc(id,data) {
    return request({
        url: '/console/api/datasets/'+id+'/documents',
        method: 'post',
        data
    })
}

// 获取文档的embeding状态
export function getEmbedingStatus(id,batch) {
    return request({
        url: '/console/api/datasets/'+id+'/batch/'+batch+'/indexing-status',
        method: 'get'
    })
}

// 获取文档的embeding状态
export function getReembedingStatus(id,docId) {
    return request({
        url: '/console/api/datasets/'+id+'/documents/'+docId+'/indexing-status',
        method: 'get'
    })
}
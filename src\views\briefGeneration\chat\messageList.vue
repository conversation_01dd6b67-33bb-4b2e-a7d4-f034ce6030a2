<template>
  <div class="message-list">
    <div
      v-if="isLoading"
      v-loading="isLoading"
      element-loading-text="加载聊天记录"
      element-loading-spinner="el-icon-loading"
      element-loading-background="transparent"
      class="loading-container"
    />
    <message
      v-for="(item, idx) of messageList"
      :id="item.id"
      :key="`${item.id}${idx}`"
      :type="item.type"
      :content="item.content"
      :links="item.links"
      :fileObj="item.fileObj"
      :observation="item.observation || null"
      :enable-typing-effect="enableTypingEffect"
      :enable-buttons="enableButtons"
      :fn-btn-list="fnBtnList"
      :deep-think-comp="deepThinkComp"
      :enable-auto-scroll="enableAutoScroll"
      @typing="$emit('typing')"
      @finish-typing="$emit('finish-typing')"
      @regenerate="$emit('regenerate', idx)"
      @like="$emit('like', idx)"
      @cancel-like="$emit('cancel-like', idx)"
      @dislike="$emit('dislike', idx)"
      @cancel-dislike="$emit('cancel-dislike', idx)"
      @insert="$emit('insert', idx)"
    >
      <template #tail>
        <slot name="tail" :msg="item"></slot>
      </template>
    </message>
  </div>
</template>

<script>
import Message from './message.vue'

/**
 * 显示消息列表，包括开场白及
 */
export default {
  components: {
    Message
  },
  props: {
    /**
     * 消息列表
     * 数据格式:
     *  [
     *    {
     *      id: 'xxxxxx',
     *      type: 'question',
     *      content: 'show me an example how to make a request',
     *    },
     *    {
     *      id: 'xxxxxxxx'
     *      type: 'answer',
     *      content: 'hello',
     *    }
     *  ]
     */
    messageList: {
      type: Array,
      required: true
    },

    /**
     * 是否在加载聊天记录
     */
    isLoading: {
      type: Boolean,
      required: true
    },

    /**
     * 是否开启打字效果
     */
    enableTypingEffect: {
      type: Boolean,
      required: false,
      default: true
    },

    /**
     * 是否展示按钮
     */
    enableButtons: {
      type: Boolean,
      required: false,
      default: true
    },

    /**
     * 获取按钮列表的函数
     */
    fnBtnList: {
      type: Function,
      required: false
    },

    /**
     * 深度思考的展示组件 
     */
    deepThinkComp: {
      type: Object,
      required: false
    },

    /**
     * 是否自动滚动
     */
    enableAutoScroll: {
      type: Boolean,
      required: false,
      default: false
    }
  },
  methods: {
  }
};
</script>

<style scoped>
.message-list {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: stretch;
  width: 100%;
  height: auto;
}
.stop-response {
  width: 128px;
  height: 33px;
  padding: 8px 20px;
  align-items: center;
  border-radius: 16px;
  border: 1px solid lightgray;
  color: lightgray;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  margin-top: 1em;
  font-size: 12px;
  position: relative;
  top: -30px;
  left: 55px;
}
.stop-response .stop-icon {
  display: inline-block;
  width: 10px;
  height: 10px;
  background-color: lightgray;
  border-radius: 2px;
  margin-right: 5px;
}
.loading-container {
  height: 60px;
  background-color: transparent;
}
.empty {
  width: 100%;
  height: 190px;
  background-color: transparent;
}
</style>

<template>
  <el-dialog
    :close-on-click-modal="false"
    v-model="props.visible"
    title="作品分享"
    width="334px"
    @close="cancel"
  >
    <div style="display: flex; align-items: center">
      <el-input
        class="share-link"
        v-model="newTitle"
        maxlength="50"
        readonly
        style="margin-right: 10px"
      ></el-input>
      <el-button class="cancel-btn" type="primary" @click="copyLink">
        复制链接
      </el-button>
    </div>
  </el-dialog>
</template>

<script setup>
import { ref, defineProps, defineEmits, watch } from 'vue'
import { ElMessage } from 'element-plus'
const props = defineProps({
  visible: {
    type: Boolean,
    required: true
  },
})

const emit = defineEmits(['visible', 'newTitle'])

const newTitle = ref('https://www.zhihuigaoxiao/x/xxxx.com/')

const cancel = () => {
  emit('toVisible', false)
}

const copyLink = () => {
  ElMessage.success('链接已复制到剪贴板')
  const el = document.createElement('textarea')
  el.value = newTitle.value
  document.body.appendChild(el)
  el.select()
  document.execCommand('copy')
  document.body.removeChild(el)
}


</script>
<style scoped lang='scss'>
.share-link {
  width: 250px;
  height: 32px;
  background: #f5f7fd;
  border-radius: 4px;
  border: 1px solid #eaeaec;
  :deep(.el-input__wrapper) {
    background: #f5f7fd !important;
    outline: none;
    box-shadow: none;
    &:hover {
      box-shadow: none;
    }
  }
}

.cancel-btn {
  width: 81px;
  height: 32px;
  background: #2a74f9;
  border-radius: 4px;
}
</style>
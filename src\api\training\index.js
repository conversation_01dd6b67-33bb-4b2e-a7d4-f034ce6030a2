import request from "@/utils/request"

export function getTrainingApplicationList (params) {
    return request({
        url: "/training/application/list",
        method: "get",
        params,
    })
}

export function delCourse (data) {
    return request({
        url: "/training/application/delete",
        method: "post",
        data,
    })
}

export function setDisclosure (data) {
    return request({
        url: "/training/application/open",
        method: "post",
        data,
    })
}

export function createCourse (data) {
    return request({
        url: "/training/application/save",
        method: "post",
        data,
    })
}

export function copyCourse (data) {
    return request({
        url: "/training/application/copy",
        method: "post",
        data,
    })
}

export function setPublic (data) {
    return request({
        url: "/training/application/public",
        method: "post",
        data,
    })
}

// ai实训详情列表-教师   /Training/score/list?pageNum=2&pageSize=10
export function getTrainingScoreList (data, params) {
    return request({
        url: "/Training/score/list",
        method: "post",
        data,
        params
    })
}

// ai实训详情列表-教师打分 /Training/score/edit
export function getTrainingScoreEdit (data) {
    return request({
        url: "/Training/score/edit",
        method: "put",
        data,
    })
}

// ai实训数据统计-教师  /Training/score/teacherTrainingAnalysis?trainingId=1
export function getTrainingAnalysis (data) {
    return request({
        url: "/Training/score/teacherTrainingAnalysis",
        method: "get",
        params: data,
    })
}
// 根据课程id获取班级信息 /ClassInfo/getClassByLessons?id=1
export function getClassByLessons (data) {
    return request({
        url: "/ClassInfo/getClassByLessons",
        method: "get",
        params: data,
    })
}


// ai实训详情列表-学生 /Training/score/studentList?trainingId=1
    export function getTrainingStudentList (data) {
        return request({
            url: "/Training/score/studentList",
            method: "get",
            params: data,
        })
    }
// ai实训数据统计-学生 /Training/score/studentTrainingAnalysis?trainingId=1
export function getTrainingStudentAnalysis (data) {
  return request({
      url: "/Training/score/studentTrainingAnalysis",
      method: "get",
      params: data,
  })
}    

// 开始实训 /Training/score/add
export function getTrainingAdd (data) {
    return request({
        url: "/Training/score/add",
        method: "post",
        data,
    })
}
// ai实训任务 提交 /Training/score/trainingStudentSubmit
export function getTrainingStudentSubmit (data) {
    return request({
        url: "/Training/score/trainingStudentSubmit",
        method: "put",
        data,
    })
}

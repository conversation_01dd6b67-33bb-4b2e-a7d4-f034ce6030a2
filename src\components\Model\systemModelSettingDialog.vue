<template>
  <el-dialog
    v-model="isVisible"
    width="500px"
  >
    <el-form label-position="top">
      <el-form-item
        v-for="item of modelTypeSelectors"
        :key="item.type"
        :label="item.label"
      >
        <model-selector
          :type="item.type"
          :teleported="true"
          v-model:model-name="item.model"
          v-model:model-provider="item.provider"
          style="width:100%;"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="close">取消</el-button>
      <el-button type="primary" @click="save">保存</el-button>
    </template>
  </el-dialog>
</template>

<script setup>
import { onMounted } from 'vue';
import ModelSelector from './modelSelector.vue';
import { getDefaultModel, setDefaultModels } from '@/api/model';
import { ElMessage } from 'element-plus';

// 是否展示弹窗
const isVisible = ref(false);

// 模型分类下拉框种类
const modelTypeSelectors = ref([
  {label: '系统推理模型',   type: 'llm',             model: '', provider: ''},
  {label: 'Embedding 模型', type: 'text-embedding',  model: '', provider: ''},
  {label: 'Rerank 模型',    type: 'rerank',          model: '', provider: ''},
  {label: '语音转文本模型', type: 'speech2text',     model: '', provider: ''},
  {label: '文本转语音模型', type: 'tts',             model: '', provider: ''}
]);

/**
 * 获取默认默型数据
 */
function getDefaultModelData() {
  modelTypeSelectors.value.forEach((item) => {
    getDefaultModel(item.type).then((rs) => {
      if (rs.data) {
        item.model = rs.data.model;
        item.provider = rs.data.provider.provider;
      }
    });
  })
}

/**
 * 打开弹窗
 */
function open() {
  isVisible.value = true;
}

/**
 * 关闭弹窗
 */
function close() {
  isVisible.value = false;
}

/**
 * 保存
 */
async function save() {
  const params = {
    model_settings: []
  };

  modelTypeSelectors.value.forEach((item) => {
    params.model_settings.push({
      model_type: item.type,
      model: item.model ? item.model : undefined,
      provider: item.provider ? item.provider : undefined
    });
  });

  await setDefaultModels(params);

  ElMessage({
    type: 'success',
    message: '保存成功'
  });

  isVisible.value = false;
}

onMounted(() => {
  getDefaultModelData();
});

defineExpose({
  open
});

</script>

<style scoped>
</style>
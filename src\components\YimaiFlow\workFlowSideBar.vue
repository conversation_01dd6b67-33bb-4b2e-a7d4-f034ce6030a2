<template>
    <aside class="work-flow-aside" v-show="!isCollapseSideBar">
      <div class="team-config">
          <div class="title">
              {{props.title}} 
              <el-button  size="small" :icon="ArrowLeft" style="width: 20px;height: 20px;padding: 0;"
                  @click="isCollapseSideBar = true"></el-button>
          </div>
          <div>
              <el-form label-width="80px" label-position="top">
                  <el-form-item label="提示模板:">
                      <el-input v-model="alterInfoFrom.alterText" @change="alterTextChange" type="textarea" :rows="3" :resize="'none'"></el-input>
                  </el-form-item>
                  <el-form-item>
                      <template #label>
                          <div>
                              <span style="line-height: 24.5px;">提示表单:</span>
                              <el-button :size="'small'" primary style="margin-left: 10px;" @click="editKeys">
                                  <el-icon style="color: #5050E6;margin-right: 5px;"><EditPen /></el-icon>点击编辑
                              </el-button>
                          </div>
                      </template>
                      <!-- <el-input v-model="alterInfoFrom.alterText" type="textarea" :rows="3" :resize="'none'"></el-input> -->
                  </el-form-item>
                  <el-form-item>
                      <template #label>
                          <div>
                              <span style="line-height: 24.5px;">开场白设置:</span>
                              <el-button :size="'small'" primary style="margin-left: 10px;" @click="editOpening">
                                  <el-icon style="color: #5050E6;margin-right: 5px;"><EditPen /></el-icon>点击编辑
                              </el-button>
                          </div>
                      </template>
                      <!-- <el-input v-model="alterInfoFrom.alterText" type="textarea" :rows="3" :resize="'none'"></el-input> -->
                  </el-form-item>
              </el-form>
          </div>
      </div>
      <div class="ai-choose">
          <!-- <div class="title">
              智能体选择
          </div> -->
          <el-tabs v-model="activeName" class="custom-tabs-item" :stretch=true>
              <el-tab-pane name="agent" >
                  <template #label>
                      <span style="width: 100px;text-align: center;">智能体</span>
                  </template>
                  <el-collapse v-model="activeNames" class="custom-collapse-item">
                      <el-collapse-item title="聊天型智能体" name="chat">
                          <template v-for="item in props.agents.filter(agent => agent.agentType == 1)">
                              <custom-card :img="baseUrl+item.agentIconUrl" :draggable="true" @dragstart="onDragStart($event, 'agent' ,item)"
                                  :title="item.agentName" :detail="item.goal" style="margin-bottom: 10px;" :is-del-tool="true">
                              </custom-card>
                          </template>
                      </el-collapse-item>
                      <el-collapse-item title="文本型智能体" name="text">
                          <template v-for="item in props.agents.filter(agent => agent.agentType == 2)">
                              <custom-card :img="baseUrl+item.agentIconUrl" :draggable="true" @dragstart="onDragStart($event, 'agent' ,item)"
                                  :title="item.agentName" :detail="item.goal" style="margin-bottom: 10px;" :is-del-tool="true">
                              </custom-card>
                          </template>
                      </el-collapse-item>
                      <el-collapse-item title="开发型智能体" name="code">
                          <template v-for="item in props.agents.filter(agent => agent.agentType == 3)">
                              <custom-card :img="baseUrl+item.agentIconUrl" :draggable="true" @dragstart="onDragStart($event, 'agent' ,item)"
                                  :title="item.agentName" :detail="item.goal" style="margin-bottom: 10px;" :is-del-tool="true">
                              </custom-card>
                          </template>
                      </el-collapse-item>
                      <el-collapse-item title="角色型智能体" name="role">
                          <template v-for="item in props.agents.filter(agent => agent.agentType == 4)">
                              <custom-card :img="baseUrl+item.agentIconUrl" :draggable="true" @dragstart="onDragStart($event, 'agent' ,item)"
                                  :title="item.agentName" :detail="item.goal" style="margin-bottom: 10px;" :is-del-tool="true">
                              </custom-card>
                          </template>
                      </el-collapse-item>
                  </el-collapse>
              </el-tab-pane>
              <el-tab-pane name="tool" >
                  <template #label>
                      <span style="width: 100px;text-align: center;">工具</span>
                  </template>
                  <el-collapse v-model="activeToolNames" class="custom-collapse-item">
                      <el-collapse-item title="代码型工具" name="code">
                          <template v-for="item in props.tools.filter(tool => tool.toolType == 1)">
                              <custom-card :img="baseUrl+item.toolImageUrl" :draggable="true" @dragstart="onDragStart($event, 'tool_code', item)"
                                  :title="item.toolName" :detail="item.toolDesc" style="margin-bottom: 10px;" :is-del-tool="true">
                              </custom-card>
                          </template>
                      </el-collapse-item>
                      <el-collapse-item title="API型工具" name="api">
                          <template v-for="item in props.tools.filter(tool => tool.toolType == 2)">
                              <custom-card :img="baseUrl+item.toolImageUrl" :draggable="true" @dragstart="onDragStart($event, 'tool_api', item)"
                                  :title="item.toolName" :detail="item.toolDesc" style="margin-bottom: 10px;" :is-del-tool="true">
                              </custom-card>
                          </template>
                      </el-collapse-item>
                  </el-collapse>
              </el-tab-pane>
              <el-tab-pane name="template" >
                  <template #label>
                      <span style="width: 100px;text-align: center;">流程模板</span>
                  </template>
                  <custom-card :draggable="true" @dragstart="onDragStart($event, 'template', templates[0])" :img="baseUrl + templates[0].templateImageUrl"
                      :title="templates[0].templateName" :detail="templates[0].templateDesc" style="margin-bottom: 10px;" :is-del-tool="true">
                  </custom-card>
                  <!-- <el-collapse v-model="activeTemplateNames" class="custom-collapse-item">
                      <el-collapse-item title="基础模板" name="baseTemplate">
                          <custom-card :draggable="true" @dragstart="onDragStart($event, 'template', templates[0])"
                              :title="templates[0].templateName" :detail="templates[0].templateDesc" style="margin-bottom: 10px;" :is-del-tool="true">
                          </custom-card>
                      </el-collapse-item>
                  </el-collapse> -->
              </el-tab-pane>
          </el-tabs>
      </div>
      <!--dialog-->
      <el-dialog v-model="dialogVisible" width="40%" :show-close="false" align-center :close-on-click-modal="false">
          <template #header="{ titleId, titleClass }">
              <div class="my-header">
                  <h4 :id="titleId" :class="titleClass">编辑提示表单</h4>
                  <el-button type="primary" :icon="CirclePlus" text 
                      @click="alterInfoFrom.alterKeys.push({keyName:'',keyCode:'',keyType:'text'})">
                  </el-button>
              </div>
          </template>
          <div style="width: 100%;padding: 0 20px;">
              <el-table :data="alterInfoFrom.alterKeys" :height="600">
                  <el-table-column prop="keyName" label="字段名">
                      <template #default="scope">
                          <el-input v-model="scope.row.keyName" placeholder="请输入字段名"/>
                      </template>
                  </el-table-column>
                  <el-table-column prop="keyCode" label="Key">
                      <template #default="scope">
                          <el-input v-model="scope.row.keyCode" placeholder="请输入Key"/>
                      </template>
                  </el-table-column>
                  <el-table-column prop="keyType" label="字段类型" width="100">
                      <template #default="scope">
                        <el-select v-model="scope.row.keyType">
                            <el-option :key="1" label="文本" value="text" />
                            <el-option :key="2" label="图片" value="image" />
                            <el-option :key="3" label="文件" value="file" />
                        </el-select>
                      </template>
                  </el-table-column>
                  <el-table-column label="操作" width="80">
                      <template #default="scope">
                          <el-button type="danger" text size="small" :icon="Delete" @click="alterInfoFrom.alterKeys.splice(scope.$index,1)" ></el-button>
                      </template>
                  </el-table-column>
              </el-table>
          </div>
          <template #footer>
              <span class="dialog-footer">
                  <el-button @click="dialogVisible = false">取消</el-button>
                  <el-button type="primary" @click="confirmChangeKeys">确定</el-button>
              </span>
          </template>
      </el-dialog>

        <!--开场白设置-->
        <el-dialog v-model="openingVisible" width="40%" :show-close="false" align-center :close-on-click-modal="false">
            <template #header="{ titleId, titleClass }">
                <div class="my-header">
                    <h4 :id="titleId" :class="titleClass">开场白设置</h4>
                </div>
            </template>
            <el-row style="padding:0 20px;" ref="childContext">
                <el-col :span="24">
                    <div style="font-size: 14px;font-weight: 400;color: #032220;">开场白：</div>
                    <el-input v-model="alterInfoFrom.prologue" placeholder="请输入开场白" style="width: 100%;" type="textarea" :rows="4" />
                </el-col>
                <el-col :span="24" style="margin-top: 20px;">
                    <div>
                        <span style="font-size: 14px;font-weight: 400;color: #032220;">
                            开场预置问题：
                        </span>
                        <el-button :icon="Plus" size="small" link type="primary" style="float:right;" @click="addOpeningQuestion">添加</el-button>
                    </div>
                    <div style="max-height: calc(100vh - 500px);overflow-y: auto;">
                        <div v-for="(item,index) in alterInfoFrom.openingQuestionList" style="width: 100%;margin-bottom: 5px;">
                            <el-input v-model="item.openingQuestion" placeholder="请输入问题" style="width: 95%;" type="text" />
                            <el-button :icon="Remove" link @click="delOpeningQuestion(index)" style="width: 5%;"></el-button>
                        </div>
                    </div>
                </el-col>
            </el-row>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="openingVisible = false">取消</el-button>
                    <el-button type="primary" @click="alterOpeningChange">确定</el-button>
                </span>
            </template>
        </el-dialog>
    </aside>
  </template>
  <script setup>
  import { ref } from 'vue'
  import {EditPen,ArrowLeft,Delete,CirclePlus ,Remove , Plus} from '@element-plus/icons-vue'
  import CustomCard from '@/components/CustomCard/index'
  const props = defineProps({
      title:{
          type:String,
          default:'团队配置编排',
          required:false
      },
      alterText:{
          type:String,
          default:'',
          required:false
      },
      alterKeys:{
          type:Array,
          default:[],
          required:false
      },
      agents:{
          type:Array,
          default:[],
          required:false
      },
      tools:{
          type:Array,
          default:[],
          required:false
      },
      prologue:{
          type:String,
          default:'',
          required:false
      },
      openQuestion:{
          type:String,
          default:'',
          required:false
      },
  })
  
  const emit = defineEmits(['update:alterText','update:alterKeys','update:agents','update:tools','update:Opening'])
  
  const alterInfoFrom = ref({
      alterText:'',
      alterKeys:[],
      prologue:''
  })
  const dialogVisible = ref(false)
  const isCollapseSideBar = ref(false)
  
  const activeName = ref('agent')
  const activeNames = ref([])
  const activeTemplateNames = ref([])
  const activeToolNames = ref([])

  //开场设置弹框
  const openingVisible = ref(false)
  
  const templates = ref([
      {
        templateImageUrl:'/profile/upload/2024/02/07/1006232_20240207094311A003.png',
          templateName:'设计团队模板',
          templateDesc:'基础的设计团队模板，可以帮您快速建造设计流程',
          templateJson:'{"nodes":[{"type":"custom","data":{"type":"start","data":{"label":"开始","desc":"工作流的起始节点，用于设置启动工作流所需的信息。"}},"events":{},"id":"start","position":{"x":-504.2917403700368,"y":293.070650304685}},{"type":"custom","data":{"type":"end","data":{"label":"结束","desc":"工作流的最后一个节点，用于在工作流运行后返回结果信息。"}},"events":{},"id":"end","position":{"x":3369.1392278426833,"y":307.54326768895135}},{"type":"custom","data":{"type":"agent","data":{"agentId":16,"agentName":"产品经理","agentCode":"manager_agent","agentDescribe":"您是公司的战略规划者和产品愿景的守护者。具有市场营销、商务管理或相关领域的学士或硕士学位，具备产品管理或市场调研经验。在产品管理、市场调研或相关领域拥有一定的工作经验，熟悉市场分析、竞品分析和产品规划等工作，并具备团队管理和协调能力。","goal":"确保产品符合市场需求，满足用户期望，并与公司的战略目标相契合","agentIconUrl":"/profile/upload/2024/02/04/产品经理_20240204024430A013.png","agentType":4,"modelId":16,"temperature":0,"input":[{"key":"key","name":"变量"},{"key":"key_other","name":"另一个变量"}],"output":[{"name":"output","type":"String"}]}},"events":{},"id":"d5800ece-c149-46d9-86fa-a665f45e1d7c","position":{"x":-14.990256792142816,"y":-18.901843625464863},"label":"d5800ece-c149-46d9-86fa-a665f45e1d7c node"},{"type":"custom","data":{"type":"agent","data":{"agentId":17,"agentName":"创意总监","agentCode":"director_agent","agentDescribe":"您是产品的创造者和视觉传达者。具有工业设计、视觉传达设计或相关专业的学士或硕士学位，具备产品设计或视觉传达设计经验。在产品设计、视觉传达设计或相关领域拥有一定的工作经验，熟悉创意设计、材料工艺选择和样品制作等工作，并具备良好的审美能力和沟通能力。","goal":"负责将概念转化为实际的产品设计，并确保产品在外观和结构上达到预期效果","agentIconUrl":"/profile/upload/2024/02/04/设计总监_20240204024439A015.png","agentType":4,"modelId":16,"temperature":0,"input":[{"key":"proposals_number","name":"方案数量"},{"key":"product","name":"产品"}],"output":[{"name":"output","type":"String"}]}},"events":{},"id":"956c148b-db8a-4676-8d2c-d24b51009ee0","position":{"x":518.9521588700919,"y":481.998742042156},"label":"956c148b-db8a-4676-8d2c-d24b51009ee0 node"},{"type":"custom","data":{"type":"agent","data":{"agentId":18,"agentName":"CMF设计师","agentCode":"designer_agent","agentDescribe":"您是产品外观和手感的创造者，为产品赋予独特的视觉和触感体验.具有工业设计、材料科学、纺织设计或相关专业的学士或硕士学位，具备CMF设计或相关领域的经验。在产品CMF设计、材料选择、色彩搭配或相关领域拥有一定的工作经验，熟悉不同材料的特性和应用，具备对色彩趋势和市场需求的敏锐洞察力，并具备与供应商合作的能力。","goal":"负责确定产品的颜色、材料和饰面，为产品赋予独特的外观和手感，同时满足市场趋势和用户喜好","agentIconUrl":"/profile/upload/2024/02/04/CMF设计师_20240204024427A012.png","agentType":4,"modelId":16,"temperature":0,"input":[{"key":"product","name":"产品"},{"key":"proposals_number","name":"方案数量"}],"output":[{"name":"output","type":"String"}]}},"events":{},"id":"658c0c55-a3fc-4495-94e0-89b75f37f6fd","position":{"x":1109.3831133385677,"y":42.93946220827377},"label":"658c0c55-a3fc-4495-94e0-89b75f37f6fd node"},{"type":"custom","data":{"type":"agent","data":{"agentId":38,"agentName":"知识产权分析师","agentCode":"lawer","agentDescribe":"您是公司知识产权领域的专家和保护者。您的任务是评估、分析和管理公司的知识产权资产，确保其合规性和价值最大化。您需要熟悉专利、商标、版权等知识产权形式的法律法规，并协助制定保护策略和处理知识产权事务。通过市场调研、竞争分析和法律咨询，您致力于保护公司的创新成果，维护其竞争优势和商业利益。","goal":"负责评估、分析和管理公司或个人的知识产权资产","agentIconUrl":"/profile/upload/2024/03/04/715998_20240304031637A006.png","agentType":4,"modelId":16,"temperature":0,"input":[],"output":[{"name":"output","type":"String"}]}},"events":{},"id":"4347d602-b516-4b74-b606-07af948f2dee","position":{"x":1630.8921665072214,"y":224.75723190785862},"label":"4347d602-b516-4b74-b606-07af948f2dee node"},{"type":"custom","data":{"type":"agent","data":{"agentId":29,"agentName":"物料工程师","agentCode":"material_planners","agentDescribe":"作为物料规划员，您在供应链管理和物料规划领域拥有丰富的经验和专业知识。您曾在跨国公司或制造业企业担任物料规划相关职位，负责协调物料采购、库存管理和生产计划，以确保生产线的顺畅运作和成本控制。您熟悉供应链管理系统和物料需求计划工具，能够有效地优化物料流程，降低库存成本，提高供应链效率。您对市场趋势和物料市场具有敏锐的洞察力，能够及时调整物料采购计划，以适应市场变化和客户需求。您秉持着精益管理和持续改进的理念，致力于优化物料规划流程，提升企业的竞争力和效益","goal":"负责确定产品所需的所有零部件和原材料，以确保生产过程中所需的所有物料都被考虑到","agentIconUrl":"/profile/upload/2024/02/27/微信图片_20240227145041_20240227065122A001.png","agentType":4,"modelId":16,"temperature":0,"input":[],"output":[{"name":"output","type":"String"}]}},"events":{},"id":"a173fe84-4382-458b-8332-c412b99adff1","position":{"x":2150.1038229658807,"y":-64.0790716464267},"label":"a173fe84-4382-458b-8332-c412b99adff1 node"},{"type":"custom","data":{"type":"agent","data":{"agentId":19,"agentName":"项目经理","agentCode":"officer_agent","agentDescribe":"您是团队决策的记录者和客观评估者，为团队提供准确的信息和客观的评估。具有信息管理、商务管理或相关领域的学士或硕士学位，具备记录和汇报工作经验。在记录和汇报工作方面有一定的经验，熟悉信息整理和报告撰写，能够客观评估方案的可行性，并以第三人称客观总结和提交报告。","goal":"负责记录的人员，评估方案可行性，如实总结方案，用第三人称汇总提交报告","agentIconUrl":"/profile/upload/2024/02/04/汇报员_20240204024435A014.png","agentType":4,"modelId":16,"temperature":0,"input":[],"output":[{"name":"output","type":"String"}]}},"events":{},"id":"5b3554f4-bd9f-4a12-805b-2a0eb0fad3fd","position":{"x":2802.5644886041973,"y":434.07684074233384},"label":"5b3554f4-bd9f-4a12-805b-2a0eb0fad3fd node"}],"edges":[{"sourceHandle":"start__handle-right","targetHandle":"d5800ece-c149-46d9-86fa-a665f45e1d7c__handle-left","type":"close","source":"start","target":"d5800ece-c149-46d9-86fa-a665f45e1d7c","data":{},"events":{},"markerEnd":"arrowclosed","style":{"stroke":"#5050E6"},"labelBgStyle":{"fill":"#5050E6"},"id":"vueflow__edge-startstart__handle-right-d5800ece-c149-46d9-86fa-a665f45e1d7cd5800ece-c149-46d9-86fa-a665f45e1d7c__handle-left","sourceX":-200.2917403700368,"sourceY":324.070650304685,"targetX":-18.990192861177377,"targetY":189.09787140565726},{"sourceHandle":"d5800ece-c149-46d9-86fa-a665f45e1d7c__handle-right","targetHandle":"956c148b-db8a-4676-8d2c-d24b51009ee0__handle-left","type":"close","source":"d5800ece-c149-46d9-86fa-a665f45e1d7c","target":"956c148b-db8a-4676-8d2c-d24b51009ee0","data":{},"events":{},"markerEnd":"arrowclosed","style":{"stroke":"#5050E6"},"labelBgStyle":{"fill":"#5050E6"},"id":"vueflow__edge-d5800ece-c149-46d9-86fa-a665f45e1d7cd5800ece-c149-46d9-86fa-a665f45e1d7c__handle-right-956c148b-db8a-4676-8d2c-d24b51009ee0956c148b-db8a-4676-8d2c-d24b51009ee0__handle-left","sourceX":289.0092630454997,"sourceY":189.09787140565726,"targetX":514.9521189304086,"targetY":689.9986756375528},{"sourceHandle":"956c148b-db8a-4676-8d2c-d24b51009ee0__handle-right","targetHandle":"658c0c55-a3fc-4495-94e0-89b75f37f6fd__handle-left","type":"close","source":"956c148b-db8a-4676-8d2c-d24b51009ee0","target":"658c0c55-a3fc-4495-94e0-89b75f37f6fd","data":{},"events":{},"markerEnd":"arrowclosed","style":{"stroke":"#5050E6"},"labelBgStyle":{"fill":"#5050E6"},"id":"vueflow__edge-956c148b-db8a-4676-8d2c-d24b51009ee0956c148b-db8a-4676-8d2c-d24b51009ee0__handle-right-658c0c55-a3fc-4495-94e0-89b75f37f6fd658c0c55-a3fc-4495-94e0-89b75f37f6fd__handle-left","sourceX":822.9519594139788,"sourceY":689.9986756375528,"targetX":1105.383003287936,"targetY":256.93935054677286},{"sourceHandle":"658c0c55-a3fc-4495-94e0-89b75f37f6fd__handle-right","targetHandle":"4347d602-b516-4b74-b606-07af948f2dee__handle-left","type":"close","source":"658c0c55-a3fc-4495-94e0-89b75f37f6fd","target":"4347d602-b516-4b74-b606-07af948f2dee","data":{},"events":{},"markerEnd":"arrowclosed","style":{"stroke":"#5050E6"},"labelBgStyle":{"fill":"#5050E6"},"id":"vueflow__edge-658c0c55-a3fc-4495-94e0-89b75f37f6fd658c0c55-a3fc-4495-94e0-89b75f37f6fd__handle-right-4347d602-b516-4b74-b606-07af948f2dee4347d602-b516-4b74-b606-07af948f2dee__handle-left","sourceX":1413.382843771506,"sourceY":256.93935054677286,"targetX":1626.8921966784865,"targetY":417.75713842360267},{"sourceHandle":"4347d602-b516-4b74-b606-07af948f2dee__handle-right","targetHandle":"a173fe84-4382-458b-8332-c412b99adff1__handle-left","type":"close","source":"4347d602-b516-4b74-b606-07af948f2dee","target":"a173fe84-4382-458b-8332-c412b99adff1","data":{},"events":{},"markerEnd":"arrowclosed","style":{"stroke":"#5050E6"},"labelBgStyle":{"fill":"#5050E6"},"id":"vueflow__edge-4347d602-b516-4b74-b606-07af948f2dee4347d602-b516-4b74-b606-07af948f2dee__handle-right-a173fe84-4382-458b-8332-c412b99adff1a173fe84-4382-458b-8332-c412b99adff1__handle-left","sourceX":1934.8918969401598,"sourceY":417.75713842360267,"targetX":2146.103853137146,"targetY":140.92088457741892},{"sourceHandle":"a173fe84-4382-458b-8332-c412b99adff1__handle-right","targetHandle":"5b3554f4-bd9f-4a12-805b-2a0eb0fad3fd__handle-left","type":"close","source":"a173fe84-4382-458b-8332-c412b99adff1","target":"5b3554f4-bd9f-4a12-805b-2a0eb0fad3fd","data":{},"events":{},"markerEnd":"arrowclosed","style":{"stroke":"#5050E6"},"labelBgStyle":{"fill":"#5050E6"},"id":"vueflow__edge-a173fe84-4382-458b-8332-c412b99adff1a173fe84-4382-458b-8332-c412b99adff1__handle-right-5b3554f4-bd9f-4a12-805b-2a0eb0fad3fd5b3554f4-bd9f-4a12-805b-2a0eb0fad3fd__handle-left","sourceX":2454.103693620716,"sourceY":140.92088457741892,"targetX":2798.5645187754626,"targetY":621.0767224040271},{"sourceHandle":"5b3554f4-bd9f-4a12-805b-2a0eb0fad3fd__handle-right","targetHandle":"end__handle-left","type":"close","source":"5b3554f4-bd9f-4a12-805b-2a0eb0fad3fd","target":"end","data":{},"events":{},"markerEnd":"arrowclosed","style":{"stroke":"#5050E6"},"labelBgStyle":{"fill":"#5050E6"},"id":"vueflow__edge-5b3554f4-bd9f-4a12-805b-2a0eb0fad3fd5b3554f4-bd9f-4a12-805b-2a0eb0fad3fd__handle-right-endend__handle-left","sourceX":3106.564219037136,"sourceY":621.0767224040271,"targetX":3365.1392278426833,"targetY":338.54326768895135}],"position":[-233.01446235410094,246.8961017924527],"zoom":0.5,"viewport":{"x":-233.01446235410094,"y":246.8961017924527,"zoom":0.5}}'
      }
  ])
  
  const baseUrl = import.meta.env.VITE_APP_BASE_API;
  function onDragStart(event, nodeType ,nodeInfo) {
      if (event.dataTransfer) {
          let info = {
              type:nodeType,
              data:nodeInfo
          }
          event.dataTransfer.setData('application/vueflow', JSON.stringify(info))
          event.dataTransfer.effectAllowed = 'move'
      }
  }
  
  // const initFrom = () => {
  //     console.log(props.alterKeys);
  //     alterInfoFrom.value.alterText = props.alterText
  //     alterInfoFrom.value.alterKeys = props.alterKeys
  // }
  
  const editKeys = () => {
      dialogVisible.value = true
      alterInfoFrom.value.alterKeys = JSON.parse(JSON.stringify(props.alterKeys))  
  }
  
  const confirmChangeKeys = () => {
      emit('update:alterKeys',alterInfoFrom.value.alterKeys)
      dialogVisible.value = false
  }
  
  const alterTextChange = () => {
      emit('update:alterText',alterInfoFrom.value.alterText)
  }
  
  const alterOpeningChange = () => {
    let openingQuestionList = alterInfoFrom.value.openingQuestionList.filter(item=>{return item.openingQuestion!=''})
    let Opening = {
        prologue:alterInfoFrom.value.prologue,
        openQuestion:openingQuestionList.length>0?
        JSON.stringify(openingQuestionList):''
    }
      emit('update:Opening',Opening)
      openingVisible.value=false
  }

  //开场设置
  const editOpening = () => {
    openingVisible.value=true
  }

    //添加开场问题
    const addOpeningQuestion = () => {
        if(!alterInfoFrom.value.openingQuestionList){
            alterInfoFrom.value.openingQuestionList=[{openingQuestion:''}]
        }
        alterInfoFrom.value.openingQuestionList.push({openingQuestion:''})
    }
    //删除
    const delOpeningQuestion = (index) => {
        alterInfoFrom.value.openingQuestionList.splice(index,1)
    }
  // initFrom()
  
  watch(() => props.alterText,(newValue) => {
      alterInfoFrom.value.alterText = newValue
  })

  watch(() => props.prologue,(newValue) => {
      alterInfoFrom.value.prologue = newValue
  })

  watch(() => props.openQuestion,(newValue) => {
      alterInfoFrom.value.openingQuestionList = newValue?JSON.parse(newValue):[{openingQuestion:''}]
  })
  
  defineExpose({
      isCollapseSideBar
  })
  </script>
  <style lang="scss" scoped>
      .work-flow-aside{
          max-height: 100%;
          width: 300px;
          background-color: white;
          padding: 8px 12px;
          padding-top: 20px;
          line-height: none;
          .team-config{
            //   height: 220px;
              border-bottom: 1px solid #ebeef5;
              .title{
                  font-size: 18px;
                  font-weight: bold;
                  margin-bottom: 20px;
                  height: 32px;
                  line-height: 32px;
                  align-items: center;
                  justify-content: space-between;
                  display: flex;
                  flex-direction: row;
              }
              .field{
                  display: flex;
                  margin-bottom: 10px;
                  .label{
                      width: 100px;
                      text-align: left;
                      margin-right: 10px;
                      ::after{
                          content: ' :';
                      }
                  }
                  .content{
                      width: 80%;
                  }
              }
          }
          .ai-choose{
              margin-top: 20px;
  
              .title{
                  font-size: 18px;
                  font-weight: bold;
                  margin-bottom: 20px;
              }
              .field{
                  display: flex;
                  margin-bottom: 10px;
              }
              .ai-options-content{
                  height: 80%;
                  display: flex;
                  .ai-option{
                      border: 1px solid #ccc;
                      margin: 1%;
                      height: 48%;
                      width: 48%;
                      display: flex;
                      justify-content: center;
                      align-items: center;
                      flex-direction: column;
                      .option-icon{
                          display: flex;
                          justify-content: center;
                          align-items: center;
                          height: 50px;
                      }
                      .option-title{
                          height: 26px;
                          line-height: 26px;
                          font-size: 16px;
                          font-weight: bold;
                      }
                      .option-detail{
                          height: 16px;
                          line-height: 16px;
                          font-size: 12px;
                      }
                  }
              }
          }
      }

    ::v-deep .el-dialog {
        display: flex;
        flex-direction: column;
        margin: 0 !important;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        max-height: calc(100% - 30px);
        max-width: calc(100% - 30px);
    }

    ::v-deep(.el-dialog__body) {
        padding: 0;
    }
  </style>
  <style scoped>
  .custom-collapse-item{
      height: calc( 100vh - 500px);
      /* max-height: 540px; */
      overflow: auto;
  }
  .custom-collapse-item ::v-deep.el-collapse{
      border-top:none ;
  }
  .custom-tabs-item ::v-deep.el-tabs__item{
      padding: none;
  }
  
  .my-header{
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
      gap: 16px;
      height: 40px;
  }
  .dialog-footer{
      display: flex;
      flex-direction: row;
      justify-content: center;
      align-items: center;
      height: 40px;
      /* padding: 0 20%; */
  }
  </style>
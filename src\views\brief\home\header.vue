<template>
  <div class="header">
    <div class="left">
      <inline-svg src="/icons/menu.svg" />
      AI工具
    </div>
    <div class="right">
      <user />
    </div>
  </div>>
</template>

<script setup>
import User from '../user.vue'
import InlineSvg from 'vue-inline-svg'
</script>

<style lang="scss" scoped>
.header {
  height: 90px;
  width: 100%;
  padding: 0 10px;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;

  .left {
    padding-left: 64px;
    font-size: 14px;
    color: #929499;
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;

    svg {
      width: 14px;
      height: 14px;
      margin-right: 30px;
    }
  }

  .right {
    padding-right: 64px;
    display: flex;
    flex-direction: row;
    justify-content: flex-end;
    align-items: center;
  }
}
</style>
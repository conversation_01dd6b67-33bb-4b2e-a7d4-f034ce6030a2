<template>
    <div style="display: flex;">
        <div class="common-container">
            <div class="common-header">
                <div class="title">应用商店</div>
                <div class="header-right">
                    <div style="width: 90px;margin-right: 8px;">
                        <el-select v-model="queryParams.launchStatus" @change="searchList">
                            <el-option 
                                :label="'所有'"
                                :value="'all'" />
                            <template v-for="item in app_launched_status">
                                <el-option 
                                :label="item.label"
                                :value="item.value" />
                            </template>
                        </el-select>
                    </div>
                    <el-input
                        v-model="queryParams.applicationName"
                        placeholder="搜索"
                        clearable
                        :prefix-icon="Search"
                        style="width: 200px;"
                        @keyup.enter="searchList" 
                        maxlength="100"
                    />
                </div>
            </div>
            <application-store-label-filter @change="filterSearch" />
            <div class="common-content">
                <div class="app-list" 
                    v-loading="loading"
                    v-infinite-scroll="nextPage" 
                    :infinite-scroll-disabled="disabledload"
                    :infinite-scroll-distance="10"
                    >
                    <template v-for="item in list">
                        <ApplicationStoreCard :app="item" 
                         @edit-tags="openEditTagsDialog"
                         @submit="searchList" @click="chatDrawerOpen(item)"/>
                    </template>
                </div>
                <div v-if="noMore && list.length>0" style="text-align: center;margin-top: 20px;color: rgb(191, 191, 191);font-size: 14px;">没有更多了</div>
                <div v-if="!list ||list.length==0" style="height:calc(100vh - 200px);display: flex;flex-direction: column;align-items: center;justify-content: center;">
                    <img :src="noData" style="height: 100px;width: 100px;"/>
                    <div style="color:#dbdbdb;font-weight:550">
                        暂无数据
                    </div>
                </div>
            </div>
        </div>
        <edit-tags-dialog ref="editTagsDialogRef" @submit="searchList" />

        <!--会话-->
        <el-drawer v-model="chatDrawerShow" :with-header="false" :size="isTaskOrientedTeam?'60%':'45%'" :close-on-click-modal="false">
            <div style="border-bottom: 1px solid #ccc;">
                <div style="padding:10px;display:flex;justify-content:space-between"><span style="align-items:center;line-height:32px" >预览</span> 
                    <el-button :icon="Close" text link @click="chatDrawerCancel" />
                </div>
            </div> 
            <div style="background-color: #F7F7FA;height: calc(100vh - 72px);">
                <yi-mai-chat :chat-url="currentInstruct && currentInstruct.applicationType == 'agent'?agentUrl+'/conversation/ws':''"
                ref="messageCtrl" :current-instruct="currentInstruct" :preface-state="false" :is-title="true" :is-key-list="true" :is-debugging= "true" 
                v-if="currentInstruct && !isTaskOrientedTeam">
                </yi-mai-chat>
                <!--任务型团队会话-->
                <div v-if="currentInstruct && isTaskOrientedTeam">
                    <task-oriented-team ref="messageCtrl" :current-instruct="currentInstruct" @conversation-refresh="conversationRefresh" 
                    :is-debugging = "true"></task-oriented-team>
                </div>
            </div>
        </el-drawer>
    </div>
</template>

<script setup>
import { listApp,getClassify } from "@/api/application/store"
import {Plus,Search,Close} from '@element-plus/icons-vue'
import ApplicationStoreCard from './components/applicationStoreCard'
import ApplicationStoreLabelFilter from './components/applicationStoreLabelFilter'
import EditTagsDialog from './components/editTagsDialog'
import YiMaiChat from '@/components/YiMaiChatIntegration/YiMaiChat/index.vue'
import TaskOrientedTeam from '@/components/YiMaiChatIntegration/TaskOrientedTeam.vue'
import { getDetail } from '@/api/agent/body'
import {getTeamInfo} from '@/api/teamManagement/team'
import { getCurrentInstructTeam } from '@/api/YiMaiChat/sessionRecordsDify'
import noData from "@/assets/icons/svg/no-data.svg";

const { proxy } = getCurrentInstance()
const { app_launched_status } = proxy.useDict("app_launched_status")
const editTagsDialogRef = ref(null)

const agentUrl = import.meta.env.VITE_APP_AGENT_CHAT
const teamUrl = import.meta.env.VITE_APP_TEAM_CHAT

const queryParams = ref({
    launchStatus:'all',
    applicationName:'',
    pageNum:1,
    pageSize:15
})

const tagFilterParams = ref({
    mode:null,ids:null,
    pid:null,isRecommend:null
})
const currentCategory = ref(null)

const list = ref([])
const total = ref(0)

const loading = ref(false)
const noMore = computed(() => list.value.length >= total.value)
const disabledload = computed(() => loading.value || noMore.value)

const getList = () => {
    loading.value = true
    const params = {
        ...queryParams.value,
        ...tagFilterParams.value
    }
    if(queryParams.value.launchStatus == 'all'){
        delete params.launchStatus
    }
    getClassify().then(classify => {
        const childClassify = classify.data.filter(item => item.id == currentCategory.value).reduce((prev,cur) =>{
            return [...prev,...cur.children]
        },[])
        listApp(params).then(res => {
            total.value = res.total
            const newList = res.rows.map(item => {
                const classifyNames = []
                if(currentCategory.value == 'mode'){
                    const modeTags = [
                        {label: '聊天助手',value: 'advanced-chat'},
                        {label: 'Agent',value: 'agent'},
                        {label: '工作流',value: 'workflow'}
                    ]
                    classifyNames.push(modeTags.find(tag => tag.value == item.mode).label)
                }else{
                    item.classifyIds?.split(',').forEach(id => {
                        const find = childClassify.find(c => c.id == id)
                        if(find){
                            classifyNames.push(find.classifyName)
                        }
                    })
                }
                return {
                    ...item,
                    classifyNames
                }
            })
            list.value = [...list.value, ...newList]
            loading.value = false
        })
    })
}

const filterSearch = (command) => {
    loading.value = true
    queryParams.value.pageNum = 1
    list.value = []
    tagFilterParams.value = {mode:null,ids:null,pid:null,isRecommend:null}
    if(command.value == 'category-change'){
        currentCategory.value = command.category
        if(command.category != 'mode'){
            tagFilterParams.value.pid = command.category
        }
    }
    if(command.value == 'tag-click'){
        if(command.tag == 'primary'){
            tagFilterParams.value.isRecommend = 1
        }else if(['advanced-chat','agent','workflow'].includes(command.tag)){
            tagFilterParams.value.mode = command.tag
        }else{
            tagFilterParams.value.ids = command.tag
        }
    }
    getList()
}

const searchList = () => {
    queryParams.value.pageNum = 1
    list.value = []
    getList()
}

const nextPage = () => {
    if (noMore.value) return
    queryParams.value.pageNum++
    getList()
}

const toSave = () => {
    
}

const openEditTagsDialog = (appInfo) => {
    editTagsDialogRef.value.open()
    editTagsDialogRef.value.setAppInfo(appInfo)
}

//会话弹框
const chatDrawerShow = ref(false)
const currentInstruct = ref(null)
const messageCtrl = ref(null)
const isTaskOrientedTeam = ref(false)
const chatDrawerOpen = (app) => {
    if(app.applicationType == 'agent'){
        isTaskOrientedTeam.value = false
        getDetail(app.applicationId).then((resp) => {
            resp.data.keyList.forEach(element => {
                element.keyType = element.keyType==3?'select':element.keyType==2?'paragraph':'text'
            });
            currentInstruct.value = {
                applicationIcon:resp.data.agentIconUrl,
                applicationId:resp.data.agentId,
                applicationName:resp.data.agentName,
                applicationType:'agent',
                isUpload:resp.data.isUpload,
                keyList:resp.data.keyList,
                mode:resp.data.agentType==1?'advanced-chat':'agent',
                openQuestion:resp.data.openQuestion,
                prologue:resp.data.prologue,
                prompt:resp.data.prompt,
                uploadSettings:resp.data.uploadSettings
            }
            chatDrawerShow.value = true
        })
    }
    else{
        getTeamInfo(app.applicationId).then(res => {
            let instruct = {
                appId:res.data.appId,
                applicationIcon:res.data.teamImgUrl,
                applicationName:res.data.teamName,
                applicationType:'team',
                keyList:[],
                mode:res.data.mode,
                openQuestion:res.data.openQuestion,
                prologue:res.data.prologue,
                prompt:res.data.prompt,
            }
            getCurrentInstructTeam(res.data.appId).then(response => {
                let currentInstructStaging = {}
                currentInstructStaging = {...instruct,...response}
                currentInstructStaging.appId = res.data.appId
                //会话型
                if(res.data.mode == 'advanced-chat'){
                    currentInstructStaging.chatUrl = teamUrl+'/console/api/apps/'+res.data.appId+'/team_chat_messages'
                    currentInstructStaging.isTaskOrientedTeam = false
                    isTaskOrientedTeam.value = false
                    currentInstruct.value = currentInstructStaging
                    chatDrawerShow.value = true
                }//任务型
                else if(res.data.mode == 'workflow'){
                    currentInstructStaging.chatUrl = teamUrl+'/console/api/apps/'+res.data.appId+'/workflows/run'
                    currentInstructStaging.isTaskOrientedTeam = true
                    isTaskOrientedTeam.value = true
                    currentInstruct.value = currentInstructStaging
                    chatDrawerShow.value = true
                }
            })
        })
    }
}
const chatDrawerCancel = () => {
    currentInstruct.value = null
    // messageCtrl.value.openNewSessionClear()
    chatDrawerShow.value = false
}

</script>

<style lang='scss' scoped>
.common-container {
    display: flex;
    flex-direction: column;
    background-color: #F7F7FA;
    height: calc(100vh - 52px);
    width: 100%;
    margin: 0;
    padding: 0;
    background-size: cover;
}
.common-header {
    height: 80px;
    padding: 34px 30px 4px 30px;
    line-height: 24px;
    display: flex;
    justify-content: space-between;

    .title {
        color: #032220;
        font-size: 18px;
        font-weight: 500;
    }

    .header-right {
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: row;
    }
}
.common-content {
    margin: 0 30px;
    height: calc(100vh - 200px);
    max-height: calc(100vh - 200px);
    display: flex;
    flex-direction: column;
    flex-shrink: 0;
    flex-grow: 1;
    padding-bottom: 10px;
    overflow: auto;
    .app-list{
        display: grid;
        grid-template-columns: repeat(3, minmax(0, 1fr));
        gap: 1rem;
        align-content: flex-start;
    }
}

::v-deep .el-drawer.rtl {
    height: 98%;
    top: 10px;
    bottom: 10px;
    right: 10px;
    border-radius:10px;
}
</style>
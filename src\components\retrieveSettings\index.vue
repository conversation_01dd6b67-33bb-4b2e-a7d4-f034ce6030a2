<template>
    <el-row class="retrieve-settings">
        <el-col :span="24" v-for="(retrieval,index) in retrievalList" :key="index" style="margin-top: 10px;">
            <div class="radio" 
            :class="retrievalModel.search_method==retrieval.search_method?'radio-sel':''">
                <div style="display:flex;justify-content:white-space;cursor: pointer;" @click="retrievalClick(retrieval)">
                    <div  class="title-image-box">
                        <img :src="retrieval.img" style="background-color:#7d50e630;" />
                    </div>
                    <div style="width:100%;margin-left:30px">
                        <div class="title">
                            <span>{{retrieval.title}}</span>
                            <el-radio-group v-model="retrievalModel.search_method">
                                <el-radio :label="retrieval.search_method">{{ '' }}</el-radio>
                            </el-radio-group>
                        </div>
                        <div class="content">
                            {{retrieval.content}}
                        </div>
                    </div>
                </div>
                <div class="launch-content" v-if="retrievalModel.search_method==retrieval.search_method">
                    <div style="display:flex;align-items:center;">
                        <el-switch v-model="retrievalModel.reranking_enable" 
                            :disabled="retrievalModel.search_method=='hybrid_search'" 
                            @change="rerankEnabledChange"
                            /> <!--  -->
                        <span class="font" style="margin-left:10px">Rerank 模型</span>  
                        <el-tooltip content="重排序模型将根据候选文档列表与用户问题语义匹配度进行重新排序，从而改进语义排序的结果。" 
                        effect="light" placement="top" popper-class="tooltip-pop">
                            <el-icon class="icon" style="margin-left:5px"><QuestionFilled /></el-icon>
                        </el-tooltip>
                    </div>
                    <el-row v-if="retrievalModel.reranking_enable">
                        <!-- <el-col :span="24" class="icon-group">
                            <img :src="rerankmodel" style="background-color:#f4f4f5;" />
                            <img :src="warning" style="margin-left: 5px;"/>
                        </el-col> -->
                        <el-col>
                            <model-selector
                                type="rerank"
                                v-model:model-name="retrievalModel.reranking_model.reranking_model_name"
                                v-model:model-provider="retrievalModel.reranking_model.reranking_provider_name"
                                style="width: 100%; margin-top: 10px;"
                            />
                        </el-col>
                    </el-row>
                    <el-row style="margin-top:20px">
                        <el-col :span="12">
                            <div style="display:flex;align-items:center;">
                                <span class="font">Top K</span>
                                <el-tooltip content="用于筛选与用户问题相似度最高的文本片段。系统同时会根据选用模型上下文窗口大小动态调整分段数量。" 
                                effect="light" placement="top" popper-class="tooltip-pop">
                                    <el-icon class="icon" style="margin-left:5px"><QuestionFilled /></el-icon>
                                </el-tooltip>
                            </div>
                        </el-col>
                        <el-col :span="12">
                            <div style="display:flex;align-items:center;">
                                <el-switch v-model="retrievalModel.score_threshold_enabled" 
                                    :disabled="!(retrievalModel.search_method == 'semantic_search' || 
                                        retrievalModel.reranking_enable)"
                                    @change="scoreThresholdEnabledChange" /> 
                                <span class="font" style="margin-left:10px">Score 阈值</span>
                                <el-tooltip content="用于设置文本片段筛选的相似度阈值。" 
                                effect="light" placement="top" popper-class="tooltip-pop">
                                    <el-icon class="icon" style="margin-left:5px"><QuestionFilled /></el-icon>
                                </el-tooltip>
                            </div>
                        </el-col>
                        <el-col :span="12" class="slider-demo-block" style="padding-right:10px">
                            <span class="demonstration">
                            {{ retrievalModel.top_k }}</span>
                            <el-slider v-model="retrievalModel.top_k" :min="0" :max="10" :step="1" />
                        </el-col>
                        <el-col :span="12" class="slider-demo-block" style="padding-left:10px">
                            <span class="demonstration">
                            {{ retrievalModel.score_threshold_enabled==true?retrievalModel.score_threshold:'' }}
                            </span>
                            <el-slider v-model="retrievalModel.score_threshold" :min="0.01" :max="0.99" :step="0.01" 
                                :disabled="!(retrievalModel.search_method == 'semantic_search' || 
                                retrievalModel.reranking_enable) ||
                                !retrievalModel.score_threshold_enabled"/>
                        </el-col>
                    </el-row>
                </div>
            </div>
        </el-col>
    </el-row>
</template>
<script setup>
    import { nextTick, ref } from 'vue'
    import model from "@/assets/icons/svg/model.svg";
    import vectorretrieval from "@/assets/icons/svg/vectorretrieval.svg";
    import fullTextSearch from "@/assets/icons/svg/fullTextSearch.svg";
    import mixedRetrieval from "@/assets/icons/svg/mixedRetrieval.svg";
    import ModelSelector from '@/components/Model/modelSelector.vue';
    import { getDefaultModel } from '@/api/model';

    const props = defineProps({
        modelValue: [String, Object, Array],
    });

    //检索设置列表
    const retrievalList = ref([
        {search_method:'semantic_search',title:'向量检索',img:vectorretrieval,content:'通过生成查询嵌入并查询与其向量表示最相似的文本分段'},
        {search_method:'full_text_search',title:'全文检索',img:fullTextSearch,content:'索引文档中的所有词汇，从而允许用户查询任意词汇，并返回包含这些词汇的文本片段'},
        {search_method:'hybrid_search',title:'混合检索',img:mixedRetrieval,content:'同时执行全文检索和向量检索，并应用重排序步，从两类查询结果中选择匹配用户问题的最佳结果，需配置 Rerank 模型 API'},
    ])

    const default_model = ref('')
    const default_provider = ref('')

    const { proxy } = getCurrentInstance();
    const emit = defineEmits();
    const retrievalModel = ref({});
    watch(() => props.modelValue, val => {
        if (val) {
            retrievalModel.value = val
            return val
        } else {
            retrievalModel.value = {}
            return {}
        }
    },{ deep: true, immediate: true });

    //切换检索方式处理
    const retrievalClick = (retrieval) => {
        if(retrieval.search_method!=retrievalModel.value.search_method){
            retrievalModel.value.search_method=retrieval.search_method;
            if(retrieval.search_method=='hybrid_search'){
                retrievalModel.value.reranking_enable=true
            }
            // retrievalModel.value.reranking_enable=false;
            // retrievalModel.value.reranking_model={
            //     reranking_provider_name:'',
            //     reranking_model_name:''
            // };
            // retrievalModel.value.top_k=3;
            // retrievalModel.value.score_threshold_enabled=false;
            // retrievalModel.value.score_threshold=null;
            emit("update:modelValue", retrievalModel.value);
        }
    }

    //Rerank 模型 选择模型处理
    const rerankModelClick = (item) => {
        retrievalModel.value.reranking_model.reranking_model_name=item.model
        retrievalModel.value.reranking_model.reranking_provider_name=item.reranking_provider_name
    }

    //点击Rerank 模型下拉 获取宽度赋值给下拉弹框
    const rerankModelWidth = ref(0)
    const rerank_search = ref(null);
    const rerankClick = () => {
        rerankModelWidth.value=rerank_search.value[0].offsetWidth;
    }

    const scoreThresholdEnabledChange = () => {
        
    }
    
  //开启rerank模型，默认值设置第一个 ，关闭默认值设置为空
    const rerankEnabledChange = (item) => {
        if(item){
            retrievalModel.value.reranking_model.reranking_model_name=default_model.value
            retrievalModel.value.reranking_model.reranking_provider_name=default_provider.value
        }else{
            retrievalModel.value.reranking_model.reranking_model_name=''
            retrievalModel.value.reranking_model.reranking_provider_name=''
        }
    }

    getDefaultModel('rerank').then(res => {
        if (res.data) {
            default_model.value = res.data.model;
            default_provider.value = res.data.provider.provider;
        }
    })

</script>
<style scoped lang="scss">
.retrieve-settings{
    .radio{
        line-height: 20px;
        // width:45%;
        border:1px solid #dcdfe6;
        border-radius:10px;
        .title-image-box{
            text-align:center;
            padding-top:10px;
            padding-left:30px;
            img{
                padding:5px;
                height: 35px;
                width: 35px;
                border-radius: 4px;
            }
        }
        .title{
            margin-top:5px;
            display: flex;
            justify-content: space-between;
            align-items: center;

        }
        .content{
            font-size: 13px;
            color:#999999;
            padding-right: 5px;
            min-height:30px;
            padding-bottom: 10px;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        .launch-content{
            min-height: 100px;
            border-top:1px solid #dcdfe6;
            padding: 20px 40px;
            .icon{
                color: #999999;
            }
            .icon-group{
                height:40px;
                background-color:#ffd65236;
                border-radius: 10px; 
                padding: 5px;
                margin-top:5px;
                img{
                    padding:5px;
                    height: 30px;
                    width: 30px;
                    border-radius: 4px;
                }
            }
            .slider-demo-block {
                max-width: 600px;
                display: flex;
                align-items: center;
                .el-slider {
                    margin-top: 0;
                    margin-left: 12px;
                }
                .demonstration {
                    width:80px;
                    font-size: 16px;
                    line-height: 44px;
                    color:#706f6f;
                    font-weight: 540;
                }
            }
            .image-box{
                text-align:center;
                padding-top:10px;
                img{
                    height: 25px;
                    width: 25px;
                    border-radius: 4px;
                }
            }
            .option-title{
                cursor: pointer; 
                line-height: 32px;
                // width:125px;
                background-color:#f3f3f3;
                border-radius: 8px;
                padding:0 5px;
            }
        }
    }
    .font{
        font-size: 1vw; /* 这里的5vw表示字体大小为视口宽度的5% */
        white-space: nowrap; /* 防止文字换行 */
        overflow: hidden; /* 超出容器部分隐藏 */
        text-overflow: ellipsis; /* 超过部分显示省略号 */
    }
    .radio-sel{
        border: 1px solid #5050E6;
    }
}
</style>
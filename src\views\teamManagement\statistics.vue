<template>
    <div class="single-page-container">
        <div class="single-page-header">
            <div class="left-box">
                <el-button icon="Back" @click="router.back();" text style="margin-right: 10px;" link></el-button>
                <div class="text-box" v-if="teamInfo">
                    <div class="title">
                        {{ teamInfo.teamName }}
                    </div>
                    <div class="detail">
                        {{ teamInfo.teamDescribe }}
                    </div>
                </div>
            </div>
            <div class="right-box">
            </div>
        </div>
        <div class="whole">
            <div class="title-box">
                <div class="title"><img :src="dataBoard" />数据看板</div>
                <div class="sel">
                    <el-date-picker v-model="dateRange" value-format="YYYY-MM-DD" type="daterange" range-separator="-"
                        start-placeholder="开始日期" end-placeholder="结束日期" popper-class="date-style" :shortcuts="shortcuts"
                        @change="dateRangeChange"></el-date-picker>
                </div>
            </div>
            <div class="data-box">
                <div class="data-overview">
                    <div v-for="(item, index) in overviewList" class="overview" @click="overviewClick(item)"
                    :style="{'background-color':overviewSel.code==item.code?'#DFDFDF':'white' }">
                        <div class="left">
                            <!--图标-->
                            <img class="icon" :src="item.icon">
                        </div>
                        <div class="right">
                            <!--名称-->
                            <div class="title">
                                {{ item.name }}
                                <el-tooltip :content="item.desc" 
                                effect="light" placement="top" popper-class="tooltip-pop">
                                    <el-icon style="margin-left:5px;color: #999999"><QuestionFilled /></el-icon>
                                </el-tooltip>
                            </div>
                            <!--值-->
                            <div class="value">
                                {{ item.code=='TokenConsumeTotal'?overview[item.code]+'K':overview[item.code] }}
                            </div>
                        </div>
                    </div>
                </div>
                <div class="data-trend-box">
                    <div style="display: flex;align-items: center;">
                        数据趋势
                        <span style="font-size: 12px;color: rgb(154, 154, 154);">（{{ overviewSel.name }}）</span>
                    </div>
                    <div class="data-trend">
                        <div ref="chart" style="height: 100%;" />
                    </div>
                </div>
            </div>
        </div>

    </div>
</template>
<script setup>
import { getTeamInfo, getStatisticsData } from '@/api/teamManagement/statistics'
import router from "@/router";
import dataBoard from '@/assets/icons/svg/data-board.svg'
import { dayjs } from 'element-plus';
import appliedUser from '@/assets/icons/svg/applied-user.svg'
import appliedUse from '@/assets/icons/svg/applied-app-visits.svg'
import appliedMessages from '@/assets/icons/svg/applied-messages.svg'
import appliedToken from '@/assets/icons/svg/applied-token.svg'
import * as echarts from 'echarts';

const route = useRoute();

const id = route.query.id

const teamInfo = ref(null)
const getDetailMethod = () => {
    getTeamInfo(id).then(res => {
        teamInfo.value = res.data
    })
}
getDetailMethod()

//日期选择器处理
const shortcuts = [
    {
        text: '近1周',
        value: () => {
            const fourWeeksAgo = dayjs().subtract(6, 'day').startOf('day');
            const today = dayjs().endOf('day');
            return [fourWeeksAgo.format('YYYY-MM-DD'), today.format('YYYY-MM-DD')]
        },
    },
    {
        text: '近30天',
        value: () => {
            const threeMonthsAgo = dayjs().subtract(1, 'month');
            const date_end = dayjs().endOf('day');
            return [threeMonthsAgo.format('YYYY-MM-DD'), date_end.format('YYYY-MM-DD')]
        },
    },
    {
        text: '近半年',
        value: () => {
            const twelveMonthsAgo = dayjs().subtract(6, 'month');
            const date_end = dayjs().endOf('day');
            return [twelveMonthsAgo.format('YYYY-MM-DD'), date_end.format('YYYY-MM-DD')]
        },
    },
    {
        text: '近一年',
        value: () => {
            const twelveMonthsAgo = dayjs().subtract(12, 'month');
            const date_end = dayjs().endOf('day');
            return [twelveMonthsAgo.format('YYYY-MM-DD'), date_end.format('YYYY-MM-DD')]
        },
    }
];

//日期
const dateRange = ref([
    dayjs().subtract(6, 'day').startOf('day').format('YYYY-MM-DD'),
    dayjs().endOf('day').format('YYYY-MM-DD')
])

//总览数据
const overviewList = ref([
    { code: 'userTotal', name: '活跃用户数', icon: appliedUser, desc: '累计与AI有实质互动的唯一用户数，不计调试期用户。' },
    { code: 'appliedUse', name: '使用数', icon: appliedUse, desc: '用户每开启新会话计一次，调试会话除外。' },
    { code: 'messagesTotal', name: '消息数', icon: appliedMessages, desc: '每回答用户一个问题算一条消息数，调试不计。' },
    { code: 'TokenConsumeTotal', name: 'Token消耗数', icon: appliedToken, desc: '累计使用大模型消耗的Token数，调试不计。' }
])

//总览数据值
const overview = ref({
    userTotal: 98,
    appliedUse: 1500,
    messagesTotal: 6520,
    TokenConsumeTotal: 2000
})

//选中的code 默认用户数
const overviewSel = ref({
    code: 'userTotal',
    name: '用户数',
    icon: appliedUser
})
const overviewClick = (item) => {
    overviewSel.value = item
    getChartData(overviewSel.value.code)
}
//图表数据
const chartData = ref({})
//图表
const chart = ref(null)

const dateRangeChange = () => {
    getChartData(overviewSel.value.code)
    const params = {
        start: dateRange.value[0] + ' 00:00',
        end: dateRange.value[1] + ' 23:59',
    }
    getStatisticsData(teamInfo.value.appId, 'all-daily', params).then(res => {
        overview.value.userTotal = res.terminal_count
        overview.value.appliedUse = res.conversation_count
        overview.value.messagesTotal = res.messages
        overview.value.TokenConsumeTotal = res.token_count
    })
}

//获取数据
const getChartData = (type) => {
    const params = {
        start: dateRange.value[0] + ' 00:00',
        end: dateRange.value[1] + ' 23:59',
    }
    const searchTypeMap = {
        userTotal: 'app-daily-end-users',
        appliedUse: 'app-daily-conversations',
        messagesTotal: 'daily-message',
        TokenConsumeTotal: 'app-token-costs'
    }
    getStatisticsData(teamInfo.value.appId, searchTypeMap[type], params).then(res => {
        const typeValueKeyMap = {
            userTotal: 'terminal_count',
            appliedUse: 'conversation_count',
            messagesTotal: 'messages',
            TokenConsumeTotal: 'token_count'
        }
        chartData.value.xData = res.data.map(item => item.date)
        chartData.value.data = res.data.map(item => item[typeValueKeyMap[type]])
        getChart()
    })
}

const getChart = () => {
    const chartInstance = echarts.init(chart.value);
    chartInstance.setOption({
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'cross'
            }
        },
        xAxis: {
            type: 'category',
            boundaryGap: false,
            data: chartData.value.xData
        },
        yAxis: {
            type: 'value'
        },
        grid: {
            top: '10%',    // 上方的距离
            bottom: '5%', // 下方的距离
        },
        series: [{
            data: chartData.value.data,
            type: 'line',
            smooth: true,
            areaStyle: {
                normal: {
                    color: '#5087ec54'
                },
                emphasis: {
                    color: '#5087ec54'
                },
            }
        }]
    })
    window.addEventListener("resize", () => {
        chartInstance.resize();
    });
}

const initPage = () => {
    getTeamInfo(id).then(res => {
        teamInfo.value = res.data
        const params = {
            start: dateRange.value[0] + ' 00:00',
            end: dateRange.value[1] + ' 23:59',
        }
        getStatisticsData(teamInfo.value.appId, 'all-daily', params).then(res => {
            overview.value.userTotal = res.terminal_count
            overview.value.appliedUse = res.conversation_count
            overview.value.messagesTotal = res.messages
            overview.value.TokenConsumeTotal = res.token_count
        })
        getChartData(overviewSel.value.code)
    })
}

onMounted(() => {
    initPage()
});

</script>
<style scoped lang="scss">
.single-page-container {
    background-color: #F7F7FA;

    .single-page-header {
        height: 80px;
        display: flex;
        background-color: white;
        margin: 0 20px;
        justify-content: space-between;
        align-items: center;
        flex-direction: row;
        border-radius: 8px;

        .left-box {
            margin-left: 24px;
            display: flex;
            align-items: center;

            .text-box {
                display: flex;
                flex-direction: column;
                margin-left: 10px;

                .title {
                    display: flex;
                    align-items: center;
                    height: 25px;
                    font-size: 18px;
                    font-weight: 500;
                    color: #032220;
                }

                .detail {
                    height: 17px;
                    font-size: 12px;
                    color: #999999;
                    font-weight: 400;
                    margin-top: 4px;
                }
            }
        }

        .right-box {
            margin-right: 20px;
        }
    }
}

.whole {
    padding: 10px 20px;
    height: calc(100vh - 130px);
    background-color: #F7F7FA;

    .title-box {
        display: flex;
        align-items: center;
        justify-content: space-between;

        .title {
            display: flex;
            align-items: center;

            img {
                width: 30px;
                margin-bottom: -2px;
            }
        }

        .sel {
            margin-right: 10px;
        }
    }

    .data-box {
        display: flex;
        flex-direction: column;
        padding: 10px;
        height: calc(100vh - 200px);

        .data-overview {
            flex: 0.3;
            background-color: white;
            border-radius: 8px;
            padding: 10px 170px;
            display: flex;
            justify-content: space-between;
            align-items: center;

            .overview {
                cursor: pointer;
                display: flex;
                flex-direction: row;
                justify-content: space-between;
                padding: 10px 40px 10px 15px;
                border-radius: 8px;
                border: 1px solid #DFDFDF;

                .left {
                    .icon {
                        width: 30px;
                        height: 30px;
                    }
                }

                .right {
                    margin-left: 20px;

                    .title {
                        font-size: 14px;
                        color: #6C6C6C;
                        margin-bottom: 5px;
                    }

                    .value {
                        margin-top: 15px;
                        font-size: 16px;
                        font-weight: 600;
                        color: black
                    }
                }
            }

            .overview:hover {
                background-color: #DFDFDF;
            }
        }

        .data-trend-box {
            flex: 1;
            margin-top: 10px;
            height: 100%;

            .data-trend {
                height: 100%;
                background-color: white;
                border-radius: 8px;
                padding: 10px;
                margin: 10px 0;
            }
        }
    }
}
</style>
<template>
    <div style="display: flex;flex-direction: column;gap: .5rem;">
        <template v-for="segOption in segOptions">
            <div class="doc-seg-option"
                :class="{'selected': segSetting == segOption.value}" 
                @click="segSettingChange(segOption.value)">
                <el-radio-group v-model="segSetting" style="width: 24px;height: 24px;">
                    <el-radio :label="segOption.value">{{ '' }}</el-radio>
                </el-radio-group>
                <div style="width: calc(100% - 30px);">
                    <div class="doc-seg-option-title">{{ segOption.label }}</div>
                    <div class="doc-seg-option-desc">{{ segOption.desc }}</div>
                    <template v-if="segSetting == 0 && segOption.value == 0">
                        <div class="custom-config">
                            <div class="item">
                                <label class="label">检索增强</label>
                                <el-checkbox v-model="segAgg">
                                    <div style="display: flex;align-items: center;justify-content: start;gap: .25rem;">
                                        分段聚合
                                        <el-tooltip effect="light" placement="top"
                                            content="将文本进行分段处理后，在每个片段中加上段落描述，增强了大模型对知识库的检索能力。">
                                            <el-icon>
                                                <QuestionFilled />
                                            </el-icon>
                                        </el-tooltip>
                                    </div>
                                </el-checkbox>
                                <label class="label" style="margin-top: 10px;" v-if="segAgg">段落描述内容</label>
                                <el-input v-model="segAggDescText" :maxlength="100"
                                    placeholder="可自定义段落描述内容，默认为‘以下内容与(文件名)相关：’"  v-if="segAgg"/>
                            </div>
                        </div>
                    </template>
                    <template v-if="segSetting == 1 && segOption.value == 1">
                        <div class="custom-config">
                            <div class="item">
                                <label class="label requrie">分段标识符</label>
                                <el-input v-model="customIdt" />
                            </div>
                            <div class="item">
                                <label class="label requrie" style="align-items: center;display: flex;">分段最大长度
                                    <el-tooltip effect="light" placement="top"
                                        content="指分割的片段最多包含的标记数(即Token数)">
                                        <el-icon style="margin-left: 5px;">
                                            <QuestionFilled />
                                        </el-icon>
                                    </el-tooltip>
                                </label>
                                <el-input-number style="width: 100%; margin-bottom: 10px;" :min="100" :max="2000"
                                    v-model="maxLength" controls-position="right"
                                    @blur="() => { maxLength = maxLength || 800 }" />
                            </div>
                            <div class="item">
                                <label class="label">文本预处理规则</label>
                                <el-checkbox v-model="replaceEmpty">替换掉连续的空格、换行符和制表符</el-checkbox>
                                <el-checkbox v-model="deleteAllEmail">删除所有 URL和电子邮箱地址</el-checkbox>
                            </div>
                            <div class="item">
                                <label class="label">检索增强</label>
                                <el-checkbox v-model="segAgg">
                                    <div style="display: flex;align-items: center;justify-content: start;gap: .25rem;">
                                        分段聚合
                                        <el-tooltip effect="light" placement="top"
                                            content="将文本进行分段处理后，在每个片段中加上段落描述，增强了大模型对知识库的检索能力。">
                                            <el-icon>
                                                <QuestionFilled />
                                            </el-icon>
                                        </el-tooltip>
                                    </div>
                                </el-checkbox>
                                <label class="label" style="margin-top: 10px;" v-if="segAgg">段落描述内容</label>
                                <el-input v-model="segAggDescText" :maxlength="100"
                                    placeholder="可自定义段落描述内容，默认为‘以下内容与(文件名)相关：’"  v-if="segAgg"/>
                            </div>
                        </div>
                    </template>
                    <template v-if="segSetting == 2 && segOption.value == 2">
                        <div class="custom-config">
                            <div class="item">
                                <label class="label">分段规则</label>
                                <el-select v-model="segRule" style="width: 100%;margin-bottom: 10px;">
                                    <template v-for="item in segRuleOptions">
                                        <el-option :label="item.label" :value="item.value"></el-option>
                                    </template>
                                </el-select>
                            </div>
                            <div class="item">
                                <label class="label">图片支持</label>
                                <el-checkbox v-model="supportImg" @click="supportImg = !supportImg">嵌入图片链接
                                    <el-tooltip effect="light" placement="top" content="将文档中的图片转换为可访问的链接">
                                        <el-icon>
                                            <QuestionFilled />
                                        </el-icon>
                                    </el-tooltip>
                                </el-checkbox>
                                <label class="label" style="margin-top: 10px;">图片通用描述语</label>
                                <el-input v-model="supportImgDescText" :maxlength="100"
                                    placeholder="可自定义图片描述语，默认为‘以下为相关图片链接:’" />
                            </div>
                            <div class="item">
                                <label class="label">检索增强</label>
                                <el-checkbox v-model="segAgg">
                                    <div style="display: flex;align-items: center;justify-content: start;gap: .25rem;">
                                        分段聚合
                                        <el-tooltip effect="light" placement="top"
                                            content="将文本进行分段处理后，在每个片段中加上段落描述，增强了大模型对知识库的检索能力。">
                                            <el-icon>
                                                <QuestionFilled />
                                            </el-icon>
                                        </el-tooltip>
                                    </div>
                                </el-checkbox>
                                <label class="label" style="margin-top: 10px;" v-if="segAgg">段落描述内容</label>
                                <el-input v-model="segAggDescText" :maxlength="100"
                                    placeholder="可自定义段落描述内容，默认为‘以下内容与(文件名)相关：’"  v-if="segAgg"/>
                            </div>
                        </div>
                    </template>
                    <template v-if="segSetting == 3 && segOption.value == 3">
                        <div class="custom-config">
                            <div class="item">
                                <label class="label">检索增强</label>
                                <el-checkbox v-model="segAgg">
                                    <div style="display: flex;align-items: center;justify-content: start;gap: .25rem;">
                                        分段聚合
                                        <el-tooltip effect="light" placement="top"
                                            content="将文本进行分段处理后，在每个片段中加上段落描述，增强了大模型对知识库的检索能力。">
                                            <el-icon>
                                                <QuestionFilled />
                                            </el-icon>
                                        </el-tooltip>
                                    </div>
                                </el-checkbox>
                                <label class="label" style="margin-top: 10px;" v-if="segAgg">段落描述内容</label>
                                <el-input v-model="segAggDescText" :maxlength="100"
                                    placeholder="可自定义段落描述内容，默认为‘以下内容与(文件名)相关：’"  v-if="segAgg"/>
                            </div>
                        </div>
                    </template>
                    <template v-if="segSetting == 4 && segOption.value == 4">
                        <div class="custom-config">
                            <llm-model-panel v-model:model-params="modelParams"
                                    v-model:model-name="modelInfo.model_name"
                                    v-model:model-provider="modelInfo.model_provider" />
                            <div class="item">
                                <label class="label requrie">一次可分析最大页数</label>
                                <el-slider v-model="maxPage" :min="1" :marks="{ 1: '1', 15: '15' }" :max="15" :step="1" />
                            </div>
                            <div class="item">
                                <label class="label requrie">是否总结</label>
                                <el-radio-group v-model="enableSummarize">
                                    <el-radio :label="true">是</el-radio>
                                    <el-radio :label="false">否</el-radio>
                                </el-radio-group>
                            </div>
                            <div class="item">
                                <label class="label">检索增强</label>
                                <el-checkbox v-model="segAgg">
                                    <div style="display: flex;align-items: center;justify-content: start;gap: .25rem;">
                                        分段聚合
                                        <el-tooltip effect="light" placement="top"
                                            content="将文本进行分段处理后，在每个片段中加上段落描述，增强了大模型对知识库的检索能力。">
                                            <el-icon>
                                                <QuestionFilled />
                                            </el-icon>
                                        </el-tooltip>
                                    </div>
                                </el-checkbox>
                                <label class="label" style="margin-top: 10px;" v-if="segAgg">段落描述内容</label>
                                <el-input v-model="segAggDescText" :maxlength="100"
                                    placeholder="可自定义段落描述内容，默认为‘以下内容与(文件名)相关：’"  v-if="segAgg"/>
                            </div>
                        </div>
                    </template>
                </div>
            </div>
        </template>
    </div>
</template>
<script setup>
import {QuestionFilled} from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import llmModelPanel from '@/components/Model/llmModelPanel'

const props = defineProps({
    currentSegSetting:{
        type: Number,
        default:0
    }
})
const emits = defineEmits(['update:currentSegSetting'])

const segSetting = ref(0)

const segOptions = ref([
    { label: '自动分段与清洗', desc: '自动分段与预处理规则', value: 0 },
    { label: '自定义', desc: '自定义分段规则、分段长度及预处理规则', value: 1 },
    { label: '智能分段', desc: '自动根据目录结构进行分段', value: 2 },
    { label: '智能分段(增强)', desc: '自动分段解析扫描文档', value: 3 },
    { label: '分析报告分段', desc: '分析报告并自动分段', value: 4 },
])

//选项一对应自定义参数
const maxLength = ref(800)
const customIdt = ref("\\n")
const replaceEmpty = ref(false)
const deleteAllEmail = ref(false)

//选项二对应自定义参数
const segRuleOptions = ref([
    { label: '章节分段', value: 1 }
])
const segRule = ref(1)
const segAgg = ref(true)
const supportImg = ref(true)
const supportImgDescText = ref('')
//段落描述内容
const segAggDescText = ref('')

//选项四对应自定义参数
const modelInfo = ref({
    model_name:'',
    model_provider:'',
})
const modelParams = ref('{}')

const maxPage = ref(1)
const enableSummarize = ref(true)

const segSettingChange = (val) => {
    segSetting.value = val
    emits('update:currentSegSetting',val)
}

const validateSegSetting = () => {
    if (segSetting.value == 1) {
        if(!maxLength.value){
            ElMessage.error('请输入最大长度')
            return false;
        }
        if(!customIdt.value){
            ElMessage.error('请输入自定义分段符')
            return false;
        }
    }
    return true
}

const getProcessRule = () => {
    
    let processRule = {
        rules: {},
        mode: 'automatic'
    }
    if(segSetting.value == 0){
        processRule = {
            rules: {
                enhanced_retrieval: { 
                    enable_segmented: segAgg.value,
                    segmented_aggregation: segAgg.value?segAggDescText.value:''
                }
            },
            mode: "automatic"
        }
    }
    if (segSetting.value == 1) {
        processRule = {
            rules: {
                pre_processing_rules: [
                    {
                        "id": "remove_extra_spaces",
                        "enabled": replaceEmpty.value
                    },
                    {
                        "id": "remove_urls_emails",
                        "enabled": deleteAllEmail.value
                    }
                ],
                "segmentation": {
                    "separator": customIdt.value,
                    "max_tokens": maxLength.value,
                    "chunk_overlap": 50
                },
                enhanced_retrieval: { 
                    enable_segmented: segAgg.value,
                    segmented_aggregation: segAgg.value?segAggDescText.value:''
                }
            },
            mode: "custom"
        }
    }
    if (segSetting.value == 2) {
        processRule = {
            rules: {
                advance_rules: [
                    {
                        subsection_rule: true
                    },
                    {
                        image_support: [
                            { embedding_image_url: true },
                            { image_caption: supportImgDescText.value || "以下为相关图片链接:" },
                            { multimodal_images: false }
                        ]
                    }
                ],
                enhanced_retrieval: { 
                    enable_segmented: segAgg.value,
                    segmented_aggregation: segAgg.value?segAggDescText.value:''
                }
            },
            mode: "advanced"
        }
    }
    if (segSetting.value == 3) {
        processRule = {
            rules: {
                enhanced_retrieval: { 
                    enable_segmented: segAgg.value,
                    segmented_aggregation: segAgg.value?segAggDescText.value:''
                }
            },
            mode: "advanced_enhance"
        }
    }
    if (segSetting.value == 4) {
        processRule = {
            rules: {
                report_rules: {
                    mode_info: {
                        mode_provider: modelInfo.value.model_provider,
                        mode_name: modelInfo.value.model_name,
                    },
                    model_parameters: JSON.parse(modelParams.value),
                    max_page: maxPage.value
                },
                enhanced_retrieval: { 
                    enable_segmented: segAgg.value,
                    segmented_aggregation: segAgg.value?segAggDescText.value:''
                },
                enable_summarize: enableSummarize.value
            },
            mode: "report"
        }
    }
    return processRule
}

defineExpose({
    getProcessRule,
    validateSegSetting
})

</script>
<style scoped lang="scss">
.doc-seg-option{
    padding: 2px 12px;
    border-radius: 8px; 
    border: 1px solid #ccc;
    display: flex;
    justify-content: space-between;
    cursor: pointer;
    .doc-seg-option-title {
        font-size: 14px;
        height: 40px;
        line-height: 50px;
        font-weight: 600;
    }

    .doc-seg-option-desc {
        font-size: 14px;
        font-weight: 400;
        line-height: 20px;
        margin-bottom: 10px;
        padding-left: 0;
        color: rgba(56, 55, 67, 0.6);
    }
}

.selected {
    color: #5050e6;
    border: 1px solid #5050e6;
    .doc-seg-option-desc{
        color: #5050e6;
    }
}

.custom-config {
    margin-bottom: 15px;
    margin-top: 16px;
    padding-top: 16px;
    border-top: 1px solid #E6E6E9;

    .item {
        margin-bottom: 20px;
        display: flex;
        flex-direction: column;

        .label {
            color: #383743;
            display: block;
            font-size: 14px;
            font-weight: 600;
            line-height: 22px;
            margin-bottom: 8px;
        }

        .requrie {
            &:after {
                color: #FF441E;
                content: "*";
                font-weight: 600;
                margin-left: 4px;
            }
        }
        ::v-deep(.el-radio-group .el-radio){
            margin-right: 0 !important;
            margin: 5px 0;
            width: 80px;
            height: auto;
        }
        ::v-deep(.el-radio__input) {
            position: initial;
        }
    }
}

::v-deep(.el-radio-group) {
    margin-right: 0 !important;
    width: 100%;
}

::v-deep(.el-radio.is-bordered) {
    padding: 0 15px 0 19px;
}

::v-deep(.el-radio-group .el-radio) {
    margin-right: 0 !important;
    margin: 5px 0;
    width: 100%;
    height: auto;
}

::v-deep(.el-radio-group .el-radio .el-radio__label) {
    width: 100%;
}

::v-deep(.el-radio__input) {
    position: absolute;
    top: 18px;
    left: 7px
}
</style>
const examples = [
  {
    key: "json",
    name: "天气(JSON)",
    content: `{
        "openapi": "3.1.0",
        "info": {
          "title": "Get weather data",
          "description": "Retrieves current weather data for a location.",
          "version": "v1.0.0"
        },
        "servers": [
          {
            "url": "https://weather.example.com"
          }
        ],
        "paths": {
          "/location": {
            "get": {
              "description": "Get temperature for a specific location",
              "operationId": "GetCurrentWeather",
              "parameters": [
                {
                  "name": "location",
                  "in": "query",
                  "description": "The city and state to retrieve the weather for",
                  "required": true,
                  "schema": {
                    "type": "string"
                  }
                }
              ],
              "deprecated": false
            }
          }
        },
        "components": {
          "schemas": {}
        }
      }`,
  },
  {
    key: "yaml",
    name: "宠物商店(YAML)",
    content: `# Taken from https://github.com/OAI/OpenAPI-Specification/blob/main/examples/v3.0/petstore.yaml
  
      openapi: "3.0.0"
      info:
        version: 1.0.0
        title: Swagger Petstore
        license:
          name: MIT
      servers:
        - url: https://petstore.swagger.io/v1
      paths:
        /pets:
          get:
            summary: List all pets
            operationId: listPets
            tags:
              - pets
            parameters:
              - name: limit
                in: query
                description: How many items to return at one time (max 100)
                required: false
                schema:
                  type: integer
                  maximum: 100
                  format: int32
            responses:
              '200':
                description: A paged array of pets
                headers:
                  x-next:
                    description: A link to the next page of responses
                    schema:
                      type: string
                content:
                  application/json:    
                    schema:
                      $ref: "#/components/schemas/Pets"
              default:
                description: unexpected error
                content:
                  application/json:
                    schema:
                      $ref: "#/components/schemas/Error"
          post:
            summary: Create a pet
            operationId: createPets
            tags:
              - pets
            responses:
              '201':
                description: Null response
              default:
                description: unexpected error
                content:
                  application/json:
                    schema:
                      $ref: "#/components/schemas/Error"
        /pets/{petId}:
          get:
            summary: Info for a specific pet
            operationId: showPetById
            tags:
              - pets
            parameters:
              - name: petId
                in: path
                required: true
                description: The id of the pet to retrieve
                schema:
                  type: string
            responses:
              '200':
                description: Expected response to a valid request
                content:
                  application/json:
                    schema:
                      $ref: "#/components/schemas/Pet"
              default:
                description: unexpected error
                content:
                  application/json:
                    schema:
                      $ref: "#/components/schemas/Error"
      components:
        schemas:
          Pet:
            type: object
            required:
              - id
              - name
            properties:
              id:
                type: integer
                format: int64
              name:
                type: string
              tag:
                type: string
          Pets:
            type: array
            maxItems: 100
            items:
              $ref: "#/components/schemas/Pet"
          Error:
            type: object
            required:
              - code
              - message
            properties:
              code:
                type: integer
                format: int32
              message:
                type: string`,
  },
  {
    key: "blankTemplate",
    name: "空白模板",
    content: `{
        "openapi": "3.1.0",
        "info": {
          "title": "Untitled",
          "description": "Your OpenAPI specification",
          "version": "v1.0.0"
        },
        "servers": [
          {
            "url": ""
          }
        ],
        "paths": {},
        "components": {
          "schemas": {}
        }
      }`,
  },
];

export default examples;

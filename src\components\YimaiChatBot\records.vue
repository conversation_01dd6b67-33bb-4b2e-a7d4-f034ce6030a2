<template>
    <div class="chat-records-main" ref="recordsBox">
        <div v-for="(item, index) in props.messageItemManager.refMessageItemList"
            :class="item.isUser?'user-record-box':'robot-record-box'"
            :key="index">
            <div class="head-img" v-if="!item.isUser">
              <img :src="item.roleInfo.agentIconUrl ? baseUrl+item.roleInfo.agentIconUrl:mockDefaultImg">
            </div>
            <div :class="item.isUser?userContentClassName:robotContentClassName" >
              <div class="user-name">
                {{ item.isUser ? '我' : item.roleInfo.agentName ? item.roleInfo.agentName : '小智'}}
              </div>
              <div :class="item.isUser?userBoxClassName:robotBoxClassName" >
                <div v-if="item.isUser">
                    {{ item.content }}
                </div>
                <template v-else>
                  <v-md-preview :text="item.content"/>
                </template>
              </div>
            </div>
            <div class="head-img" v-if="item.isUser">
              <img :src="mockUserImg">
            </div>
        </div>
        <div class="robot-record-box" v-if="props.messageItemManager.loading.value">
            <div class="head-img">
                <img :src="props.messageItemManager.nowMessageRole.value.agentIconUrl ? baseUrl+props.messageItemManager.nowMessageRole.value.agentIconUrl:mockDefaultImg">
            </div>
            <div class="robot-content">
              <div class="user-name">
                {{ props.messageItemManager.nowMessageRole.value.agentName? props.messageItemManager.nowMessageRole.value.agentName : '小智'}}
              </div>
              <div class="robot-record-item">
                  <template v-if="props.messageItemManager.waitting.value">
                      wait...
                  </template>
                  <template v-else>
                      <v-md-preview :text="props.messageItemManager.nowMessageHtml.value"/>
                  </template>
              </div>
            </div>
        </div>
    </div>
</template>
<script setup>
import VMdPreview from '@kangc/v-md-editor/lib/preview';
import '@kangc/v-md-editor/lib/style/base-editor.css';
import githubTheme from '@kangc/v-md-editor/lib/theme/github.js';
import '@kangc/v-md-editor/lib/theme/style/github.css';
import "highlight.js/styles/atom-one-light.css";
import hljs from 'highlight.js';
import {defineProps, watch, ref, nextTick} from 'vue';
import mockDefaultImg from '@/assets/images/roboto.jpg'

VMdPreview.use(githubTheme,{
  Hljs: hljs,
})

const props = defineProps({
    messageItemManager:{
        type:Object,
        required: false
    }
});

const baseUrl = import.meta.env.VITE_APP_BASE_API;
const recordsBox = ref();

const userBoxClassName = "user-record-item";
const userContentClassName = "user-content";

const robotBoxClassName = "robot-record-item";
const robotContentClassName = "robot-content";

const mockUserImg = 'https://gimg2.baidu.com/image_search/src=http%3A%2F%2Fsafe-img.xhscdn.com%2Fbw1%2F4d40b566-1f0a-4f8d-bc97-c513df8775b3%3FimageView2%2F2%2Fw%2F1080%2Fformat%2Fjpg&refer=http%3A%2F%2Fsafe-img.xhscdn.com&app=2002&size=f9999,10000&q=a80&n=0&g=0n&fmt=auto?sec=1706239065&t=82418a0dc2a04830de8580a08a94ff20'

const moveScroll = () => {
  nextTick(()=> {
    recordsBox.value.scrollTop = recordsBox.value.scrollHeight;
  })
}

watch(props.messageItemManager.nowMessageHtml, () => {
  moveScroll();
})

watch(props.messageItemManager.nowMessageLine, () => {
  moveScroll();
});

watch(props.messageItemManager.refMessageItemList, () => {
  moveScroll();
});

</script>

<style lang="scss" >
.chat-records-main {
  height: 100%;
  width: 100%;
  overflow-y: auto;
  overflow-x: hidden;
}

.record-item {
  display: inline-block;
  border-radius: 10px;
  background-color: #fff;
  padding: 8px 12px;
  max-width: calc(100% - 60px);
  margin-bottom: 10px;
  font-size: 15px;
}

.user-content{
  display: flex;
  flex-direction: column;
  align-items: end;
  width: 100%;
}

.robot-content {
  display: flex;
  flex-direction: column;
  align-items: start;
  width: 100%;
}

.user-record-item {
  @extend .record-item;
  color: white;
  background-color: #5050e6;
  text-align: left;
  font-size: 14px;
  font-weight: 500;
}

.robot-record-item {
  @extend .record-item;
  color: #032220;
  background-color: #F9FBFC;
  font-size: 14px;
  font-weight: 500;
}

.record-box {
  width: calc(100% - 20px);
  margin-left: 10px;
  display: inline-flex;
  .head-img{
    img{
      height: 40px;
      width: 40px;
      border: 2px solid #E6E6E6;
      border-radius: 8px;
      margin: 0 10px;
    }
  }
  
}
.user-name{
    line-height: 20px;
    height: 20px;
    color: #B2B2B2;
    font-size: 12px;
  }
.user-record-box {
  @extend .record-box;
  justify-content: flex-end;
  text-align: right;
  float: right;
}

.robot-record-box {
  @extend .record-box;
  text-align: left;
}

.code-pre {
  overflow-x: auto;
}
p{
  margin-bottom: 0 !important;
}
.github-markdown-body{
  padding: 0;
  font-size: 14px;
  font-weight:500;
}
</style>
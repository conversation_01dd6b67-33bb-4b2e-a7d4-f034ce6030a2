<template>
    <div class="edit-custom-tool">
        <div class="content">
            <div class="main">
                <div>
                    <div class="label required">名称</div>
                    <div class="field">
                        <span class="emoji" :style="{backgroundColor:formData.icon.background}" @click="showEmojiPicker">
                            <single-emoji :content="formData.icon.content"></single-emoji>
                        </span>
                        <el-input v-model="formData.provider" placeholder="输入工具名称" />
                    </div>
                </div>
                <div>
                    <div class="schema-label">
                        <div class="left-side">
                            <div class="label   required">Schema</div>
                            <div class="divider"></div>
                            <a target="_blank" href="https://swagger.io/specification/" rel="noopener noreferrer"
                                class="link">
                                <div>查看 OpenAPI-Swagger 规范</div>
                            </a>
                        </div>
                        <div class="right-side">
                            <el-dropdown trigger="click">
                                <el-button size="small">
                                    <el-icon class="el-icon--right"><Plus /></el-icon>从URL中导入
                                </el-button>
                                <template #dropdown>
                                    <div style="display: flex;
                                        flex-direction: row;
                                        align-items: center;
                                        padding: .5rem;
                                        width: 244px;">
                                        <el-input v-model="openAPIUrl" size="small" placeholder="输入URL" />
                                        <el-button style="margin-left: 5px;" size="small" type="primary" @click="importFromUrl">导入</el-button>
                                    </div>
                                </template>
                            </el-dropdown>
                            <el-dropdown @command="changeSchema" trigger="click">
                                <el-button size="small" style="margin-left: 5px;">
                                    例子<el-icon class="el-icon--right"><arrow-down /></el-icon>
                                </el-button>
                                <template #dropdown>
                                    <el-dropdown-menu>
                                        <template v-for="(example,index) in examples">
                                            <el-dropdown-item :command="index">{{example.name}}</el-dropdown-item>
                                        </template>
                                    </el-dropdown-menu>
                                </template>
                            </el-dropdown>
                        </div>
                    </div>
                    <el-input type="textarea" v-model="formData.schema" :rows="13"  
                        placeholder="请输入您的OpenAPI schema" @change="schemaChange"></el-input>
                </div>
                <div>
                    <div class="label">
                        可用工具
                    </div>
                    <el-table :data="formData.paramsSchemas" width="100%" border>
                        <el-table-column prop="operation_id" label="名称" show-overflow-tooltip  />
                        <el-table-column prop="summary" label="描述" width="236px"/>
                        <el-table-column prop="method" label="方法" width="60px" />
                        <el-table-column label="路径" >
                            <template #default="scope">
                               {{ getPath(scope.row.server_url) }}
                            </template>
                        </el-table-column>
                        <el-table-column label="操作" width="76px">
                            <template #default="scope">
                                <test-api :toolInfo="{
                                    tool_name:scope.row.operation_id,
                                    schema_type:formData.schema_type,
                                    schema:formData.schema,
                                    provider_name:formData.provider,
                                    credentials:formData.credentials,
                                    parameters:scope.row.parameters
                                }"></test-api>
                            </template>
                        </el-table-column>
                    </el-table>
                </div>
                <config-credentials :credentials="formData.credentials" 
                    @change:credentials="updateCredentials">
                </config-credentials>
                <div>
                    <div class="label">
                        标签
                    </div>
                    <el-select v-model="formData.labels" multiple filterable >
                        <template v-for="item in labelOptions">
                            <el-option :label="item.label.zh_Hans" :value="item.name" />
                        </template>
                    </el-select>
                </div>
                <div>
                    <div class="label">
                        隐私协议
                    </div>
                    <el-input :maxlength="200" v-model="formData.privacy_policy" placeholder="输入隐私协议" />
                </div>
                <div>
                    <div class="label">
                        自定义免责声明
                    </div>
                    <el-input :maxlength="200" v-model="formData.custom_disclaimer" placeholder="输入自定义免责声明" />
                </div>
            </div>
        </div>
        <el-dialog title="选择图标" v-model="emojiPickerVisible" width="600px" append-to-body>
            <emoji-picker ref="emojiPickerRef"/>
            <div style="display: flex;width: 100%;justify-content: center;align-items: center;margin-top: 20px">
                <el-button type="primary" @click="confrimIcon">确认</el-button>
                <el-button @click="emojiPickerVisible = false">取消</el-button>
            </div>
        </el-dialog>
    </div>
</template>
<script setup>
import { ArrowDown } from '@element-plus/icons-vue'
import { getSchemaToolsByProvider,getAllTags } from '@/api/newTool'
import singleEmoji from './singleEmoji'
import emojiPicker from './emojiPicker'
import examples from './examples'
import configCredentials from './configCredentials'
import testApi from './testApi'
import {importOpenApiSchema} from '@/api/newTool/index'
import { ElMessage } from 'element-plus'

const formData = ref({
    icon:{
        content:"🕵️",
        background: "#FEF7C3"
    },
    provider:'',
    schema:'',
    paramsSchemas:[],
    credentials:{
        api_key_header: "Authorization",
        api_key_header_prefix: "bearer",
        api_key_value: "",
        auth_type: "none"
    },
    labels:[],
    privacy_policy:'',
    custom_disclaimer:'',
    schema_type:''
})

const labelOptions = ref([])
const emojiPickerVisible = ref(false)
const emojiPickerRef = ref(null)
const openAPIUrl = ref('')

const schemaChange = (val) => {
    getSchemaToolsByProvider({schema:val}).then(res => {
        let {parameters_schema,schema_type} = res
        formData.value.paramsSchemas = parameters_schema
        formData.value.schema_type = schema_type
    }).catch(err => {
        formData.value.paramsSchemas = []
        formData.value.schema_type = ''
    })
}

const getPath = (url) => {
    if (!url)
      return ''

    try {
      const path = new URL(url).pathname
      return path || ''
    }
    catch (e) {
      return url
    }
}

const showEmojiPicker = () => {
    emojiPickerVisible.value = true
}

const confrimIcon = () => {
    const icon = {
        content:emojiPickerRef.value.currentEmoji,
        background:emojiPickerRef.value.currentBackgroundColor
    }
    formData.value.icon = icon
    emojiPickerVisible.value = false
}

const changeSchema = (index) => {
    const exampleContent = examples[index]
    formData.value.schema = exampleContent?.content
    schemaChange(formData.value.schema)
}

const updateCredentials = (val) => {
    formData.value.credentials = val
}

const importFromUrl = () => {
    importOpenApiSchema({url:openAPIUrl.value}).then(res => {
        formData.value.schema = res.schema
        schemaChange(formData.value.schema)
    })
}

const resetFromValue = () => {
    formData.value = {
        icon:{
            content:"🕵️",
            background: "#FEF7C3"
        },
        provider:'',
        schema:'',
        paramsSchemas:[],
        credentials:{
            api_key_header: "Authorization",
            api_key_header_prefix: "bearer",
            api_key_value: "",
            auth_type: "none"
        },
        labels:[],
        privacy_policy:'',
        custom_disclaimer:'',
        schema_type:''
    }
}

const validateValue = () => {
    return new Promise((resolve,reject) => {
        if(!formData.value.provider){
            ElMessage.error('工具名称不能为空')
            reject()
        }else if(!formData.value.schema){
            ElMessage.error('工具Schema不能为空')
            reject()
        }else if(!formData.value.schema_type){
            ElMessage.error('请输入正确的Schema')
            reject()
        }else{
            resolve()
        }
    })
}

getAllTags().then(res => {
    labelOptions.value = res
})

defineExpose({
    formData,
    resetFromValue,
    validateValue
})

</script>

<style scoped lang="scss">
.edit-custom-tool{
    flex-grow: 1;
    overflow-y: auto;
    .content{
        display: flex;
        flex-direction: column;
        height: 100%;
        .main{
            height: 0;
            flex-grow: 1;
            overflow-y: auto;
            padding: .75rem 1.5rem;
            .required::after{
                content: '*';
                color: #f56c6c;
                margin-left: 4px;
            }
            .label{
                padding: .5rem 0;
                font-size: .875rem;
                font-weight: 500;
                color: rgb(16, 24, 40);
                line-height: 1.25rem;
            }
            .field{
                display: flex;
                align-items: center;
                justify-content: space-between;
                gap: .75rem;
            }
            .emoji{
                height: 36px;
                width: 36px;
                border-radius: .5rem;
                display: flex;
                position: relative;
                align-items: center;
                justify-content: center;
                line-height: 1.75rem;
                flex-shrink: 0;
                cursor: pointer;
            }
            .schema-label{
                display: flex;
                align-items: center;
                justify-content: space-between;
                .left-side{
                    display: flex;
                    align-items: center;
                    .divider{
                        margin-left:.5rem;
                        margin-right:.5rem;
                        height: .75rem;
                        width: 1px;
                        border: 0.5px solid rgba(0, 0, 0, 0.05);
                    }
                    .link{
                        display: flex;
                        height: 18px;
                        align-items: center;
                        color: rgb(21, 94, 239);
                        font-size: .75rem;
                        line-height: 1rem;
                        font-weight: 400;
                    }
                }
                .right-side{
                    display: flex;
                    align-items: center;
                    justify-content: flex-end;
                    width: 224px;
                    position: relative;
                    .url-import-schema{
                        display: flex;
                        flex-direction: row;
                        padding: .5rem;
                        width: 244px;
                    }
                }
            }
            >:not([hidden])~:not([hidden]) {
                margin-top: 1rem;
            }
        }
    }
}
</style>

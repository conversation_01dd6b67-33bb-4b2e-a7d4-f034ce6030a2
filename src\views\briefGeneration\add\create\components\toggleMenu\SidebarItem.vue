<template>
  <div v-if="!item.hidden">
    <template v-if="!item.children || item.children.length === 0">
      <el-menu-item
        :index="String(item.id)"
        @click="() => handleMenuClick(item)"
        style="width: 100%"
        :disabled="startGenerating"
      >
        <template #title>
        <div class="menu-title">
          <div :title="item.name" class="name">{{ item.name }}</div>
          <div v-show="item.parentId==0&&pageType=='add'" v-html="statusDom" class="statusBox"></div>
        </div>
        </template>
      </el-menu-item>
    </template>
    <el-sub-menu v-else ref="subMenu" :index="String(item.id)" teleported>
      <template #title>
        <div class="menu-title">
          <div :title="item.name" class="name">{{ item.name }}</div>
          <div v-show="item.parentId==0&&pageType=='add'" v-html="statusDom" class="statusBox"></div>
        </div>
      </template>
      <sidebar-item
        v-for="(child, i) in item.children"
        :key="i"
        :is-nest="true"
        :item="child"
        class="nest-menu"
      />
    </el-sub-menu>
  </div>
</template>

<script setup>
import { isExternal } from "@/utils/validate";
import { getNormalPath } from "@/utils/ruoyi";
import { startGenerating,pageType } from "@/views/briefGeneration/add/create/js/sharedState.js";
import wait from "@/assets/images/brief/wait.svg" 
import load from "@/assets/images/brief/load.svg" 
import end from "@/assets/images/brief/end.svg" 
const props = defineProps({
  // route object
  item: {
    type: Object,
    required: true,
  },
});
function handleMenuClick(menuItem) {
  const id = String(menuItem.id).replace(/^\//, ""); // 去掉前导斜杠
  const el = document.getElementById(id);
  console.log('id,el :>> ', id,el);
  if (el) {
    el.scrollIntoView({ behavior: "smooth", block: "start" });
  }
}
const statusDom = computed(() => {
  if(props.item.status=='wait'){
    return `<img src="${wait}" style="width:24px;height:24px;margin-right:5px;" />待生成`
  }else if(props.item.status=='load'){
    return `<img src="${load}" style="width:24px;height:24px;margin-right:5px;" />生成中`
  }else if(props.item.status=='end'){
    return `<img src="${end}" style="width:24px;height:24px;margin-right:5px;" />完成`
  }
});
</script>
<style lang="scss" scoped>
/*隐藏文字*/
.el-menu--collapse .el-sub-menu__title span {
  display: none;
}
/*隐藏 > */
.el-menu--collapse .el-sub-menu__title .el-submenu__icon-arrow {
  display: none;
}
.el-menu--collapse .el-sub-menu.is-active {
  background: #1e4462;
}
.el-menu-item.is-active {
  background: #1e4462;
}
.el-menu-item.is-active,
.el-sub-menu.is-active {
  position: relative; // 让伪元素定位参照
}
.el-menu-item.is-active,
.el-sub-menu.is-active > .el-sub-menu__title {
  position: relative;
}

.el-menu-item.is-active::after,
.el-sub-menu.is-active > .el-sub-menu__title::after,
.el-menu-item:hover::after,
.el-sub-menu:hover > .el-sub-menu__title::after {
  content: "";
  display: block;
  position: absolute;
  right: 0;
  top: 0;
  width: 2.5px;
  height: 100%;
  background: #6AF6FF;
  border-radius: 2px 0 0 2px;
  z-index: 1;
}
.menu-title {
  width: 100%;
  display: flex;
  // justify-content: space-between;
  align-items: center;
  .name{
    overflow: hidden;
    text-overflow: ellipsis;
    flex: 1;
    min-width: 0;
  }
}
.statusBox{
  display: flex;
  align-items: center;
  color: rgba(172,225,231,1);
}
</style>

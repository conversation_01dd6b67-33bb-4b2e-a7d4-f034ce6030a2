<template>
  <el-scrollbar ref="scrollbarRef">
    <slot></slot>
  </el-scrollbar>
</template>

<script setup>
import { watch, nextTick } from 'vue'

const props = defineProps({
  watch: {
    type: null,
    required: false,
    default: ''
  }
})

const scrollbarRef = ref(null)

function scrollToBottom() {
  console.log('------scroll-----')
  scrollbarRef.value.setScrollTop(1e10)
}

watch(() => props.watch, () => {
  nextTick(() => {
    scrollToBottom()
  })
}, {deep: true})
</script>